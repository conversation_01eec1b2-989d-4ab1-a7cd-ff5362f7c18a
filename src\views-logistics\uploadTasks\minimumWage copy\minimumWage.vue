<template>
  <content-panel>
    <!-- 保底工资 -->
    <template v-slot:search>
      <search-box
        class="search-box">
        <el-form
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          label-position="left"
          size="mini">
          <el-form-item
            label="员工姓名:"
            prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch">
              <template
                slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="员工姓名" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item
            label="厂牌编号:"
            prop="staffCode">
            <el-input
              clearable
              v-model.trim="searchForm.staffCode"
              size="mini"
              @keyup.enter.native="onSearch">
              <template
                slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenStaffCodes"
                  titleName="厂牌编号" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item
            label="区域:"
            prop="staffCode">
            <el-select
              v-model="searchForm.processId"
              filterable
              clearable
              placeholder="请选择区域"
              @change="onSearch">
              <el-option
                v-for="item in procedureOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="核算班组:"
            prop="processId">
            <el-select
              v-model="searchForm.processId"
              filterable
              clearable
              placeholder="请选择核算班组"
              @change="onSearch">
              <el-option
                v-for="item in procedureOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template
          v-slot:right>
          <el-button
            size="small"
            type="primary"

            @click="onSearch">
            查询
          </el-button>
          <el-button
            size="small"
            type="warning"
            @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel
      ref="tablePanel">
      <template
        v-slot:header-right>
          <el-button
          size="small"
          type="primary"
          @click="handleAdd">
          新增
        </el-button>
        <el-button
          size="small"
          type="primary"
          @click="batchDelete">
          批量删除
        </el-button>
      </template>
      <el-table stripe border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :data="tableData"
        :height="maxTableHeight"
        @selection-change="onSelectionChange">
        <el-table-column
          width="40"
          type="selection">
        </el-table-column>
        <el-table-column
          label="员工姓名"
          prop="staffName"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="厂牌编号"
          prop="staffCode"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="区域"
          prop="machineNum"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="班组"
          prop="assessAmount"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="总出勤天数"
          prop="accountingMonth"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="保底工资"
          prop="companyAllowance"
          show-overflow-tooltip>
          <template
            slot-scope="{row}">
            {{ row.amount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="left"
          width="80">
          <template
            slot-scope="{row}">
            <el-button
              type="text"
              size="small"
              @click="handleEdit(row)">
              编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div
          class="table_footer">
          <ul>
            <li>
              <span>核算月份:</span><span>{{ infoList.accountingMonth }}</span>
            </li>
            <li>
              <span>人数:</span><span>{{ debitInfo.people }}</span>
            </li>
            <li>
              <span>保底工资:</span><span>{{ debitInfo.companyAllowance | moneyFormat }}</span>
            </li>
          </ul>
        </div>
        <el-pagination
          :current-page="pageNum"
          :page-size="pageSize"
          :total="total"
          :page-sizes="[50, 100, 200]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onSizeChange"
          @current-change="onNumChange">
        </el-pagination>
      </template>
    </table-panel>
    <add-dialog
      v-if="visible"
      :visible="visible"
      :title="title"
      :editForm="editForm"
      @cancel="handleCancel"></add-dialog>
    <common-dialog
      v-if="isVisible"
      :isVisible="isVisible"
      :list="list"
      @cancel="commonCancel"></common-dialog>
  </content-panel>
</template>
<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import addDialog from './addDialog'
import commonDialog from './commonDialog'
export default {
  name: "MinimumWage",
  mixins: [tableMixin,pagePathMixin],
  components: {
    addDialog,
    commonDialog
  },
  data() {
    return {
      searchForm: {
        staffCode: "",
        staffName: "",
      },
      infoList: {},
      tableData: [{},{},{},{}],
      procedureOptions: [],
      loading: false,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      filterParam: {},
      params: {},
      factoryId: "",
      permission: "",
      resizeOffset: 70,
      visible: false,
      isVisible: false,
      editForm: {},
      debitInfo: { sysTotal: 0, totalSocialSecurity: 0 },
    };
  },
  watch: {
    $route: {
      handler(value) {
        if (value.path.includes("minimumWage") && value.path.includes("logistics")) {
          this.infoList = JSON.parse(this.$Base64.decode(value.query.data));
          this.factoryId = this.infoList.factoryId;
          // this.getList();
          this.getDebitList();
          this.getPermission();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    //获取保底工资列表
    getList() {
      this.loading = true;
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        filterData: {
          factoryId: this.factoryId,
          accountingMonth: this.infoList.accountingMonth,
          ...this.filterParam,
          ...this.params,
        },
      };
      this.$api.logisticsDataUpload.coSubsidy
        .getCoSubsidy(params)
        .then(({ data: { list, total } }) => {
          this.tableData = list || [];
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 获取底部数据信息
    getDebitList() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.infoList.accountingMonth,
        ...this.filterParam,
        ...this.params,
      };
      this.$api.logisticsDataUpload.coSubsidy.coSubsidyStatistic(params).then((res) => {
        this.debitInfo = res.data
          ? res.data
          : {
            sysTotal: 0,
            totalSocialSecurity: 0,
          };
      });
    },
    //是否具有待办任务操作权限
    getPermission() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.infoList.accountingMonth,
        type: "30",
      };
      this.$api.logisticsWorkbench.agencyPermission(params).then((res) => {
        this.permission = res.data;
      });
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
      this.getDebitList();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    onSelectionChange() { },
    //新增
    handleAdd(row) {
      this.title="新增"
      this.visible = true
      this.editForm = {}
    },
    //编辑
    handleEdit(row) {
      this.title="编辑"
      this.visible = true
      this.editForm = {
        id: row.id || '',
      }
    },
    //批量删除
    batchDelete() {
      this.isVisible = true
    },
    handleCancel(type) {
      this.visible = false;
      if (type == 'cancel') return
      this.getList()
      this.getDebitList()
    },
    commonCancel(type) {
      this.isVisible = false;
      if (type == 'cancel') return
      this.getList()
      this.getDebitList()
    },
    onSizeChange(pageSize) {
      this.pageSize = pageSize;
      // 切换每页条数后从第一页查询
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },

  },
};
</script>
<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}

ul {
  list-style: none;
  display: flex;
  padding: 0;
}

i {
  font-style: normal;
}

>>>.table-panel-footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .table_footer {
    overflow: auto;

    >ul {
      display: flex;
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        white-space: nowrap;
        padding: 0 10px;
      }
    }
  }
}
</style>
