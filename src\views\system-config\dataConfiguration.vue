<template>
  <content-panel>
    <template v-slot:search>
      <search-box
        class="search-box">
        <el-form
          :inline="true"
          :model="logForm"
          ref="searchForm"
          label-position="left"
          size="mini">
          <el-form-item
            label="数据名称:"
            prop="description">
            <el-input
              v-model="logForm.dataItemName"
              size="mini"
              clearable
              placeholder="请输入数据名称"
              @keyup.enter.native="onSearch">
            </el-input>
          </el-form-item>
          <!-- <el-form-item label="数据类型:" prop="materialCode">
            <el-select
              @change="onSearch"
              v-model="logForm.processId"
              filterable
              placeholder="请选择数据类型"
            >
              <el-option
                v-for="item in procedureOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item> -->
        </el-form>
        <template
          v-slot:right>
          <el-button
            size="small"
            type="primary"
            @click="onSearch">
            查询</el-button>
          <el-button
            size="small"
            type="warning"
            @click="resetSearchForm">
            重置</el-button>
        </template>
      </search-box>
    </template>
    <table-panel
      ref="tablePanel">
      <template
        v-slot:header-right>
        <div ref="btnRight"
          style="display: flex; justify-content: space-between">
          <el-button
            @click="add"
            size="small"
            type="primary">新增
          </el-button>
        </div>
      </template>
      <el-table stripe border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :data="tableData"
        :height="maxTableHeight"
        style="width: 100%">
        <!-- <el-table-column
          prop="serialNumber"
          label="编号"
          width="230"
          align="left"
        >
        </el-table-column> -->
        <el-table-column
          prop="dataItemName"
          label="数据名称"
          width="230"
          align="left">
        </el-table-column>
        <el-table-column
          prop="dataType"
          label="数据类型"
          width="300"
          align="left">
          <template
            slot-scope="scope">
            {{ scope.row.dataType == 0 ? "固定配置" : "灵活配置" }}
          </template>
        </el-table-column>
        <el-table-column
          prop="isActive"
          label="启用状态"
          align="left">
          <template
            slot-scope="scope">
            <div>
              <el-switch
                @change="onSwitch(scope.row)"
                inactive-color="#ff4949"
                v-model="scope.row.isActive"
                :active-text="scope.row.isActive == 1 ? '启用' : '禁用'"
                :active-value="1"
                :inactive-value="0">
              </el-switch>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="comments"
          label="备注说明"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="otherPlanName"
          label="所属任务"
          align="left">
        </el-table-column>
        <el-table-column
          align="left">
          <template
            slot-scope="scope">
            <el-button
              size="small"
              type="text"
              @click="edit(scope.row)">
              编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageNum"
        :page-size="pageSize"
        :total="total"
        :page-sizes="[50, 100, 200]"
        layout="total, sizes, prev, pager, next, jumper">
      </el-pagination>
    </table-panel>
    <q-dialog
      :visible="visible"
      :innerScroll="true"
      :title="title"
      :innerHeight="330"
      width="500px"
      :isLoading="isLoading"
      @cancel="handleCancel"
      @confirm="handleConfirm"
      :before-close="handleCancel">
      <el-form :inline="false"
        :model="logForms"
        ref="searchForm"
        label-position="left"
        label-width="90px"
        size="mini">
        <el-form-item
          label="数据名称:">
          <el-input
            v-model.trim="logForms.dataItemName" clearable>
          </el-input>
        </el-form-item>
        <!-- <el-form-item label="启用状态:">
          <el-switch
            active-color="#13ce66"
            inactive-color="#ff4949"
            v-model="logForms.status"
            :active-text="logForms.status ? '启用' : '禁用'"
          >
          </el-switch>
        </el-form-item> -->
        <el-form-item
          label="备注说明:"
          label-position="top">
          <el-input
            type="textarea"
            resize="none"
            rows="10"
            maxlength="300"
            show-word-limit
            v-model.trim="logForms.comments">
          </el-input>
        </el-form-item>
      </el-form>
    </q-dialog>
  </content-panel>
</template>

  <script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import moment from "moment";
export default {
  name: "Configuration",
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      logForm: {
        dataItemName: "",
      },
      logForms: {
        comments: "",
        dataItemName: "",
      },
      procedureOptions: [
        { id: "1", name: "测试" },
        { id: "2", name: "测试2" },
      ],
      tableData: [],
      loading: false,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      filterParam: {},
      visible: false,
      title: "",
      Info: {},
      isLoading:false
    };
  },
  created() {
    this.getList();
  },
  mounted() { },

  methods: {
    //搜索
    onSearch() {
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.logForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      this.pageNum = 1;
      this.getList();
    },
    getList() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        filterData: {
          ...this.logForm,
        },
      };
      this.$api.dataConfiguration
        .dataConfigList(params)
        .then((res) => {
          this.tableData = res.data.list;
          this.total = res.data.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    add() {
      this.title = "新增";
      this.visible = true;
      this.logForms = {
        comments: "",
        dataItemName: "",
      };
    },
    onSwitch(row) {
      // console.log("row :>> ", row.isActive);
      // var formData = new FormData();
      // formData.append("id", row.id);
      this.$api.dataConfiguration.dataConfigToggle(row.id).then((res) => {
        if (res.code == 200) {
          if (row.isActive == 1) {
            this.$notify.success({
              title: "成功",
              message: "启用成功",
            });
          } else if (row.isActive == 0) {
            this.$notify.success({
              title: "成功",
              message: "禁用成功",
            });
          }
        }
        this.getList();
      });
    },
    edit(row) {
      this.title = "编辑";
      this.visible = true;
      this.logForms = {
        comments: row.comments,
        dataItemName: row.dataItemName,
      };
      this.Info = {
        ...row,
      };
    },
    handleCancel() {
      this.visible = false;
    },
    handleConfirm() {
      if (this.title == "新增") {
        let params = {
          ...this.logForms,
        };
        this.isLoading = true;
        this.$api.dataConfiguration.dataConfigSave(params).then((res) => {
          if (res.code == 200) {
            this.$notify.success({
              title: "成功",
              message: "新增成功",
            });
            this.getList();
            this.visible = false;
          }
        }).finally(()=>{
          this.isLoading = false;
        });
      } else if (this.title == "编辑") {
        let params = {
          ...this.logForms,
          id: this.Info.id,
        };
        this.$api.dataConfiguration.dataConfigUpdate(params).then((res) => {
          if (res.code == 200) {
            this.$notify.success({
              title: "成功",
              message: "编辑成功",
            });
            this.getList();
            this.visible = false;
          }
        }).finally(()=>{
            this.isLoading = false;
        });
      }
    },
    //重置
    resetSearchForm() {
      this.logForm.dataItemName = ''
      this.onSearch();
    },
    //分页   页数条数改变函数
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    handleCurrentChange(val) {
      this.pageNum = val;
      this.getList();
    },
    handleEdit() { },
    handleDelete() { },
  },
};
</script>
  <style>
.el-tooltip__popper {
  max-width: 30% !important;
}
</style>
  <style lang="stylus" scoped>
  .el-form-item--mini.el-form-item, .el-form-item--small.el-form-item {
    margin-bottom: 25px;
  }

  >>> .el-form--inline .el-form-item {
    margin-right: 40px;
  }

  // >>> .el-input__suffix {
  // .el-icon-circle-close:before {
  // margin-left: -70px;
  // }
  // }
  >>> .el-icon-more {
    margin-top: 1px;
    padding: 1px;
    width: 40px;
    height: 24px;
    text-align: center;
    line-height: 22px;
    border: 1px solid rgba(121, 121, 121, 1);
    border-radius: 0px 2px 2px 0px;
    position: absolute;
    background-color: rgba(242, 242, 242, 1);
    right: -5px;

    &.active {
      color: #ff8900;
      border: 1px solid #ff8900;
    }
  }

  .code-collection {
    position: absolute;
    top: 45px;
    left: 90px;
    width: 250px;
    height: 436px;
    padding: 10px;
    box-sizing: border-box;
    background: #fff;
    -moz-box-shadow: 0px 0px 7px #BFBFBF;
    -webkit-box-shadow: 0px 0px 7px #BFBFBF;
    box-shadow: 0px 0px 7px #BFBFBF;
    border-radius: 5px;
    z-index: 2001;

    .title {
      color: #666;
    }

    .btn-div {
      padding: 10px 0 10px 10px;
      text-align: right;
      font-size: 12px;

      .qk, .gb, .ss {
        display: inline-block;
        padding: 4px 15px 2px;
        border-radius: 4px;
        border: 1px solid #dedede;
        margin-left: 10px;
        cursor: pointer;
      }

      .gb {
        background: #0bb78e;
        border: 1px solid #0bb78e;
        color: #fff;
      }

      .ss {
        background: #ff8900;
        border: 1px solid #ff8900;
        color: #fff;
      }
    }
  }
</style>
