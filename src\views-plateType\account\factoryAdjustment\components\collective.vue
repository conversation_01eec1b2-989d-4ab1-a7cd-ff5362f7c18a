<template>
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form size="mini" :inline="true" :model="searchForm" ref="searchForm" label-width="90px" class="rangeTime"
          label-position="right">
          <el-form-item label="核算班组:" prop="groupId">
            <el-select v-model="searchForm.groupId" filterable clearable placeholder="请选择核算班组"
              @change="handleGroupChange">
              <el-option v-for="item in groupList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary" @click="handleSearch">
            查询
          </el-button>
          <el-button size="small" type="warning" @click="handleReset">
            重置
          </el-button>
        </template>
      </search-box>
    </template>

    <table-panel ref="tablePanel">
      <el-table stripe border v-loading="loading" ref="tableRef" highlight-current-row :height="maxTableHeight"
        :data="tableData" :key="tableKey" :row-key="getRowKey">
        <el-table-column type="index" width="50" />
        <el-table-column prop="groupName" label="核算班组" align="left" />
        <el-table-column prop="beforeAmount" label="调整前计件金额" align="left" >
           <template slot="header">
            <span>调整前计件金额</span>
            <el-tooltip placement="top">
              <div slot="content">
                调整前计件金额=系统自动汇总金额+手工分摊金额。
              </div>
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="inAmount" label="本月划入" align="left" />
        <el-table-column prop="outAmount" label="本月划出" align="left" />
        <el-table-column prop="afterAmount" label="调整后计件金额" align="left" >
          <template slot="header">
            <span>调整后计件金额</span>
            <el-tooltip placement="top">
              <div slot="content">
                调整后计件金额=调整前计件金额+本月划入（-本月划出）。
              </div>
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="reason" label="调整事由" show-overflow-tooltip align="left" />
        <el-table-column prop="updateTime" label="修改时间" align="left" />
        <el-table-column label="操作" align="left" width="235">
          <template slot-scope="scope">
            <el-button type="text" size="mini"  v-show="permission && detailInfo.nodeName == '分厂调整-未提交'" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button type="text" size="mini" @click="handleEditRecord(scope.row)">
              调整记录
            </el-button>
            <el-button type="text" size="mini"  v-show="permission && detailInfo.nodeName == '分厂调整-未提交'" @click="handleDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <template v-slot:footer>
        <div class="table_footer">
          <ul>
            <li>
              <span>核算工厂:</span>
              <span>{{ factoryInfo.factoryName }}</span>
            </li>
            <li>
              <span>核算月份:</span>
              <span>{{ factoryInfo.accountingMonth }}</span>
            </li>
            <li>
              <span>调整前计件金额:</span>
              <span>{{ adjustmentInfo.beforeAmount | moneyFormat }}</span>
            </li>
            <li>
              <span>本月划入:</span>
              <span>{{ adjustmentInfo.inAmount | filterDataPiece }}</span>
            </li>
            <li>
              <span>本月划出:</span>
              <span>{{ adjustmentInfo.outAmount | filterDataPiece }}</span>
            </li>
            <li>
              <span>调整后计件金额:</span>
              <span>{{ adjustmentInfo.afterAmount | moneyFormat }}</span>
            </li>
          </ul>
        </div>

        <div style="text-align: right">
          <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="pagination.pageNum" :page-size="pagination.pageSize" :total="pagination.total"
            :page-sizes="PAGE_SIZES" layout="total, sizes, prev, pager, next, jumper" />
        </div>
      </template>
    </table-panel>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import { moneyFormatZh } from "@/utils";

// 常量定义
const ALLOT_METHOD = {
  GROUP: 'group',
  PERSON: 'person'
};

const PAGE_SIZES = [50, 100, 200];
const DEFAULT_PAGE_SIZE = 50;

export default {
  name: "CollectiveAdjustment",
  mixins: [tableMixin, pagePathMixin],
  props: { 
    permission: {
      type: String|Boolean,
      required: true,
    },
    detailInfo: Object,
  },
  data() {
    return {
      // 常量
      ALLOT_METHOD,
      PAGE_SIZES,

      // 搜索表单
      searchForm: {
        groupId: "",
        allotMethod: ALLOT_METHOD.GROUP,
      },

      // 过滤参数
      filterParam: {
        allotMethod: ALLOT_METHOD.GROUP,
      },

      // 数据列表
      groupList: [],
      tableData: [],
      adjustmentInfo: {},
      factoryInfo: {},

      // 状态
      loading: false,
      tableKey: 0,

      // 分页
      pagination: {
        pageSize: DEFAULT_PAGE_SIZE,
        pageNum: 1,
        total: 0,
      },

      // 其他
      resizeOffset: 55,
    };
  },

  async created() {
    await this.initializeData();
    this.handleSearch();
  },

  filters: {
    filterData(value) {
      return !value ? "-" : moneyFormatZh(value);
    },
    filterDataPiece(value) {
      return !value ? "0" : moneyFormatZh(value);
    }
  },

  methods: {
    // 初始化数据
    async initializeData() {
 
        this.factoryInfo = JSON.parse(this.$Base64.decode(this.$route.query.data));
        await this.loadGroupList();
      
    },

    // 加载班组列表
    async loadGroupList() {
     
        const { data } = await this.$api.plateTypeSystemManage.getBasicPermission.getBasicGroupList(this.factoryInfo.factoryId);
        this.groupList = data.map(item => ({
          label: item.name,
          name: item.name,
          id: item.id,
        }));
      
    },

    // 获取行键
    getRowKey(row) {
      return row.id;
    },

    // 构建请求参数
    buildRequestParams() {
      return {
        accountingMonth: this.factoryInfo.accountingMonth,
        factoryId: this.factoryInfo.factoryId,
        allotMethod: ALLOT_METHOD.GROUP,
        ...this.filterParam,
      };
    },

    // 获取调整统计信息
    async getAdjustmentStatistics() {
    
        const params = this.buildRequestParams();
        const { data } = await this.$api.plateTypeWorkbench.factoryAdjustment.getFactoryAdjustmentStatistics(params);
        this.adjustmentInfo = data || {};
    
    },

    // 获取列表数据
    async getList() {
      this.loading = true;
      try {
        await this.getAdjustmentStatistics();

        const params = {
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize,
          filterData: this.buildRequestParams(),
        };

        const { data: { list, total } } = await this.$api.plateTypeWorkbench.factoryAdjustment.getFactoryAdjustmentList(params);

        this.tableData = list || [];
        this.pagination.total = total;
        this.tableKey = Math.random();
      }  finally {
        this.loading = false;
      }
    },

    // 构建过滤参数
    buildFilterParams() {
        const filterParam = { allotMethod: ALLOT_METHOD };

      for (const [key, val] of Object.entries(this.searchForm)) {
        if (this.isValidValue(val)) {
          filterParam[key] = val;
        }
      }

      return filterParam;
    },

    // 验证值是否有效
    isValidValue(value) {
      return value !== undefined && value !== null && value !== "";
    },

    // 事件处理方法
    handleGroupChange() {
      this.handleSearch();
    },

    handleSearch() {
      this.filterParam = this.buildFilterParams();
      this.pagination.pageNum = 1;
      this.getList();
    },

    handleReset() {
      this.$refs.searchForm.resetFields();
      this.filterParam = {
        allotMethod: ALLOT_METHOD.GROUP,
      };
      this.handleSearch();
    },

    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.pageNum = 1;
      this.getList();
    },

    handleCurrentChange(page) {
      this.pagination.pageNum = page;
      this.getList();
    },

    // 操作方法
    handleEdit(row) {
      this.$emit('handleEdit', row);
    },

    handleDelete(row) {
      this.$emit('handleDelete', row);
    },

    handleEditRecord(row) {
      this.$emit('handleEditRecord', row);
    },
  },
};
</script>

<style lang="stylus" scoped>
.table_footer
  ul
    list-style: none
    display: flex
    align-items: center
    padding: 0
    margin: 10px 0 0 0

    li
      margin-right: 10px
      white-space: nowrap

      span:first-child
        font-weight: 500
        margin-right: 5px

>>>.table-panel-footer-top
  display: flex
  justify-content: space-between
  align-items: center

  .table_footer
    overflow: auto

    > ul
      display: flex
      list-style: none
      padding: 0
      margin: 0

      li
        white-space: nowrap
        padding: 0 10px
</style>