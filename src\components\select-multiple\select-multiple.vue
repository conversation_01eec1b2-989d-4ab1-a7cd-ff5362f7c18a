<template>
  <el-tooltip class="tooltip" effect="dark" :disabled="contentTips.join('/')&&contentTips.join('/').length>1?false:true" :content="contentTips.join('，')"
    placement="top-start">
    <el-select :key="isKey" :disabled="isDisabled" filterable v-model="values" size="small" multiple style="width:100%" placeholder="请选择"
      @change="handleChange">
      <el-option v-for="item in options" :key="item.value" :label="item[labelText]" :value="item[valueText]">
      </el-option>
    </el-select>
  </el-tooltip>
</template>
<script>
var observer = null
export default {
  name: "selectMultiple",
  props: {
    options: {
      type: Array,
      default: () => []
    },
    value: {
      type: Array,
      default: () => []
    },
    labelText: {
      type: String,
      default: ""
    },
    valueText: {
      type: String,
      default: ""
    },
    modelValue: {
      type: Array,
      default: () => []
    },
    className: {
      type: String,
      default: ""
    },
    contentTips: {
      type: Array,
      default: () => []
    },
    isDisabled: {
      type: Boolean,
      default: true
    },
  },
  data() {
    return {
      isKey: false,
    };
  },
  mounted() {
    let tagLIstDom = document.querySelector(`.el-select__tags`);
    //   需要加上组件自定义的类名，防止监听失效
    let tagSpanDom = document.querySelector(`.${this.className} .el-select__tags > span`);
    // if (!tagSpanDom) return
    let hideDom = document.createElement("span");
    hideDom.classList = ["count-node1"]; //设置样式
    tagSpanDom.append(hideDom); //插入到span中
    var config = { childList: true };
    // 当观察到突变时执行的回调函数
    var callback = function (mutationsList) {
      mutationsList.forEach(function (item, index) {
        if (item.type == "childList") {
          let tagList = item.target.childNodes;
          let tagWidth = 0; //标签总宽度
          let tagNum = 0; //标签多余个数
          let avaliableTagWidth = 0 //显示标签的总宽度
          for (let i = 0; i < tagList.length; i++) {
            const e = tagList[i];
            if (tagWidth > tagLIstDom.offsetWidth) {
              e.style.display = "none"; //隐藏多余标签
            } else {
              e.style.display = "inline-block"; //显示标签
            }
            tagWidth += e.offsetWidth + 5;
            if (tagWidth > tagLIstDom.offsetWidth) {
              e.style.display = "none"; //隐藏多余标签
            } else {
              e.style.display = "inline-block"; //显示标签
            }
            if (e.style.display != "none") {
              tagNum++;
              hideDom.style.display = "none"; //隐藏多余标签个数
              const margin = tagNum === 1 ? 0 : 7
              avaliableTagWidth += e.offsetWidth + margin
            } else {
              hideDom.style.display = "inline-block"; //显示多余标签个数
              // hideDom.style.left = `${avaliableTagWidth}px` //数字标签的位置设置
              hideDom.innerHTML = `+${tagList.length - tagNum}`;  //显示多余标签个数
            }
          }
        }
      });
    };

    // 创建一个链接到回调函数的观察者实例
    observer = new MutationObserver(callback);

    // 开始观察已配置突变的目标节点
    observer.observe(tagSpanDom, config);

    // 随后，您还可以停止观察
    // observer.disconnect();
  },
  methods: {
    handleChange(val) {
      this.value = val
    }
  },
  computed: {
    values: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      }
    }
  },
  //销毁时
  beforeDestroy() {
    // 停止观察
    observer.disconnect();
  }
};
</script>
<style>
.count-node1 {
  position: absolute;
  right: 0;
  /* top: 2px; */
  display: none;
  height: 24px;
  padding: 0 8px;
  line-height: 22px;
  /* margin-left: 61px; */
  background-color: #f4f4f5;
  border: 1px solid #e9e9eb;
  border-radius: 4px;
  color: #909399;
  font-size: 12px;
  box-sizing: border-box;
}
</style>
  