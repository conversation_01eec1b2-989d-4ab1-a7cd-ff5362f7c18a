<template>
  <content-panel>
    <table-panel ref="tablePanel">
      <template v-slot:header-left>
        <el-tabs v-model="activeName" ref="tabs">
          <el-tab-pane v-for="item in tabList" :key="item.value" :label="item.name" :name="item.value">
          </el-tab-pane>
        </el-tabs>
      </template>
      <template v-slot:header-right>
        <div ref="btnRight">
          <el-button size="small" type="primary" @click="handleExport">
            导出
          </el-button>
        </div>
      </template>
      <component :is="componentName" :key="activeName" :ref="activeName" @getStatus="getStatus">
      </component>
    </table-panel>
  </content-panel>
</template>
<script>
import registerComponent from "./registerComponent";
export default {
  name: 'PlateTypeWeeklyPayroll',
  mixins: [registerComponent],
  data() {
    return {
      activeName: "PayrollList",
      componentName: "",
      tabList: [
        {
          name: "周工资表",
          value: "PayrollList",
          isShow: true,
        },
      ],
      info: {},
      isLoading: false,
      isFilterEmpty: 1,
    };
  },
  watch: {
    activeName: {
      handler(value) {
        this.componentName = this.filterComponent(value);

      },
      immediate: true,
    },
  },
  methods: {
    parseRouteQueryData(data = this.$route.query.data) {
      try {
        // 验证data是否为有效JSON字符串
        if (typeof data !== "string" || !data.trim()) {
          console.warn("Route query data is empty or invalid:", data);
          return {};
        }
        return JSON.parse(data);
      } catch (error) {
        console.error("Failed to parse route query data:", error);
        return {};
      }
    },
    getStatus(val) {
      this.isFilterEmpty = val ? 1 : 0;
    },
    filterComponent(val) {
      let values = this.tabList.map((item) => ({
        name: item.value,
        component: item.value,
      }));
      return values.find((item) => item.name == val).component;
    },
    //导出
    handleExport() {
      const { factoryId, accountingMonth, accountingWeek } = this.parseRouteQueryData();
      let params = {
        pageNum: 1,
        pageSize: 1000,
        factoryId,
        accountingMonth,
        accountingWeek,
        moduleId: 3
      };
      this.$api.common.doExport("plankSalaryWeekDetailExport", params).then(() => {
        this.$message.success("导出操作成功，请前往导出记录查看详情");
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
