<template>
  <div class="login-container">
    <div class="ico-login">
      <el-form
        size="small"
        :model="loginForm"
        :rules="rules"
        ref="loginForm"
        label-position="left"
        label-width="30px"
        class="login-page"
      >
        <div class="welcome">欢迎登录</div>
        <el-form-item prop="name" label class="username">
          <el-input ref="nameField"
            placeholder="输入您的用户名"
            v-model="loginForm.name"
            class="form-input"
          >
            <img
              src="../../assets/images/yh.png"
              slot="prepend"
              class="input-img"
            />
          </el-input>
        </el-form-item>
        <el-form-item prop="pass" class="password">
          <el-input
            placeholder="输入您的密码"
            v-model="loginForm.pass"
            type="password"
            class="form-input"
            @keyup.enter.native="submitForm"
          >
            <img
              src="../../assets/images/mima.png"
              slot="prepend"
              class="input-img"
            />
          </el-input>
        </el-form-item>
        <el-form-item style="width: 100%">
          <el-button class="login-btn" type="primary" :loading="loading"  @click="submitForm">登录</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Login',
  data() {
    return {
      loading: false,
      loginForm: {
        pass: '',
        name: '',
      },
      rules: {
        name: [{ required: true, message: '用户名不能为空', trigger: 'blur' }],
        pass: [{ required: true, message: '密码不能为空', trigger: 'blur' }],
      },
      redirect: undefined,
      otherQuery: {},
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        const query = route.query
        if (query) {
          this.redirect = query.redirect
          this.otherQuery = this.getOtherQuery(query)
        }
      },
      immediate: true
    },
  },
  methods: {
    submitForm() {
      this.$refs.loginForm.validate(async (valid) => {
        if (!valid) return

        this.loading = true;
        await this.$store.dispatch('user/login', {
            userName: this.loginForm.name,
            password: this.loginForm.pass,
          }).catch(err => {
            this.loading = false;
          });

        const {success, message} = await this.$api.common.checkPermission(this.loginForm.name)
          .catch(err => {
            console.log(err);
            this.$store.dispatch('user/resetToken');
          });
        this.loading = false;

        if (success) {
          this.$router.push({
            path: this.redirect || '/',
            query: this.otherQuery,
          })
        } else {
          this.$store.dispatch('user/resetToken');
          this.$message.error(message)
        }
      })
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
    },
  },
  mounted() {
    this.$refs.nameField.focus();
  }
}
</script>

<style lang="stylus" scoped>
.el-form-item
  // margin-bottom 12px !important
.login-container
  width 100%
  height 100%
  position relative
  background-image url('../../assets/images/pc-bg.png')
  background-repeat no-repeat
  background-size 100% 100%
  .ico-login
    position absolute
    top 30%
    right 7%
    width 300px
    height 330px
    background #fff
    box-shadow 0 2px 12px 0 rgba(0, 0, 0, 0.1)
    border-radius 12px
    .welcome
      width 80%
      margin 40px auto 30px
      text-align center
      padding-bottom 15px
      border-bottom 1px solid #e5e5e5
      color #0bb78e
      font-size 22px
      font-weight bold
    .password
      margin-top 30px
.login-btn
  margin-top 20px
  width 245px
  height 37px
  border-radius 4px
.form-input
  >>> .el-input-group__prepend
    background-color #ffffff
    padding 0 5px
    line-height 1
  >>> .el-input__inner
    width 210px
    height 35px
    line-height 35px
.input-img
  width 20px
  height 20px
</style>
