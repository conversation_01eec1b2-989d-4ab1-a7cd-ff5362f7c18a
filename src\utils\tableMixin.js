export default {
  data() {
    return {
      observeElement: null,
      maxTableHeight: 400
    }
  },
  watch: {
    $route(to, from) {
      if (this.visitedRoutes.includes(to.name)) {
        // 已经访问过
        setTimeout(() => {
          this.setMaxHeight();
          console.log('tableMixin watch route 1500ms later');
        }, 1500);
      } else {
        this.$store.dispatch('tagsView/addWatchedView', to)
      }
    }
  },
  computed: {
    visitedRoutes() {
      return this.$store.state.tagsView.watchedVisitedRoutes;
    }
  },
  methods: {
    setMaxHeight(dynamicHeight) {
      if (!this.$refs.tablePanel || !this.$refs.tablePanel.$refs || !this.$refs.tablePanel.$refs.tableWrap) {
        return;
      }
      // 表格header +分页组件高度+ 间距
      const OFFSET = (this.resizeOffset || 52) + (typeof dynamicHeight === 'number' ? dynamicHeight : 0);
      this.$nextTick(() => {
        try {
          this.maxTableHeight = window.innerHeight - this.$refs.tablePanel.$refs.tableWrap.getBoundingClientRect().top - OFFSET;
        } catch (error) {
          console.log(error);
        }
      });
    },
    addListener() {
      window.addEventListener('resize', this.setMaxHeight);
    },
    removeListener() {
      window.removeEventListener('resize', this.setMaxHeight);
    }
  },
  mounted() {
    this.addListener();
    this.setMaxHeight();
    this.$bus.$on('table.updateHeight', (offset) => {
      this.setMaxHeight(offset || 0);
    });
  },
  beforeDestroy() {
    this.removeListener();
  }
}