<template>
  <qDialog :visible="visible" :innerScroll="false" title="自定义列" width="900px" :isLoading="isLoading"
    @cancel="handleCancel" @confirm="handleConfirm" :before-close="handleCancel">
    <section>
      <Custom title="自定义场景" ref="sceneRef" v-loading="sceneLoading" @customSearch="customSearch">
        <template v-slot:content>
          <div style="padding: 0 13px 13px 13px">
            <div v-for="(item, index) in filteredScene" :key="item.id" class="wrap_item">
              <span v-if="!item.isEdit" style="display: block; padding-left: 10px; cursor: pointer"
                :class="` ${btomIndex === index ? 'custom_active' : ''}`" @click="selectScene(item, index)">{{ item.name
                }}</span>
              <div class="editSceneBtn" v-else>
                <el-input v-model.trim="editSceneName" ref="editSceneName" clearable>
                </el-input>
                <el-button type="text" style="color: red" @click="editScene">
                  保存
                </el-button>
                <el-button type="text" @click="item.isEdit = false"> 取消 </el-button>
              </div>
            </div>
            <div class="addSceneBtn" v-show="isAddScene">
              <el-input v-model.trim="addSceneName" ref="addSceneName" clearable>
              </el-input>
              <el-button type="text" style="color: red" @click="(isAddScene = false), saveScene()">
                保存
              </el-button>
              <el-button type="text" @click="isAddScene = false"> 取消 </el-button>
            </div>
          </div>
        </template>
        <template v-slot:footer>
          <div style="display: flex; justify-content: center">
            <el-button type="primary" @click="addScene"> 新增 </el-button>
            <el-button type="primary" @click="handleRename">
              重命名
            </el-button>
            <el-button @click="handleDelete"> 删除 </el-button>
          </div>
        </template>
      </Custom>
      <Custom title="自定义字段" v-loading="columnLoading" :showFooter="false" @customSearch="customSearch">
        <template v-slot:content>
          <span style="padding-left: 13px; display: block">字段</span>
          <div class="custom_item">
            <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" :disabled="disabled"
              style="padding-left: 10px" @change="handleCheckAllChange">
              全选
            </el-checkbox>
            <el-checkbox-group v-model="checkedField" style="padding-left: 13px" @change="handleCheckedChange">
              <el-checkbox style="display: block" v-for="item in fieldOption" :label="item.columnName"
                :key="item.fieldName" :disabled="item.disabled">
                {{ item.columnName }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </template>
      </Custom>
    </section>
  </qDialog>
</template>

<script>
import Custom from "./custom.vue";
export default {
  name: "customDialog",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
  },
  components: {
    Custom,
  },
  data() {
    return {
      sceneList: [],
      fieldOptions: [],
      checkedField: [],
      selectedList: [],
      checkAll: false,
      isAddScene: false,
      iSEditScene: false,
      disabled: false,
      sceneLoading: false,
      columnLoading: false,
      isIndeterminate: false,
      sceneName: "",
      selectSceneName: "",
      addSceneName: "",
      editSceneName: "",
      fieldName: "",
      sceneId: "",
      btomIndex: 0,
      isLoading: false,
    };
  },
  computed: {
    filteredScene() {
      return this.sceneName
        ? this.sceneList.filter(
          (item) => item.name && item.name.includes(this.sceneName)
        ) || []
        : this.sceneList;
    },
    fieldOption() {
      return this.fieldName
        ? this.fieldOptions.filter(
          (item) => item.columnName && item.columnName.includes(this.fieldName)
        ) || []
        : this.fieldOptions;
    },
  },
  async created() {
    this.sceneLoading = true;
    this.columnLoading = true;
    this.getColumnList();
    await this.getSceneList();
    let sceneObj = JSON.parse(sessionStorage.getItem("softwareSalaryQuerySceneData"));
    if (!sceneObj) {
      this.allInit();
    } else {
      let btomIndex = sceneObj ? sceneObj.btomIndex : 0;
      this.btomIndex = btomIndex;
      if (btomIndex == 0) {
        this.allInit();
      } else {
        if (this.sceneList.length > 0) {
          this.init();
          let selectSceneColumns = this.sceneList[btomIndex].columns;
          this.sceneColumns(selectSceneColumns, sceneObj.name);
        }
      }
    }
  },
  methods: {
    customSearch({ title, value }) {
      switch (title) {
        case "自定义场景":
          this.sceneName = value;
          this.btomIndex = 0;
          if (value == "") {
            let sceneObj = JSON.parse(sessionStorage.getItem("softwareSalaryQuerySceneData"));
            if (sceneObj && sceneObj.name != "默认全部") {
              let btomIndex = sceneObj ? sceneObj.btomIndex : 0;
              this.btomIndex = btomIndex;
              this.init();
              let selectSceneColumns = this.sceneList[btomIndex].columns;
              if (selectSceneColumns.length > 0 && sceneObj.name != "默认全部") {
                this.sceneColumns(selectSceneColumns, sceneObj.name);
              }
            } else {
              this.allInit();
            }
          } else {
            this.init();
            if (this.filteredScene.length > 0) {
              let selectSceneColumns = this.filteredScene[this.btomIndex].columns;
              this.sceneId = this.filteredScene[this.btomIndex].id;
              if (selectSceneColumns.length > 0 && !["默认全部"].includes(value)) {
                this.checkedField = selectSceneColumns.map((item) => item.columnName);
                this.checkAll = this.checkedField.length == this.fieldOptions.length;
                this.isIndeterminate = !this.checkAll;
              }
            }
          }

          break;
        case "自定义字段":
          this.fieldName = value;
          break;
        default:
          break;
      }
    },
    //查询所有自定义场景
    getSceneList() {
      return this.$api.softwareWorkbench
        .sceneConfigurationList("SalaryQuery")
        .then(({ data }) => {
          this.sceneList =
            data &&
            data.map((item) => ({
              ...item,
              isEdit: false,
            }));
        })
        .finally(() => {
          this.sceneLoading = false;
        });
    },
    //获取自定义列
    getColumnList() {
      return this.$api.softwareInformation.salarySearch
        .salaryQueryColumns({
          sceneId: this.sceneId,
        })
        .then(({ data }) => {
          this.fieldOptions =
            data &&
            data.map((item) => ({
              columnName: item.columnName,
              fieldName: item.fieldName,
              disabled: false,
            }));
        })
        .finally(() => {
          this.columnLoading = false;
        });
    },
    //初始化
    init() {
      this.fieldOptions = this.fieldOptions.map((item) => ({
        ...item,
        disabled: false,
      }));
      this.checkedField = this.fieldOptions.map((item) => item.columnName);
      this.disabled = false;
      this.checkAll = true;
      this.isIndeterminate = false;
    },
    //默认全部初始化
    allInit() {
      this.fieldOptions = this.fieldOptions.map((item) => ({
        ...item,
        disabled: true,
      }));
      this.checkedField = this.fieldOptions.map((item) => item.columnName);
      this.disabled = true;
      this.checkAll = true;
      this.isIndeterminate = false;
    },
    //场景数据校验
    checkScene(value) {
      if (!value) {
        this.$message({
          type: "warning",
          message: "场景不能为空!",
        });
        return false;
      }
      let configureName = this.sceneList.map((item) => item.name);
      if (configureName.includes(value)) {
        this.$message({
          type: "warning",
          message: "该场景已存在,请勿重复添加!",
        });
        return false;
      }
      return true;
    },
    //场景新增
    addScene() {
      this.isAddScene = true;
      this.addSceneName = "";
      this.$nextTick(() => {
        this.$refs.sceneRef.$refs.dialogScrollbar.$refs.wrap.scrollTop = 400 + 50;
        this.$refs.addSceneName.focus();
      });
    },
    //新增场景保存
    saveScene() {
      if (!this.checkScene(this.addSceneName)) return;
      this.sceneLoading = true;
      this.columnLoading = true;
      this.$api.softwareWorkbench
        .sceneConfigurationAdd({
          moduleType: "SalaryQuery",
          name: this.addSceneName,
        })
        .then(async (res) => {
          await this.getSceneList();
          this.btomIndex = this.sceneList.length - 1;
          this.$message({
            type: "success",
            message: "新增场景成功",
          });
          await this.getColumnList();
          this.init();
        });
    },
    //重命名
    handleRename() {
      this.editSceneName = this.selectSceneName;
      if (this.filteredScene[this.btomIndex].name == "默认全部") return;
      this.sceneList.forEach((item, index) => {
        if (item.name == this.selectSceneName) {
          item.isEdit = true;
        }
      });
      this.$nextTick(() => {
        this.$refs.editSceneName[0].focus();
      });
    },
    //重命名场景保存
    editScene() {
      if (!this.checkScene(this.editSceneName)) return;
      this.$api.softwareWorkbench
        .sceneConfigurationEdit({
          id: this.sceneId,
          moduleType: "SalaryQuery",
          name: this.editSceneName,
        })
        .then((res) => {
          if (res.code === 200) {
            this.$message({
              type: "success",
              message: "修改场景成功",
            });
            this.getSceneList();
          }
        });
    },
    //选择场景
    selectScene({ name, id }, index) {
      this.btomIndex = index;
      this.selectSceneName = name;
      this.sceneId = id;
      this.columnLoading = true;
      this.init();
      if (name == "默认全部") {
        this.allInit();
      }
      let selectSceneColumns = this.filteredScene[index].columns;
      if (selectSceneColumns.length > 0 && name != "默认全部") {
        this.sceneColumns(selectSceneColumns, name);
      }
      setTimeout(() => {
        this.columnLoading = false;
      }, 500);
    },
    //根据场景获取已有字段
    sceneColumns(selectSceneColumns = [], name = "") {
      this.checkedField = selectSceneColumns.map((item) => item.columnName);
      this.checkAll = this.checkedField.length == this.fieldOptions.length;
      this.isIndeterminate = !this.checkAll;
    },
    //删除场景
    handleDelete() {
      if (document.querySelector(".custom_active").innerText == "默认全部") {
        this.$message({
          type: "error",
          message: "不能删除默认场景!",
        });
        return;
      }
      let id = this.sceneList.find(
        (item) => item.name == document.querySelector(".custom_active").innerText
      ).id;
      this.$confirm("此操作将永久删除该条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$api.softwareWorkbench.sceneConfigurationDelete({ id }).then(async (res) => {
            this.$message({
              type: "success",
              message: "删除成功!",
            });
            this.btomIndex = 0;
            sessionStorage.removeItem("softwareSalaryQuerySceneData");
            this.allInit();
            this.sceneLoading = true;
            await this.getSceneList();
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    //字段全选
    handleCheckAllChange(value) {
      this.checkedField = value ? this.fieldOptions.map((item) => item.columnName) : [];
      this.isIndeterminate = false;
    },
    //字段多选
    handleCheckedChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.fieldOptions.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.fieldOptions.length;
    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      let sceneObj = this.sceneList.find(
        (item) => item.name == document.querySelector(".custom_active").innerText
      );
      let checkedField = this.checkedField.map((item) => {
        let temp = "";
        this.fieldOptions.forEach((it) => {
          if (it.columnName == item) {
            temp = it;
          }
        });
        return temp;
      });
      this.isLoading = true;
      this.$api.softwareWorkbench
        .addColumns({
          sceneId: sceneObj.id || "",
          columns: checkedField
            .map((item) => ({
              columnName: item.columnName,
              fieldName: item.fieldName,
            }))
            .filter((item) => item.columnName),
        })
        .then((res) => {
          this.$notify.success({
            title: "成功",
            message: "自定义列修改成功",
          });
          sessionStorage.setItem(
            "softwareSalaryQuerySceneData",
            JSON.stringify({
              btomIndex: this.btomIndex,
              name: sceneObj.name,
              id: sceneObj.id,
            })
          );
          this.$emit("cancel", "confirm");
        }).finally(() => {
          this.isLoading = false;
        });
    },
  },
};
</script>

<style lang="stylus" scoped>
section {
  display: flex;
  justify-content: space-between;

  .custom {
    width: 48%
  }
}

.wrap_item {
  margin: 10px 0;
  height: 30px;
  line-height: 30px;
}

.custom_active {
  background: #c8eee5;
  border: 1px solid #0bb78e;
  border-radius: 5px;
  color: #0bb78e;
}
.addSceneBtn,.editSceneBtn{
     display: flex;

  .el-input {
    margin-right: 10px;

    >>>.el-input__inner {
      height: 26px;
    }
  }

  >>>.el-button--text {
    padding: 0 !important;
  }
}
</style>
