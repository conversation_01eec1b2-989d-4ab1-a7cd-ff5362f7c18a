<template>
  <div>
    <div class="flex-demo">
      <div class="flex-left">固定部分</div>
      <div class="flex-right">
        <!-- 内容可通过content传入 -->
        <text-tip :width="80" :content="textContent"/>
      </div>
    </div>
    <div class="flex-demo">
      <div class="flex-left">固定部分</div>
      <div class="flex-right">
        <text-tip width="80px">
          width可传固定宽度，数字可带px或不带
        </text-tip>
      </div>
    </div>
    <div class="flex-demo">
      <div class="flex-left">固定部分</div>
      <div class="flex-right">
        <text-tip width="calc(100% - 50px)">
          width宽度可使用calc进行计算
        </text-tip>
      </div>
    </div>
  </div>
  
</template>

<script>
import TextTip from './text-with-tooltip'
export default {
  components: {
    TextTip
  },
  data() {
    return {
      textContent: '长文本提示内容'
    }
  }
}
</script>

<style lang="stylus" scoped>
.flex-demo
  display: flex
  width 200px
  border 1px solid red
  .flex-left
    width 80px
    flex none
    background-color: blue
  .flex-right
    flex 1
</style>