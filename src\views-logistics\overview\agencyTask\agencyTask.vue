<template>
  <!-- 待办任务-->
  <content-panel>
    <template v-slot:search>
      <search-box
        class="search-box">
        <el-form size="mini"
          :inline="true"
          :model="searchForm"
          ref="searchForm">
          <el-form-item
            label="核算月份:"
            prop="accountingMonth">
            <el-date-picker
              v-model="searchForm.accountingMonth"
              type="month"
              placeholder="请选择日期"
              @change="onSearch">
            </el-date-picker>
          </el-form-item>
          <el-form-item
            label="任务阶段:"
            prop="nodeId">
            <el-select
              v-model="searchForm.nodeId"
              filterable
              clearable
              placeholder="请选择任务状态"
              @change="onSearch">
              <el-option
                v-for="item in statusOptions"
                :key="item.id"
                :label="item.nodeName"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template
          v-slot:right>
          <el-button
            size="small"
            type="primary"

            @click="onSearch">
            查询</el-button>
          <el-button
            size="small"
            type="warning"
            @click="resetSearchForm">
            重置</el-button>
        </template>
      </search-box>
    </template>
    <table-panel
      ref="tablePanel">
      <template
        v-slot:header-left>
        <el-tabs
          v-model="activeTab">
          <el-tab-pane
            name="0"
            label="当前任务">
          </el-tab-pane>
          <el-tab-pane
            name="1"
            label="历史任务">
          </el-tab-pane>
        </el-tabs>
      </template>
      <template
        v-slot:header-right>
        <el-button
          size="small"
          type="primary"
          v-permission="
            'was-customized$workBench$logistics$workOverview$downloadAttendance'
          "
          @click="downloadAttendance">下载考勤</el-button>
      </template>
      <el-table stripe border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :data="tableData"
        :height="maxTableHeight"
        style="width: 100%">
        <el-table-column
          prop="factoryName"
          label="核算组织"
          width="130"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="accountingMonth"
          label="核算月份"
          width="80"
          align="left">
        </el-table-column>
        <el-table-column
          prop="nodeName"
          label="任务阶段"
          width="120"
          align="left"
          show-overflow-tooltip
          >
        </el-table-column>
        <el-table-column
          label="任务状态"
          width="80"
          align="left">
          <template
            slot-scope="{ row }">
            {{ row.completeState!=null && row.completeState == 0 ? "进行中" : "已完成" || ''}}
          </template>
        </el-table-column>
        <el-table-column
          prop="salaryId"
          label="工资表"
          align="left"
          show-overflow-tooltip>
          <template
            slot-scope="{row}">
            <el-button
              type="text"
              v-if="row.salaryId"
              v-permission="'was-customized$workBench$logistics$workOverview$payroll'"
              @click="handleJump(row)"
              class="active">
              {{ row.salaryId }}
            </el-button>
            <span v-else>/</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="rejectReason"
          label="驳回原因"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="操作"
          align="left"
          :width="activeTab == '0' ? 500 : 150">
          <template
            slot-scope="scope">
            <el-button
              v-permission="'was-customized$workBench$logistics$workOverview$ViewTasks'"
              type="text"
              size="mini"
              @click="handleLook(scope.$index, scope.row)">
              查看任务</el-button>
            <el-button
              type="text"
              size="mini"
              v-show="scope.row.nodeName == '资料收集' && activeTab == '0'"
              v-permission="
                'was-customized$workBench$logistics$workOverview$endCollectionBtn'
              "
              @click="endCollection(scope.row)">
              结束收集</el-button>
            <el-button
              type="text"
              size="mini"
              v-show="!btnShowList.adjustment.includes(scope.row.nodeName) && activeTab == '0'"
              @click="adjustment(scope.row)"
              v-permission="
                'was-customized$workBench$logistics$workOverview$branchAdjustment'
              ">分厂调整
            </el-button>
            <el-button
              type="text"
              size="mini"
              v-show="
                scope.row.nodeName == '分厂调整-已提交'&& activeTab == '0'
              "
              @click="adjustmentReview(scope.row)"
              v-permission="
                'was-customized$workBench$logistics$workOverview$adjustmentReview'
              ">调整审核
            </el-button>
            <el-button
              type="text"
              size="mini"
              v-show="
                scope.row.nodeName == '分厂调整-已审核' && activeTab == '0'
              " v-permission="
                'was-customized$workBench$logistics$workOverview$submitFirstInstance'
              "
              @click="submitFirstInstance(scope.row)">
              提交一审</el-button>
            <el-button
              type="text"
              size="mini"
              v-permission="
                'was-customized$workBench$logistics$workOverview$waitingManager'
              "
              v-show="
                scope.row.nodeName == '财务一审' &&
                isSectionChief &&
                scope.row.firstAuditStatus == '2' &&
                activeTab == '0'
              ">等待经理一审
            </el-button>
            <el-button
              type="text"
              size="mini"
              v-show="
                scope.row.nodeName == '财务一审' &&
                isManager &&
                scope.row.firstAuditStatus == '1' &&
                activeTab == '0'
              ">等待科长一审
            </el-button>
            <el-button
              type="text"
              size="mini"
              v-show="scope.row.nodeName == '财务一审' && activeTab == '0'"
              v-permission="
                'was-customized$workBench$logistics$workOverview$firstInstancePassed'
              "
              @click="financialFirstReview(scope.row)">
              财务一审</el-button>
            <el-button
              type="text"
              size="mini"
              v-permission="
                'was-customized$workBench$logistics$workOverview$personalTaxUpload'
              "
              @click="update(scope.row)"
              v-show="btnShowList.update.includes(scope.row.nodeName) && activeTab == '0'">
              个税上传</el-button>
            <el-button
              type="text"
              size="mini"
              v-show="
                scope.row.nodeName == '提交二审' &&
                activeTab == '0'
              " v-permission="
                'was-customized$workBench$logistics$workOverview$submitSecondInstance'
              "
              @click="submitSecondInstance(scope.row)">
              提交二审</el-button>
            <el-button
              type="text"
              size="mini"
              v-show="scope.row.nodeName == '财务二审' && activeTab == '0'"
              v-permission="
                'was-customized$workBench$logistics$workOverview$passSecondInstance'
              "
              @click="financialSecondReview(scope.row)">
              财务二审</el-button>
            <el-button
              size="mini"
              type="text"
              v-show="btnShowList.forcedBack.includes(scope.row.nodeName) && activeTab == '0'"
              v-permission="'was-customized$workBench$logistics$workOverview$forcedBack'"
              @click="forcedBack(scope.row)">
              强制退回</el-button>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div
          style="text-align: right">
          <el-pagination
            @size-change="onSizeChange"
            @current-change="onNumChange"
            :current-page="pageNum"
            :page-size="pageSize"
            :total="total"
            :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>
        </div>
      </template>
    </table-panel>
    <task-dialog
      v-if="visible"
      :visible="visible"
      :title="title"
      :taskInfo="taskInfo"
      @cancel="handleCancel" />
  </content-panel>
</template>
<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import moment from "moment";
import taskDialog from "./taskDialog";
export default {
  name: "LogisticsTask",
  mixins: [tableMixin,pagePathMixin],
  components: { taskDialog },
  data() {
    return {
      searchForm: {
        accountingMonth: "",
        nodeId: "",
      },
      activeTab: "0",
      visible: false,
      title: "",
      statusOptions: [],
      btnShowList:Object.freeze({
        adjustment:['资料收集','财务标记'],
        update:['个税待提交','提交二审','财务二审','任务结束'],
        forcedBack:['财务一审','个税待提交','提交二审','财务二审','任务结束'],
      }),
      filterParam: {},
      tableData: [],
      loading: false,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      taskInfo: {},
      isManager: "",
      isSectionChief: "",
      startTime: 0,
      diffTime: 0,
    };
  },
  created() {
    this.$api.roleInfo.getRoleList({moduleId:2}).then((res) => {
      if (res.data.includes("was00028")) {
        this.isManager = false;
        this.isSectionChief = true;
      } else {
        this.isManager = true;
        this.isSectionChief = false;
      }
    });
    this.$api.logisticsWorkbench
      .listProcess({ moduleId: 2 })
      .then(({ data }) => {
        this.statusOptions = data || [];
      });
  },
  watch: {
    $route: {
      handler(value) {
        if (value.path.includes("overview")&&value.path.includes("logistics")) {
          this.getList(this.activeTab);
        }
      },
      // 深度观察监听
      deep: true,
    },
    activeTab: {
      handler(value) {
        this.getList(value);
      },
      immediate: true,
    },
  },
  methods: {
    //获取任务总览列表
    getList(complete = "0") {
      this.loading = true;
      this.$api.logisticsWorkbench
        .getTaskOverviewList({
          pageSize: this.pageSize,
          pageNum: this.pageNum,
          filterData: { ...this.filterParam, complete,moduleId:2 },
        })
        .then(({ data: { list, total } }) => {
          this.tableData = list.map(item=>({...item,factoryName:item.factoryId=="TEST"?'测试专用':'物流本部'})) || [];
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.filterParam = {};
      this.onSearch();
    },
    //搜索
    onSearch() {
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (key == "accountingMonth" && moment.isDate(val)) {
          this.filterParam.accountingMonth = moment(val).format("YYYY-MM");
        } else if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      this.pageNum = 1;
      this.getList(this.activeTab);
    },
    //工资表  分配表  上卡现金表 跳转
    handleJump(data) {
      const { id, factoryId, factoryName, accountingMonth, completeState } = data;
      let params = {
        id,
        factoryId,
        factoryName,
        accountingMonth,
        completeState,
      };
      this.openSubPage({
        path: "/logistics/workbench/payroll",
        query: {
          data: JSON.stringify(params),
        },
      });
    },
    //财务一审
    financialFirstReview(row) {
      this.title = "财务一审";
      this.visible = true;
      this.taskInfo = { ...row };
    },
    //财务二审
    financialSecondReview(row) {
      this.title = "财务二审";
      this.visible = true;
      this.taskInfo = { ...row };
    },
    //调整审核
    adjustmentReview(row) {
      this.title = "调整审核";
      this.visible = true;
      this.taskInfo = { ...row };
    },
    //强制退回
    forcedBack(row) {
      this.title = "强制退回";
      this.visible = true;
      this.taskInfo = { ...row };
    },
    //提交一审
    async submitFirstInstance(row) {
      this.title = "提交一审";
      this.visible = true;
      this.taskInfo = { ...row };
    },
    //提交二审
    async submitSecondInstance(row) {
      this.title = "提交二审";
      this.visible = true;
      this.taskInfo = { ...row };
    },
    //结束收集
    async endCollection(row) {
      this.title = "结束收集";
      this.visible = true;
      this.taskInfo = { ...row };
    },
    //查看任务
    handleLook(index, data) {
      this.openSubPage({
        path: "/logistics/workbench/taskDetails",
        query: {
          data:this.$Base64.encode(
          JSON.stringify({
            factoryId: data.factoryId,
            accountingMonth: data.accountingMonth
          }),
        )
        },
      });
    },
    //个税上传
    update(data) {
      const { id, factoryId, factoryName, accountingMonth } = data;
      let params = { id, factoryId, factoryName, accountingMonth };
      this.openSubPage({
        path: "/logistics/workbench/upload",
        query: {
          data: this.$Base64.encode(
          JSON.stringify(params),
        )
        },
      });

    },
    //分厂调整
    adjustment(data) {
      const { id, factoryId, factoryName, accountingMonth, nodeName } = data;
      let params = { id, factoryId, factoryName, accountingMonth, nodeName };
      this.openSubPage({
        path: "/logistics/workbench/adjustment",
        query: {
          data: this.$Base64.encode(
          JSON.stringify(params),
          )
        },
      });
    },
    //下载考勤
    downloadAttendance() {
      let nowTime = moment();
      this.diffTime = nowTime.diff(this.startTime, "second");
      if (this.diffTime < 11) {
        this.$message.warning("10s内不能重复点击");
        return;
      } else {
        this.startTime = nowTime;
      }
      let params = {
        accountingMonth: this.searchForm.accountingMonth
          ? moment(this.searchForm.accountingMonth).format("YYYY-MM")
          : "",
        factoryId: this.searchForm.factoryId,
      };
      this.$api.common.doExport("logisticsExportstaffattendance", params).then(() => {
        this.$message.success("导出操作成功，请前往导出记录查看详情");
      });
    },
    handleCancel(type) {
      this.visible = false;
      if (type == "cancel") return;
      this.getList();
    },
    onSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList(this.activeTab);
    },
    onNumChange(val) {
      this.pageNum = val;
      this.getList(this.activeTab);
    },
  },
};
</script>

<style lang="stylus" scoped>
#item {
  margin: 0;
  padding: 5px;
}

.active {
  width: 100%;
  padding: 0;

  >>>span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    width: 100%;
    text-align: left;
  }
}

.formData {
  .el-form-item {
    display: flex;

    >>>.el-form-item__content {
      width: 100%;
      margin-left: 0 !important;
    }
  }
}
</style>
