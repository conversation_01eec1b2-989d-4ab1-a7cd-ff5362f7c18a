<template>
  <qDialog
    :visible="editVisible"
    :innerScroll="true"
    :innerHeight="height"
    :title="editTitle"
    :showFooter="showFooter"
    width="900px"
    :modal-append-to-body="false"
    append-to-body
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <div>
      <el-table
        border
        stripe
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :data="tableData"
        :key="Math.random()"
      >
        <el-table-column
          v-for="item of customFields"
          :key="item.fieldName"
          :prop="item.fieldName"
          :label="item.columnName"
        >
          <template slot-scope="scoped">
            <span>{{ scoped.row[item.fieldName] }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </qDialog>
</template>

<script>
export default {
  props: {
    editVisible: {
      type: Boolean,
      required: true,
    },
    editTitle: {
      type: String,
      required: true,
    },
    modifyData: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      loading: false,
      customFields: [], //表头
      height: 400,
      width: "",
      row: 8,
      showFooter: false,

      monthRules: {},
      tableData: [],
    };
  },
  watch: {},
  created() {
    this.getList();
  },
  methods: {
    getList() {
      let params = {
        ...this.modifyData,
      };
      this.$api.softwareInformation.payrollManagement.listDeductDetail(params).then(({ data }) => {
        this.customFields = data.headerList || []
        this.tableData = data.dataList || []
      
      });
    },
    handleCancel() {
      this.$emit("cancel", {
        type: "cancel",
        isVisible: false,
      });
    },
    handleConfirm() {
      this.$emit("cancel", {
        type: "confirm",
        isVisible: false,
      });
    },
  },
};
</script>

<style lang="stylus" scoped>


</style>
