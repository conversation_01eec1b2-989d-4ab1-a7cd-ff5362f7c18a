<template>
  <!-- 高温补贴 -->
  <content-panel class="panel-tabs">
    <div class="panel-headers">
      <div class="headers-left">
        <b>启用开关</b>
        <el-switch :disabled="!isModifyType" class="mg15" v-model="configForm.onOff" active-value="on" inactive-value="off"></el-switch>
        <el-tooltip content="开启后配置生效,反之不生效" placement="right">
          <i class="el-icon-question"></i>
        </el-tooltip>
      </div>
      <div class="headers-right">
        <el-button size="small" type="primary" @click="isModifyType = true" v-if="!isModifyType">编辑</el-button>
        <template v-else>
          <el-button size="small" type="primary" @click="changeSave" :loading="saveLoading">保存</el-button>
          <el-button size="small" type="warning" @click="changeCancel">取消</el-button>
        </template>
      </div>
    </div>
    <div class="panel-content">
      <el-row :gutter="10" class="content-top">
        <el-col :span="1">序号</el-col>
        <el-col :span="3">核算工厂</el-col>
        <el-col :span="5">核算班组</el-col>
        <el-col :span="12">核算标准</el-col>
        <el-col :span="3">操作</el-col>
      </el-row>

      <el-row :gutter="10" v-for="(item, index) in ruleList" :key="index">
        <div class="content-item">
          <el-col :span="1" style="line-height:30px">{{item.sort}} </el-col>
          <el-col :span="3">
            <el-select :disabled="!isModifyType" v-model="item.factoryId" filterable clearable :append-to-body="false" placeholder="请选择核算工厂" prop="factoryId"
              @change="changeFactoryId(item)">
              <el-option v-for="item in factoryNameOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-col>
          <el-col :span="5">

            <select-multiple :isDisabled="!isModifyType" :contentTips="getHiddenName(item,item.shiftGroup,item.processesOption)" :class="`shiftGroup`+index"
              :className="`shiftGroup`+index" v-model="item.shiftGroup" :options="item.processesOption" labelText="name" valueText="id"></select-multiple>
          </el-col>

          <el-col :span="12" class="flexWrap">
            <div style="display:flex;width:300px;margin-bottom:5px" v-for="(obj,index) in item.multiRules" :key="index">
              <el-tooltip class="tooltip" effect="dark" :disabled="getCondition(obj.condition)&&getCondition(obj.condition).length>1?false:true" :content="getCondition(obj.condition)" placement="top-start">
                <el-cascader style="width:140px" :disabled="!isModifyType" v-model="obj.condition" :options="monthList" collapse-tags placeholder="请选择"
                  :props="{multiple:true}" clearable>
                </el-cascader>
              </el-tooltip>
              <el-input class="selectWrap" :disabled="!isModifyType" type="text"
                oninput="value=value.replace(/[^\d.]/g, '');if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+ 3)}" placeholder="请输入"
                v-model="obj.criterion" @blur="checkAmount(obj)">
                <template slot="append">元/天</template>
              </el-input>
              <i class="el-icon-remove-outline iconDel" v-if="item.multiRules.length > 1 && index != 0" @click="multiRulesDel(item,index)"
                :disabled="!isModifyType"></i>
              <i class="el-icon-remove-outline iconDel colff" v-else :disabled="!isModifyType"></i>
            </div>
            <i class="el-icon-circle-plus-outline iconAdd" @click="multiRulesAdd(item)" :disabled="!isModifyType"></i>
          </el-col>
          <el-col :span="3">
            <div class="details_btn">
              <el-button type="text" @click="newbuilt(index)" :disabled="!isModifyType"> 新建 </el-button>
              <el-button type="text" @click="changeCopy(item,index)" :disabled="!isModifyType"> 复制 </el-button>
              <el-button type="text" style="color: red" @click="handleDelete(index)" v-show="ruleList.length > 1 && index != 0" :disabled="!isModifyType">
                删除
              </el-button>
            </div>
          </el-col>
        </div>
      </el-row>
    </div>

  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import { moneyDelete } from "@/utils";
export default {
  name: "managementsystem",
  mixins: [tableMixin],
  data() {
    return {
      configForm: {
        onOff: "on",
        taskType: "HIGH_TEMP_SUBSIDY",
      },

      isModifyType: false,
      saveLoading: false,
      // 表格相关
      tableData: [],
      ruleList: [{ sort: 1, factoryId: "", multiRules: [{ condition: "", criterion: "" }], processesOption: [], }],
      tabList: [],
      // conditionsList: [
      //   { label: "考勤大于等于28天小于30天", value: "考勤大于等于28天小于30天", },
      //   { label: "考勤大于等于30天", value: "考勤大于等于30天" },
      // ],
      monthList: [
        { label: "1月", value: "1", },
        { label: "2月", value: "2", },
        { label: "3月", value: "3", },
        { label: "4月", value: "4", },
        { label: "5月", value: "5", },
        { label: "6月", value: "6", },
        { label: "7月", value: "7", },
        { label: "8月", value: "8", },
        { label: "9月", value: "9", },
        { label: "10月", value: "10", },
        { label: "11月", value: "11", },
        { label: "12月", value: "12", },

      ],

      reasonList: [],
    };
  },
  async created() {

    await this.$api.softwareSystemManage.getBasicPermission
      .getQuFactory({ moduleId: 4 })
      .then((res) => {
        this.tabList = res.data || [];
        this.getList()
      });

  },
  computed: {
    factoryNameOptions() {
      return (
        this.tabList.length > 0 &&
        this.tabList.map((item) => ({ id: item.id, name: item.name, process: item.process }))
      );
    },
  },
  methods: {
    getHiddenName(item, value, list) {
      if (!value) return
      let arr = []
      value.forEach((v) => {
        list.forEach((i) => {
          if (v == i.id) {
            arr.push(i.name ? i.name : i.processName)
          }
        })
      })
      // item.resultText = arr.join('/')
      return arr

    },
    getCondition(arr) {
      if (arr && arr.length) {
        let a1 = []
         arr.forEach((v) => {
          a1.push(v+'月')
        })
        return a1.join(',')
      }
    },
    //获取页面配置
    getList() {
      this.loading = true;
      this.$api.softwareSystemManage.getBasicPermission
        .getConfigList('HIGH_TEMP_SUBSIDY')
        .then(({ data }) => {
          const { onOff, id, ruleList } = data
          this.configForm = {
            onOff: onOff ? onOff : "on",
            taskType: "HIGH_TEMP_SUBSIDY",
            id
          }
          ruleList.forEach((v, index) => {
            v.processesOption = this.factoryNameOptions.find((item) => v.factoryId == item.id).process
            v.sort = index + 1
            v.multiRules = v.conditionCriterion
          })
          if (ruleList.length) {
            this.ruleList = ruleList
          }

        })
        .finally(() => {
          this.loading = false;
        });
    },
    changeFactoryId(item) {
      item.shiftGroup = [] //选择工厂时置空之前选中的班组
      item.processesOption = this.factoryNameOptions.find((v) => v.id == item.factoryId).process
    },
    changeCancel() {
      this.isModifyType = false;
      this.getList()
    },
    // 判断是否有相同对象元素
    hasDuplicateObjects(array) {
      array.forEach((v) => {
        v.multiRules = v.multiRules.flat();
      });
      let result = false;
      for (let i = 0; i < array.length; i++) {
        for (let j = i + 1; j < array.length; j++) {
          if (
            array[i].factoryId == array[j].factoryId &&
            this.intersection(array[i].shiftGroup, array[j].shiftGroup)
            && this.intersection(array[i].multiRules, array[j].multiRules)
          ) {
            result = true;
            this.$message.closeAll();
            this.$message.error(
              `第${array[i].sort}条和${array[j].sort}条核算标准配置中存在相同配置,请查看！`
            );
          }
        }
      }
      return result;
    },
    intersection(arr1, arr2) {
      let arr = arr1.filter((value) => arr2.includes(value));
      return arr.length;
    },
    hasDuplicateValue(arr) {
      return arr.length !== new Set(arr).size;
    },
    changeSave() {

      if (!this.ruleList.every(item => item.factoryId)) {
        this.$message.error('请选择核算工厂！')
        return
      }
      if (!this.ruleList.every(item => item.shiftGroup && item.shiftGroup.length)) {
        this.$message.error('请选择核算班组！')
        return
      }

      if (!this.ruleList.every(v => v.multiRules.every(item => item.condition.length))) {
        this.$message.error('请选择核算标准的月份!')
        return
      }
      if (!this.ruleList.every(v => v.multiRules.every(item => Number(item.criterion)))) {
        this.$message.error('请填写核算标准对应月份的金额!')
        return
      }
       //  检验谁否有重复月份 start
       let isRepeat = false;
      this.ruleList.forEach((v) => {
        v.arr = [];
        v.multiRules.forEach((i) => {
          i.condition.flat().forEach((k) => {
            v.arr.push(k);
          });
        });
      });
      this.ruleList.forEach((v) => {
        if (this.hasDuplicateValue(v.arr)) {
          isRepeat = true;
        }
      });
      if (isRepeat) {
        this.$message.error("同一条配置禁止存在相同月份!");
        return;
      }
      let checkSame = JSON.parse(JSON.stringify(this.ruleList)).map((item) => {
        return {
          factoryId: item.factoryId,
          shiftGroup: item.shiftGroup.flat(),
          multiRules: item.multiRules.map((v) => {
            return v.condition.flat();
          }),
          sort: item.sort,
        };
      })
      if (this.hasDuplicateObjects(checkSame)) return


      let params = {
        ...this.configForm,
        ruleList: this.ruleList.map((item) => {
          return {
            factoryId: item.factoryId,
            shiftGroup: item.shiftGroup.flat(),
            taskType: "HIGH_TEMP_SUBSIDY",
            multiRules: item.multiRules.map((v) => {
              return {
                condition: v.condition.flat(),
                criterion: v.criterion
              }
            })
          }
        })
      }

      this.saveLoading = true;
      this.$api.softwareSystemManage.getBasicPermission
        .configUpdate(params)
        .then(() => {
          this.$message.success("保存成功");
          this.getList()
        })
        .finally(() => {
          this.saveLoading = false;
          this.isModifyType = false;

        });
    },
    multiRulesAdd(item) {
      if (!this.isModifyType) return
      item.multiRules.push({ condition: [], criterion: "" })
    },
    multiRulesDel(item, index) {
      if (!this.isModifyType) return
      item.multiRules.splice(index, 1)
    },
    //新建
    newbuilt() {
      let len = this.ruleList.length
  
      this.ruleList.push({ sort: len + 1, factoryId: "", shiftGroup: [], multiRules: [{ condition: [], criterion: "" }], processesOption: [], });
    },
    //复制
    changeCopy(item) {
      let copyItem = JSON.parse(JSON.stringify(item))
      copyItem.sort = this.ruleList.length + 1
   
      this.ruleList.push(copyItem);
    },
    //删除
    handleDelete(index) {
      this.ruleList.splice(index, 1);
    },
    //校验金额
    checkAmount(item) {
      let flag = false;
      let amount = moneyDelete(item.criterion);
      if (!amount) {
        this.$message({
          message: "金额不能为空",
          type: "warning",
        });
        flag = true;
      }
      if (!flag) {
        if (!/^-?\d{1,5}(\.\d{1,2})?$/.test(amount)) {
          this.$message({
            message: "小数点前面仅支持5位数,小数点后面仅支持2位数",
            type: "warning",
          });
          item.criterion = ""
          // flag = true;
          // if (name) this.calculateForm[name] = this.payingAmount;
        }
      }
    },
  },
};
</script>

<style lang="stylus" scoped>
.enable {
  font-size: 14px;
  font-weight: 530;
}

.mg15 {
  margin: 0px 15px;
}

>>>.is-group .cell {
  white-space: pre-line;
}

.panel-tabs {
  >>> .main-area {
    padding-top: 0;
    height: 84vh;
  }
}

.tabs-row {
  margin-bottom: 5px;
  display: flex;
  align-items: center;
}

.tabs {
  >>> .el-tabs__header {
    margin-bottom: 0;
  }
}

>>>.table_active {
  background: pink !important;
}

>>> .itemVal {
  margin-left: 5px;
  width: 100%;
  padding: 0 5px;
}

>>> .el-input-group__append {
  background: #aaa;
  color: white;
  padding: 0 7px;
}

.details {
  display: flex;
  padding-bottom: 18px;

  &_content {
    flex: 1;
    display: flex;
    justify-content: space-between;

    > .el-select, > .el-input, > span {
      flex: 1 0 30%;
    }
  }

  &_btn {
    width: 20%;
  }
}
</style>

<style lang="stylus" scoped>
>>> .el-input input {
  height: 30px;
  line-height: 30px;
  /* width: 95px; */
  padding: 0px 10px;
}

>>>.el-cascader {
  height: 30px;
  line-height: 30px;
}

.panel-headers {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px 0px;

  .headers-left {
    display: flex;
    align-items: center;
  }

  .headers-right {
  }
}

.panel-content {
  margin-top: 20px;
  // border:1px solid red;
  overflow:hidden;
  overflow-y:scroll;
  height:90%;

  .content-top {
    font-size: 14px;
    font-weight: bold;
  }

  .content-item {
    margin-top: 10px;
  }

  .details_btn {
    display: flex;
    align-items: center;

    >>>.el-button {
      padding: 5px;
    }
  }

  .flexWrap {
    display: flex;
    // overflow: hidden;
    flex-wrap: wrap;
  }

  .selectWrap {
    width: 110px;
    margin-left: 5px;
  }

  .iconAdd {
    font-size: 22px;
    color: #0bb78e;
    line-height: 28px;
    cursor: pointer;
    margin: 0px 5px;
  }

  .iconDel {
    font-size: 22px;
    color: red;
    line-height: 28px;
    cursor: pointer;
    margin: 0px 5px;
  }

  .colff {
    color: white;
  }
}
</style>