<template>
  <qDialog
    :visible="visible"
    :innerScroll="true"
    :innerHeight="400"
    title="查看详情"
    width="900px"
    :showFooter="false"
    :before-close="handleCancel"
  >
    <el-form :model="stagesForm" label-width="82px" size="small">
      <el-row>
        <el-col :span="24">
          <el-form-item label="流程编号:">
            {{ stagesForm.code }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="员工姓名:">
            {{ stagesForm.borrowStaffName }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="厂牌编号:">
            {{ stagesForm.borrowStaffCode }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="借支类型:">
            {{ stagesForm.type }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="申请金额:">
            {{ stagesForm.processAmount | moneyFormat }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="审定金额:">
            {{ stagesForm.realAmount | moneyFormat }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="实付金额:">
            {{ stagesForm.actualAmount | moneyFormat }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="已还款:">
            {{ stagesForm.payedAmount | moneyFormat }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="待还款:">
            {{ stagesForm.payingAmount | moneyFormat }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="扣款详情:">
            <ul class="deduction">
              <li v-for="item in detailsList" :key="item.id">
                <!-- <span>{{ item.factoryName }}</span> -->
                <span>{{ item.repaymentTime }}</span>
                <span>{{ item.periodAmount | moneyFormat }}</span>
                <span>{{ item.isPay }}</span>
              </li>
            </ul>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注说明:">
            {{ stagesForm.comments }}
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </qDialog>
</template>

<script>
export default {
  name: "stageDialog",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    formData: Object,
  },
  data() {
    return {
      stagesForm: {
        code: "",
        factoryId: "",
        borrowStaffName: "",
        borrowStaffCode: "",
        type: "",
        processAmount: "",
        realAmount: "",
        actualAmount: "",
        payedAmount: "",
        payingAmount: "",
        comments: "",
      },
      detailsList: [],
    };
  },
  async created() {
    await this.getDetailed();
  },
  methods: {
    //获取借支详情
    getDetailed() {
      return this.$api.logisticsInformation.debitAccount
        .debitAccountDetails({ id: this.formData.id })
        .then(({ data }) => {
          this.stagesForm = { ...data };
          this.detailsList = data.repayments || [];
        });
    },
    handleCancel() {
      this.$emit("cancel");
    },
  },
};
</script>

<style lang="stylus" scoped>
.deduction {
  width: 100%;
  margin: 0;
  padding: 0;

  li {
    span {
      padding-right: 10px;
    }
  }
}
</style>
