<template>
  <q-dialog
    :visible="isVisible"
    title="导入" width="30%"
    :innerScroll="false"
    @close="onCancel"
    @cancel="onCancel"
    @confirm="onConfirm">
    <el-form ref="uploadForm"
      :model="uploadForm"
      label-width="90px"
      size="small">
      <el-form-item
        label="任务类型:"
        prop="taskType">
        <el-select
          v-model="uploadForm.taskType"
          filterable
          placeholder="请选择"
          clearable
          @change="selectFactory">
          <el-option
            v-for="item in taskOptions"
            :key="item.type"
            :label="item.value"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="代办任务:"
        prop="agencyType"
        v-show="isAgencyTask">
        <el-select
          v-model="uploadForm.agencyType"
          filterable
          placeholder="请选择"
          clearable
          @change="selectType">
          <el-option
            v-for="item in agencyOptions"
            :key="item.id"
            :label="item.value"
            :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="导入月份:" prop="accountingMonth">
        <el-select v-model="uploadForm.accountingMonth" filterable placeholder="请选择" clearable>
          <el-option v-for="item in monthOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item
        label="数据导入:"
        prop="file">
        <el-upload
          class="upload"
          :http-request="upload"
          action="*"
          accept=".xls,.xlsx"
          :before-upload="beforeUpload"
          :on-change="onFileChange"
          :auto-upload="false"
          ref="upload">
          <el-button
            size="small"
            type="primary">
            选择EXCEL
          </el-button>
          <div slot="tip"
            class="el-upload__tip">
            只能上传xls、xlsx文件，且大小不能超过10MB
          </div>
        </el-upload>
      </el-form-item>
    </el-form>
  </q-dialog>
</template>

<script>
import { assignValue } from "@/utils";
import { SUB_APP_CODE } from "@/api/api";
export default {
  // name: 'ImportFile',
  props: {
    // 重新对外暴露visible，控制显示隐藏
    visible: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    // 通过计算属性，对.sync进行转换，外部也可以直接使用visible.sync
    isVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
    reportName() {
      return JSON.parse(this.$route.query.data);
    },
    isAgencyTask() {
      return this.uploadForm.taskType != "员工管理" && !this.uploadForm.taskType.includes('成本')&&this.uploadForm.taskType!='奖惩台账';
    },
  },
  data() {
    return {
      uploadForm: {
        file: "",
        taskType: "",
        agencyType: "",
      },
      channelOptions: [],
      tableInfo2: [],
      querys: {},
      agencyOptions: [],
      taskOptions: [],
      tempOptions: [],
      agencyList: [],
      title: "",
      tableInfo1: [],
      BranchInformation: {},
    };
  },
  created() {
    this.getList();
    // this.$api.systemManage.getBasicPermission
    //   .getBasicPermissionAll()
    //   .then((res) => {
    //     if (res.code === 200) {
    //       this.factoryOptions = res.data.map((item) => ({
    //         label: item.name,
    //         name: item.name,
    //         id: item.id,
    //       }));
    //     }
    //   });
    //获取导入模块列表
    this.$api.exportApI.getImportModules().then((res) => {
      if (res.code === 200) {
        this.taskOptions = res.data;
      }
    });
    // this.querys = this.$route.query;
    // console.log(" this.querys.reportName1 :>> ", this.querys.reportName1);
    // console.log("this.querys :>> ", this.querys);
  },
  methods: {
    //获取Excel配置列表
    getList() {
      this.$api.importList
        .getExcelList({
          pageNum: 1,
          pageSize: 10000,
          filterData: {
            appCode: "was-customized",
            reportType: "IMPORT",
          },
        })
        .then((res) => {
          if (res.code === 200) {
            this.tempOptions = res.data.list;
          }
        });
    },
    // getChannelSource() {
    //   this.$api.customerMgmt.getChannelList()
    //     .then(({data}) => {
    //       if (Array.isArray(data)) {
    //         this.channelOptions = data.map(item => ({
    //           label: item.key,
    //           value: item.value
    //         }));
    //       }
    //     });
    // },
    selectFactory(val) {
      this.title = val;
      console.log("val :>> ", val);
      if (!val) return;
      let params = this.taskOptions.find(
        (item) => item.value === this.uploadForm.taskType
      );
      //获取导入任务
      this.$api.exportApI.getImportTask(params).then((res) => {
        if (res.code === 200) {
          this.agencyOptions = res.data.map((item) => ({
            otherId: item.otherId,
            id: item.id,
            accountingMonth: item.accountingMonth,
            factoryId: item.factoryId,
            otherId: item.otherId,
            id: item.id,
            value:
              item.factoryName +
              "," +
              item.accountingMonth +
              (item.planName ? "," + item.planName : ""),
          }));
          this.agencyList = res.data;
          console.log("this.agencyList :>> ", this.agencyList);
        }
      });
    },
    selectType(value) {
      console.log('value :>> ', value);
      this.BranchInformation = value;
      if (value.otherId != undefined) {
        let planId = value.otherId;
        let accountingMonth = value.accountingMonth
        let type = "common";
        let factoryId = value.factoryId
        this.$api.exportApI.otherHead(planId, type, factoryId, accountingMonth).then((res) => {
          this.tableInfo1 = res.data;
        });
      }
    },
    beforeUpload(file) {
      const excelTypes = [
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      ];
      if (!excelTypes.includes(file.type)) {
        this.$message.error("只能上传Excel文件");
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 10MB!");
        return false;
      }
      return true;
    },
    onFileChange(file) {
      this.uploadForm.file = file.raw;
      this.$refs.uploadForm.validateField("file");
    },
    upload({ file }) {
      // let reportName = this.tempOptions.find(item => item.fileName === this.uploadForm.taskType).reportName
      if (this.title === "其他补贴" || this.title === "其他扣款") {
        this.tableInfo2 = this.tableInfo1.filter(
          (v) => v.columnName != "核算月份" && v.columnName != "修改时间"
        );
        const loading = this.$loading({
          lock: true,
          text: "文件导入中...",
          spinner: "el-icon-loading",
        });
        let taskDTO = {
          excelImportDTO: {
            reportName:
              this.title == "其他补贴"
                ? "otherSubsidyImport"
                : "otherDeductionImport",
            appCode: "was-customized",
            paramMap: {
              customColumnDTO: this.tableInfo2,
              backlogId: this.BranchInformation.id,
              otherDetailId: this.BranchInformation.otherId,
              factoryId: this.BranchInformation.factoryId, //传id
              accountingMonth: this.BranchInformation.accountingMonth,
              cloumnVO: this.tableInfo2,
            },
          },
          columnDTOs: this.tableInfo2,
        };
        var formData = new FormData();
        formData.append("file", this.uploadForm.file);
        formData.append("taskDTO", JSON.stringify(taskDTO));
        this.$api.importList
          .getCustomColumnImportant(formData)
          .then((res) => {
            this.$notify.success({
              title: "成功",
              message: "操作成功",
            });
            this.onCancel();
            this.$emit("after");
          })
          .catch((err) => {
            this.$message.error("导入数据失败，请重新导入！");
          })
          .finally(() => {
            loading.close();
          });
      } else {
        let reportName = this.tempOptions.find(
          (item) => item.fileName === this.uploadForm.taskType
        ).reportName;
        const loading = this.$loading({
          lock: true,
          text: "文件导入中...",
          spinner: "el-icon-loading",
        });
        let fieldValue = this.taskOptions.find(
          (item) => item.value === this.uploadForm.taskType
        ).name;
        let obj
        if (this.uploadForm.taskType != "员工管理" && this.uploadForm.taskType != "成本赔偿扣款台账") {
          obj = this.uploadForm.agencyType.value.split(",");
        }
        let planObj = this.agencyList.find(
          (item) => item.factoryName == obj[0] && item.accountingMonth == obj[1]
        );
        let paramMap =
          (this.uploadForm.taskType == "员工管理" || this.uploadForm.taskType == "成本赔偿扣款台账")
            ? [
              {
                columnName: "任务类型",
                columnValue: this.uploadForm.taskType,
                fieldName: "taskType",
                fieldValue: fieldValue,
              },
            ]
            : [
              {
                columnName: "导入工厂",
                columnValue: planObj.factoryId,
                fieldName: "factoryId",
                fieldValue: planObj.factoryId,
              },
              {
                columnName: "任务类型",
                columnValue: this.uploadForm.taskType,
                fieldName: "taskType",
                fieldValue,
              },
              {
                columnName: "导入月份",
                columnValue: obj[1],
                fieldName: "accountingMonth",
                fieldValue: obj[1],
              },
              {
                columnName:
                  this.uploadForm.taskType == "个税扣款" ||
                    this.uploadForm.taskType == "分厂调整"
                    ? "任务总览ID"
                    : "代办任务ID",
                columnValue: planObj.id,
                fieldName:
                  this.uploadForm.taskType == "个税扣款" ||
                    this.uploadForm.taskType == "分厂调整"
                    ? "taskId"
                    : "backlogId",
                fieldValue: planObj.id,
              },
            ];
        const extData = {
          reportName,
          appCode: SUB_APP_CODE,
          paramMap,
        };
        this.$api.common
          .importFile(file, extData)
          .then((res) => {
            this.$notify.success({
              title: "成功",
              message: "操作成功",
            });
            this.onCancel();
            this.$emit("after");
          })
          .catch((err) => {
            this.$message.error("导入数据失败，请重新导入！");
          })
          .finally(() => {
            loading.close();
          });
      }
      // return;
      // console.log("import:", file);
      // const loading = this.$loading({
      //   lock: true,
      //   text: "文件导入中...",
      //   spinner: "el-icon-loading",
      // });
      // const extData = {
      //   reportName: this.reportName.reportName,
      //   appCode: SUB_APP_CODE,
      //   factoryId: this.reportName.factoryId,
      //   backlogId: this.reportName.backlogId,
      //   accountingMonth: this.reportName.accountingMonth,
      //   paramMap: [
      //     {
      //       columnName: "导入工厂",
      //       columnValue: this.uploadForm.factoryId,
      //       fieldName: "factoryId",
      //       fieldValue: this.uploadForm.factoryId,
      //     },
      //     {
      //       columnName: "任务类型",
      //       columnValue: this.uploadForm.type,
      //       fieldName: "type",
      //       fieldValue: this.uploadForm.type,
      //     },
      //     {
      //       columnName: "导入月份",
      //       columnValue: this.uploadForm.accountingMonth,
      //       fieldName: "accountingMonth",
      //       fieldValue: this.uploadForm.accountingMonth,
      //     },
      //   ],
      // };
      // this.$api.common
      //   .importFile(file, extData)
      //   .then((res) => {
      //     this.$notify.success({
      //       title: "成功",
      //       message: "导入数据成功",
      //     });
      //     this.onCancel();
      //     this.$emit("after");
      //   })
      //   .catch((err) => {
      //     this.$message.error("导入数据失败，请重新导入！");
      //   })
      //   .finally(() => {
      //     loading.close();
      //   });
    },
    getLabelByValue(options = [], value) {
      const option = options.find((item) => item.value === value);
      return (option && option.label) || "";
    },
    onCancel() {
      this.isVisible = false;
    },
    onConfirm() {
      // this.$refs.uploadForm.validate((valid) => {
      //   if (!valid) return;
      //   this.$refs.upload.submit();
      // });
      if (this.uploadForm.taskType == "员工管理" || this.uploadForm.taskType == '成本赔偿扣款台账') {
        if (!this.uploadForm.taskType || !this.uploadForm.file) {
          this.$message({
            message: "任务类型不能为空，请选择一个excel文件",
            type: "success",
          });
          return;
        }
      } else {
        if (!Object.values(this.uploadForm).every((item) => item != "")) {
          this.$message({
            message: "任务类型、代办任务不能为空，请选择一个excel文件",
            type: "success",
          });
          return;
        }
      }
      this.$refs.upload.submit();
    },
  },
};
</script>

<style lang="stylus" scoped>
.upload {
  >>> .el-upload__tip {
    line-height: 30px;
    margin-top: 0;
  }
}

.el-form {
  .el-form-item {
    display: flex;

    >>>.el-form-item__content {
      margin: 0 !important;
      flex: 1;

      .el-select {
        width: 100% !important;
      }
    }
  }
}
</style>