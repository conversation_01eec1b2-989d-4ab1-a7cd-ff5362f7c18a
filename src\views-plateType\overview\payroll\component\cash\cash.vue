<template>
  <!-- 上卡现金表 -->
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          size="mini"
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          label-position="right"
        >
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="员工姓名"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input
              v-model.trim="searchForm.staffCode"
              size="mini"
              clearable
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenStaffCodes"
                  titleName="厂牌编号"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="核算班组:" prop="belongProcessId">
            <el-select
              @change="onSearch"
              v-model="searchForm.belongProcessId"
              placeholder="请选择需要检索的工序"
              clearable
              filterable
            >
              <el-option
                v-for="item in teamOptinon"
                :key="item.name"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="支付方式:" prop="payType">
            <el-select
              @change="onSearch"
              v-model.trim="searchForm.payType"
              placeholder="请选择支付方式"
              clearable
              filterable
            >
              <el-option
                v-for="item in paymentOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary"  @click="onSearch">
            查询</el-button
          >
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置</el-button
          >
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :data="tableData"
        :height="maxTableHeight"
        style="width: 100%"
        :default-sort="{ prop: 'pieceWage', order: 'descending' }"
      >
        <el-table-column
          v-for="item in tableColumn"
          :key="item.name"
          :label="item.label"
          :width="item.width"
          align="left"
          :prop="item.name"
          :fixed="item.type ? 'right' : false"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <div>
              <span v-if="!item.type">{{ filterData(item.name,scope.row[item.name]) }}</span>
              <el-button
                v-else
                type="text"
                size="small"
                v-show="permission"
                @click="handleEdit(scope.row)"
              >
                编辑</el-button
              >
            </div>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div class="table_footer">
          <ul>
            <li>
              <span>核算工厂:</span><span>{{ info.factoryName }}</span>
            </li>
            <li>
              <span>核算月份:</span><span>{{ info.accountingMonth }}</span>
            </li>
            <li>
              <span>人数:</span><span>{{ statiStics.total }}</span>
            </li>
            <li>
              <span>上卡金额:</span
              ><span>{{ statiStics.bankAmount | moneyFormat }}</span>
            </li>
            <li>
              <span>现金金额:</span
              ><span>{{ statiStics.cashAmount | moneyFormat }}</span>
            </li>
          </ul>
        </div>
        <div style="text-align: right">
          <el-pagination
            @size-change="onSizeChange"
            @current-change="onNumChange"
            :current-page="pageNum"
            :page-size="pageSize"
            :total="total"
            :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper"
          >
          </el-pagination>
        </div>
      </template>
    </table-panel>
    <edit-dialog
      v-if="visible"
      :visible="visible"
      :formData="formData"
      @cancel="handleCancel"
    ></edit-dialog>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import { moneyFormat } from "@/utils";
import editDialog from "./editDialog";
export default {
  name: "PlateTypeCash",
  components: { editDialog },
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      searchForm: {
        content: "",
        belongProcessId: "",
        payType: "",
        staffCode: "",
        staffName: "",
      },
      formData: {},
      visible: false,
      tableData: [], //表格数据
      loading: false,
      teamOptinon: [], //工序
      paymentOptions: [
        { label: "上卡", value: "0" },
        { label: "现金", value: "1" },
      ], //支付方式
      resizeOffset: 67,
      permission: "",
      pageSize: 50,
      pageNum: 1,
      total: 0,
      filterParam: {},
      params: {},
      factoryId: "",
      //表格表头信息
      tableHeader: Object.freeze([
        { label: "员工姓名", name: "staffName", width: "100" },
        { label: "厂牌编号", name: "staffCode", width: "130" },
        { label: "身份证号", name: "idCard", width: "180" },
        { label: "核算工厂", name: "belongFactoryName", width: "150" },
        { label: "核算班组", name: "belongProcessName", width: "180" },
        { label: "核算月份", name: "accountingMonth", width: "100" },
        { label: "农行卡号", name: "abcNumber", width: "180" },
        { label: "光大卡号", name: "cebNumber", width: "180" },
        { label: "支付方式", name: "isCash", width: "100" },
        { label: "金额", name: "amount", width: "120" },
        { label: "备注说明", name: "comments" },
        { label: "", type: "operation", width: "100" },
      ]),
      info: {},
      statiStics: {},
    };
  },
  created() {},
  computed: {
    tableColumn() {
      this.tableHeader = this.tableHeader.map((item) => ({
        ...item,
        isShow: item.type ? this.permission : true,
      }));
      return this.tableHeader.filter((item) => item.isShow);
    },
  },
  watch: {
    $route: {
      handler(value) {
        if (value.path.includes("payroll")&&value.path.includes("plateType")) {
          this.info = JSON.parse(value.query.data);
          this.factoryId = this.info.factoryId;
          this.getAvailableGroups();
          this.getList();
          this.getDebitList();
          this.TaskPermissionrSubsidy();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {},
  methods: {
    //获取上卡现金表
    getList() {
      this.loading = true;
      this.$api.plateTypeWorkbench
        .getCash({
          pageSize: this.pageSize,
          pageNum: this.pageNum,
          filterData: {
            accountingMonth: this.info.accountingMonth,
            factoryId: this.factoryId,
            ...this.filterParam,
          },
        })
        .then((res) => {
            this.tableData = res.data.list.map((item) => ({
              ...item,
              amount: moneyFormat(item.amount),
            }))|| [];
            this.total = res.data.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //统计
    getDebitList() {
      let params = {
        accountingMonth: this.info.accountingMonth,
        factoryId: this.factoryId,
        ...this.filterParam,
      };
      this.$api.plateTypeStatiStics.statisticsBankCash(params).then((res) => {
        this.statiStics = res.data
          ? res.data
          : {
              total: 0,
              cashAmount: 0,
              bankAmount: 0,
            };
      });
    },
    // 获取历史任务操作权限
    TaskPermissionrSubsidy() {
      let params = {
        accountingMonth: this.info.accountingMonth,
        factoryId: this.factoryId,
      };
      this.$api.plateTypeStatiStics
        .statisticsOthegetCompleteTaskPermissionrSubsidy(params)
        .then((res) => {
          this.permission = res.data;
        });
    },
    //根据工厂和核算月份查询班组列表
    getAvailableGroups() {
      this.$api.plateTypeWorkbench
        .availableGroups({
          factoryId: this.factoryId,
          accountingMonth: this.info.accountingMonth,
        })
        .then(({ data }) => {
          this.teamOptinon = data;
        });
    },
    filterData(name,value){
      if( name!='金额' ) {
        return value
      }else{
        if(!value)return '-'
        return value
      }
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
      this.getDebitList();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    //编辑
    handleEdit(row) {
      this.visible = true;
      this.formData = {
        id: row.id || "",
        staffName: row.staffName || "",
        staffCode: row.staffCode || "",
        accountingMonth: row.accountingMonth || "",
        isCash: row.isCash || "",
        amount: row.amount || "",
        comments: row.comments || "",
      };
    },
    handleCancel(type) {
      this.visible = false;
      if (type == "cancel") return;
      this.getList();
      this.getDebitList();
    },
    //导出
    handleExport() {
      let params = {};
      if (Object.keys(this.filterParam).length) {
        params = {
          // factoryId: this.factoryId,
          ...this.filterParam,
        };
      }
      this.$api.common
        .doExport("plankExportbankcash", {
          ...params,
          ...this.params,
          factoryId: this.info.factoryId,
          accountingMonth: this.info.accountingMonth,
        })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success("导出操作成功，请前往导出记录查看详情");
          }
        });
    },
    onSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(val) {
      this.pageNum = val;
      this.getList();
    },
  },
};
</script>

<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}

.el-form--label-left {
  .el-form-item {
    margin-right: 16px;
    margin-bottom: 10px;

    &.third {
      >>>.el-form-item__content {
        margin: 0 !important;
      }
    }
  }
}

.table-panel {
  position: relative;

  >>>.btn_right {
    position: absolute;
    right: 0;
    z-index: 2;
  }
}

ul {
  list-style: none;
  display: flex;
  padding: 0;
}

>>>.table-panel-footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .table_footer {
    overflow: auto;

    >ul {
      display: flex;
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        white-space: nowrap
        padding: 0 10px;
      }
    }
  }
}

>>>.el-tabs__nav-wrap::after {
  display: block;
  height: 0;
}
</style>
