<template>
  <el-checkbox 
    :ref="'condition-item-' + source.value"
    class="condition-item" 
    v-model="checked" 
    @change="onCheckChange">
    {{source.label}}
  </el-checkbox>
</template>

<script>
import Emitter from '@/utils/emitter-mixin'
export default {
  name: 'ConditionItem',
  mixins: [Emitter],
  props: {
    source: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  watch: {
    'source.checked': {
      handler(val) {
        this.checked = val
      },
      immediate: true
    }
  },
  data() {
    return {
      checked: false
    }
  },
  methods: {
    onCheckChange(checked) {
      this.dispatch('QTableHeaderFilter', 'checkBoxValueChange', this.source.value, checked);
    }
  }
}
</script>

<style lang="stylus" scoped>
.condition-item
    display block
</style>