<template>
  <qDialog :visible="visible"
    title="扣款备注"
    :innerHeight="150"
    width="500px"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel">
    <el-form
      :model="deductionForm"
      ref="deductionForm"
      :rules="rules"
      label-width="120px"
      size="small">
      <el-form-item
        label="备注说明:"
        prop="remark">
        <el-input
          type="textarea"
          v-model.trim="deductionForm.remark"
          resize="none"
          rows="3"
          maxlength="300"
          show-word-limit
          placeholder=""></el-input>
      </el-form-item>
    </el-form>
  </qDialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    deductionList: Array
  },
  data() {
    return {
      deductionForm: {
        remark: ""
      },
      rules: {
        remark: [{ required: true, message: "备注说明不能为空", trigger: 'blur' }]
      }
    }
  },
  methods: {
    handleCancel() {
      this.$emit('deductionCancel', 'confirm')
    },
    handleConfirm() {
      this.$refs.deductionForm.validate(valid => {
        if (!valid) return
        this.$api.logisticsInformation.unpaidLedger
          .unPaidPayRemark({ ids: this.deductionList, ...this.deductionForm })
          .then((res) => {
            this.$notify({
              title: '成功',
              message: '批量备注成功',
              type: 'success'
            });
            this.$emit('deductionCancel', 'confirm')
          });
      })
    },
  }
}
</script>

<style lang="stylus" scoped>
.el-form-item {
  display: flex;

  >>>.el-form-item__content {
    margin-left: 0 !important;
    width: 100% !important;
  }
}

>>>.el-textarea__inner {
  padding-right: 50px;
}

.el-textarea {
  >>>.el-input__count {
    right: 20px !important;
  }
}
</style>