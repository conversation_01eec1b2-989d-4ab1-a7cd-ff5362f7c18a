<template>
  <content-panel>
    <qDialog
      :visible="visible"
      :innerScroll="false"
      :innerHeight="500"
      :title="title"
      width="600px"
      :isLoading="isLoading"
      @cancel="handleCancel"
      @confirm="handleConfirm"
      :before-close="handleCancel"
    >
      <el-form
        :model="addForm"
        :rules="rules"
        ref="addForm"
        size="small"
        label-width="173px"
      >
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="核算工厂:" prop="factoryId">
              <el-select
                v-model="addForm.factoryId"
                filterable
                clearable
                style="width: 100%"
                placeholder="请选择核算工厂"
                @change="changeFactoryId"
              >
                <el-option
                  v-for="item in factoryList"
                  :key="item.value"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item
              label="核算班组(工序):"
              prop="shiftGroupStr"
              :show-message="!addForm.shiftGroupStr"
            >
              <el-input
                disabled
                clearable
                placeholder="请选择核算班组"
                v-model="addForm.shiftGroupStr"
                type="text"
              >
                <template slot="append">
                  <el-button @click="selectlist">选择</el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="核算标准:">
              <div
                class="details"
                v-for="(item, index) in detailList"
                :key="item.date"
              >
                <div class="details_content">
                  <el-cascader
                    style="width: 140px"
                    v-model="item.condition"
                    :options="monthList"
                    collapse-tags
                    placeholder="请选择"
                    :props="{ multiple: true }"
                    clearable
                  >
                  </el-cascader>
                  <el-input
                    style="width: 100px"
                    clearable
                    placeholder="请输入金额"
                    v-model="item.criterion"
                    oninput="value=value.replace(/[^\d.]/g, '');if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+ 3)}"
                    @blur="checkAmount(item)"
                  >
                    <template slot="append">元/天</template>
                  </el-input>
                </div>
                <div class="details_btn">
                  <el-button type="text" @click="stages(index)">
                    新增
                  </el-button>
                  <el-button
                    v-show="detailList.length > 1 && index != 0"
                    type="text"
                    @click="handleDelete(item, index)"
                    style="color: red;"
                  >
                    删除
                  </el-button>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </qDialog>
    <add-Data
      v-if="isvisible"
      :visible.sync="isvisible"
      :processesList="processesList"
      :checkData="addForm.shiftGroup"
      @confirmGroupIds="confirmGroupIds"
    />
  </content-panel>
</template>
<script>
import { assignValue } from "@/utils";
import { moneyDelete } from "@/utils";
import addData from "./add-data.vue";
export default {
  components: { addData },
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    modifyData: {
      type: Object,
      default: {},
    },
    editForm: Object,
    modifyNum: Number,
  },
  computed: {
    title() {
      return this.modifyNum == 1
        ? "新增"
        : this.modifyNum == 2
        ? "编辑"
        : "复制";
    },
    processesList() {
      if (this.factoryList.length && this.addForm.factoryId) {
        return this.factoryList.find((v) => v.id == this.addForm.factoryId)
          .process;
      }
    },
  },
  data() {
    return {
      addForm: {
        factoryId: "",
        shiftGroup: [],
        id: "",
        shiftGroupStr: "",
      },

      multiRules: [],
      detailList: [{ condition: [], criterion: "" }],
      monthList: [
        { label: "1月", value: "1" },
        { label: "2月", value: "2" },
        { label: "3月", value: "3" },
        { label: "4月", value: "4" },
        { label: "5月", value: "5" },
        { label: "6月", value: "6" },
        { label: "7月", value: "7" },
        { label: "8月", value: "8" },
        { label: "9月", value: "9" },
        { label: "10月", value: "10" },
        { label: "11月", value: "11" },
        { label: "12月", value: "12" },
      ],
      rules: {
        factoryId: [
          { required: true, message: "核算工厂不能为空", trigger: "change" },
        ],
        shiftGroupStr: [
          { required: true, message: "核算班组不能为空", trigger: "blur" },
        ],
        criterion: [
          {
            required: true,
            pattern: /^(0|[0-9]{1,5})((\.\d{0,2})*)$/,
            message: "小数点前面仅支持5位数,小数点后面仅支持2位数",
            trigger: "blur",
          },
        ],
      },
      factoryList: [],

      isLoading: false,
      isvisible: false,
    };
  },
  created() {
    this.getFactoryList();
    this.init();
  },
  methods: {
    getCondition(arr) {
      if (arr && arr.length) {
        let a1 = [];
        arr.forEach((v) => {
          a1.push(v + "月");
        });
        return a1.join(",");
      }
    },
    stages(index) {
      if (this.detailList.length > 11) {
        this.$message.error("最多只能分12次");
        return;
      }
      this.detailList.push({ condition: [], criterion: "" });
    },
    handleDelete(v, value) {
      this.detailList.splice(value, 1);
    },
    getFactoryList() {
      this.$api.softwareSystemManage.getBasicPermission
        .getQuFactory({ moduleId: 4 })
        .then((res) => {
          this.factoryList = res.data || [];
        });
    },
    changeFactoryId() {
      this.addForm.shiftGroupStr = "";
      this.addForm.shiftGroup = [];
    },
    init() {
      if (this.modifyData) {
        this.initModifyData();
      }
    },
    initModifyData() {
      if (this.modifyNum == 3) {
        //复制不需要id
        this.modifyData.id = "";
      }
      this.detailList = this.modifyData.conditionCriterion;
      assignValue(this.addForm, this.modifyData);
    },

    //选择工序
    selectlist() {
      if (!this.addForm.factoryId) {
        this.$message.error("请先选择核算工厂");
        return;
      }
      this.isvisible = true;
    },
    confirmGroupIds(data) {
      console.log("data", data);
      let names = data.map((v) => {
        return v.name;
      });
      this.addForm.shiftGroupStr = names.join(",");
      this.addForm.shiftGroup = data.map((v) => {
        return v.id;
      });
    },
    // //校验金额
    checkAmount(item) {
      let flag = false;
      let amount = moneyDelete(item.criterion);

      if (!flag) {
        if (!/^-?\d{1,5}(\.\d{1,2})?$/.test(amount)) {
          this.$message({
            message: "小数点前面仅支持5位数,小数点后面仅支持2位数",
            type: "warning",
          });
          item.criterion = "";
        }
      }
    },
    handleConfirm() {
      this.$refs.addForm.validate((valid) => {
        if (!valid) return;
        if (this.detailList.some((val) => !val.condition.length)) {
          this.$message({
            type: "error",
            message: "请选择月份",
          });
          return;
        }
        if (this.detailList.some((val) => val.criterion == "")) {
          this.$message({
            type: "error",
            message: "请输入月份对应的金额",
          });
          return;
        }
        this.detailList.forEach((v) => {
          v.condition = v.condition.flat();
        });
        this.isLoading = true;
        let params = {
          ...this.addForm,
          multiRules: this.detailList,
          taskType: "HIGH_TEMP_SUBSIDY",
        };
        // modifyNum:1新增 2编辑 3复制

        this.$api.softwareSystemManage.getBasicPermission
          .configsaveOrUpdate(params)
          .then(() => {
            this.$notify({
              title: "成功",
              message: this.title + "成功",
              type: "success",
            });
            this.$emit("cancel", "confirm");
          })
          .finally(() => {
            this.isLoading = false;
          });
      });
    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
  },
};
</script>

<style lang="stylus" scoped>
.staffCode {
  >>>.el-input-group__append {
    background: #24c69a;
    color: #fff;

    .el-button {
      padding: 5px 20px;
    }
  }
}

.el-row {
  &::before, &::after {
    display: none;
  }

  display: flex;
  justify-content: space-between;
}

.el-form-item {
  display: flex;

  >>>.el-form-item__label {
    text-align: right;

  }

  >>>.el-form-item__content {
    width: 100%;
    margin-left: 0 !important;
  }
}

.assignedFactory {
  >>>.el-form-item__label {
    &::before {
      content: '*';
      color: #F23D20;
      margin-right: 4px;
      visibility: hidden;
    }
  }
}

>>>.el-date-editor {
  width: 100%;
}
.illustrate{
  color:#FF8900;
  padding:10px;
  margin-left:15px

}
>>>.el-cascader{
  margin-right:10px
}


>>>.el-input-group__append button.el-button{
  color: #fff;
  background-color: #0bb78e;
  border: 4px solid #0bb78e;
}
.details{
  display: flex;
  padding-bottom: 18px;

  &_content {
    flex: 1;
    display: flex;
    justify-content: space-between;

    >.el-select, >.el-input, >span {
      flex: 1 0 30%;
    }
  }

  &_btn {
    width: 20%;

    >.el-button {
      mar;
    }
  }
}
>>>.details_btn[data-v-b89b2e42] {
  width:25%
}
>>>.el-input--suffix .el-input__inner {
  padding-right:8px
}
>>>[data-v-b89b2e42] .el-cascader{

}
>>>.el-input.is-disabled .el-input__inner{
  background-color: #fff;
}
</style>
