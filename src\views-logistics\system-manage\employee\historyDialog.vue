<template>
  <qDialog :visible="isVisible" title="历史数据" :innerHeight="150" width="600px" @cancel="handleCancel"
    @confirm="handleConfirm" :before-close="handleCancel">
    <el-table stripe border v-loading="loading" ref="tableRef" height="150" highlight-current-row :data="tableData">
      <el-table-column width="50" type="index" align="left" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="staffCode" label="厂牌编号" align="left" show-overflow-tooltip>
      </el-table-column>
      <el-table-column label="更新时间" align="left" show-overflow-tooltip>
        <template slot-scope="{row}">
          {{ row.updateTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column prop="subProcess" label="子工序" align="left" width="80" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="productionCategory" label="员工类别" align="left" width="80" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="category" label="员工类别" align="left" show-overflow-tooltip>
      </el-table-column>
    </el-table>
  </qDialog>
</template>

<script>
export default {
  name: "historyDialog",
  props: {
    isVisible: {
      type: Boolean,
      required: true
    },
    editForm: Object
  },
  data() {
    return {
      tableData: [],
      loading: false
    };
  },
  created() {
    this.getDetails();
  },
  methods: {
    //获取员工详情
    getDetails() {
      this.loading = true;
      this.$api.information.employee.historyStaff({ staffCode: this.editForm.staffCode }).then(({ data }) => {
        this.tableData = data || [];
      }).finally(() => {
        this.loading = false;
      });
    },

    handleCancel() {
      this.$emit('historyCancel', 'cancel');
    },
    handleConfirm() {
      this.$emit('historyCancel', 'cancel');
    }
  }
};
</script>

<style lang="stylus" scoped></style>