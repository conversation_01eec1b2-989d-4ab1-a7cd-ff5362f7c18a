<template>
  <qDialog :visible="visible"
    :innerScroll="false"
    :isShowCancelBtn="isAllTrue"
    :title="title"
    :isLoading="isLoading"
    width="500px"
    @confirm="handleConfirm"
    @cancel="handleCancel"
    :before-close="handleCancel">
    <div
      v-show="['结束收集','提交一审','提交二审'].includes(title)">
      <taskVerify
        v-loading="loading"
        :list="listData"
        :isAllTrue="isAllTrue"
        :title="`验证成功！是否${title}？`" />
    </div>
    <span
      v-show="title == '强制退回'">强制退回将退回至【资料收集阶段】，中间数据全部重置，是否强制退回？</span>
    <el-form :model="formData"
      v-show="['财务一审', '财务二审', '调整审核'].includes(title)"
      ref="formRef"
      label-width="120px"
      class="formData">
      <el-form-item label="">
        <el-radio-group
          v-model="formData.type">
          <el-radio
            label="1">通过</el-radio>
          <el-radio
            label="0">不通过</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        v-if="formData.type == 0"
        label="驳回原因:"
        prop="reason">
        <el-input
          type="textarea"
          resize="none"
          rows="3"
          maxlength="20"
          show-word-limit
          v-model.trim="formData.reason">
        </el-input>
      </el-form-item>
    </el-form>
  </qDialog>
</template>

<script>
export default {
  name: "taskDialog",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    taskInfo: Object,
  },
  data() {
    return {
      formData: {
        type: "1",
        reason: "",
      },
      listData: [],
      loading: false,
      isLoading:false
    };
  },
  created() {
    this.isLoading = false
    if(['结束收集','提交一审','提交二审'].includes(this.title)){
      this.nodeVerification()
    }
  },
  computed: {
    isAllTrue() {
      return this.listData.map((item) => item.icon).every((item) => item);
    },
  },
  methods: {
    //提交一审、提交二审、结束收集校验
    nodeVerification() {
      this.loading = true;
      let params = {
        taskId: this.taskInfo.id,
      };
      this.$api.softwareWorkbench
        .checkNode(params)
        .then(({ data }) => {
          this.listData = data || [];
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleConfirm() {
      if (["结束收集", "提交一审", "提交二审"].includes(this.title)) {
        if (!this.isAllTrue) {
          this.$emit("cancel", "cancel");
        } else {
          this.isLoading = true
          this.$api.softwareWorkbench
            .nextNode({
              taskId: this.taskInfo.id,
            })
            .then((res) => {
              this.$message({
                message: `${this.title}成功`,
                type: "success",
                duration: 1500,
              });
              this.$emit("cancel", "confirm");
            }).finally(()=>{
            this.isLoading = false;
          });
        }
      } else {
        switch (this.title) {
          case "财务一审":
            this.isLoading = true
            if (this.formData.type == "1") {
              this.$api.softwareWorkbench
                .nextNode({
                  taskId: this.taskInfo.id,
                  params: {
                    isPass: this.formData.type,
                  },
                })
                .then((res) => {
                  this.$emit("cancel", "confirm");
                }).finally(()=>{
                  this.isLoading = false;
              });
            } else {
              this.$api.softwareWorkbench
                .nextNode({
                  taskId: this.taskInfo.id,
                  params: {
                    isPass: this.formData.type,
                    reason: this.formData.reason,
                  },
                })
                .then((res) => {
                  this.$emit("cancel", "confirm");
                }).finally(()=>{
                  this.isLoading = false;
               });
            }

            break;
          case "财务二审":
             this.isLoading = true
            if (this.formData.type == "1") {
              this.$api.softwareWorkbench
                .nextNode({
                  taskId: this.taskInfo.id,
                  params: {
                    isPass: this.formData.type,
                  },
                })
                .then((res) => {
                  this.$emit("cancel", "confirm");
                }).finally(()=>{
                  this.isLoading = false;
                });
            } else {
              this.$api.softwareWorkbench
                .nextNode({
                  taskId: this.taskInfo.id,
                  params: {
                    isPass: this.formData.type,
                    reason: this.formData.reason,
                  },
                })
                .then((res) => {
                  this.$emit("cancel", "confirm");
                }).finally(()=>{
                  this.isLoading = false;
              });
            }

            break;
          case "调整审核":
             this.isLoading = true
            if (this.formData.type == "1") {
              this.$api.softwareWorkbench
                .nextNode({
                  taskId: this.taskInfo.id,
                  params: {
                    isPass: this.formData.type,
                  },
                })
                .then((res) => {
                  this.$emit("cancel", "confirm");
                }).finally(()=>{
                    this.isLoading = false;
                })
            } else {
              this.$api.softwareWorkbench
                .nextNode({
                  taskId: this.taskInfo.id,
                  params: {
                    isPass: this.formData.type,
                    reason: this.formData.reason,
                  },
                })
                .then((res) => {
                  this.$emit("cancel", "confirm");
                }).finally(()=>{
                    this.isLoading = false;
                });
            }
            break;
          default:
            this.isLoading = true;
            this.$api.softwareWorkbench
              .forceBack({
                taskId: this.taskInfo.id,
              })
              .then((res) => {
                this.$emit("cancel", "confirm");
              }).finally(()=>{
            this.isLoading = false;
          });
        }
      }
    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
  },
};
</script>

<style lang="scss" scoped></style>
