<template>
  <qDialog
    :visible="isVisible"
    :innerScroll="true"
    :innerHeight="400"
    :title="title"
    width="850px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <el-form
      :model="addForm"
      ref="addForm"
      label-width="120px"
      :rules="rules"
      size="small"
    >
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input
              v-model.trim="addForm.staffCode"
              clearable
              placeholder="请输入厂牌编号"
            >
              <template slot="append">
                <el-button type="primary" @click="searchStaffCode">
                  查询
                </el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="processCode" label="应聘人员:">
            {{ addForm.staffName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item
            class="processCode"
            label="应聘人身份证号:"
            label-width="134px"
          >
            {{ addForm.idCard }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            class="processCode"
            label="应聘组织全路径:"
            label-width="134px"
          >
            {{ addForm.orgPath }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <!-- :picker-options="pickerOptions" -->
          <el-form-item label="报名日期:" prop="startDate">
            <el-date-picker
              v-model="addForm.startDate"
              type="date"
              placeholder="选择日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="应聘组织:" class="processCode">
            {{ addForm.orgAbbr }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item class="processCode" label="应聘岗位:">
            {{ addForm.post }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="现工序:" prop="curProcess" class="processCode">
            <el-input v-model="addForm.curProcess" placeholder="请输入组织路径" maxlength="20">
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item
            label="现班组:"
            prop="curShiftGroup"
            class="processCode"
          >
            <el-input
              v-model="addForm.curShiftGroup"
              maxlength="20"
              placeholder="请输入现班组"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="现子工序:"
            prop="curSubProcess"
            class="processCode"
          >
            <el-input
              v-model="addForm.curSubProcess"
              maxlength="20"
              placeholder="请输入现子工序"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item
            label="现组织全路径:"
            prop="curOrgPath"
            class="processCode"
          >
            <el-input
              v-model="addForm.curOrgPath"
              maxlength="50"
              placeholder="请输入现组织全路径"
              class="organization"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item
            label="补贴类型:"
            prop="subsidyType"

          >
            <el-select
              v-model="addForm.subsidyType"
              filterable
              clearable
              placeholder="请选择补贴类型"
            >
              <el-option
                v-for="item in subsidy"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="熟练程度:"
            prop="masteryLevel"
            v-if="addForm.subsidyType != '无补贴'"
          >
            <el-select
              v-model="addForm.masteryLevel"
              filterable
              clearable
              placeholder="请选择熟练程度"
            >
              <el-option
                v-for="item in skilledList"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </qDialog>
</template>

<script>
import moment from "moment";
import { moneyDelete } from "@/utils";
import NP from "number-precision";
NP.enableBoundaryChecking(false);
export default {
  name: "addCost",
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      addForm: {
        id: "",
        staffCode: "",
        staffName: "",
        startDate: "",
        idCard: "",
        curShiftGroup: "",
        curSubProcess: "",
        curOrgPath: "",
        orgPath: "",
        subsidyType: "",
        masteryLevel: "",
        curProcess: "",
        idCardMd5:"",
      },
      newStaffCode: "",
      proof: [],
      fileList: [],
      // pickerOptions: {
      //   disabledDate(time) {
      //     return time.getTime() > Date.now();
      //   },
      // },

      rules: {
        startDate: [
          { required: true, message: "请选择报名日期", trigger: "change" },
        ],
        staffCode: [
          { required: true, message: "请输入厂牌编号", trigger: "blur" },
        ],
        subsidyType: [
          { required: true, message: "请选择补贴类型", trigger: "change" },
        ],
        masteryLevel: [
          { required: true, message: "请选择熟手程度", trigger: "change" },
        ],
      },
      tabList: [],
      isLoading: false,
      num: 1,
      subsidy: Object.freeze([
        {
          name: "新手补贴",
          value: "新手补贴",
        },
        {
          name: "新员工补贴",
          value: "新员工补贴",
        },
        {
          name: "熟手补贴",
          value: "熟手补贴",
        },
        {
          name: "无补贴",
          value: "无补贴",
        },
      ]),
    };
  },
  computed: {
    skilledList() {
      this.addForm.masteryLevel = "";
      let name = this.addForm.subsidyType;
      if (name == "新手补贴" || name == "新员工补贴") {
        return [{ name: "生手", value: "生手" }];
      }
      if (name == "熟手补贴") {
        return [
          { name: "熟手A", value: "熟手A" },
          { name: "熟手B", value: "熟手B" },
          { name: "标准", value: "标准" },
        ];
      }
    },
  },
  methods: {
    //根据厂牌编号查询
    searchStaffCode() {
      this.$refs.addForm.validateField(["staffCode"], async (valid) => {
        if (valid) return;
        await this.$api.plateTypeInformation.employee
          .employeeDetails({ staffCode: this.addForm.staffCode })
          .then(({ data }) => {
            this.newStaffCode = data.staffCode || "";
            this.addForm = {
              ...this.addForm,
              staffName: (data && data.staffName) || "",
              idCard: (data && data.idCard) || "",
              orgPath: (data && data.orgPath) || "",
              orgAbbr: (data && data.orgAbbr) || "",
              post: (data && data.workJobs) || "",
              id: (data && data.id) || "",
              idCardMd5: (data && data.idCardMd5) || "",
            };
          });
      });
    },

    handleCancel() {
      const filePath = this.fileList.map((item) => item.proofUrl);
      if (filePath.length > 0) {
        this.$api.common.deleteUploadFile(JSON.stringify(filePath));
      }
      this.$emit("cancel", {
        type: "cancel",
        isVisible: false,
      });
    },
    async handleConfirm() {
      this.$refs.addForm.validate((valid) => {
        if (!valid) return;
        if (!this.addForm.staffName) {
          this.$message.warning("请先输入厂牌编号查询责任人");
          return;
        }
        this.isLoading = true;
        let params = {
          ...this.addForm,
          staffCode: this.newStaffCode,
          startDate: moment(this.addForm.startDate).format("YYYY-MM-DD"),
        };
        this.$api.plateTypeInformation.newEmployeeSubsidies
          .newstaffsave(params)
          .then((data) => {
            this.$notify.success({
              title: "成功",
              message: "新增成功",
            });
            this.$emit("cancel", {
              type: "confirm",
              isVisible: false,
            });
          })
          .finally(() => {
            this.isLoading = false;
          });
      });
    },
  },
};
</script>

<style lang="stylus" scoped>
.organization{
    width:700px
}
>>>.el-form-item__label {
  text-align: left;
}
.processCode {
  >>>.el-form-item__label {
    &::before {
      content: '*';
      color: #F23D20;
      margin-right: 4px;
      visibility: hidden;
    }
  }
}

>>>.el-input-group__append {
  background: #24c69a;
  color: #fff;

  .el-button {
    padding: 5px 10px;
  }
}

>>>.el-select, >>>.el-date-editor {
  width: 100%;
}

.el-upload__tip {
  padding-left: 10px;
  display: inline-block;
  color: #24c69a;
}

>>>.el-textarea__inner {
  padding-right: 50px;
}

.el-textarea {
  >>>.el-input__count {
    right: 20px !important;
  }
}
.details {
  display: flex;
  padding-bottom: 18px;

  &_content {
    flex: 1;
    display: flex;
    justify-content: space-between;

    >.el-select, >.el-input, >span {
      flex: 1 0 30%;
    }
  }

  &_btn {
    width: 20%;

    >.el-button {
      mar;
    }
  }
}

/deep/.el-input__inner{
  background:white !important
}
</style>
