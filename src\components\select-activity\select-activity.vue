<template>
  <div class="container">
    <div class="input-box">
      <el-input
        v-model="selectedNames"
        v-popover:popover
        placeholder="请选择活动"
        size="small"
        clearable
        @clear="onInputClear"
        @focus="isPopvisible=true"
        @blur="isPopvisible=false">
        <el-button slot="append" type="primary" size="small" @click="showSelectDialog">选择</el-button>
      </el-input>
      <!-- 已选择活动提示 -->
      <template v-if="selectedItems.length > 1">
        <el-popover
          popper-class="input-popper"
          placement="bottom"
          :visible-arrow="true"
          width="210"
          offset="15"
          trigger="manual"
          v-model="isPopvisible">
          <el-scrollbar class="pop-container" wrap-class="pop-content">
            <tip-item v-for="item in selectedItems" :key="item.id" :item="item" @remove="onRemoveItem" />
          </el-scrollbar>
        </el-popover>
      </template>
    </div>
    <el-dialog
      v-if="isVisible"
      :visible.sync='isVisible'
      v-bind="$attrs"
      title="选择活动"
      width="800px"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      class="main-dialog">
      <div class="content">
        <el-form class="searchForm" :inline="true" :model="searchForm" ref="searchForm">
          <el-form-item label="活动名称:" prop="title">
            <el-input v-model="searchForm.title" placeholder="请输入活动名称" size="small"></el-input>
          </el-form-item>
          <el-form-item label="活动类型:" prop="activityType">
            <el-select v-model="searchForm.activityType" size="small" clearable placeholder="请选择活动类型">
              <el-option
                v-for="item in activityTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item style="float: right;">
            <el-button size="small" type="primary" @click="onSearch">查询</el-button>
            <el-button size="small" @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
        <el-container class="table-container">
          <el-table
            ref="tableRef"
            v-loading="loading"
            :highlight-current-row="!isMultiple"
            @current-change="onCurrentChange"
            @selection-change="onSelectChange"
            :data="tableData"
            :header-cell-style="{ backgroundColor: '#FAFAFA' }"
            max-height="340"
            style="width: 100%">
            <el-table-column v-if="isMultiple"
              type="selection"
              width="55">
            </el-table-column>
            <el-table-column prop="title" label="活动名称" show-overflow-tooltip></el-table-column>
            <el-table-column width="200" label="活动类型" prop="activityType">
              <template slot-scope="scope">
                {{ scope.row.activityType | activityType }}
              </template>
            </el-table-column>
          </el-table>
          <el-footer style="text-align: right;">
            <el-pagination
              @size-change="onSizeChange"
              @current-change="onNumChange"
              :current-page="pageNum"
              :page-sizes="[20,50,100]"
              :page-size="pageSize"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
            ></el-pagination>
          </el-footer>
        </el-container>
      </div>
      <!-- el-dialog的footer插槽 -->
      <template #footer>
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleCancel" size="small">取消</el-button>
          <el-button type="primary" size="small" @click="handleConfirm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import TipItem from './tip-item'
export default {
  name: 'SelectActivity',
  inheritAttrs: false,
  components: {
    TipItem
  },
  props: {
    value: Array,
    // 已选中的活动
    selectedData: {
      type: [Array],
      default: () => []
    },
    // 是否
    isMultiple: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      loading: true,
      searchForm: {
        // 活动名称
        title: ''
      },
      activityTypeOptions: [
        {label: 'H5', value: 1},
        {label: '小程序', value: 2}
      ],
      // 当前搜索条件
      searchParam: {
        filterData: {
          title: '',
          activityType: ''
        }
      },
      // 累加选中的活动
      selectedItems: [],
      selectedIds: [],

      // 表格相关
      tableData: [],
      selection: [],
      pageSize: 50,
      pageNum: 1,
      total: 0,

      isVisible: false,
      selectedNames: '',
      isPopvisible: false
    }
  },
  computed: {},
  watch: {
    value(val) {
      if (val && val.length) {
        this.handleSelectedNames();
      } else {
        this.clearInput();
      }
    }
  },
  methods: {
    showSelectDialog() {
      this.isVisible = true;
    },
    getList() {
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize
      };

      // 过滤条件
      if (this.searchParam) {
        params = {
          ...params,
          ...this.searchParam
        }
      }

      this.loading = true;
      return this.$api.activity.getActivities({ data: params })
        .then(({data}) => {
          if (!data) return;

          this.tableData = data.records;
          this.total = data.total;
        })
        .finally(_ => this.loading = false);
    },
    // 初次加载，选中之前选中的数据
    doSelect() {
      // 先从tableData中找出要选中的数据，单选和多选调用不同的方法
      const selectedIds = this.selectedData.map(_ => _.id);

      if (!selectedIds.length) return;

      const selectedRows = [];
      this.tableData.forEach(item => {
        if (selectedIds.includes(item.id)) {
          selectedRows.push(item);
        }
      })

      if (this.isMultiple) {
        this.doMultiPleSelect(selectedRows);
      } else {
        this.doSingleSelect(selectedRows[0]);
      }
    },
    doSingleSelect(row) {
      this.$refs.tableRef.setCurrentRow(row);
    },
    doMultiPleSelect(rows) {
      if (rows) {
        rows.forEach(row => {
          this.$refs.tableRef.toggleRowSelection(row);
        });
      }
    },
    // 多选
    onSelectChange(selection = []) {
      this.selection = selection.map(item => ({
        id: item.id,
        name: item.title
      }));
    },
    // 单选
    onCurrentChange(selection) {
      this.selection = [{
        id: selection.id,
        name: selection.title
      }];
    },
    // 改变每页展示数据
    onSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getList();
    },
    // 改变当前页
    onNumChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    onSearch() {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.searchParam = {
        filterData: {}
      };
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== 'undefined' && val !== null) {
          this.searchParam.filterData[key] = val
        }
      }
      this.pageNum = 1;
      this.getList();
    },
    resetSearch() {
      this.$refs.searchForm.resetFields();
      this.searchParam = {
        filterData: {
          title: ''
        }
      };
      this.getList();
    },
    handleSelectedNames() {
      this.selectedNames = this.selectedItems.map(item => item.name).join(', ');
    },
    onInputClear() {
      this.clearInput();
      this.emitValue();
    },
    clearInput() {
      this.selectedNames = '';
      this.selectedItems = [];
      this.selection = [];
    },
    // 累加选择
    addToSelection(items = []) {
      if (this.isMultiple) {
        // 将items累加到selectedItems中
        items.forEach(item => {
          if (!this.selectedIds.includes(item.id)) {
            this.selectedItems.push(item);
          }
        });
      } else {
        this.selectedItems = items;
      }
    },
    onRemoveItem(itemId) {
      const removeIndex = this.selectedItems.findIndex(item => item.id === itemId);
      if (removeIndex > -1) {
        this.selectedItems.splice(removeIndex, 1);
      }
      this.emitValue();
    },
    emitValue() {
      this.selectedIds = this.selectedItems.map(item => item.id);
      this.$emit('input', this.selectedIds);
    },
    handleCancel() {
      this.isVisible = false;
    },
    handleConfirm() {
      this.addToSelection(this.selection);
      // 至少选择一个活动
      if (!this.selectedItems.length) {
        this.$message({
          message: '请至少选择一个活动',
          type: 'warning'
        });
        return;
      }
      this.emitValue();
      this.isVisible = false;
    }
  },
  filters: {
    activityType(val) {
      const map = {
        1: 'H5',
        2: '小程序'
      }
      return map[val] || '';
    }
  },
  mounted() {
    this.getList().then(() => {
      // TODO 由于涉及翻页，翻页后不知道某行是否是用户手动选中或取消选中，无法根据传入的selectedData做相应处理，
      // 因此只能处理第一页的选中，这时可接收这种方案，可取消注释
      // setTimeout(() => {
      //    // 延时等表格数据加载完成后，再执行选中
      //   this.doSelect();
      // }, 50);
    });
  }
}
</script>

<style lang="stylus" scoped>
.main-dialog
  >>> .el-dialog__header
    padding 20px 20px 0
  >>> .el-dialog__body
    padding 0px 20px
  >>> .el-footer
    padding 10px 0 0
    height 40px !important
  >>> .el-table td,
  >>> .el-table th
    padding 5px 0
  >>> .el-table__header-wrapper thead
    line-height 20px
.searchForm
  >>> .el-form-item
    margin-bottom 5px
.input-box
  display inline-block
  padding 3px 0px
  >>> .input-popper
    padding 10px 0 !important
    // padding-right 0 !important
    height 200px !important
  .pop-container
    height 100%
  >>> .pop-content
    overflow-x hidden !important
</style>