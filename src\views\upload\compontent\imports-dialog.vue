<template>
  <q-dialog
    :visible="isVisible"
    title="导入"
    width="500px"
    :innerScroll="false"
    @close="onCancel"
    @cancel="onCancel"
    @confirm="onConfirm"
  >
    <el-form
      ref="uploadForm"
      :model="uploadForm"
      :rules="rules"
      label-width="80px"
    >
      <el-form-item label="文件:" prop="file">
        <el-upload
          class="upload"
          :http-request="upload"
          action="*"
          accept=".xls,.xlsx"
          :before-upload="beforeUpload"
          :on-change="onFileChange"
          :auto-upload="false"
          ref="upload"
        >
          <el-button size="small" type="primary">选择文件</el-button>
          <div slot="tip" class="el-upload__tip">
            只能上传xls、xlsx文件，且大小不能超过10MB
          </div>
        </el-upload>
      </el-form-item>
    </el-form>
  </q-dialog>
</template>

<script>
import { assignValue } from "@/utils";
import { SUB_APP_CODE } from "@/api/api";
export default {
  name: "ImportFile",
  props: {
    // 重新对外暴露visible，控制显示隐藏
    visible: {
      type: Boolean,
      default: false,
    },
    tableInfo1: {
      type: Array,
      default: () => ({}),
    },
    activeName1: {
      type: String,
      default: () => ({}),
    },
    dateInfo: {
      type: String,
      default: () => ({}),
    },
    reportName1: {
      type: String,
      default: () => ({}),
    },
  },
  computed: {
    // 通过计算属性，对.sync进行转换，外部也可以直接使用visible.sync
    isVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
  },
  data() {
    return {
      uploadForm: {
        activity: "",
        channelSource: "",
        file: "",
      },
      tableInfo2: [],
      channelOptions: [],
      rules: {
        activity: [
          { required: true, message: "活动名称不能为空", trigger: "blur" },
        ],
        channelSource: [
          { required: true, message: "渠道不能为空", trigger: "change" },
        ],
        file: [{ required: true, message: "文件不能为空", trigger: "change" }],
      },
    };
  },
  methods: {
    beforeUpload(file) {
      const excelTypes = [
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      ];
      if (!excelTypes.includes(file.type)) {
        this.$message.error("只能上传Excel文件");
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 10MB!");
        return false;
      }
      return true;
    },
    onFileChange(file) {
      console.log("file :>> ", file);
      this.uploadForm.file = file.raw;
      this.$refs.uploadForm.validateField("file");
    },
    upload({ file }) {
      console.log("import:", file);
      const loading = this.$loading({
        lock: true,
        text: "文件导入中...",
        spinner: "el-icon-loading",
      });

      // let taskDTO = {
      //   reportName: "otherSubsidyImport",
      //   appCode: "was-customized",
      //   paramMap: {
      //     customColumnDTO: this.tableInfo1,
      //     factoryId: this.activeName1,
      //     accountingMonth: this.dateInfo,
      //   },
      //   columnDTOs: this.tableInfo1,
      // };
      let taskDTO = {
        excelImportDTO: {
          reportName: this.reportName1,
          appCode: "was-customized",
          paramMap: {
            customColumnDTO: this.tableInfo2,
            factoryId: this.activeName1, //传id
            accountingMonth: this.dateInfo,
            cloumnVO: this.tableInfo2,
          },
        },
        columnDTOs: this.tableInfo2,
      };
      var formData = new FormData();
      formData.append("file", this.uploadForm.file);
      formData.append("taskDTO", JSON.stringify(taskDTO));
      this.$api.importList
        .getCustomColumnImportant(formData)
        .then((res) => {
          this.$notify.success({
            title: "成功",
            message: "导入数据成功",
          });
          this.onCancel();
          this.$emit("after");
        })
        .catch((err) => {
          this.$message.error("导入数据失败，请重新导入！");
        })
        .finally(() => {
          loading.close();
        });
    },
    getLabelByValue(options = [], value) {
      //   const option = options.find(item => item.value === value);
      //   return option && option.label || '';
    },
    onCancel() {
      this.isVisible = false;
    },
    onConfirm() {
      console.log(this.uploadForm);
      this.$refs.uploadForm.validate((valid) => {
        if (!valid) return;
        this.$refs.upload.submit();
      });
    },
  },

  created() {
    this.tableInfo2 = this.tableInfo1.filter(
      (v) => v.columnName != "核算月份" && v.columnName != "修改时间"
    );
    console.log(' this.tableInfo2 :>> ',  this.tableInfo2);
  },
};
</script>

<style lang="stylus" scoped>
.upload {
  >>> .el-upload__tip {
    line-height: 30px;
    margin-top: 0;
  }
}
</style>