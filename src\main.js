import Vue from 'vue'
import 'normalize.css'

import App from './App.vue'
import router from './router'
import store from './store'

import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
Vue.use(ElementUI);
import VXETable from 'vxe-table'
import 'vxe-table/lib/style.css'
Vue.use(VXETable)
import './styles/index.styl'
import '@/assets/css/theme/index.css'
import '@/assets/css/iconfont/iconfont.css'
import Base64 from '@/utils/base64.js'
Vue.prototype.$Base64 = Base64;
import './permission'
import './utils/filter'
// import './utils/vxe-table'
import './components/index'
import Permission from './directive/permission/'
Vue.use(Permission);

import api from './api'
Vue.prototype.$api = api;

import bus from '@/utils/bus'
Vue.prototype.$bus = bus;

import Layout from './layout'
import qyPlugin from 'modules-plugin-qy'
import {baseUrl} from './utils/constant'
console.log('process.env.BUILD_ENV:', process.env.BUILD_ENV);
Vue.use(qyPlugin, {
  BUILD_ENV: process.env.BUILD_ENV,
  baseUrl: baseUrl,
  router,
  layout: Layout
})   

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
});
