<template>
  <qDialog
    :visible="visible"
    title="编辑"
    width="500px"
    :innerHeight="400"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <el-form ref="editForm" :rules="rules" :model="editForm" label-width="100px">
      <el-form-item label="厂牌编号:">
        {{ editForm.staffCode }}
      </el-form-item>
      <el-form-item label="身份证号:" prop="idCard">
        <template>
          <span style="margin-right: 10px">{{ editForm.idCard }}</span>
          <i
            v-if="editForm.idCard"
            :class="['iconfont', showType ? 'icon-guanbi' : ' icon-yincangmima']"
            @click="toggle"
          ></i>
        </template>
      </el-form-item>
      <el-form-item label="员工姓名:">
        {{ editForm.staffName }}
      </el-form-item>
      <el-form-item label="核算月份:">
        {{ editForm.accountingMonth }}
      </el-form-item>
      <el-form-item label="分配厂牌:" prop="assignCode">
        <el-radio-group v-model="editForm.assignCode">
          <el-radio v-for="item in codeList" :label="item.id" :key="item.code">{{
            item.code
          }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="体检费:" prop="examAmount">
        <el-input
          oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
          v-model="editForm.examAmount"
          clearable
        >
        </el-input>
      </el-form-item>
    </el-form>
  </qDialog>
</template>

<script>
import { moneyFormat, moneyDelete } from "@/utils";
export default {
  name: "editDialog",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    formData: Object,
  },
  data() {
    return {
      editForm: {
        idCard: "",
        staffCode: "",
        staffName: "",
        accountingMonth: JSON.parse(this.$Base64.decode(this.$route.query.data)).accountingMonth,
        assignCode: "",
        amount: "",
        idCardRsa: "",
        assignCode: "",
        examAmount: "",
      },
      codeList: [],
      rules: {
        assignCode: [{ required: true, message: "请选择厂牌编号", trigger: "change" }],
        examAmount: [
          { required: true, message: "体检费不能为空", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (!/^-?\d{1,5}(\.\d{1,2})?$/.test(moneyDelete(value))) {
                return callback(new Error("小数点前面仅支持5位数,小数点后面仅支持2位数"));
              }
              callback();
            },
            trigger: "blur",
          },
        ],
      },
      showType: false,
      idCardOrigin: "",
      idCardDecoded: "",
      isLoading: false,
    };
  },
  created() {
    this.getDetail();
  },
  methods: {
    //体检费明细
    async getDetail() {
      const { data } = await this.$api.dataUpload.physicalExam.physicalDetail({
        id: this.formData.id,
      });
      this.editForm = {
        ...this.editForm,
        idCard: data.idCard || "",
        staffName: data.staffName || "",
        staffCode: data.staffCode || "",
        idCardRsa: data.idCardRsa || "",
        examAmount: data.examAmount || "",
      };
      this.idCardOrigin = data.idCard || "";
      this.codeList =
        data.staffCodes.map((item) => ({
          code: item,
          id: item,
        })) || [];
      this.editForm.assignCode = this.codeList.find(
        (item) => item.code == this.editForm.staffCode
      ).id;
    },
    //身份证展示隐藏
    toggle() {
      if (!this.showType) {
        if (this.idCardDecoded) {
          this.editForm.idCard = this.idCardDecoded;
          this.showType = true;
          return;
        }
        if (this.editForm.staffCode.includes("SG")) {
          this.$api.information.employee
            .idCardDecodeStaff({ idCardRsa: this.editForm.idCardRsa })
            .then((res) => {
              this.editForm.idCard = res.data;
              this.idCardDecoded = data || "";
              this.showType = true;
            });
        } else {
          this.$api.information.employee.decrypt(this.editForm.idCardRsa).then((res) => {
            this.editForm.idCard = res.data;
            this.idCardDecoded = res.data || "";
            this.showType = true;
          });
        }
      } else {
        this.editForm.idCard = this.idCardOrigin;
        this.showType = false;
      }
    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      this.$refs.editForm.validate((valid) => {
        if (!valid) return;
        this.isLoading = true;
        let params = {
          id: this.formData.id,
          examAmount: this.editForm.examAmount,
          assignCode: this.editForm.assignCode,
        };
        this.$api.dataUpload.physicalExam.editSave(params).then(() => {
          this.$notify.warning({
            title: "成功",
            message: "编辑成功",
          });
          this.$emit("cancel", "confirm");
        }).finally(()=>{
          this.isLoading = false;
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
