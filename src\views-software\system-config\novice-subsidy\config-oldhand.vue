<template>
    <!-- 新手补贴参数配置 -->
    <content-panel class="panel-tabs">
      <el-tabs v-model="activeTab" class="tabs">
        <el-tab-pane name="standard" label="标准配置"> </el-tab-pane>
        <el-tab-pane name="parameter" label="参数设置"> </el-tab-pane>

      </el-tabs>
      <component :is="componentName"> </component>
    </content-panel>
  </template>

  <script>
  import standard from "./standard/standard";
  import parameter from "./parameter/parameter";
  export default {
    name: "SoftwareConfinovicesubsidy",
    components: {
        standard,
        parameter,
    },
    data() {
      return {
        showTab: false,
        activeTab: "standard",
        componentName: "",
        tabList: Object.freeze([
          {
            name: "standard",
            component: standard,
          },
          {
            name: "parameter",
            component: parameter,
          },

        ]),
        routerInfo: {},
      };
    },
    watch: {
      activeTab: {
        handler(value) {
          this.componentName = this.tabList.find(
            (item) => item.name === value
          ).component;
        },
        immediate: true,
      },
    },
    methods: {
    }
  };
  </script>

  <style lang="stylus" scoped>
  .panel-tabs {
    >>> .main-area {
      padding-top: 0;
    }
  }

  .tabs {
    >>> .el-tabs__header {
      margin-bottom: 5px;
    }
  }
  </style>
