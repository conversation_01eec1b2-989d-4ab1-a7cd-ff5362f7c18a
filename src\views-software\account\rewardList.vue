<template>
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          size="mini"
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          label-position="right"
          label-width="98px"
        >
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="员工姓名"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input
              v-model.trim="searchForm.staffCode"
              size="mini"
              clearable
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenStaffCodes"
                  titleName="厂牌编号"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="奖惩类型:" prop="type">
            <el-radio-group v-model="searchForm.type" @change="onSearch">
              <el-radio label="0">全部 </el-radio>
              <el-radio label="1">奖励 </el-radio>
              <el-radio label="2">惩罚 </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary"  @click="onSearch">
            查询</el-button
          >
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置</el-button
          >
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-left>
        <ul>
            <li><span>核算工厂:</span><span>{{ statisticsInfo.factoryName }}</span></li>
            <li><span>核算月份:</span><span>{{ statisticsInfo.accountingMonth }}</span></li>
            <li><span>人数:</span><span>{{ Info.peopleTotal }}</span></li>
            <li><span>奖励:</span><span>{{ Info.reward | moneyFormat }}</span></li>
            <li><span>处罚:</span><span>{{ Info.punishment | moneyFormat }}</span></li>
          </ul>
      </template>
      <template v-slot:header-right>
          <el-button
            size="small"
            type="primary"
            @click="handleExport"
          >
            导出
          </el-button>
      </template>
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :height="maxTableHeight"
        :data="tableData"
      >
        <el-table-column prop="code" label="奖惩编号" width="150" align="left">
        </el-table-column>
        <el-table-column prop="type" label="奖惩类型" width="80" align="left">
        </el-table-column>
        <el-table-column prop="staffName" label="姓名" width="150" align="left">
        </el-table-column>
        <el-table-column prop="staffCode" label="厂牌编号" width="120" align="left">
        </el-table-column>
        <el-table-column
          prop="amount"
          label="实际奖惩金额"
          width="150"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            {{ row.amount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          prop="processMonth"
          label="实际执行月份"
          width="120"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column prop="rewardDate" label="奖惩日期" width="100" align="left">
          <template slot-scope="{ row }">
            {{ row.rewardDate | shortDate }}
          </template>
        </el-table-column>
        <el-table-column prop="hasAttend" label="是否有考勤" width="100" align="left">
          <template slot-scope="{ row }">
           <span :style="{'color':row.hasAttend=='否'?'red':''}"> {{row.hasAttend  || ""}}</span>
          </template>
        </el-table-column>
        <el-table-column label="奖惩状态" width="150" align="left">
          <template slot-scope="{ row }">
           <!-- <span :style="{'color':row.deductStatus=='异常'?'red':''}"> {{
              row.deductStatus  || ""
            }}</span> -->
            <span :style="{'color':row.isPay=='未完成'?'red':''}"> {{row.isPay  || ""}}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="reason"
          label="奖惩原因"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="fileNo"
          label="文件编号"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="comments"
          label="备注说明"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div style="text-align: right">
          <el-pagination
            @size-change="onSizeChange"
            @current-change="onNumChange"
            :current-page="pageNum"
            :page-size="pageSize"
            :total="total"
            :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper"
          >
          </el-pagination>
        </div>
      </template>
    </table-panel>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
export default {
  name: "SoftwareRewardList",
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      searchForm: {
        type: "0",
        staffCode: "",
        staffName: "",
      },
      tableData: [],
      loading: false,
      resizeOffset: 55,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      title: "",
      filterParam: {},
      params: {},
      statisticsInfo: {},
      Info: {},
    };
  },
  watch: {
    $route: {
      handler(value) {
        if (value.path.includes("reward")&&value.path.includes("software")) {
          this.statisticsInfo = JSON.parse(this.$Base64.decode(value.query.data));
          this.factoryId = this.statisticsInfo.factoryId;
          this.getList();
          this.getStatisticsList();
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    //获取奖惩台账列表
    getList() {
      this.loading = true;
      this.$api.softwareInformation.punishment
        .getPunishmentList({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: {
            factoryId: this.factoryId,
            accountingMonth: this.statisticsInfo.accountingMonth,
            ...this.filterParam,
            type:
              this.searchForm.type == "0" ? "" : String(Number(this.searchForm.type) - 1),
            ...this.params,
          },
        })
        .then((res) => {
          if (res.code === 200) {
            this.tableData = res.data.list || [];
            this.total = res.data.total;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //获取统计
    getStatisticsList() {
      this.$api.softwareInformation.punishment
        .rewardsStatistics({
          accountingMonth: this.statisticsInfo.accountingMonth,
          factoryId: this.factoryId,
          ...this.filterParam,
          type:
            this.searchForm.type == "0" ? "" : String(Number(this.searchForm.type) - 1),
          ...this.params,
        })
        .then((res) => {
          if (res.code === 200) {
            console.log("res.data :>> ", res.data);
            this.Info = res.data || {
              peopleTotal: 0,
              punishment: 0,
              reward: 0,
            };
          }
        });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
      this.getStatisticsList();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
      //导出
    handleExport() {
      const {factoryId,accountingMonth} = JSON.parse(this.$Base64.decode(this.$route.query.data))
      let params = {
         factoryId,
         accountingMonth,
      }
      if (Object.keys(this.filterParam).length) {
        params = {
          ...params,
          ...this.filterParam,
        };
      }
      this.$api.common
        .doExport("softwareExportRewardPunishment", { ...params, ...this.params })
        .then((res) => {
            this.$message.success("导出操作成功，请前往导出记录查看详情");
        });
    },
    handleCancel() {
      this.visible = false;
    },
    onSizeChange(pageSize) {
      this.pageSize = pageSize;
      // 切换每页条数后从第一页查询
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    cancel(value) {
      this.ImportVisible = value;
    },
    confirm(value) {
      this.ImportVisible = value;
    },
  },
};
</script>

<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}

>>>.el-tabs__nav-wrap::after {
  height: 0;
}

ul {
  list-style: none;
  display: flex;
  align-items: center;
  padding: 0;
  margin: 0
  li{
    margin-right: 10px;
  }
}

i {
  font-style: normal;
}
</style>
