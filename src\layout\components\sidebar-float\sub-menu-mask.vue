<template>
  <div class="sub-menu-mask" :class="{'is-collapsed': isCollapsed}"></div>
</template>

<script>
export default {
  props: {
    isCollapsed: Boolean
  },
  mounted() {
    // 挂载到body下，解决z-index问题
    document.body.appendChild(this.$el);
  },
}
</script>

<style lang="stylus" scoped>
.sub-menu-mask
  position fixed
  background rgba(0,0,0,0.2)
  left 150px
  top 30px
  right 0
  bottom 0
  // 比浮动按钮低一级
  z-index 9998
.sub-menu-mask.is-collapsed
  left 54px
</style>