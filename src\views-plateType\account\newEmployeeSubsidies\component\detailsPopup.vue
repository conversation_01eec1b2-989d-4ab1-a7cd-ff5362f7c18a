<template>
  <qDialog
    :visible="visible"
    :innerScroll="true"
    :innerHeight="420"
    title="查看详情"
    width="850px"
    :isLoading="isLoading"
    :showFooter="false"
    :before-close="handleCancel"
  >
    <el-form
      :model="addForm"
      ref="addForm"
      label-width="120px"
      :rules="rules"
      size="small"
    >
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item class="processCode" label="报名日期:">
            {{ addForm.startDate }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="processCode" label="应聘人员:">
            {{ addForm.staffName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item class="processCode" label="厂牌编号:">
            {{ addForm.staffCode }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item
            class="processCode"
            label="应聘人身份证号:"
            label-width="134px"
          >
            {{ addForm.idCard }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="processCode" label="应聘岗位:">
            {{ addForm.post }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item class="processCode" label="应聘组织:">
            {{ addForm.orgAbbr }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="processCode" label="现工序:">
            {{ addForm.curProcess }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item class="processCode" label="现子工序:">
            {{ addForm.curSubProcess }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="processCode" label="现班组:">
            {{ addForm.curShiftGroup }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item
            class="processCode"
            label="应聘组织全路径:"
            label-width="134px"
          >
            {{ addForm.orgPath }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item class="processCode" label="现部门全路径:">
            {{ addForm.curOrgPath }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item
            class="processCode"
            label="补贴类型:"
          >
            {{ addForm.subsidyType }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            class="processCode"
            label="熟练程度:"
          >
            {{ addForm.masteryLevel }}
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </qDialog>
</template>

<script>
export default {
  name: "detailsPopup",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    formData: Object,
  },
  data() {
    return {
      addForm: {},
      isLoading: false,
      rules: {},
    };
  },
  created() {
    const {
      orgAbbr,
      id,
      startDate,
      staffName,
      staffCode,
      idCard,
      post,
      curProcess,
      curSubProcess,
      curShiftGroup,
      orgPath,
      curOrgPath,
      subsidyType,
      masteryLevel,
    } = this.formData;
    this.addForm = {
      orgAbbr,
      id,
      startDate,
      staffName,
      staffCode,
      idCard,
      post,
      curProcess,
      curSubProcess,
      curShiftGroup,
      orgPath,
      curOrgPath,
      subsidyType,
      masteryLevel,
    };
  },
  methods: {
    handleCancel() {
      this.$emit("cancel");
    },
  },
};
</script>

<style lang="stylus" scoped>
.processCode {
  >>>.el-form-item__label {
    &::before {
      content: '*';
      color: #F23D20;
      margin-right: 4px;
      visibility: hidden;
    }
  }
}
</style>
