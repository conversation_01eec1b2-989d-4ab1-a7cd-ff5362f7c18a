<template>
  <qDialog
    :visible="visible"
    :title="title"
    :innerScroll="false"
    width="600px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <el-form ref="addForm" :rules="rules" :model="addForm" label-width="100px">
      <el-form-item label="厂牌编号:" v-show="title == '新增'" prop="staffCode">
        <el-input clearable v-model.trim="addForm.staffCode">
          <template slot="append">
            <el-button type="primary" @click="searchEmployee('staffCode')">
              查询</el-button
            >
          </template>
        </el-input>
        <span class="error_info" v-show="isShowCode">{{ message }}</span>
      </el-form-item>
      <el-form-item label="厂牌编号:" v-show="title == '编辑'">{{
        addForm.staffCode
      }}</el-form-item>
      <el-form-item label="身份证号:" v-show="title == '新增'" prop="idCard">
        <el-input
          type="text"
          v-model.trim="addForm.idCard"
          clearable
          maxlength="18"
          show-word-limit
        >
          <template slot="append">
            <el-button type="primary" @click="searchEmployee('idCard')"> 查询</el-button>
          </template>
        </el-input>
        <span class="error_info" v-show="isShowIdCard">{{ message }}</span>
      </el-form-item>
      <el-form-item label="身份证号:" v-show="title == '编辑'">
        <template>
          <span style="margin-right: 10px">{{ addForm.idCard }}</span>
          <i
            v-if="addForm.idCard"
            :class="['iconfont', showType ? 'icon-guanbi' : ' icon-yincangmima']"
            @click="toggle"
          ></i>
        </template>
      </el-form-item>
      <el-form-item label="员工姓名:">
        {{ addForm.staffName }}
      </el-form-item>
      <el-form-item label="核算月份:">
        {{ addForm.accountingMonth }}
      </el-form-item>
      <el-form-item label="分配厂牌:" prop="assignStaffCode">
        <el-radio-group v-model="addForm.assignStaffCode">
          <el-radio v-for="item in codeList" :label="item" :key="item">{{
            item
          }}</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="社保扣款:" prop="socialSecurityDeduct">
        <el-input
          oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
          v-model="addForm.socialSecurityDeduct"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="20元保险:">
          <ul class="deductionFactory">
            <li v-for="item in deductionFactory" :key="item.name">
             <span>{{ item.factoryName }}</span>
              <el-switch
                  v-model="item.isDeduct"
                  :active-text="item.isDeduct == 'Y' ? '扣款' : '不扣款'"
                  active-value="Y"
                  inactive-value="N"
                  :disabled="!item.editFlag"
                  @change="onSwitch"
                >
                </el-switch>
            </li>
            </ul>
      </el-form-item>
      <el-form-item label="保险状态:">
         <span :style="{ color: addForm.isUnionFees != 'abnormal' ? '#0BB78E' : 'red' }">
          {{ addForm.isUnionFees == "abnormal" ? "异常" : "正常" }}</span
        >
      </el-form-item>
      <el-form-item label="备注说明:" prop="comments">
        <el-input
          type="textarea"
          v-model="addForm.comments"
          rows="3"
          maxlength="300"
          show-word-limit
          resize="none"
        >
        </el-input>
      </el-form-item>
    </el-form>
  </qDialog>
</template>

<script>
export default {
  name: "addDialog",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    editForm: Object,
  },
  data() {
    return {
      addForm: {
        staffCode: "",
        idCard: "",
        staffName: "",
        accountingMonth: JSON.parse(this.$Base64.decode(this.$route.query.data)).accountingMonth,
        socialSecurityDeduct: "",
        isInsurance: false,
        isUnionFees:true,
        comments: "",
        assignStaffCode: "",
        idCardRsa: "",
      },
      rules: {
        staffCode: [
          {
            required: true,
            message: "员工编号不能为空",
            trigger: "blur",
          },
        ],
        idCard: [
          {
            required: true,
            message: "身份证号不能为空",
            trigger: "blur",
          },
        ],
        assignStaffCode: [
          {
            required: true,
            message: "分配厂牌不能为空",
            trigger: "change",
          },
        ],
        socialSecurityDeduct: [
          {
            validator: (rule, value, callback) => {
              if (!/^-?\d{1,5}(\.\d{1,2})?$/.test(value)) {
                return callback(new Error("小数点前面仅支持5位数,小数点后面仅支持2位数"));
              }
              callback();
            },
            trigger: "blur",
          },
        ],
      },
      codeList: [],
      idCardDecoded: "",
      showType: false,
      idCardOrigin: "",
      isShowCode: false,
      isShowIdCard: false,
      message: "",
      idCardMd5: "",
      idCard: "",
      isLoading:false,
      deductionFactory:[],
      isShowSwitch:[]
    };
  },
  created() {
    if (this.title == "编辑") this.getDetails();
  },
  watch:{
    'addForm.idCard':{
      handler(value){
          !value && (this.isShowIdCard = false)
      }
    },
    'addForm.staffCode':{
       handler(value){
         !value && (this.isShowCode = false)
      }
    }
  },
  methods: {
    //查询员工信息
    searchEmployee(name) {
      this.$refs.addForm.validateField(name, (valid) => {
        if (valid) {
          name == "staffCode" ? (this.isShowCode = false) : (this.isShowIdCard = false);
          return;
        }
        const { factoryId, accountingMonth } = JSON.parse(this.$Base64.decode(this.$route.query.data));
        let params = {
          factoryId,
          accountingMonth,
          [name]: this.addForm[name],
        };
        if (name == "idCard") {
          this.idCard = this.addForm.idCard;
          params.idCard = this.idCard;
          this.$nextTick(() => {
            this.$refs.addForm.clearValidate();
          });
        }
        this.$api.plateTypeDataUpload.employeeAttendance.queryStaff({moduleId:"3",...params}).then((res) => {
          let data = res.data || [];
          if (data.length == 0) {
            this.message = "该人员无考勤数据！";
            name == "staffCode" ? (this.isShowCode = true) : (this.isShowIdCard = true);
            this.$nextTick(() => {
              this.$refs.addForm.clearValidate();
            });
            return
          } else {
            this.isShowCode = false;
            this.isShowIdCard = false;
          }
          this.codeList = data.staffCodes || [];
          this.idCardMd5 = data.idCardMd5 || "";
          this.twenties()
          this.addForm = {
            ...this.addForm,
            staffCode: (name == "idCard" ? "" : data.staffCode) || "",
            staffName: data.staffName || "",
            idCard: (name == "idCard" ? this.idCard : data.idCard) || "",
            assignStaffCode:
              name != "idCard"
                ? this.codeList.length == 1
                  ? this.codeList[0]
                  : data.staffCode
                : "",
          };
        });
      });
    },
    //20元社保扣款明细
    twenties(){
      const {factoryId,accountingMonth,factoryName} = JSON.parse(this.$Base64.decode(this.$route.query.data))
      let params = {
        idCardMd5: this.idCardMd5,
        accountingMonth
      }
      this.$api.plateTypeDataUpload.SocialSecurity.twenties(params).then(({data})=>{
        if(!data.length){
           this.deductionFactory = [{factoryId,id:'',factoryName,isDeduct:'Y',editFlag:true}]
        }else{
          this.deductionFactory = data.find(item=>item.factoryId == factoryId) ? data : [{factoryId,id:'',factoryName,isDeduct:'N',editFlag:true},...data]
        }
        this.onSwitch()
      })
    },
    //社保扣款详情
    async getDetails() {
      const { data } = await this.$api.plateTypeDataUpload.SocialSecurity.viewEdit({
        id: this.editForm.id,
      });
      this.idCardOrigin = data.idCard || "";
      this.codeList = data.staffCodes || [];
      this.deductionFactory = data.twenties || []
      this.idCardMd5 = data.idCardMd5 || "";
      this.addForm = {
        ...this.addForm,
        staffCode: data.staffCode || "",
        idCard: data.idCard || "",
        staffName: data.staffName || "",
        socialSecurityDeduct: data.socialSecurityDeduct || "",
        comments: data.comments || "",
        assignStaffCode:
          (this.codeList.length == 1 ? this.codeList[0] : data.staffCode) || "",
        idCardRsa: data.idCardRsa || "",
      };
      this.onSwitch()
    },
    //身份证展示/隐藏
    toggle() {
      if (!this.showType) {
        if (this.idCardDecoded) {
          this.addForm.idCard = this.idCardDecoded;
          this.showType = true;
          return;
        }
        if (this.addForm.staffCode.includes("SG")) {
          this.$api.information.employee
            .idCardDecodeStaff({ idCardRsa: this.addForm.idCardRsa })
            .then((res) => {
              this.addForm.idCard = res.data;
              this.idCardDecoded = res.data;
              this.showType = true;
            });
        } else {
          this.$api.information.employee.decrypt(this.addForm.idCardRsa).then((res) => {
            this.addForm.idCard = res.data;
            this.idCardDecoded = res.data;
            this.showType = true;
          });
        }
      } else {
        this.addForm.idCard = this.idCardOrigin;
        this.showType = false;
      }
    },
    onSwitch() {
      this.isShowSwitch = this.deductionFactory
        .filter((item) => item.isDeduct == "Y")
        .map((item) => item.isDeduct);
      this.addForm.isUnionFees = this.isShowSwitch.length > 1 ? "abnormal" : "normal";
    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      const { factoryId, accountingMonth } = JSON.parse(this.$Base64.decode(this.$route.query.data));
      this.onSwitch()
      if (!this.isShowSwitch.length && this.addForm.socialSecurityDeduct == '') {
        this.$message.warning("社保扣款和20元保险任选一个");
        return;
      }
      if (!this.addForm.assignStaffCode) {
        this.$message.warning("请选择分配厂牌");
        return;
      }
      this.isLoading = true;
       let params = {
          factoryId,
          accountingMonth,
          staffCode: this.addForm.staffCode,
          socialSecurityDeduct: this.addForm.socialSecurityDeduct,
          comments: this.addForm.comments || "",
          assignStaffCode: this.addForm.assignStaffCode,
          idCardMd5: this.idCardMd5,
          twenties:this.deductionFactory || []
        };
      if (this.title == "新增") {
        this.$api.plateTypeDataUpload.SocialSecurity.getSocialSecuritySaveOrUpdate(params).then(
          (res) => {
            this.$notify.success({
              title: "成功",
              message: "新增成功",
            });
            this.$emit("cancel", "confirm");
          }).finally(()=>{
            this.isLoading = false;
          });
      } else {
        this.$api.plateTypeDataUpload.SocialSecurity.getSocialSecuritySaveOrEdit({id: this.editForm.id,...params}).then(
          (res) => {
            this.$notify.success({
              title: "成功",
              message: "编辑成功",
            });
            this.$emit("cancel", "confirm");
          }).finally(()=>{
            this.isLoading = false;
          });
      }
    },
  },
};
</script>

<style lang="stylus" scoped>
>>>.el-form-item__label:before{
    display: none
}
>>>.el-input-group__append {
  background: #24c69a;
  color: #fff;

  .el-button {
    padding: 5px 25px;
  }
}
.error_info{
    color: #F23D20;
    font-size: 12px;
    line-height: 1;
    padding-top: 4px;
    position: absolute;
    top: 100%;
    left: 0;
}
ul {
  list-style: none;
  display: flex;
  padding: 0;
}

.deductionFactory {
  flex-direction: column;
  margin: 0;

  >li {
    span {
      margin-right: 15px;
    }
  }
}
</style>
