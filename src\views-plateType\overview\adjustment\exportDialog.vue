<template>
  <qDialog :visible="visible"
    title="调整前工资表"
    :innerScroll="false"
    :innerHeight="500"
    :showFooter="false"
    width="600px"
    @cancel="handleCancel"
    :before-close="handleCancel">
    <el-table stripe border
      v-loading="loading"
      highlight-current-row
      :height="300"
      :data="tableData">
      <el-table-column
        align="center"
        type="index"
        label="序号" width="60">
      </el-table-column>
      <el-table-column
        label="提交时间"
        align="center"
        show-overflow-tooltip>
        <template
          slot-scope="{ row }">
          {{ row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column
        prop="createUser"
        label="提交人"
        align="center"
        show-overflow-tooltip>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="80">
        <template
          slot-scope="{ row }">
          <el-button
            type="text"
            @click="hanleExport(row)">导出</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div
      style="text-align: right">
      <el-pagination
        background small
        layout="total,prev, pager, next"
        :total="total"
        :current-page="pageNum"
        @current-change="onNumChange">
      </el-pagination>
    </div>
  </qDialog>
</template>

<script>
export default {
  name: "exportDialog",
  props: {
    visible: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      pageNum: 1,
      total: 0
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true;
      const { factoryId, accountingMonth } = JSON.parse(this.$Base64.decode(this.$route.query.data))
      let params = {
        pageSize: 10,
        pageNum: this.pageNum,
        filterData: {
          factoryId,
          accountingMonth
        }
      }
      this.$api.plateTypeWorkbench.beforeSalary(params).then(({ list, total }) => {
        this.tableData = list || [];
        this.total = total || 0;
      }).finally(() => {
        this.loading = false;
      })
    },
    //导出
    hanleExport({ serialNumber}) {
      const { factoryId, accountingMonth } = JSON.parse(this.$Base64.decode(this.$route.query.data))
      this.$api.plateTypeWorkbench
        .factoryEditSalaryExport({
          factoryId,
          accountingMonth,
          type:0,
          serialNumber
        })
        .then((res) => {
          this.$message.success("导出操作成功，请前往导出记录查看详情");
        });
    },
    onNumChange(val) {
      this.pageNum = val;
      this.getList();
    },
    handleCancel() {
      this.$emit('cancel', 'cancel')
    }
  }
}
</script>

<style lang="scss" scoped>
</style>