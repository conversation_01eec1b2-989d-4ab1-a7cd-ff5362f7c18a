<template>
  <qDialog :visible="isVisible" :innerScroll="true" :innerHeight="430" title="新增" width="850px" :isLoading="isLoading" @cancel="handleCancel"
    @confirm="handleConfirm" :before-close="handleCancel">
    <el-form :model="addForm" ref="addForm" label-width="120px" :rules="rules" size="small">
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item class="processCode" label="奖惩编号:">
            {{ addForm.code }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="责任分厂:" prop="factoryId">
            <el-select v-model="addForm.factoryId" filterable clearable placeholder="请选择责任分厂" @change="changeFactoryId">
              <el-option v-for="item in tabList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="厂牌编号:" prop="staffCode" class="staffCode">
            <el-input v-model.trim="addForm.staffCode" clearable placeholder="请输入厂牌编号">
              <template slot="append">
                <el-button type="primary" @click="searchStaffCode"> 查询 </el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="processCode" label="员工姓名:">
            {{ addForm.staffName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="级别:" class="processCode"> {{addForm.iopWorkGrade}}</el-form-item>
        </el-col>
        <el-col :span="12"></el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="执行年月:" prop="accountingMonth">
            <el-date-picker v-model="addForm.accountingMonth" type="month" :picker-options="pickerOptions" placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="奖惩日期:" prop="rewardDate">
            <el-date-picker v-model="addForm.rewardDate" type="date" placeholder="选择奖惩日期">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="奖惩类型:" prop="type" class="processCode">
            <el-select v-model="addForm.type" filterable clearable placeholder="请选择奖惩类型">
              <el-option v-for="item in payrollOptions" :key="item.value" :label="item.name" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="奖惩金额:" prop="amount">
            <el-input :validate-event="false" oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}" v-model="addForm.amount" clearable
              @blur="onBlur('amount')" placeholder="请输入奖惩金额">
              <template slot="append">元</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="实际奖惩金额:" prop="actualAmount">
            <el-input :validate-event="false" oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}" v-model="addForm.actualAmount"
              clearable @blur="onBlur('actualAmount')" placeholder="请输入实际奖惩金额">
              <template slot="append">元</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label=""></el-form-item>
        </el-col>
      </el-row>
      <!-- 奖惩详情 -->
      <el-row>
        <el-col :span="24">
          <el-form-item label="奖惩详情" class="processCode">
            <span style="color: #afafaf; line-height: 40px">
              <span style="padding-right: 110px">实际核算工厂</span>
              <span style="padding-right: 105px">实际执行年月</span>
              <span>实际奖惩金额</span>
            </span>
            <div class="details" v-for="(item, index) in detailList" :key="item.date">
              <div class="details_content">
                <el-select v-model="item.factoryId" placeholder="请选择工厂名称" filterable>
                  <el-option v-for="item in tabList" :key="item.name" :label="item.label" :value="item.id">
                  </el-option>
                </el-select>
                <el-date-picker style="margin: 0 15px" v-model="item.accountingMonth" type="month" placeholder="选择日期" :picker-options="pickerOptionsDetails"
                  @change="seleteDate(item)">
                </el-date-picker>
                <el-input class="repaymentAmount" v-model.trim="item.repaymentAmount" disabled placeholder=""
                  oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}" @blur="onStageBlur(index, item)">
                </el-input>
              </div>
              <div class="details_btn">
                <el-button type="text" @click="stages(index)"> 分期 </el-button>
                <el-button v-show="detailList.length > 1 && index != 0" type="text" @click="handleDelete(item, index)">
                  删除
                </el-button>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item label="文件编号:" prop="fileNo" class="processCode">
            <el-input v-model="addForm.fileNo" clearable maxlength="45" show-word-limit placeholder="请输入文件编号">
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item class="processCode" label="奖惩原因:" prop="reason">
            <el-input v-model="addForm.reason" clearable show-word-limit maxlength="45">
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item class="processCode" label="备注:" prop="comments">
            <el-input clearable v-model="addForm.comments" show-word-limit maxlength="45">
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </qDialog>
</template>

<script>
import moment from "moment";
import { moneyDelete } from "@/utils";
import NP from "number-precision";
NP.enableBoundaryChecking(false);
export default {
  name: "addDialog",
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      addForm: {
        code: "",
        factoryId: "",
        staffCode: "",
        staffName: "",
        accountingMonth: moment().subtract(1, "months").format("YYYY-MM"),
        rewardDate: "",
        actualFactoryId: "",
        actualMonth: "",
        actualAmount: "",
        type: "1",
        amount: "",
        reason: "",
        comments: "",
      },
      detailList: [{ factoryId: "", accountingMonth: moment().subtract(1, "months").format("YYYY-MM"), repaymentAmount: "" }],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      pickerOptionsDetails: {
        disabledDate: (time) => {
          const date = new Date(time);
          const year = date.getFullYear();
          const month = date.getMonth() + 1;
          const checkYear = year + "-" + (month < 10 ? "0" + month : month);
          const disabledMonths = this.detailList.map((item) => {
            return item.accountingMonth;
          }); // 要禁用的月份数组
          return disabledMonths.includes(checkYear);
        },
      },
      rules: {
        factoryId: [{ required: true, message: "请选择责任分厂", trigger: "change" }],
        actualFactoryId: [
          { required: true, message: "请选择实际责任分厂", trigger: "change" },
        ],
        staffCode: [{ required: true, message: "请输入厂牌编号", trigger: "blur" }],
        accountingMonth: [
          { required: true, message: "请选择执行年月", trigger: "change" },
        ],
        actualMonth: [
          { required: true, message: "请选择实际执行年月", trigger: "change" },
        ],
        rewardDate: [{ required: true, message: "请选择奖惩日期", trigger: "change" }],
        amount: [
          { required: true, message: "请输入奖惩金额", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (!/^-?\d{1,5}(\.\d{1,2})?$/.test(moneyDelete(value))) {
                callback(new Error("小数点前面仅支持5位数,小数点后面仅支持2位数"));
              }
              callback();
            },
            trigger: "blur",
          },
        ],
        actualAmount: [
          { required: true, message: "请输入实际奖惩金额", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (!/^-?\d{1,5}(\.\d{1,2})?$/.test(moneyDelete(value))) {
                callback(new Error("小数点前面仅支持5位数,小数点后面仅支持2位数"));
              }
              callback();
            },
            trigger: "blur",
          },
        ],
      },
      //奖惩类型
      payrollOptions: Object.freeze([
        {
          name: "奖励",
          value: "0",
        },
        {
          name: "处罚",
          value: "1",
        },
      ]),
      tabList: [],
      isLoading: false,
      num: 1,
    };
  },
  created() {
    this.$api.plateTypeSystemManage.getBasicPermission.getQuFactory({moduleId:3}).then((res) => {
      this.tabList =
        res.data.map((item) => ({
          label: item.name,
          name: item.name,
          id: item.id,
        })) || [];
    });
  },
  watch: {
    "addForm.factoryId": {
      handler(value) {
        this.addForm.actualFactoryId = value;
      },
    },
    "addForm.accountingMonth": {
      handler(value) {
        this.addForm.actualMonth = value;
      },
      immediate: true,
    },
  },
  methods: {
    changeFactoryId(val) {
      // let factoryId = this.tabList.find((item) => item.name == val).id;
      this.detailList[0].factoryId = this.detailList[0].factoryId ? this.detailList[0].factoryId :val
    },
    addOneMonth(yearMonth) {
      var date = new Date(yearMonth);
      date.setMonth(date.getMonth() + 1);
      var newYearMonth = date.toISOString().substring(0, 7);
      return newYearMonth;
    },
    //分期
    stages(index) {

      if (!this.addForm.factoryId && !this.detailList[0].factoryId) {
        this.$message.error("请先选择核算分厂");
        return;
      }
      if (!this.addForm.actualAmount) {
        this.$message.error("请先输入实际奖惩金额");
        return;
      }

      if (this.detailList.length > 5) {
        this.$message.error("最多只能分6期");
        return;
      }
      let arr = JSON.parse(JSON.stringify(this.detailList));
      let lastIndex = arr.length - 1;
      this.num++;
      this.detailList.push({
        accountingMonth: this.addOneMonth(this.detailList[lastIndex].accountingMonth),
        repaymentAmount: this.toFixedTwo(this.addForm.actualAmount / this.num),
        factoryId: this.addForm.factoryId,
      });

      let detailList = this.detailList.map((item, index) => {
        return {
          ...item,
          repaymentAmount: this.toFixedTwo(this.addForm.actualAmount / this.num),
        };
      });
      this.detailList = detailList.map((item, index) => {
        if (Number(moneyDelete(this.addForm.actualAmount)) % this.num != 0) {
          if (index === 0) {
            let count = 0
            for (let i = 1; i < detailList.length; i++) {
              count += Number(detailList[i].repaymentAmount)
            }
            item.repaymentAmount = this.returnFloat(NP.minus(this.addForm.actualAmount ,count))
          }
        }
        return item;
      });
      this.isBlur = false;
    },
    toFixedTwo(num) {
      if (typeof num !== 'number' || Number.isNaN(num)) return num;
      let resultNum = Math.floor(num * 100) / 100
      return this.returnFloat(resultNum)
    },
    returnFloat(num) {
      num = num.toString(); // 转成字符串类型  如能确定类型 这步可省去
      if (num.indexOf(".") !== -1) {
        let [integerPart, decimalPart] = num.split(".");

        if (decimalPart.length > 2) {
          decimalPart = decimalPart.substring(0, 2);
        } else if (decimalPart.length === 1) {
          decimalPart += "0";
        }

        num = `${integerPart}.${decimalPart}`;
      } else {
        num += ".00";
      }
      return num;
    },
    //统计
    total(arr) {
      let total = arr.reduce(function (pre, cur, index, arr) {
        if (index === 0) {
          return pre + 0;
        }
        return NP.plus(pre, Number(moneyDelete(cur.repaymentAmount)));
      }, 0);
      return total;
    },
    handleDelete(v, value) {
      this.num--;

      this.detailList.splice(value, 1);
      if (this.detailList.length > 0) {
        let detailList = this.detailList.map((item, index) => {
          return {
            ...item,
            repaymentAmount: this.toFixedTwo(this.addForm.actualAmount / this.num),
          };
        });
        this.detailList = detailList.map((item, index) => {
          if (Number(moneyDelete(this.addForm.actualAmount)) % this.num != 0) {
            if (index === 0) {
              let count = 0
              for (let i = 1; i < detailList.length; i++) {
                count += Number(detailList[i].repaymentAmount)
              }
              item.repaymentAmount = this.returnFloat(NP.minus(this.addForm.actualAmount ,count))
            }
          }
          return item;
        });
      }
      if (this.detailList.length == 1) {
        this.num = 1;
      }
    },
    //校验金额
    checkAmount(value, name = "") {
      console.log("name", name);
      let flag = false;
      let amount = moneyDelete(value);
      if (!amount) {
        this.$message({
          message: "金额不能为空",
          type: "warning",
        });
        flag = true;
      }
      if (!flag) {
        if (!/^-?\d{1,5}(\.\d{1,2})?$/.test(amount)) {
          this.$message({
            message: "小数点前面仅支持5位数,小数点后面仅支持2位数",
            type: "warning",
          });
          flag = true;
          if (name) this.addForm[name] = this.addForm.actualAmount;
        }
      }
      console.log('flag', flag)
      return flag;
    },
    onStageBlur(num, it) {
      if (this.checkAmount(it.repaymentAmount)) return;
      let detailList = this.detailList.map((item, index) => {
        item.repaymentAmount = moneyDelete(item.repaymentAmount);
        if (num === index) {
          item.repaymentAmount = item.repaymentAmount;
        }
        return item;
      });
      let total = detailList.reduce(function (pre, cur, index, arr) {
        if (index === 0) {
          return pre + 0;
        }
        return NP.plus(pre, Number(moneyDelete(cur.repaymentAmount)));
      }, 0);
      this.detailList = this.detailList.map((item, index) => {
        if (index == 0) {
          let repaymentAmount = (item.repaymentAmount =
            NP.minus(Number(moneyDelete(this.addForm.actualAmount)), total)
          );
          if (Number(moneyDelete(repaymentAmount)) < 0) {
            this.$message.error("操作错误,奖惩金额配置异常,请核查后重试!");
          } else {
          }
        }
        return {
          ...item,
          repaymentAmount: moneyDelete(item.repaymentAmount),
        };
      });
    },
    //选择月份
    seleteDate(item) {
      item.accountingMonth = moment(item.accountingMonth).format("YYYY-MM");
    },
    //根据厂牌编号查询
    searchStaffCode() {
      this.$refs.addForm.validateField(["staffCode"], async (valid) => {
        if (valid) return;
        await this.$api.information.employee
          .employeeDetails({ staffCode: this.addForm.staffCode })
          .then(({ data }) => {
            this.addForm = {
              ...this.addForm,
              staffName: data && data.staffName,
            };
          });
      });
    },
    //校验金额
    onBlur(name) {
      this.addForm[name] = moneyDelete(this.addForm[name]);
      this.$refs.addForm.validateField([name], (valid) => {
        if (valid) {
          this.addForm[name] = "";
          return;
        }
        this.addForm = {
          ...this.addForm,
          [name]: this.addForm[name],
        };
      });
      if(name == 'actualAmount'){
        this.num = 1
        this.detailList = [{ factoryId: this.addForm.factoryId, accountingMonth:moment().subtract(1, "months").format("YYYY-MM"), repaymentAmount: this.addForm.actualAmount  }]
      }
    },
    handleCancel() {
      this.$emit("cancel", {
        type: "cancel",
        isVisible: false,
      });
    },
    async handleConfirm() {
      this.$refs.addForm.validate((valid) => {
        if (!valid) return;
        if (!this.addForm.staffName) {
          this.$message.warning("请先输入厂牌编号查询责任人");
          return;
        }
        this.isLoading = true
        // let actualFactoryId = this.tabList.find(
        //   (item) => item.name == this.addForm.actualFactoryId
        // ).id;
        // let factoryId = this.tabList.find((item) => item.name == this.addForm.factoryId)
        //   .id;
        let params = {
          ...this.addForm,
          // actualFactoryId,
          // factoryId,
          accountingMonth: moment(this.addForm.accountingMonth).format("YYYY-MM"),
          actualMonth: moment(this.addForm.actualMonth).format("YYYY-MM"),
          rewardDate: moment(this.addForm.rewardDate).format("YYYY-MM-DD"),
          amount: moneyDelete(this.addForm.amount),
          actualAmount: moneyDelete(this.addForm.actualAmount),
          repaymentList:this.detailList
        };
        delete params.code;
        this.$api.plateTypeInformation.rewardLedger.addReward(params).then((data) => {
          this.$notify.success({
            title: "成功",
            message: "新增成功",
          });
          this.$emit("cancel", {
            type: "confirm",
            isVisible: false,
          });
        }).finally(() => {
          this.isLoading = false;
        });
      });
    },
  },
};
</script>

<style lang="stylus" scoped>
>>>.el-form-item__label {
  text-align: left;
}

.processCode {
  >>>.el-form-item__label {
    &::before {
      content: '*';
      color: #F23D20;
      margin-right: 4px;
      visibility: hidden;
    }
  }
}

.staffCode {
  >>>.el-input-group__append {
    background: #24c69a;
    color: #fff;

    .el-button {
      padding: 5px 15px;
    }
  }
}

>>>.el-select, >>>.el-date-editor {
  width: 100%;
}

.el-upload__tip {
  padding-left: 10px;
  display: inline-block;
  color: #24c69a;
}

>>>.el-textarea__inner {
  padding-right: 50px;
}

.el-textarea {
  >>>.el-input__count {
    right: 20px !important;
  }
}

.details {
  display: flex;
  padding-bottom: 18px;

  &_content {
    flex: 1;
    display: flex;
    justify-content: space-between;

    >.el-select, >.el-input, >span {
      flex: 1 0 30%;
    }
  }

  &_btn {
    width: 20%;

    >.el-button {
      mar;
    }
  }
}

/deep/.el-input__inner {
  background: white !important;
}
</style>
