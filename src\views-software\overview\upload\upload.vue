<template>
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          size="mini"
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          label-position="right"
        >
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="员工姓名"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input
              v-model.trim="searchForm.staffCode"
              size="mini"
              clearable
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenStaffCodes"
                  titleName="厂牌编号"
                />
              </template>
            </el-input>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary"  @click="onSearch">
            查询</el-button
          >
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置</el-button
          >
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-left>
        <ul>
            <li>
              <span>核算工厂:</span><span>{{ info.factoryName }}</span>
            </li>
            <li>
              <span>核算月份:</span><span>{{ info.accountingMonth }}</span>
            </li>
            <li>
              <span>人数:</span><span>{{ wageStatisticInfo.peopleCount }}</span>
            </li>
            <li>
              <span>个税扣款:</span
              ><span>{{ wageStatisticInfo.iiTaxTotal | moneyFormat }}</span>
            </li>
        </ul>
      </template>
      <template v-slot:header-right>
        <div ref="btnRight">
          <el-button
            size="small"
            type="primary"
            @click="handleImport"
            v-show="permission"
          >
            导入</el-button
          >
          <el-button size="small" type="primary" @click="handleAdd" v-show="permission">
            新增</el-button
          >
          <el-button
            :loading='isLoading'
            size="small"
            type="primary"
            @click="handleSubmit"
            v-show="permission"
          >
            提交</el-button
          >
        </div>
      </template>
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :height="maxTableHeight"
        style="width: 100%"
        :data="tableData"
      >
        <el-table-column
          label="姓名"
          prop="staffName"
          width="80"
          align="left"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="厂牌编号"
          prop="staffCode"
          width="120"
          align="left"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="身份证号"
          prop="idCard"
          width="180"
          align="left"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="核算月份"
          prop="accountingMonth"
          width="100"
          align="left"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="个税扣款"
          prop="deductAmount"
          width="150"
          align="left"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="备注说明"
          prop="comments"
          align="left"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column label="分配厂牌" width="80" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span :style="{ color: row.deductStatus == 'normal' ? '' : 'red' }">{{
              (row.deductStatus && (row.deductStatus == "normal" ? "正常" : "异常")) || ""
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="修改时间" width="150" align="left" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ row.updateTime | dateFormat }}
          </template>
        </el-table-column>
        <el-table-column align="left" width="150">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="handleEdit(scope.$index, scope.row)"
              v-show="permission"
            >
              编辑</el-button
            >
            <el-button
              type="text"
              size="small"
              @click="handleDelete(scope.$index, scope.row)"
              v-show="permission"
            >
              删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
          <div style="text-align: right">
            <el-pagination
              @size-change="onSizeChange"
              @current-change="onNumChange"
              :current-page="pageNum"
              :page-size="pageSize"
              :total="total"
              :page-sizes="[50, 100, 200]"
              layout="total, sizes, prev, pager, next, jumper"
            >
            </el-pagination>
          </div>
      </template>
    </table-panel>
    <add-dialog
      v-if="visible"
      :isVisible="visible"
      :title="title"
      :editForm="editForm"
      @cancel="handleCancel"
    ></add-dialog>
    <Import
      v-if="ImportVisible"
      :visible="ImportVisible"
      @cancel="cancel"
      @confirm="confirm"
      :importInfo="importInfo"
    />
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import { moneyFormat, moneyDelete } from "@/utils";
import addDialog from "./addDialog.vue";
export default {
  name: "SoftwareUpload",
  components: { addDialog },
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      searchForm: {
        content: "",
        belongFactoryId: "",
        staffCode: "",
        staffName: "",
      },
      editForm: {},
      title: "",
      statusOptions: Object.freeze([
        { label: "未上传", id: "0" },
        { label: "待提交", id: "1" },
        { label: "已完成", id: "2" },
        { label: "退回", id: "3" },
      ]),
      columnList: [],
      tableData: [],
      factoryId: "",
      visible: false,
      loading: "",
      filterParam: {},
      params: {},
      resizeOffset: 55,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      permission: "",
      info: {},
      ImportVisible: false,
      importInfo: {},
      wageStatisticInfo: {},
      isLoading:false,
    };
  },
  watch: {
    $route: {
      handler(value) {
        if (value.path.includes("upload")&&value.path.includes("software")) {
          this.info = JSON.parse(this.$Base64.decode(value.query.data));
          this.factoryId = this.info.factoryId;
          this.importInfo = {
            reportName: "softwareIndividualincometaximport",
            paramMap: {
              columnValue: "软体-个税扣款",
              factoryId: this.factoryId,
              accountingMonth: this.info.accountingMonth,
              id: this.info.id,
            },
          };
          this.getList();
          this.getDebitList();
          this.getPermission();
        }
      },
      immediate: true,
      deep: true,
    },
  },
  computed: {
    tableColumn() {
      this.columnList = this.columnList.map((item) => ({
        ...item,
        isShow: item.type ? this.permission : true,
      }));
      return this.columnList.filter((item) => item.isShow);
    },
  },
  methods: {
    //获取个税扣款列表
    getList() {
      this.loading = true;
      this.$api.softwareWorkbench
        .getIndividualTaxList({
          pageSize: this.pageSize,
          pageNum: this.pageNum,
          filterData: {
            factoryId: this.factoryId,
            accountingMonth: this.info.accountingMonth,
            ...this.filterParam,
            ...this.params,
          },
        })
        .then((res) => {
          if (res.code === 200) {
            this.tableData =
              res.data.list.map((item, index) => ({
                ...item,
                deductAmount: moneyFormat(item.deductAmount),
              })) || [];
            this.total = res.data.total;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //是否具有总览任务操作权限
    getPermission() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.info.accountingMonth,
        name: "INCOME_TAX",
      };
      this.$api.softwareWorkbench.overviewPermission(params).then((res) => {
        this.permission = res.data;
      });
    },
    //获取个税扣款统计
    getDebitList() {
      let params = {
        accountingMonth: this.info.accountingMonth,
        factoryId: this.factoryId,
        ...this.filterParam,
        ...this.params,
      };
      this.$api.softwareWorkbench.IndividualTaxStatistics(params).then((res) => {
        this.wageStatisticInfo = res.data
          ? res.data
          : {
              iiTaxTotal: 0,
              peopleCount: 0,
            };
      });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
      this.getDebitList();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    //编辑
    handleEdit(index, row) {
      this.title = "编辑";
      this.visible = true;
      this.editForm = { ...row };
    },
    //删除
    handleDelete(index, row) {
      this.$confirm("此操作将永久删除该条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$api.softwareWorkbench.deleteIndividualTax({ id: row.id }).then((res) => {
            if (res.code === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getList();
              this.getDebitList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    //新增
    handleAdd() {
      this.editForm = {};
      this.title = "新增";
      this.visible = true;
    },
    //编辑
    handleEdit(index, row) {
      this.title = "编辑";
      this.visible = true;
      this.editForm = {
        id: row.id || "",
        idCard: row.idCard || "",
        idCardRsa: row.idCardRsa || "",
      };
    },
    //导入
    handleImport() {
      this.title = "导入";
      this.ImportVisible = true;
    },
    //提交
    handleSubmit() {
      this.isLoading = true;
      this.$api.softwareWorkbench
        .nextNode({
          taskId: this.info.id,
        })
        .then((res) => {
            this.$message({
              type: "success",
              message: "提交成功!",
            });
            this.getPermission()
        }).finally(()=>{
          this.isLoading = false;
        });
    },
    handleCancel(type) {
      this.visible = false;
      if (type == "cancel") return;
      this.getList();
      this.getDebitList();
    },
    onSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(val) {
      this.pageNum = val;
      this.getList();
    },
    cancel(value) {
      this.ImportVisible = value;
    },
    confirm(value) {
      this.ImportVisible = value;
    },
  },
};
</script>

<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}
ul {
  list-style: none;
  display: flex;
  align-items: center;
  padding: 0;
  margin: 0
  li{
    margin-right: 10px;
  }
}
i {
  font-style: normal;
}
>>>.el-tabs__nav-wrap::after {
  display: block;
  height: 0;
}
</style>
