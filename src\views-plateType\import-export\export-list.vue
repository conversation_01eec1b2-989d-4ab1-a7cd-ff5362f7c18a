<template>
  <content-panel class="main">
    <template v-slot:search>
      <search-box class="search-box">
        <el-form :inline="true" :model="searchForm" ref="searchForm" size="mini" label-width="90px">
          <el-form-item label="导出日期:" prop="exportDate">
            <el-date-picker v-model="searchForm.exportDate" type="daterange" :default-time="['00:00:00', '23:59:59']" value-format="yyyy-MM-dd HH:mm:ss" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary" @click="onSearch">查询</el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">重置</el-button>
        </template>
      </search-box>
    </template>
    <table-panel class="tab-content">
      <export-task ref="exportTask"></export-task>
    </table-panel>
  </content-panel>
</template>

<script>
import { SUB_APP_CODE } from '@/api/api'
export default {
  name: 'PlateTypeExportFile',
  data() {
    return {
      searchForm: {
        exportDate: []
      }
    }
  },
  methods: {
    onSearch() {
      const formData = this.$refs['exportTask'].setSearchForm({
        startDate: this.searchForm.exportDate[0] || '',
        endDate: this.searchForm.exportDate[1] || ''
      });
     let data = { ...formData, appCode: SUB_APP_CODE }
      this.$refs['exportTask'].init(data);
    },
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.onSearch();
    }
  },
  mounted() {
    this.onSearch();
  }
}
</script>

<style lang="stylus" scoped>
.tab-content {
  >>> .table-panel-content {
    height: calc(100vh - 140px);
  }
}

.main {
  >>> .main-area {
    padding: 0;
  }
}

.input-date {
  width: 250px !important;
}
</style>
<style lang="stylus">
.table-panel-content {
  .root {
    .el-container {
      .page-header {
        display: none;
      }

      .table-main {
        padding: 0;
      }
    }

    .main-weapper {
      height: 95%;
    }

    .el-table {
      height: calc(100% - 40px) !important;
    }
  }
}
</style>