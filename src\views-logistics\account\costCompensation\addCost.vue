<template>
  <qDialog
    :visible="isVisible"
    :innerScroll="true"
    :innerHeight="450"
    title="新增"
    width="850px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <el-form
      :model="addForm"
      ref="addForm"
      label-width="120px"
      :rules="rules"
      size="small"
    >
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item class="processCode" label="流程编号:">
            {{ addForm.processCode }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="核算分厂:" prop="factoryId">
            <el-select
              v-model="addForm.factoryId"
              filterable
              clearable
              placeholder="请选择核算分厂"
              @change="changeFactoryId"
            >
              <el-option
                v-for="item in tabList"
                :key="item.value"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input
              v-model.trim="addForm.staffCode"
              clearable
              placeholder="请输入厂牌编号"
            >
              <template slot="append">
                <el-button type="primary" @click="searchStaffCode">
                  查询
                </el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="processCode" label="责任人:">
            {{ addForm.staffName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="执行年月:" prop="executeMonth">
            <el-date-picker
              v-model="addForm.executeMonth"
              type="month"
              :picker-options="pickerOptions"
              placeholder="选择日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="扣款类型:" prop="deductType">
            <el-select
              v-model="addForm.deductType"
              filterable
              clearable
              placeholder="请选择扣款类型"
            >
              <el-option
                v-for="item in payrollOptions"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>

      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item> </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="实际扣款金额:" prop="deductAmount">
            <el-input
              oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
              v-model="addForm.deductAmount"
              clearable
              @blur="onBlur"
              placeholder="请输入实际扣款金额"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item> </el-form-item>
        </el-col>
      </el-row>
      <!-- 扣款详情 -->
      <el-row>
        <el-col :span="24">
          <el-form-item label="扣款详情" class="processCode">
            <span style="color: #afafaf; line-height: 40px">
              <span style="padding-right: 110px">实际核算工厂</span>
              <span style="padding-right: 105px">实际执行年月</span>
              <span>实际扣款金额</span>
            </span>
            <div
              class="details"
              v-for="(item, index) in detailList"
              :key="item.date"
            >
              <div class="details_content">
                <el-select
                  v-model="item.factoryId"
                  placeholder="请选择工厂名称"
                  filterable
                >
                  <el-option
                    v-for="item in tabList"
                    :key="item.name"
                    :label="item.label"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
                <el-date-picker
                  style="margin: 0 15px"
                  v-model="item.accountingMonth"
                  type="month"
                  placeholder="选择日期"
                  :picker-options="pickerOptionsDetails"
                  @change="seleteDate(item)"
                >
                </el-date-picker>
                <el-input
                  class="repaymentAmount"
                  v-model.trim="item.repaymentAmount"
                  disabled
                  placeholder=""
                  oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
                  @blur="onStageBlur(index, item)"
                >
                </el-input>
              </div>
              <div class="details_btn">
                <el-button type="text" @click="stages(index)"> 分期 </el-button>
                <el-button
                  v-show="detailList.length > 1 && index != 0"
                  type="text"
                  @click="handleDelete(item, index)"
                >
                  删除
                </el-button>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item class="processCode" label="备注说明:" prop="remark">
            <el-input
              type="textarea"
              v-model="addForm.remark"
              resize="none"
              rows="3"
              show-word-limit
              maxlength="300"
              placeholder=""
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item class="processCode" label="上传凭证:" prop="proof">
            <el-upload
              class="upload"
              :http-request="upload"
              action="*"
              :limit="10"
              :before-upload="beforeUpload"
              :on-change="onFileChange"
              :auto-upload="true"
              :on-exceed="handleExceed"
              :on-remove="handleRemove"
              ref="upload"
            >
              <el-button size="small" type="primary"> 选择附件 </el-button>
              <div slot="tip" class="el-upload__tip">
                注:文件大小不超过3MB，单次最多上传10个文件
              </div>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </qDialog>
</template>

<script>
import moment from "moment";
import {  moneyDelete } from "@/utils";
import NP from "number-precision";
NP.enableBoundaryChecking(false);
export default {
  name: "addCost",
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      detailList: [ {factoryId:"",accountingMonth:moment().subtract(1, "months").format("YYYY-MM"),repaymentAmount:""}],
      addForm: {
        processCode: "",
        factoryId: "",
        staffCode: "",
        staffName: "",
        executeMonth: moment().subtract(1, "months").format("YYYY-MM"),

        deductType: "",
        deductAmount: "",
        remark: "",
      },
      newStaffCode: "",
      proof: [],
      fileList: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      pickerOptionsDetails: {
        disabledDate: (time) => {
          const date = new Date(time);
          const year = date.getFullYear();
          const month = date.getMonth() + 1;
          const checkYear = year + "-" + (month < 10 ? "0" + month : month);
          const disabledMonths = this.detailList.map((item) => {
            return item.accountingMonth;
          }); // 要禁用的月份数组
          return disabledMonths.includes(checkYear);
        },
      },
      rules: {
        factoryId: [
          { required: true, message: "请选择核算分厂", trigger: "change" },
        ],
        staffCode: [
          { required: true, message: "请输入厂牌编号", trigger: "blur" },
        ],
        executeMonth: [
          { required: true, message: "请选择执行年月", trigger: "change" },
        ],

        deductType: [
          { required: true, message: "请选择扣款类型", trigger: "change" },
        ],
        deductAmount: [
          { required: true, message: "请输入扣款金额", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (!/^-?\d{1,5}(\.\d{1,2})?$/.test(moneyDelete(value))) {
                callback(
                  new Error("小数点前面仅支持5位数,小数点后面仅支持2位数")
                );
              }
              callback();
            },
            trigger: "blur",
          },
        ],
      },
      //扣款类型
      payrollOptions: Object.freeze([
        {
          name: "成本赔偿",
          value: "1",
        },
        // {
        //   name: "返工扣款",
        //   value: "2",
        // },
        {
          name: "低耗品",
          value: "3",
        },
        // {
        //   name: "刀具",
        //   value: "4",
        // },
        {
          name: "未打卡扣款",
          value: "5",
        },
      ]),
      tabList: [],
      isLoading: false,
      num: 1,
    };
  },
  created() {
    this.$nextTick(() => {
      this.$api.logisticsInformation.rewardLedger
        .listLogisticsFactory()
        .then((res) => {
          this.tabList =
            res.data.map((item) => ({
              label: item.name,
              name: item.name,
              id: item.id,
            })) || [];
        });
    });
  },
  methods: {
    changeFactoryId(val){
      this.detailList[0].factoryId = this.detailList[0].factoryId ?this.detailList[0].factoryId:val
    },
    addOneMonth(yearMonth) {
    var date = new Date(yearMonth);
    date.setMonth(date.getMonth() + 1);
    var newYearMonth = date.toISOString().substring(0, 7);
    return newYearMonth;
    },
    //分期
    stages(index) {

      if(!this.addForm.factoryId && !this.detailList[0].factoryId){
        this.$message.error("请先选择核算分厂");
        return;
      }
      if(!this.addForm.deductAmount){
        this.$message.error("请先输入实际扣款金额");
        return;
      }
      if(!this.addForm.deductAmount){
        this.$message.error("请先输入实际扣款金额");
        return;
      }
      if (this.detailList.length > 5) {
        this.$message.error("最多只能分6期");
        return;
      }
      let arr = JSON.parse(JSON.stringify(this.detailList));
      let lastIndex = arr.length - 1;
      this.num++;
      this.detailList.push({
        accountingMonth: this.addOneMonth(this.detailList[lastIndex].accountingMonth),
        repaymentAmount: this.toFixedTwo(this.addForm.deductAmount / this.num),
        factoryId: this.addForm.factoryId,
      });

      let detailList = this.detailList.map((item, index) => {
        return {
          ...item,
          repaymentAmount:this.toFixedTwo(this.addForm.deductAmount / this.num),
        };
      });
      this.detailList = detailList.map((item, index) => {
        if (Number(moneyDelete(this.addForm.deductAmount)) % this.num != 0) {
          if (index === 0) {
            let count = 0
            for (let i = 1; i < detailList.length; i++) {
              count += Number(detailList[i].repaymentAmount)
            }
            item.repaymentAmount = this.returnFloat(NP.minus(this.addForm.deductAmount , count))
          }
        }
        return item;
      });
      this.isBlur = false;
    },
    toFixedTwo(num) {
      if (typeof num !== 'number' || Number.isNaN(num)) return num;
      let resultNum = Math.floor(num * 100) / 100
      return this.returnFloat(resultNum)
    },
    returnFloat(num) {
      num = num.toString(); // 转成字符串类型  如能确定类型 这步可省去
      if (num.indexOf(".") !== -1) {
        let [integerPart, decimalPart] = num.split(".");

        if (decimalPart.length > 2) {
          decimalPart = decimalPart.substring(0, 2);
        } else if (decimalPart.length === 1) {
          decimalPart += "0";
        }

        num = `${integerPart}.${decimalPart}`;
      } else {
        num += ".00";
      }
      return num;
    },
        //统计
    total(arr) {
      let total = arr.reduce(function (pre, cur, index, arr) {
        if (index === 0) {
          return pre + 0;
        }
        return NP.plus(pre, Number(moneyDelete(cur.repaymentAmount)));
      }, 0);
      return total;
    },
    handleDelete(v, value) {
      this.num--;

      this.detailList.splice(value, 1);
      if (this.detailList.length > 0) {
        let detailList = this.detailList.map((item, index) => {
          return {
            ...item,
            repaymentAmount: this.toFixedTwo(this.addForm.deductAmount / this.num),
          };
        });
        this.detailList = detailList.map((item, index) => {
          if (Number(moneyDelete(this.addForm.deductAmount)) % this.num != 0) {
            if (index === 0) {
              let count = 0
              for (let i = 1; i < detailList.length; i++) {
                count += Number(detailList[i].repaymentAmount)
              }
              item.repaymentAmount = this.returnFloat(NP.minus(this.addForm.deductAmount , count))
            }
          }
          return item;
        });
      }
      if (this.detailList.length == 1) {
        this.num = 1;
      }
    },
    //校验金额
    checkAmount(value, name = "") {
      console.log("name", name);
      let flag = false;
      let amount = moneyDelete(value);
      if (!amount) {
        this.$message({
          message: "金额不能为空",
          type: "warning",
        });
        flag = true;
      }
      if (!flag) {
        if (!/^-?\d{1,5}(\.\d{1,2})?$/.test(amount)) {
          this.$message({
            message: "小数点前面仅支持5位数,小数点后面仅支持2位数",
            type: "warning",
          });
          flag = true;
          if (name) this.addForm[name] = this.addForm.deductAmount;
        }
      }
      console.log('flag',flag)
      return flag;
    },
    onStageBlur(num, it) {
      if (this.checkAmount(it.repaymentAmount)) return;
      let detailList = this.detailList.map((item, index) => {
        item.repaymentAmount = moneyDelete(item.repaymentAmount);
        if (num === index) {
          item.repaymentAmount = item.repaymentAmount;
        }
        return item;
      });
      let total = detailList.reduce(function (pre, cur, index, arr) {
        if (index === 0) {
          return pre + 0;
        }
        return NP.plus(pre, Number(moneyDelete(cur.repaymentAmount)));
      }, 0);
      this.detailList = this.detailList.map((item, index) => {
        if (index == 0) {
          let repaymentAmount = (item.repaymentAmount =
            NP.minus(Number(moneyDelete(this.addForm.deductAmount)), total)
          );
          if (Number(moneyDelete(repaymentAmount)) < 0) {
            this.$message.error("操作错误,分期金额配置异常,请核查后重试!");
          } else {
          }
        }
        return {
          ...item,
          repaymentAmount: moneyDelete(item.repaymentAmount),
        };
      });
    },
    //选择月份
    seleteDate(item) {
      item.accountingMonth = moment(item.accountingMonth).format("YYYY-MM");
    },
    //根据厂牌编号查询
    searchStaffCode() {
      this.$refs.addForm.validateField(["staffCode"], async (valid) => {
        if (valid) return;
        await this.$api.logisticsInformation.employee
          .employeeDetails({ staffCode: this.addForm.staffCode })
          .then(({ data }) => {
            this.newStaffCode = data.staffCode || "";
            this.addForm = {
              ...this.addForm,
              staffName: (data && data.staffName) || "",
            };
          });
      });
    },
    onBlur() {
      this.addForm.deductAmount = moneyDelete(this.addForm.deductAmount);
      this.$refs.addForm.validateField(["deductAmount"], (valid) => {
        if (!valid) {
          this.addForm = {
            ...this.addForm,
            deductAmount: this.addForm.deductAmount,
          };
        }
      });
      this.num = 1
      this.detailList=[ {factoryId:this.addForm.factoryId,accountingMonth: moment().subtract(1, "months").format("YYYY-MM"),repaymentAmount:this.addForm.deductAmount}]
    },
    async handleRemove(file, fileList) {
      let findPath = this.fileList.find(
        (item) => item.proofName === file.name
      ).proofUrl;
      try {
        await this.$api.common.deleteUploadFile(JSON.stringify([findPath]));
      } catch (error) {
        this.$notify.error({
          message: error,
        });
      }
    },
    beforeUpload(file) {
      const isLt3M = file.size / 1024 / 1024 < 3;
      if (!isLt3M) {
        this.$message.error("上传文件大小不能超过 3MB!");
        return false;
      }
      return true;
    },
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 10 个文件，本次选择了 ${files.length} 个文件，共选择了 ${
          files.length + fileList.length
        } 个文件`
      );
    },
    onFileChange(file, fileList) {},
    async upload({ file }) {
      try {
        const {
          data: { fileUrl },
        } = await this.$api.common.appUploadFile(file);
        this.fileList.push({
          proofName: file.name,
          proofUrl: fileUrl,
        });
      } catch (error) {
        this.$notify.error({
          message: error,
        });
      }
    },
    handleCancel() {
      const filePath = this.fileList.map((item) => item.proofUrl);
      if (filePath.length > 0) {
        this.$api.common.deleteUploadFile(JSON.stringify(filePath));
      }
      this.$emit("cancel", {
        type: "cancel",
        isVisible: false,
      });
    },
    async handleConfirm() {
      this.$refs.addForm.validate((valid) => {
        if (!valid) return;
        if (!this.addForm.staffName) {
          this.$message.warning("请先输入厂牌编号查询责任人");
          return;
        }
        this.isLoading = true;
        let params = {
          processCode: "手动添加",
          ...this.addForm,
          staffCode: this.newStaffCode,
          executeMonth: moment(this.addForm.executeMonth).format("YYYY-MM"),

          deductAmount: Number(moneyDelete(this.addForm.deductAmount)),
          proofs: this.fileList,
          repaymentList:this.detailList
        };
        this.$api.logisticsInformation.costCompensation
          .saveDeduct(params)
          .then((data) => {
            this.$notify.success({
              title: "成功",
              message: "新增成功",
            });
            this.$emit("cancel", {
              type: "confirm",
              isVisible: false,
            });
          })
          .finally(() => {
            this.isLoading = false;
          });
      });
    },
  },
};
</script>

<style lang="stylus" scoped>
>>>.el-form-item__label {
  text-align: left;
}

.processCode {
  >>>.el-form-item__label {
    &::before {
      content: '*';
      color: #F23D20;
      margin-right: 4px;
      visibility: hidden;
    }
  }
}

>>>.el-input-group__append {
  background: #24c69a;
  color: #fff;

  .el-button {
    padding: 5px 10px;
  }
}

>>>.el-select, >>>.el-date-editor {
  width: 100%;
}

.el-upload__tip {
  padding-left: 10px;
  display: inline-block;
  color: #24c69a;
}

>>>.el-textarea__inner {
  padding-right: 50px;
}

.el-textarea {
  >>>.el-input__count {
    right: 20px !important;
  }
}
.details {
  display: flex;
  padding-bottom: 18px;

  &_content {
    flex: 1;
    display: flex;
    justify-content: space-between;

    >.el-select, >.el-input, >span {
      flex: 1 0 30%;
    }
  }

  &_btn {
    width: 20%;

    >.el-button {
      mar;
    }
  }
}

/deep/.el-input__inner{
  background:white !important
}

</style>
