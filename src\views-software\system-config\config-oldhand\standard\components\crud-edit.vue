<template>
  <content-panel>
    <qDialog
      :visible="visible"
      :innerScroll="false"
      :innerHeight="500"
      :title="title"
      width="500px"
      :isLoading="isLoading"
      @cancel="handleCancel"
      @confirm="handleConfirm"
      :before-close="handleCancel"
    >
      <el-form
        :model="addForm"
        :rules="rules"
        ref="addForm"
        size="small"
        label-width="173px"
      >
        <el-row :gutter="10">

          <el-col :span="24">
            <el-form-item label="核算工厂:" prop="factoryId">
              <el-select
                v-model="addForm.factoryId"
                filterable
                clearable
                style="width: 100%"
                placeholder="请选择核算工厂"
                @change="changeFactoryId"
              >
                <el-option
                  v-for="item in factoryList"
                  :key="item.value"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="核算班组(工序):" prop="groupNames" :show-message="!addForm.groupNames">
              <el-input
                disabled
                clearable
                placeholder="请选择核算班组"
                v-model="addForm.groupNames"
                type="text"
              >
                <template slot="append">
                  <el-button @click="selectlist">选择</el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="熟手程度:" prop="grade">
              <el-select
                v-model="addForm.grade"
                filterable
                clearable
                style="width: 100%"
                placeholder="请选择熟手程度"
              >
                <el-option
                  v-for="item in Proficiency"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="24">
            <span style="margin-left: 48px">补贴标准:</span>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item
              label="出勤满30天:"
              prop="firstAmount"
              style="margin-left: 90px"
            >
              <el-input
                clearable
                placeholder="请输入金额"
                v-model="addForm.firstAmount"
                type="number"
                class="no-spin-buttons"
              >
                <template slot="append">元</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item
              label="出勤满90天:"
              prop="secondAmount"
              style="margin-left: 90px"
            >
              <el-input
                clearable
                placeholder="请输入金额"
                v-model="addForm.secondAmount"
                type="number"
                class="no-spin-buttons"
              >
                <template slot="append">元</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item
              label="出勤满180天:"
              prop="thirdAmount"
              style="margin-left: 90px"
            >
              <el-input
                clearable
                placeholder="请输入金额"
                v-model="addForm.thirdAmount"
                type="number"
                class="no-spin-buttons"
              >
                <template slot="append">元</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item class="assignedFactory" label="备注:" prop="remarks">
              <el-input
                type="textarea"
                v-model="addForm.remarks"
                resize="none"
                rows="3"
                maxlength="100"
                show-word-limit
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </qDialog>
    <add-Data
      v-if="isvisible"
      :visible.sync="isvisible"
      :processesList="processesList"
      :checkData="addForm.groupIds"
      @confirmGroupIds="confirmGroupIds"
    />
  </content-panel>
</template>
<script>
import { assignValue } from "@/utils";
import addData from "./add-data.vue";
export default {
  components: { addData },
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    modifyData: {
      type: Object,
      default: {},
    },
    editForm: Object,
    modifyNum: Number,
  },
  computed: {
    title() {
      return this.modifyNum == 1
        ? "新增"
        : this.modifyNum == 2
        ? "编辑"
        : "复制";
    },
    processesList() {
      if (this.factoryList.length && this.addForm.factoryId) {
        return this.factoryList.find((v) => v.id == this.addForm.factoryId)
          .process;
      }
    },
  },
  data() {
    return {
      addForm: {
        factoryId: "",
        groupIds: [],
        grade: "",
        firstAmount: "",
        secondAmount: "",
        thirdAmount: "",
        remarks: "",
        groupNames: "",
        id: "",
      },
      rules: {
        factoryId: [
          { required: true, message: "核算工厂不能为空", trigger: "change" },
        ],
        groupNames: [
          { required: true, message: "核算班组不能为空", trigger: "blur" },
        ],
        grade: [
          { required: true, message: "熟手程度不能为空", trigger: "change" },
        ],
        firstAmount: [
          {
            required: true,
            pattern: /^(0|[0-9]{1,5})((\.\d{0,2})*)$/,
            message: "小数点前面仅支持5位数,小数点后面仅支持2位数",
            trigger: "blur",
          },
        ],
        secondAmount: [
          {
            required: true,
            pattern: /^(0|[0-9]{1,5})((\.\d{0,2})*)$/,
            message: "小数点前面仅支持5位数,小数点后面仅支持2位数",
            trigger: "blur",
          },
        ],
        thirdAmount: [
          {
            required: true,
            pattern: /^(0|[0-9]{1,5})((\.\d{0,2})*)$/,
            message: "小数点前面仅支持5位数,小数点后面仅支持2位数",
            trigger: "blur",
          },
        ],
      },
      factoryList: [],

      isLoading: false,
      isvisible: false,
      Proficiency: [
        { label: "熟手A", value: "熟手A" },
        { label: "熟手B", value: "熟手B" },
        { label: "标准", value: "标准" },
      ],
    };
  },
  created() {
    this.getFactoryList();
    this.init();
  },
  methods: {
    getFactoryList() {
      this.$api.softwareSystemManage.getBasicPermission
      .getQuFactory({ moduleId: 4 })
        .then((res) => {
          this.factoryList = res.data || [];
        });
    },
    changeFactoryId() {
      this.addForm.groupNames = "";
      this.addForm.groupIds = [];

    },
    init() {
      if (this.modifyData) {
        this.initModifyData();
      }
    },
    initModifyData() {
      if (this.modifyNum == 3) {
        //复制不需要id
        this.modifyData.id = "";
      }
      assignValue(this.addForm, this.modifyData);
    },

    //选择工序
    selectlist() {
      if (!this.addForm.factoryId) {
        this.$message.error("请先选择核算工厂");
        return;
      }
      this.isvisible = true;
    },
    confirmGroupIds(data) {
      console.log("data", data);
      let names = data.map((v) => {
        return v.name;
      });
      this.addForm.groupNames = names.join(",");
      this.addForm.groupIds = data.map((v) => {
        return v.id;
      });
    },
    handleConfirm() {
      this.$refs.addForm.validate((valid) => {
        if (!valid) return;
        this.isLoading = true;
        let params = {
          ...this.addForm,
          taskType: "SKILLED_SUBSIDY",
        };
        // modifyNum:1新增 2编辑 3复制
        let fullApi =
          this.modifyNum == 1 || this.modifyNum == 3
            ? "oldhandAdd"
            : "oldhandedit";
        this.$api.softwareSystemManage.getBasicPermission[fullApi](params)
          .then(() => {
            this.$notify({
              title: "成功",
              message: this.title + "成功",
              type: "success",
            });
            this.$emit("cancel", "confirm");
          })
          .finally(() => {
            this.isLoading = false;
          });
      });
    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
  },
};
</script>

<style lang="stylus" scoped>
.staffCode {
  >>>.el-input-group__append {
    background: #24c69a;
    color: #fff;

    .el-button {
      padding: 5px 20px;
    }
  }
}

.el-row {
  &::before, &::after {
    display: none;
  }

  display: flex;
  justify-content: space-between;
}

.el-form-item {
  display: flex;

  >>>.el-form-item__label {
    text-align: right;

  }

  >>>.el-form-item__content {
    width: 100%;
    margin-left: 0 !important;
  }
}

.assignedFactory {
  >>>.el-form-item__label {
    &::before {
      content: '*';
      color: #F23D20;
      margin-right: 4px;
      visibility: hidden;
    }
  }
}

>>>.el-date-editor {
  width: 100%;
}
.illustrate{
  color:#FF8900;
  padding:10px;
  margin-left:15px

}
>>>.el-input-group__append button.el-button{
  color: #fff;
  background-color: #0bb78e;
  border: 4px solid #0bb78e;
}
>>>.el-input.is-disabled .el-input__inner{
  background-color: #fff;
}
::v-deep .no-spin-buttons input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

::v-deep .no-spin-buttons input::-webkit-outer-spin-button {
  -webkit-appearance: none !important;
}

::v-deep .no-spin-buttons input[type="number"] {
  -moz-appearance: textfield !important;
}
</style>
