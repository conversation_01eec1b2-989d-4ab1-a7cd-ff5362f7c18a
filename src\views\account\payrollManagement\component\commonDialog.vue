<template>
  <qDialog
    :visible="commonVisible"
    :innerScroll="innerScroll"
    :innerHeight="height"
    :showFooter="showFooter"
    :isShowCancelBtn="isShowCancelBtn"
    :title="commonTitle"
    :width="width"
    :modal-append-to-body="false"
    append-to-body
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel">
    <div
      v-if="commonTitle=='打印预览'">
      <div class="btn">
        <el-button
          size="small"
          type="primary"
          @click="handlePrint">
          打印
        </el-button>
      </div>
      <section>
        <div class="header">
          <div
            class="header_top">
            <div>
              <span>工资单类型:</span>
              <span>{{name}}</span>
            </div>
            <h3
              style="letter-spacing:10px">
              员工工资表</h3>
          </div>
          <div
            class="header_bottom">
            <div class="item">
              <span>工厂名称:</span>
              <span>{{printInfo.factoryName}}</span>
            </div>
            <div class="item">
              <span>核算班组:</span>
              <span>{{printInfo.processName}}</span>
            </div>
            <div class="item">
              <span>员工姓名:</span>
              <span>{{printInfo.staffName}}</span>
            </div>
            <div class="item">
              <span>厂牌编号:</span>
              <span>{{printInfo.staffCode}}</span>
            </div>
            <div class="item">
              <span>单据编号:</span>
              <span>{{printInfo.no}}</span>
            </div>
          </div>
        </div>
        <el-table stripe
          border
          ref="tableRef"
          :height="140"
          :data="tableData">
          <el-table-column
            prop="accountingMonth"
            label="结算月份"
            width="95"
            align="left"
            show-overflow-tooltip>
          </el-table-column>
          <el-table-column
            prop="number"
            label="出勤天数"
            width="75"
            align="left"
            show-overflow-tooltip>
            <template
              slot-scope="{row}">
              {{row.attendances.settlementAmount}}
            </template>
          </el-table-column>
          <el-table-column
            label="工资总额"
            width="80"
            align="left"
            show-overflow-tooltip>
            <template
              slot-scope="{row}">
              <span
                v-if="row.salary.settlementAmount=='0.00'">-</span>
              <span v-else>
                {{row.salary.settlementAmount|moneyFormat}}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="其他扣款"
            width="80"
            align="left"
            show-overflow-tooltip>
            <template
              slot-scope="{row}">
              <span
                v-if="row.otherDeduct.settlementAmount=='0.00'">-</span>
              <span v-else>
                {{row.otherDeduct.settlementAmount|moneyFormat}}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="厂服扣款"
            width="80"
            align="left"
            show-overflow-tooltip>
            <template
              slot-scope="{row}">
              <span
                v-if="row.uniformDeduct.settlementAmount=='0.00'">-</span>
              <span v-else>
                {{row.uniformDeduct.settlementAmount|moneyFormat}}</span>

            </template>
          </el-table-column>
          <el-table-column
            label="生活费"
            width="75"
            align="left"
            show-overflow-tooltip>
            <template
              slot-scope="{row}">
              <span
                v-if="row.living.settlementAmount=='0.00'">-</span>
              <span v-else>
                {{row.living.settlementAmount|moneyFormat}}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="保险"
            width="70"
            align="left"
            show-overflow-tooltip>
            <template
              slot-scope="{row}">
              <span
                v-if="row.insurance.settlementAmount=='0.00'">-</span>
              <span v-else>
                {{row.insurance.settlementAmount|moneyFormat}}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="工会"
            width="70"
            align="left"
            show-overflow-tooltip>
            <template
              slot-scope="{row}">
              <span
                v-if="row.labour.settlementAmount=='0.00'">-</span>
              <span v-else>
                {{row.labour.settlementAmount|moneyFormat}}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="借支扣款"
            width="80"
            align="left"
            show-overflow-tooltip>
            <template
              slot-scope="{row}">
              <span
                v-if="row.loan.settlementAmount=='0.00'">-</span>
              <span v-else>
                {{row.loan.settlementAmount|moneyFormat}}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="自离扣款"
            width="80"
            align="left"
            show-overflow-tooltip>
            <template
              slot-scope="{row}">
              <span
                v-if="!row.leaveDeduct">-</span>
              <span v-else>
                {{row.leaveDeduct.settlementAmount|moneyFormat}}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="扣款合计"
            width="80"
            align="left"
            show-overflow-tooltip>
            <template
              slot-scope="{row}">
              <span
                v-if="row.totalDeduct.settlementAmount=='0.00'">-</span>
              <span v-else>
                {{row.totalDeduct.settlementAmount|moneyFormat}}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="实发工资"
            align="left"
            width="80"
            show-overflow-tooltip>
            <template
              slot-scope="{row}">
              <span
                v-if="row.actualSalary.settlementAmount=='0.00'">-</span>
              <span v-else>
                {{row.actualSalary.settlementAmount|moneyFormat}}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="员工签名">
          </el-table-column>
        </el-table>
        <div
          class="table-footer">
          <div
            id="table_footer">
            <div
              class="footer_left">
              <span>合计人民币（小写）:</span>
              <span>{{smallNum==0?'-':smallNum}}</span>
            </div>
            <div
              class="footer_left">
              <span>合计人民币（大写）:</span>
              <span>{{smallNum==0?'-':bigNum}}</span>
            </div>
          </div>
          <div
            class="form_content">
            <span
              style="display:block;padding:10px 0 ">备注说明:1.工资总额包含（计件工资、生活补贴、奖励、返工工资、住房补贴等）
              2.其他扣款包含（处罚、成本赔偿、低耗扣款、返工扣款等）</span>
            <span
              style="line-height:30px">{{printInfo.remark}}</span>
          </div>
          <div
            class="header_bottom"
            style="margin-top:10px">
            <div class="item">
              <span>制表人员:</span>
              <span>{{printInfo.tableName}}</span>
            </div>
            <div class="item">
              <span>审核人员:</span>
              <span></span>
            </div>
            <div class="item">
              <span>办理日期:</span>
              <span>{{printInfo.handleTime |shortDate}}</span>
            </div>

          </div>
        </div>
        <div class="footer">
          <div class="item">
            <span>打印次数:</span>
            <span>{{printInfo.printCount}}</span>
          </div>
          <div class="item">
            <span>页码:</span>
            <span>{{size}}</span>
          </div>
        </div>
      </section>
      <div
        style="text-align: right">
        <el-pagination
          @current-change="onNumChange"
          :current-page="pageNum"
          :page-size="page"
          :total="total"
          layout="total, prev, pager, next, jumper">
        </el-pagination>
      </div>
    </div>
    <div
      v-if="commonTitle=='批量提交'">
      是否批量提交？
    </div>
    <div
      v-if="commonTitle=='批量审核'">
      是否批量审核并打印？
    </div>
    <div
      v-if="commonTitle=='作废'">
      <el-form
        ref="cancelForm"
        :model="cancelForm"
        :rules="cancelRules"
        label-width="150px">
        <el-form-item
          class="cancelForm"
          label="再次输入,确认作废:"
          prop="cancel">
          <el-input
            v-model="cancelForm.cancel"
            placeholder="作废">
          </el-input>
        </el-form-item>
      </el-form>
    </div>
    <div
      v-if="commonTitle=='提交'">
      是否提交？
    </div>
    <div
      v-if="commonTitle=='通过'">
      是否通过？
    </div>
    <div
      v-if="commonTitle=='退回'">
      <el-form
        ref="reasonForm"
        :model="reasonForm"
        :rules="reasonRules"
        label-width="100px">
        <el-form-item
          label="退回原因:"
          prop="reason">
          <el-input
            style="width:100%"
            type="textarea"
            resize="none"
            rows="3"
            maxlength="300"
            show-word-limit
            v-model="reasonForm.reason"
            placeholder="请输入退回原因">
          </el-input>
        </el-form-item>
      </el-form>
    </div>
    <div
      v-if="commonTitle=='退回原因'"
      class="reason">
      <div class="item">
        <span>退回人员:</span>
        <span>{{reasonInfo.backName}}</span>
      </div>
      <div class="item">
        <span>退回原因:</span>
        <span>{{reasonInfo.backReason}}</span>
      </div>
    </div>
  </qDialog>
</template>

<script>
import { moneyFormat, moneyDelete } from "@/utils";
import { getSum, dealBigMoney } from '../common'
import { formatNumber } from '@/utils'
import { soket } from './soket'
export default {
  props: {
    commonVisible: {
      type: Boolean,
      required: true
    },

    commonTitle: {
      type: String,
      required: true
    },
    info: [Object, Array]
  },
  data() {
    return {
      width: "",
      height: 0,
      page: 4,
      size: "",
      pageNum: 1,
      total: 0,
      showFooter: false,
      innerScroll: false,
      isShowCancelBtn: false,
      printInfo: {},
      tableData: [],
      tableList: [],
      //工资条类型
      payrollOptions: Object.freeze([
        {
          name: "辞职工资",
          value: "1",
        },
        {
          name: "自离工资",
          value: "2",
        },
        {
          name: "延发工资",
          value: "3",
        },
        {
          name: "其他工资",
          value: "4",
        },
      ]),
      reasonRules: {
        reason: { required: true, message: '退回原因不能为空', trigger: 'blur' }
      },
      cancelRules: {
        cancel: [{ required: true, message: '请输入作废', trigger: 'blur' },
        {
          validator: (rule, value, callback) => {
            if (value != '作废') {
              callback(new Error('输入内容只能为作废字段'))
            }
            callback()
          }, trigger: 'blur'
        },
        ]
      },
      smallNum: '',
      bigNum: '',
      reasonForm: {
        reason: '',
      },
      cancelForm: {
        cancel: ""
      },
      pageSize: "",
      name: "",
      reasonInfo: {
        backName: "",
        backReason: '',
      },
      isLoading:false,
    }
  },
  watch: {
    commonTitle: {
      handler(value) {
        switch (value) {
          case '打印预览':
            this.width = '1000px'
            this.height = 500
            this.showFooter = false
            this.innerScroll = true
            this.getDetail()
            break;
          case '批量提交':
          case '批量审核':
          case '作废':
          case '提交':
          case '退回':
          case '通过':
            this.width = '400px'
            this.height = 300
            this.showFooter = true
            this.isShowCancelBtn = true
            this.innerScroll = false
            break;
          case '退回原因':
            this.width = '400px'
            this.height = 300
            this.showFooter = true
            this.isShowCancelBtn = false
            this.innerScroll = false
            break;
          default:
            this.width = '900px'
            this.row = 8
            this.height = 400
            break;
        }
      },
      immediate: true
    },
    tableData: {
      handler(value) {
        if (value.length > 0) {
          if (this.commonTitle == '打印预览') {
            // this.$emit('table', value)
            this.smallNum = moneyFormat(getSum(value))
            this.bigNum = dealBigMoney(this.smallNum)
          }
        }
      },
      deep: true,
      immediate: true
    },
  },
  created() {
    if (this.commonTitle == '退回原因') {
      this.sendBackReason()
    }
  },
  methods: {
    //特殊工资单详情
    async getDetail() {
      const { data } = await this.$api.information.payrollManagement.salaryDetail({ id: this.info.id })
      this.printInfo = data
      this.tableList = data.list && data.list.sort((a, b) => new Date(a.accountingMonth).getTime() - new Date(b.accountingMonth).getTime())  || [];
      this.total = data.list.length
      this.name = this.payrollOptions.find(item => item.value == this.printInfo.type).name
      this.getList()
    },
    getList() {
      this.tableData = this.tableList.slice((this.pageNum - 1) * 4, this.pageNum * 4)
      this.size = `${this.pageNum}/${Math.ceil(this.tableList.length / 4)}`
      this.pageSize = this.tableList.length
    },
    //退回原因
    async sendBackReason() {
      const { data } = await this.$api.information.payrollManagement.sendBackReason({ id: this.info.id })
      this.reasonInfo = data || {}
    },
    print() {
      let printList = []
      let items = ['staffCode', 'accountingMonth', 'count']
      let obj = {
        staffCode: this.printInfo.staffCode,
        staffName: this.printInfo.staffName,
        factoryName: this.printInfo.factoryName,
        processName: this.printInfo.processName,
        no: this.printInfo.no,
        type: this.name,
        tableName:this.printInfo.tableName,
        auditName:'',
        handleTime: this.printInfo.handleTime || "",
        remark: this.printInfo.remark || "",
        pageSize: this.pageSize,
        printCount: this.printInfo.printCount,
        dataList: this.pageData(this.tableList)
      }
      let dataList = obj.dataList && obj.dataList.map(it => {
        let list = it.list.map(item => {
          let params = {
            accountingMonth: item.accountingMonth
          }
          Object.keys(item).forEach(key => {
            if (!items.includes(key)) {
                if (item[key].settlementAmount == '0.00' && key != 'attendances') {
                  params[key] = '-'
                } else {
                  params[key] = moneyFormat(item[key].settlementAmount)
                }
              }
            // if (!items.includes(key)) {
            //   if (!['自离工资', '延发工资'].includes(this.name)) {
            //     if (item[key].settlementAmount == '0.00' && key != 'attendances') {
            //       params[key] = '-'
            //     } else {
            //       params[key] = moneyFormat(item[key].settlementAmount)
            //     }
            //   } else {
            //     if (['actualSalary'].includes(key)) {
            //       if (item[key].settlementAmount == '0.00') {
            //         params[key] = '-'
            //       } else {
            //         params[key] = moneyFormat(item[key].settlementAmount)
            //       }
            //     } else {
            //       params[key] = '-'
            //     }
            //   }
            // }
          })
          return params
        })
        return {
          ...it,
          list
        }
      })
      console.log('dataList',dataList)
      printList.push({ ...obj, dataList })
      console.log('printList',printList)
      soket(printList)
    },
    pageData(tableList) {
      let arr = []
      for (let index = 0; index < Math.ceil(tableList.length / 4); index++) {
        let obj = {}
        let data = tableList.slice(index * 4, (index + 1) * 4)
        obj = {
          list: data,
          smallNum: moneyFormat(getSum(data)),
          bigNum: dealBigMoney(moneyFormat(getSum(data)))
        }
        arr.push(obj)
      }
      return arr
    },
    //打印
    handlePrint() {
      if (Number(this.printInfo.printCount) + 1 < 2) {
        this.$api.information.payrollManagement.increment({ ids: [this.printInfo.id] })
        this.print()
        return
      }
      this.$confirm('打印次数超过2次,是否打印', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.information.payrollManagement.increment({ ids: [this.printInfo.id] })
        this.print()
      }
      ).catch(() => {

      })
    },
    onNumChange(val) {
      this.pageNum = val;
      this.getList();
    },
    handleCancel() {
      this.$emit('cancel', {
        type: 'cancel',
        isVisible: false
      })
    },
    handleConfirm() {
      let params = {
        isAll: 'N',
        ids: this.info.idList
      }
      switch (this.commonTitle) {
        case '批量提交':
          this.isLoading = true
          this.$api.information.payrollManagement.batchSubmit({handleStatus:'0',...params}).then(() => {
            this.$notify.success({
              title: '成功',
              message: '批量提交成功',
            });
            this.$emit('cancel', {
              type: "confirm",
              isVisible: false
            })
          }).finally(()=>{
              this.isLoading = false;
            })
          break;
        case '批量审核':
          this.isLoading = true
          this.$api.information.payrollManagement.batchAudit({handleStatus:'1',...params}).then(({ data }) => {
            let printList = []
            let items = ['staffCode', 'accountingMonth', 'count', 'actualSalary']
            data && data.forEach(item => {
              this.name = this.payrollOptions.find(m => m.value == item.type).name
              let obj = {
                staffCode: item.staffCode,
                staffName: item.staffName,
                factoryName: item.factoryName,
                processName: item.processName,
                no: item.no,
                type: this.name,
                tableName:this.printInfo.tableName,
                auditName:'',
                handleTime: item.handleTime || "",
                remark: item.remark || "",
                pageSize: item.list.length || 0,
                printCount: item.printCount,
                dataList: this.pageData(item.list)
              }
              let dataList = obj.dataList && obj.dataList.map(it => {
                let list = it.list && it.list.map(item => {
                  let params = {
                    accountingMonth: item.accountingMonth
                  }
                  Object.keys(item).forEach(key => {
                    if (!items.includes(key)) {
                      if (!['自离工资', '延发工资'].includes(this.name)) {
                        if (item[key].settlementAmount == '0.00' && key != 'attendances') {
                          params[key] = '-'
                        } else {
                          params[key] = moneyFormat(item[key].settlementAmount)
                        }
                      } else {
                        if (['actualSalary'].includes(key)) {
                          if (item[key].settlementAmount == '0.00') {
                            params[key] = '-'
                          } else {
                            params[key] = moneyFormat(item[key].settlementAmount)
                          }
                        } else {
                          params[key] = '-'
                        }
                      }
                    }
                  })
                  return params
                })
                return {
                  ...it,
                  list
                }
              })
              printList.push({ ...obj, dataList })
            })
            soket(printList)
            this.$notify.success({
              title: '成功',
              message: '批量审核成功',
            });
            this.$emit('cancel', {
              type: "confirm",
              isVisible: false
            })
          }).finally(()=>{
              this.isLoading = false;
            })
          break;
        case '作废':
          this.$refs.cancelForm.validate(valid => {
            if (!valid) return
            this.isLoading = true
            this.$api.information.payrollManagement.revoke({ id: this.info.id }).then(() => {
              this.$notify.success({
                title: '成功',
                message: '作废成功',
              });
              this.$emit('cancel', {
                type: "confirm",
                isVisible: false
              })
            }).finally(()=>{
              this.isLoading = false;
            })
          })
          break;
        case '提交':
          this.isLoading = true
          this.$api.information.payrollManagement.submit({ id: this.info.id }).then(() => {
            this.$notify.success({
              title: '成功',
              message: '提交成功',
            });
            this.$emit('cancel', {
              type: "confirm",
              isVisible: false
            })
          }).finally(()=>{
              this.isLoading = false;
            })
          break;
        case '通过':
          this.isLoading = true
          this.$api.information.payrollManagement.audit({ id: this.info.id, }).then(({ data }) => {
            this.printInfo = data
            this.tableList = data.list
            this.total = data.list.length
            this.name = this.payrollOptions.find(item => item.value == this.printInfo.type).name
            this.pageSize = this.tableList.length
            // this.print() 通过后取消自动打印
            this.$notify.success({
              title: '成功',
              message: '通过成功',
            });
            this.$emit('cancel', {
              type: "confirm",
              isVisible: false
            })
          }).finally(()=>{
              this.isLoading = false;
          })
          break;
        case '退回':
          this.$refs.reasonForm.validate(valid => {
            if (!valid) return
            this.isLoading = true
            this.$api.information.payrollManagement.sendBack({ id: this.info.id, reason: this.reasonForm.reason }).then(() => {
              this.$notify.success({
                title: '成功',
                message: '退回成功',
              });
              this.$emit('cancel', {
                type: "confirm",
                isVisible: false
              })
            }).finally(()=>{
              this.isLoading = false;
            })
          })
          break;
        case '退回原因':
          this.$emit('cancel', {
            type: 'cancel',
            isVisible: false
          })
          break;

        default:
          break;
      }

    }
  }
}
</script>

<style lang="stylus" scoped>
section {
  border: 1px solid #000;
  padding: 10px 8px;
}
  .btn {
    text-align: right;
    margin-bottom: 10px;
  }

.header {
  margin-bottom: 10px;

  &_top {
    position: relative;
    margin-bottom: 15px;

    >div {
      position: absolute;
    }

    h3 {
      text-align: center;
    }
  }

  &_bottom {
    display: flex;
    justify-content: space-between;

    .item {
      flex: 1;

      &:nth-of-type(3), &:nth-of-type(4) {
        flex: 0.8;
      }
    }
  }
}

.table-footer {
  margin-top: 10px;

  #table_footer {
    display: flex;
    justify-content: space-between;
  }
}

.footer {
  width: 100%;
  padding: 20px 0;
  display: flex;
  justify-content: space-between;
}

.reason {
  .item {
    padding: 10px 0;
  }
}

.cancelForm {
  >>> .el-form-item__label {
    &::before {
      display: none;
    }
  }
}

>>>.el-textarea__inner {
  padding-right: 50px;
}

.el-textarea {
  >>>.el-input__count {
    right: 20px !important;
  }
}
</style>
