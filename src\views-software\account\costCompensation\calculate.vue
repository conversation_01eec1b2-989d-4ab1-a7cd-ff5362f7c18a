<template>
  <qDialog
    :visible="isVisible"
    :innerScroll="true"
     :isAuto="true"
    :title="title"
    width="850px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <el-form
      :model="calculateForm"
      ref="calculateForm"
      label-width="120px"
      :rules="rules"
      size="small"
      :key="upKey"
    >
    <el-row>
        <el-col :span="24">
          <el-form-item label="是否核算:" prop="isAttendance">
            <el-radio v-model="calculateForm.isAttendance" label="1"
              >是</el-radio
            >
            <el-radio v-model="calculateForm.isAttendance" label="2"
              >否</el-radio
            >
          </el-form-item>
        </el-col>
      </el-row>
      <template v-if="calculateForm.isAttendance == 2">
        <el-row>
          <el-col :span="24">
            <el-form-item label="选择原因:">
              <el-select
                v-model="calculateForm.resaonType"
                filterable
                clearable
                placeholder="请选择原因"
              >
                <el-option
                  v-for="item in reasonList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="calculateForm.resaonType == 3">
          <el-col :span="24">
            <el-form-item label="其他说明:">
              <el-input
                type="textarea"
                placeholder="请输入内容"
                v-model="calculateForm.remark"
                maxlength="45"
                show-word-limit
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <template v-else>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="流程编号:">
            {{ calculateForm.processCode }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="processCode" label="责任分厂:">
            {{ calculateForm.jobFactoryName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="厂牌编号:" prop="staffCode">
            {{ calculateForm.staffCode }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="processCode" label="责任人:">
            {{ calculateForm.staffName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="执行年月:" prop="executeMonth">
            {{ calculateForm.executeMonth }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="扣款类型:" class="processCode" prop="deductType">
            {{ calculateForm.deductType }}
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
          <el-form-item
            :class="[title == '查看详情' ? 'processCode' : '']"
            label="实际执行年月:"
            prop="actualExecuteMonth"
          >
            <el-date-picker
              v-if="title == '核算'"
              v-model="calculateForm.actualExecuteMonth"
              type="month"
              placeholder="选择日期"
            >
            </el-date-picker>
            <span v-else>{{ calculateForm.actualExecuteMonth }}</span>
          </el-form-item>
        </el-col> -->
      </el-row>
      <!-- <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item
            :class="[title == '查看详情' ? 'processCode' : '']"
            label="核算分厂:"
            prop="factoryId"
          >
            <el-select
              v-if="title == '核算'"
              v-model="calculateForm.factoryId"
              filterable
              clearable
              placeholder="请选择核算分厂"
            >
              <el-option
                v-for="item in tabList"
                :key="item.value"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
            <span v-else>{{ calculateForm.factoryId }}</span>
          </el-form-item>
        </el-col>
      </el-row> -->
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="流程扣款金额:" prop="deductAmount">
            {{ calculateForm.deductAmount  }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
            <el-form-item label="实际扣款金额:" prop="actualDeductAmount">
              <span v-if="!isShowRealAmount">{{
                calculateForm.actualDeductAmount
              }}</span>
              <el-input
                v-else
                oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
                v-model.trim="calculateForm.actualDeductAmount"
                clearable
                @blur="onBlur"
                placeholder="请输入实际扣款金额"
                style="width: 170px"
              >
              </el-input>
              <el-button type="text" @click="handleEdit">
                {{ isShowRealAmount ? "保存" : "修改" }}
              </el-button>
            </el-form-item>

        </el-col>
      </el-row>

        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="已扣款金额:">
              {{ calculateForm.payedAmount }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实际扣款厂牌:" prop="actualStaffCode">
              <el-select
                v-model="calculateForm.actualStaffCode"
                filterable
                clearable
                placeholder="请选择实际扣款厂牌"
              >
                <el-option
                  v-for="item in deductionList"
                  :key="item.staffCode"
                  :label="item.staffCode"
                  :value="item.staffCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item class="" label="待扣款金额:">
              <span style="color: red">{{ calculateForm.payingAmount }}</span>
              <!-- <span v-else>0</span> -->
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" v-if="detailsList && detailsList.length">
          <el-col :span="12">
            <el-form-item class="processCode" label="扣款详情:">
              <ul class="deduction">
              <li v-for="item in detailsList" :key="item.id">
                <span>{{ item.factoryName }}</span>
                <span>{{ item.repaymentTime }}</span>
                <span>{{ item.periodAmount }}</span>
                <span>{{ item.isPay }}</span>
              </li>
            </ul>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 分期行 -->
        <el-row>
          <el-col :span="24">
            <el-form-item label="扣款详情:">
              <span style="color: #afafaf; line-height: 40px">
              <span style="padding-right: 105px">实际核算工厂</span>
              <span style="padding-right: 105px">实际执行年月</span>
              <span>实际扣款金额</span>
            </span>
              <div
                class="details"
                v-for="(item, index) in detailList"
                :key="item.date"
              >
                <div class="details_content">
                  <el-select
                    v-model="item.factoryId"
                    placeholder="请选择工厂名称"
                    filterable
                  >
                    <el-option
                      v-for="item in tabList"
                      :key="item.name"
                      :label="item.label"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                  <el-date-picker
                    style="margin: 0 15px"
                    v-model="item.accountingMonth"
                    type="month"
                    placeholder="选择日期"
                    :picker-options="pickerOptions"
                    @change="seleteDate(item)"
                  >
                  </el-date-picker>
                  <span v-if="index == 0">{{ item.repaymentAmount }}</span>
                  <el-input
                    v-else
                    class="repaymentAmount"
                    v-model.trim="item.repaymentAmount"
                    placeholder="请输入金额"
                    oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
                    @blur="onStageBlur(index, item)"
                  >
                  </el-input>
                </div>
                <div class="details_btn">
                  <el-button type="text" @click="stages(index)" v-if="calculateForm.actualDeductAmount">
                    分期
                  </el-button>
                  <el-button
                    v-show="detailList.length > 1 && index != 0"
                    type="text"
                    @click="handleDelete(item, index)"
                  >
                    删除
                  </el-button>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="修改原因:">
            <el-input
              type="textarea"
              v-model="calculateForm.reason"
              resize="none"
              rows="2"
              show-word-limit
              maxlength="300"
              placeholder="请输入修改原因"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注说明:" prop="remark">
            <el-input

              type="textarea"
              v-model="calculateForm.remark"
              resize="none"
              rows="3"
              show-word-limit
              maxlength="300"
              placeholder="请输入备注说明"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="上传凭证:" prop="file">
            <el-upload

              class="upload"
              :http-request="upload"
              action="*"
              :limit="10"
              :before-upload="beforeUpload"
              :on-change="onFileChange"
              :auto-upload="true"
              :on-exceed="handleExceed"
              :on-remove="handleRemove"
              ref="upload"
            >
              <el-button size="small" type="primary"> 选择附件 </el-button>
              <div slot="tip" class="el-upload__tip">
                注:文件大小不超过3MB，单次最多上传10个文件
              </div>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
    </template>
    </el-form>
  </qDialog>
</template>

<script>
import moment from "moment";
import {  moneyDelete } from "@/utils";
import NP from "number-precision";
NP.enableBoundaryChecking(false);
export default {
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    editForm: Object,
  },
  computed: {
    //已还款
    repaidList() {
      return this.detailsList.filter((item) => item.isPay == "已还款");
    },
    //未还款
    unpaidList() {
      return this.detailsList
        .filter((item) => item.isPay == "未还款")
        .map((item) => ({
          accountingMonth: item.repaymentTime,
          repaymentAmount: item.repaymentAmount,
          factoryId: item.factoryId,
        }));
    },
  },
  data() {
    return {
      upKey:0,
      pickerOptions: {
        disabledDate: (time) => {
          const date = new Date(time);
          const year = date.getFullYear();
          const month = date.getMonth() + 1;
          const checkYear = year + "-" + (month < 10 ? "0" + month : month);
          const disabledMonths = this.detailList.map((item) => {
            return item.accountingMonth;
          }); // 要禁用的月份数组
          return disabledMonths.includes(checkYear);
        },
      },
      detailsList: [],
      dateList: [],
      detailList: [],
      isShowRealAmount: false,
      deductionList: [], //扣款厂牌下拉
      calculateForm: {
        processCode: "",
        factoryId: "",
        jobFactoryName: "",
        staffCode: "",
        staffName: "",
        executeMonth: "",

        deductType: "",
        deductAmount: "",
        reason: "",
        handleName: "",
        handleTime: "",
        actualDeductAmount: "",
        remark: "",
        payedAmount:"",
        isAttendance: "",
        remark: "",
      },
      reasonList: [
        { label: "无需核算", value: "无需核算" },
        // { label: "人力资源部", value: "人力资源部" },
        { label: "其他原因 ", value: "3" },
      ],
      proofs: [],
      fileList: [],
      filterParam: {},
      rules: {
        factoryId: [
          { required: true, message: "请选择核算分厂", trigger: "change" },
        ],
        actualStaffCode: [
          { required: true, message: "请选择实际扣款厂牌", trigger: "change" },
        ],

        actualDeductAmount: [
          { required: true, message: "请输入实际扣款金额", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (!/^-?\d{1,5}(\.\d{1,2})?$/.test(moneyDelete(value))) {
                callback(
                  new Error("小数点前面仅支持5位数,小数点后面仅支持2位数")
                );
              }
              callback();
            },
            trigger: "blur",
          },
        ],
      },
      //扣款类型
      payrollOptions: Object.freeze([
        {
          name: "成本赔偿",
          value: "1",
        },
        {
          name: "返工扣款",
          value: "2",
        },
        {
          name: "低耗品",
          value: "3",
        },
        {
          name: "刀具",
          value: "4",
        },
        {
          name: "未打卡扣款",
          value: "5",
        },
      ]),
      tabList: [],
      isLoading: false,
      isBlur: false,
      isConfirm: false,
      unpaidDetails: [],
      num: 1,
      payingAmount: "",
      actualDeductAmount:"",
    };
  },
  async created() {
    this.unpaidDetails = JSON.parse(JSON.stringify(this.unpaidList));
      await this.$api.softwareSystemManage.getBasicPermission
        .getQuFactory({ moduleId: 4 })
        .then((res) => {
          this.tabList =
            res.data.map((item) => ({
              label: item.name,
              name: item.name,
              id: item.id,
            })) || [];
        });

    this.deductDetail();
  },
  methods: {
    getAllStaffCode() {
      let params = {
        staffCode: this.calculateForm.staffCode,
      };
      this.$api.softwareInformation.costCompensation
        .getAllStaffCode(params)
        .then((res) => {
          this.deductionList = res.data;
                // 有且只有一条默认回显
        if( res.data &&  res.data.length == 1){
            this.calculateForm.actualStaffCode = res.data[0].staffCode
            this.$forceUpdate()
            this.upKey++
          }
        });
    },
    disabledPastMonth(time) {
      const disabledMonths = [0, 2]; // 禁用第一个和第三个月
      const date = time.getMonth();
      return disabledMonths.includes(date);
    },
    //统计
    total(arr) {
      let total = arr.reduce(function (pre, cur, index, arr) {
        if (index === 0) {
          return pre + 0;
        }
        return NP.plus(pre, Number(moneyDelete(cur.repaymentAmount)));
      }, 0);
      return total;
    },
    addOneMonth(yearMonth) {
      var date = new Date(yearMonth);
      date.setMonth(date.getMonth() + 1);
      var newYearMonth = date.toISOString().substring(0, 7);
      return newYearMonth;
    },
    //编辑实付金额
    handleEdit() {
      this.isShowRealAmount = !this.isShowRealAmount;
      this.calculateForm.actualDeductAmount = moneyDelete(
        this.calculateForm.actualDeductAmount
      );
      if (this.isShowRealAmount) {
        this.calculateForm.actualDeductAmount =
          this.calculateForm.actualDeductAmount
        ;
        return;
      }
      if (
        this.calculateForm.actualDeductAmount <
        Number(this.calculateForm.payedAmount)
      ) {
        this.$notify.error({
          title: "错误",
          message: "实发金额小于已还款金额",
        });
        this.calculateForm.actualDeductAmount = this.actualDeductAmount;
        return;
      }
      this.$api.softwareInformation.costCompensation
        .updateActualAmount({
          id: this.editForm.id,
          actualAmount: this.calculateForm.actualDeductAmount,
        })
        .then(async ({ success }) => {
          if (success) {
            this.$notify({
              title: "成功",
              message: "实发金额修改成功",
              type: "success",
            });
            await this.deductDetail({ id: this.editForm.id });

            this.num = 1;
          }
        });
    },

    //分期
    stages(index) {
      if(!this.payingAmount){
        this.$message.error("没有待扣款金额或者待扣款金额为0");
        return;
      }
      if (this.detailList.length > 5) {
        this.$message.error("最多只能分6期");
        return;
      }
      let arr = JSON.parse(JSON.stringify(this.detailList));
      let lastIndex = arr.length - 1;
      this.num++;
      this.detailList.push({
        accountingMonth: this.addOneMonth(
          this.detailList[lastIndex].accountingMonth
        ),
        repaymentAmount: this.toFixedTwo(this.payingAmount / this.num),
        factoryId: this.calculateForm.factoryId,
      });

      let detailList = this.detailList.map((item, index) => {
        return {
          ...item,
          repaymentAmount: this.toFixedTwo(this.payingAmount / this.num),
        };
      });
      this.detailList = detailList.map((item, index) => {
        if (Number(moneyDelete(this.payingAmount)) % this.num != 0) {
          if (index === 0) {
            let count = 0
            for (let i = 1; i < detailList.length; i++) {
              count += Number(detailList[i].repaymentAmount)
            }
            item.repaymentAmount = this.returnFloat(NP.minus(this.payingAmount ,count))
          }
        }
        return item;
      });
      this.isBlur = false;
    },
    toFixedTwo(num) {
      if (typeof num !== 'number' || Number.isNaN(num)) return num;
      let resultNum = Math.floor(num * 100) / 100
      return this.returnFloat(resultNum)
    },
    returnFloat(num) {
      num = num.toString(); // 转成字符串类型  如能确定类型 这步可省去
      if (num.indexOf(".") !== -1) {
        let [integerPart, decimalPart] = num.split(".");

        if (decimalPart.length > 2) {
          decimalPart = decimalPart.substring(0, 2);
        } else if (decimalPart.length === 1) {
          decimalPart += "0";
        }

        num = `${integerPart}.${decimalPart}`;
      } else {
        num += ".00";
      }
      return num;
    },
    handleDelete(v, value) {
      this.num--;
      this.dateList = this.dateList.map((ele) => {
        if (v.date === ele.date) {
          ele.disabled = false;
        }
        return ele;
      });
      this.detailList.splice(value, 1);
      if (this.detailList.length > 0) {
        let detailList = this.detailList.map((item, index) => {
          return {
            ...item,
            repaymentAmount:this.toFixedTwo(this.payingAmount / this.num),
          };
        });
        this.detailList = detailList.map((item, index) => {
          if (Number(moneyDelete(this.payingAmount)) % this.num != 0) {
            if (index === 0) {
              let count = 0
              for (let i = 1; i < detailList.length; i++) {
                count += Number(detailList[i].repaymentAmount)
              }
              item.repaymentAmount = this.returnFloat(NP.minus(this.payingAmount ,count))
            }
          }
          return item;
        });
      }
      if (this.detailList.length == 1) {
        this.num = 1;
      }
      let accountingMonth = this.detailList.map((item) => item.accountingMonth);
      this.dateList = this.dateList.map((ele) => {
        if (accountingMonth.includes(ele.date)) {
          ele.disabled = true;
        } else {
          ele.disabled = false;
        }
        return ele;
      });
    },
    //选择月份
    seleteDate(item) {
      item.accountingMonth = moment(item.accountingMonth).format("YYYY-MM");
    },
    visibleChange(value) {
      if (value) {
        this.filterDate();
      }
    },
    filterDate() {
      let date = this.unpaidDetails
        .map((item) => item.accountingMonth)
        .concat(this.detailList.map((item) => item.accountingMonth));
      let list = this.dateList.map((ele) => {
        if (date.includes(ele.date)) {
          ele.disabled = true;
        } else {
          ele.disabled = false;
        }
        return ele;
      });
      return list;
    },
    onStageBlur(num, it) {
      if(it.repaymentAmount > this.calculateForm.payingAmount){
        this.$message.error("不能大于待扣款金额")
        it.repaymentAmount = ""
        return
      }
      this.isBlur = true;
      this.isConfirm = false;
      if (this.checkAmount(it.repaymentAmount)) return;
      let detailList = this.detailList.map((item, index) => {
        item.repaymentAmount = moneyDelete(item.repaymentAmount);
        if (num === index) {
          item.repaymentAmount = item.repaymentAmount;
        }
        return item;
      });
      let total = detailList.reduce(function (pre, cur, index, arr) {
        if (index === 0) {
          return pre + 0;
        }
        return NP.plus(pre, Number(moneyDelete(cur.repaymentAmount)));
      }, 0);
      this.detailList = this.detailList.map((item, index) => {
        if (index == 0) {
          let repaymentAmount = (item.repaymentAmount =
            NP.minus(Number(moneyDelete(this.payingAmount)), total)
          );
          if (Number(moneyDelete(repaymentAmount)) < 0) {
            this.$message.error("操作错误,扣款金额配置异常,请核查后重试!");
            this.isConfirm = false;

          } else {
            this.isConfirm = true;
          }
        }
        return {
          ...item,
          repaymentAmount: moneyDelete(item.repaymentAmount),
        };
      });
    },
    //校验金额
    checkAmount(value, name = "") {
      console.log("name", name);
      let flag = false;
      let amount = moneyDelete(value);
      if (!amount) {
        this.$message({
          message: "金额不能为空",
          type: "warning",
        });
        flag = true;
      }
      if (!flag) {
        if (!/^-?\d{1,5}(\.\d{1,2})?$/.test(amount)) {
          this.$message({
            message: "小数点前面仅支持5位数,小数点后面仅支持2位数",
            type: "warning",
          });
          flag = true;
          if (name) this.calculateForm[name] = this.payingAmount;
        }
      }
      return flag;
    },
    //成本扣款台账详情
    async deductDetail() {
      const { data } =
        await this.$api.softwareInformation.costCompensation.detailDeduct({
          id: this.editForm.id,
        });
      this.calculateForm = {
        isAttendance: (data.isAttendance = data.isAttendance
          ? data.isAttendance
          : "1"), //默认是
        processCode: data.processCode || "",
        factoryId:data.factoryId,
        jobFactoryName: data.jobFactoryName || "",
        staffCode: data.staffCode || "",
        staffName: data.staffName || "",
        executeMonth: data.executeMonth || "",
        deductType: data.deductType || "",
        reason: data.reason || "",
        handleName: data.handleName || "",
        handleTime: data.handleTime || "",
        deductAmount: data.deductAmount || "",
        payedAmount: data.payedAmount ,
        actualStaffCode: data.staffCode,
        actualDeductAmount: data.actualDeductAmount,
        remark: data.remark || "",
        payingAmount:data.payingAmount
      };
      // 处理扣款详情分期
      this.detailsList = data.repaymentList || [];
      let value = "";
      this.payingAmount = this.calculateForm.payingAmount;
      this.actualDeductAmount = data.actualDeductAmount
      if (this.unpaidDetails.length > 0) {
        value = this.unpaidDetails.reduce(
          (pre, cur) =>
            NP.plus(pre, Number(moneyDelete(cur.actualDeductAmount))),
          0
        );
        this.payingAmount = NP.minus(
          Number(moneyDelete(this.calculateForm.payingAmount)),
          value
        );
      }
      this.getAllStaffCode();

      // 一进来给这个数组赋值一条默认值数据
      this.detailList = new Array(1).fill().map(() => {
        return {
          accountingMonth: moment().subtract(1, "month").format("YYYY-MM"),
          repaymentAmount: data.payingAmount,
          factoryId: data.factoryId,
        };
      });

      this.proofs =
        data.proofs &&
        data.proofs.filter((item) => item.proofName && item.proofUrl);
    },
    onBlur() {
      this.calculateForm.actualDeductAmount = moneyDelete(
        this.calculateForm.actualDeductAmount
      );
      this.$refs.calculateForm.validateField(
        ["actualDeductAmount"],
        (valid) => {
          if (!valid) {
            this.calculateForm = {
              ...this.calculateForm,
              actualDeductAmount:
                this.calculateForm.actualDeductAmount
              ,
            };
          }
        }
      );
    },
    async handleRemove(file, fileList) {
      let findPath = this.fileList.find(
        (item) => item.proofName === file.name
      ).proofUrl;
      try {
        await this.$api.common.deleteUploadFile(JSON.stringify([findPath]));
      } catch (error) {
        this.$notify.error({
          message: error,
        });
      }
    },
    beforeUpload(file) {
      const isLt3M = file.size / 1024 / 1024 < 3;
      if (!isLt3M) {
        this.$message.error("上传文件大小不能超过 3MB!");
        return false;
      }
      return true;
    },
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 10 个文件，本次选择了 ${files.length} 个文件，共选择了 ${
          files.length + fileList.length
        } 个文件`
      );
    },
    onFileChange(file, fileList) {},
    async upload({ file }) {
      try {
        const {
          data: { fileUrl },
        } = await this.$api.common.appUploadFile(file);
        this.fileList.push({
          proofName: file.name,
          proofUrl: fileUrl,
        });
      } catch (error) {
        this.$notify.error({
          message: error,
        });
      }
    },
    handleCancel() {
      const filePath = this.fileList.map((item) => item.proofUrl);
      if (filePath.length > 0) {
        this.$api.common.deleteUploadFile(JSON.stringify(filePath));
      }
      this.$emit("cancel", {
        type: "cancel",
        isVisible: false,
      });
    },
    handleConfirm() {
      if(this.calculateForm.isAttendance == 2){
        if(!this.calculateForm.resaonType){
          this.$message.error("请选择原因");
          return
        }
        if(this.calculateForm.resaonType == 3 && !this.calculateForm.remark ){
          this.$message.error("请填写其他原因");
          return
        }
        this.isLoading = true
        let params = {
          id:this.editForm.id,
          remark: this.calculateForm.resaonType == 3 ? '其他原因:' + this.calculateForm.remark : this.calculateForm.resaonType
        }
        this.$api.softwareInformation.costCompensation
          .noAccounting(params)
          .then((data) => {
            this.$notify.success({
              title: "成功",
              message: "操作成功",
            });
            this.$emit("cancel", {
              type: "confirm",
              isVisible: false,
            });
          })
          .finally(() => {
            this.isLoading = false;
          });
        return
      }
      if (!this.isConfirm && this.isBlur) {
        this.$message.error("操作错误,扣款金额配置异常,请核查后重试!");
        return;
      }
      if(this.calculateForm.actualDeductAmount != this.actualDeductAmount){
        this.calculateForm.actualDeductAmount = this.actualDeductAmount
      }
      this.calculateForm.actualDeductAmount = moneyDelete(
        this.calculateForm.actualDeductAmount
      );
      this.$refs.calculateForm.validate((valid) => {
        if (!valid) return;
        this.isLoading = true;
        for (const [key, val] of Object.entries(this.calculateForm)) {
          if (typeof val !== "undefined" && val !== null && val !== "") {
            this.filterParam[key] = val;
          }
        }
        let params = {
          ...this.filterParam,
          deductType: this.payrollOptions.find(
            (item) => item.name == this.calculateForm.deductType
          ).value,

          deductAmount: Number(moneyDelete(this.calculateForm.deductAmount)),
          payedAmount: Number(moneyDelete(this.calculateForm.payedAmount)),

          actualDeductAmount: Number(
            moneyDelete(this.calculateForm.actualDeductAmount)
          ),

          proofs: this.fileList,
          repaymentList: this.detailList,
        };
        this.$api.softwareInformation.costCompensation
          .businessAccounting({ ...this.editForm, ...params })
          .then((data) => {
            this.$notify.success({
              title: "成功",
              message: "核算成功",
            });
            this.$emit("cancel", {
              type: "confirm",
              isVisible: false,
            });
          })
          .finally(() => {
            this.isLoading = false;
          });
      });
    },
    async download(fileUrl, fileName) {
      try {
        let response = await fetch(fileUrl);
        let blob = await response.blob();
        let objectUrl = window.URL.createObjectURL(blob);
        let a = document.createElement("a");
        a.href = objectUrl;
        a.download = `${fileName}`;
        a.click();
        a.remove();
      } catch (error) {
        console.log(error);
      }
    },
  },
};
</script>

<style lang="stylus" scoped>
>>>.el-form-item__label {
  text-align: left;
}

.processCode {
  >>>.el-form-item__label {
    &::before {
      content: '*';
      color: #F23D20;
      margin-right: 4px;
      visibility: hidden;
    }
  }
}

>>>.el-input-group__append {
  background: #24c69a;
  color: #fff;

  .el-button {
    padding: 5px 10px;
  }
}

>>>.el-select, >>>.el-date-editor {
  width: 100%;
}

.el-col {
  padding-right: 10px;
}

.el-upload__tip {
  padding-left: 10px;
  display: inline-block;
  color: #24c69a;
}

>>>.el-textarea__inner {
  padding-right: 50px;
}

.el-textarea {
  >>>.el-input__count {
    right: 20px !important;
  }
}
.details {
  display: flex;
  padding-bottom: 18px;

  &_content {
    flex: 1;
    display: flex;
    justify-content: space-between;

    >.el-select, >.el-input, >span {
      flex: 1 0 30%;
    }
  }

  &_btn {
    width: 20%;

    >.el-button {
      mar;
    }
  }
}
.deduction {
  width: 100%;
  margin: 0;
  padding: 0;

  li {
    span {
      padding-right: 10px;
    }
  }
}
</style>
