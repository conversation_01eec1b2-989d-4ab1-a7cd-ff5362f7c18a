.navbar {
    height: 30px;
    overflow: hidden;
    position: relative;
    background: $navBg;
    box-shadow: 0 1px 4px rgba(0,21,41,.08);

    .hamburger-container {
      line-height: 30px;
      height: 100%;
      float: left;
      cursor: pointer;
      transition: background .3s;
      -webkit-tap-highlight-color:transparent;

      &:hover {
        background: rgba(0, 0, 0, .025)
      }

      svg {
        fill $navText
      }
    }

    .breadcrumb-container {
      float: left;
    }

    .errLog-container {
      display: inline-block;
      vertical-align: top;
    }

    .el-breadcrumb__separator,
    .el-breadcrumb__inner a {
      color $navText !important
    }
    .el-breadcrumb__item:last-child .no-redirect {
      color $navActiveText !important
    }      

    .right-menu {
      float: right;
      height: 100%;
      line-height: 30px;

      &:focus {
        outline: none;
      }

      .user-info-container {
        margin-right: 20px;
        margin-left: 20px
        color: $navText
        cursor: pointer

        > span > *+* {
          margin-left 5px
        }
      }
    }

    .nav-btn {
      padding 5px
      color #fff !important
      background transparent !important
      border 0
    }
  }