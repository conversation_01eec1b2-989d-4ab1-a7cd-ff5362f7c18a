<template>
  <el-popover
    :ref="'filter-pop'"
    popper-class="filter-pop"
    placement="bottom"
    :width="220" :offset="-25"
    trigger="click"
    @show="onPopShow">
    <i style="cursor: pointer"
      class="el-icon-more el_icon_more"
      slot="reference"
      :class="{ active: textareaValue.length !== 0 }"></i>

    <div class="filter-panel">
      <span
        style="text-decoration: none">请批量粘贴{{titleName}}(支持excel)</span>
      <div>
        <el-input
          type="textarea"
          :rows="14"
          v-model="textareaValue"
          @blur="onBlur">
        </el-input>
      </div>
      <div
        class="filter-panel-btn-wrap">
        <el-button type="warning" size="mini"
          @click="resetData">
          清空</el-button>
        <el-button size="mini"
          type="primary"
          @click="closePop">关闭
        </el-button>
        <el-button size="mini"
          class=""
          type="primary"
          @click="seachFilter">
          查询</el-button>
      </div>
    </div>
  </el-popover>
</template>

<script>
export default {
  name: "searchBatch",
  props: ['titleName'],
  data() {
    return {
      textareaValue: "",
      spanList: "",
    };
  },
  methods: {
    //处理文本域换行方法
    preText(pretext) {
      return pretext
        .replace(/\r\n/g, ",")
        .replace(/\n/g, ",")
        .replace(/\s/g, "&nbsp;");
    },
    onPopShow() {
      this.$emit("textareaData");
    },
    resetFilter() {
      this.textareaValue = "";
    },
    resetData() {
      this.resetFilter()
      this.$emit("focusEvent", []);
      this.$emit("seachFilter", []);
    },
    seachFilter() {
      let spanList = this.preText(this.textareaValue);
      this.$emit("seachFilter", spanList ? spanList.split(",") : null);
    },
    onBlur() {
      let spanList = this.preText(this.textareaValue);
      this.$emit("focusEvent", spanList ? spanList.split(",") : null);
    },
    closePop() {
      const popRef = this.$refs["filter-pop"];
      if (popRef) {
        popRef.doClose();
      }
    },
    created() { },
  },
};
</script>
<style scoped>
.active_header {
  color: #e67b00;
}
.spanFooter div:first-child {
  padding-bottom: 10px;
  font-size: 13px;
}
.spanFooter div:last-child {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.text_color {
  color: #d9001b;
}
</style>
<style lang="stylus" scoped>
.header-label {
  display: inline-flex;
  align-items: center;
}

.filter-panel span {
  font-size: 13px;
}

.filter-pop {
  padding: 10px 5px;
}

.filter-panel {
  .filter-input {
    >>> .el-input-group__append {
      padding: 0 10px;
    }
  }

  &-sort-wrap {
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin-bottom: 10px;
  }

  &-number-wrap {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    >span {
      flex: none;
      width: 65px;
    }

    .number-select {
      width: 120px;
    }

    .number-input {
      margin-left: 5px;
      width: 80px;

      >>> .el-input__inner {
        padding: 0 5px;
      }
    }
  }

  &-select-wrap {
    box-sizing: border-box;
    padding: 5px;
    margin-top: 10px;
    width: 100%;
    // height 200px
    border: 1px solid #DCDFE6;

    // overflow auto
    .virtual-list {
      width: 100%;
      height: 200px;
      overflow: auto;
    }

    .condition-item {
      display: block;
    }
  }

  &-btn-wrap {
    margin-top: 10px;
    text-align: right;
  }
}

.active {
  color: #fff;
  background-color: #0BB78E;
  border-color: #0BB78E;
}

.filter-panel-btn-wrap {
  height: 40px;
}

.textareaBox {
  height: 293px;
  padding: 5px 15px;
  border: 1px solid #DCDFE6;
  color: #606266;
  border-radius: 4px;
  line-height: 21px;
  font-size: 14px;
  text-rendering: auto;
  text-transform: none;
  text-indent: 0px;
  text-shadow: none;
  text-align: start;
  appearance: auto;
  flex-direction: column;
  cursor: text;
  white-space: pre-wrap;
  overflow-wrap: break-word;
  overflow-x: auto;
}

.textareaBox span {
  font-size: 14px;
}

>>> .el-textarea__inner {
  resize: none;
}

>>> .el_icon_more {
  margin-top: -14px !important;
  padding: 1px;
  width: 40px;
  height: 24px;
  text-align: center;
  line-height: 22px;
  border: 1px solid rgba(121, 121, 121, 1);
  border-radius: 0px 2px 2px 0px;
  position: absolute;
  background-color: rgba(242, 242, 242, 1);
  right: -2px !important;

  &.active {
    color: #ff8900;
    border: 1px solid #ff8900;
  }
}
</style>
