<template>
  <qDialog
    :visible="visible"
    :innerScroll="false"
    title="编辑"
    width="500px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <el-form
      ref="ruleForm"
      :model="editForm"
      label-width="88px"
      label-position="left"
      :rules="rules"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="姓名:">
            {{ editForm.staffName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="厂牌编号:">
            {{ editForm.staffCode }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="核算月份:">{{ editForm.accountingMonth }}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="支付方式:">
            <el-radio v-model="editForm.isCash" label="银行卡" :disabled="disabled">
              银行卡</el-radio
            >
            <el-radio v-model="editForm.isCash" label="现金">现金 </el-radio>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注:" prop="comments">
            <el-input
              v-model.trim="editForm.comments"
              type="textarea"
              :rows="3"
              resize="none"
              placeholder="请输入内容"
              maxlength="300"
              show-word-limit
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </qDialog>
</template>

<script>
export default {
  name: "editDialog",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    formData: Object,
  },
  data() {
    return {
      editForm: {
        staffName: "",
        staffCode: "",
        accountingMonth: "",
        isCash: "",
        comments: "",
      },
      rules: {
        comments: [
          {
            validator: (rule, value, callback) => {
              if (!value) {
                return callback(new Error("备注不能为空"));
              }
              callback();
            },
            trigger: "blur",
          },
        ],
      },
      disabled: false,
      isLoading:false
    };
  },
  created() {
    this.editForm = { ...this.editForm, ...this.formData };
    this.disabled = !this.formData.abcNumber && !this.formData.cebNumber ? true : false;
  },
  methods: {
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (!valid) return;
        this.isLoading = true
        this.$api.workbench
          .editCach({
            id: this.formData.id,
            payType: this.editForm.isCash == "银行卡" ? "0" : "1",
            remark: this.editForm.comments,
          })
          .then(() => {
            this.$message({
              type: "success",
              message: "修改成功!",
            });
            this.$emit("cancel", "confirm");
          }).finally(()=>{
            this.isLoading = false;
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
