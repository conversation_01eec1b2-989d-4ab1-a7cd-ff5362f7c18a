<template>
  <span class="content" v-if="data">
    <template>
      {{data}}
      <el-link type="actived" :underline="false" @click="status=!status" v-if="content && content.length > 100">
        {{status?'收起':'展开'}}
      </el-link>
    </template>
  </span>
</template>

<script>
export default {
  name: 'textCollapse',
  props: ['content'],
  data() {
    return {
      status: false
    }
  },
  computed: {
    data() {
      if(!this.content) return null;
      if(this.content.length < 100) {
        return this.content;
      }
      if(this.status) {
        return this.content
      }else{
        return this.content.substring(0,100)
      }
    }
  }
}
</script>

<style lang="stylus" scoped>
  *
    line-height 1.5
  .content
    transition all ease-in .2s
    word-break break-all
</style>