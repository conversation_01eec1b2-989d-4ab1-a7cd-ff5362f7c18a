<template>
  <qDialog
    :visible="visible"
    :innerScroll="true"
    :isAuto="true"
    title="选择工序"
    width="850px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <el-form
      :model="calculateForm"
      ref="calculateForm"
      label-width="120px"
      :rules="rules"
      size="small"
      :key="upKey"
    >  
      <template>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="员工姓名:" prop="factoryId">
              {{ calculateForm.staffName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="厂牌编号:" prop="accountingMonth">
              {{ calculateForm.staffCode }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
             <el-form-item label="核算工厂:" prop="accountingMonth">
              {{ calculateForm.factoryName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
          <el-form-item label="核算月份:" prop="accountingMonth">
              {{ calculateForm.accountingMonth }}
            </el-form-item>
          </el-col>
        </el-row> 
           
        <el-row>
          <el-col :span="24">
            <el-form-item label="金额分配:" prop="processAumont">
              <span style="color: #afafaf;">
                <span style="padding-right: 195px">核算班组</span>
                <span style="padding-right: 125px">金额</span>
                <span style="color: #000;">金额汇总:{{ totalAmount }}</span>
              </span>
              <div
                class="details"
                v-for="(item, index) in details"
                :key="item.date"
              >
                <div class="details_content">
                  <el-select 
                    v-model="item.groupId"
                    placeholder="请选择核算班组"
                    filterable
                  >
                    <el-option
                      v-for="item in groupList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                      :disabled="isGroupSelected(item.id,index)"
                    >
                    </el-option>
                  </el-select>  
                  <el-input  
                    class="repaymentAmount"
                    v-model.trim="item.amount"
                    placeholder="请输入金额"
                    oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+5)}"
                    @blur="onStageBlur(index, item)"
                  >
                  <template slot="append">元</template>
                  </el-input>
                </div>
                <div class="details_btn">
                  <el-button
                    type="text"
                    @click="handleAdd(item,index)" 
                  >
                    增加
                  </el-button>
                  <el-button
                    v-show="details.length > 1 && index != 0"
                    type="text"
                    @click="handleDelete(item, index)"
                  >
                    删除
                  </el-button>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row> 
      </template>
    </el-form>
  </qDialog>
</template>

<script>
import moment from "moment";
import { moneyDelete } from "@/utils";
import NP from "number-precision";
NP.enableBoundaryChecking(false);
export default {
  props: {
    visible: {
      type: Boolean,
      required: true,
    },  
    editForm: Object,
  }, 
  data() {
    return {
      upKey: 0, 
      details: [],  
      groupList: [],
      factoryCode: "", //核算工厂编码
      calculateForm: {
        factoryId: "", //核算工厂
        staffCode:"",//厂牌编号
        factoryName:"",//核算工厂名称
        accountingMonth:"",//核算月份
        amount:"",//金额 
        processAumont:""
      },  
      fileList: [],
      filterParam: {},
      rules: { 
        processAumont: [
          { required: true, message: "金额划分异常", trigger: "blur" }
        ],
      }, 
      smallProcess:{},//数据
      processList:[],//小工序
      totalAmount:"",
      isLoading: false,  
    };
  },
  async created() {  
    
  },
  watch:{
    visible: {
        handler(newVal) {
          if (newVal) {
              this.calculateForm={
                ...this.calculateForm,
                ...this.editForm
              }

 
            
              this.$api.plateTypeSystemManage.getBasicPermission.getBasicGroupList(this.calculateForm.factoryId).then((res) => {
                this.groupList = res.data.map((item) => ({
                  label: item.name,
                  name: item.name,
                  id: item.id,
                }));
                
                this.details = this.calculateForm&&this.calculateForm.groupPiece&&this.calculateForm.groupPiece.details || []
                    
                  let total = this.details.reduce(function (pre, cur, index, arr) {
      
                    return NP.plus(pre, Number(moneyDelete(cur.amount)));
                  }, 0);  
                  if(this.details.length){
                    this.calculateForm.processAumont=1
                  }
                this.totalAmount=total || this.calculateForm.groupPiece&&this.calculateForm.groupPiece.settlementAmount
                 if(!this.details.length){ 
                   this.deductDetail();
                   return
                 }
                  
              });
              
          }
        },
        immediate: true,
      },
  },
  computed: { 
    localValue: {
      get() {
        return this.visible;
      },
      set(value) {
        this.$emit("update:visible", value);
      },
    },
  },

  methods: { 
    //判断班组是否已被选择
    isGroupSelected(groupId,currentIndex){
      return this.details.some((item,index)=>{
        return item.groupId===groupId && index!==currentIndex
      })
    },
    //统计
    total(arr) {
      let total = arr.reduce(function (pre, cur, index, arr) {
        if (index === 0) {
          return pre + 0;
        }
        return NP.plus(pre, Number(moneyDelete(cur.repaymentAmount)));
      }, 0);
      return total;
    }, 
    //切换工厂
    onChangeFactory(val){ 
       this.$api.plateTypeSystemManage.getBasicPermission.getBasicGroupList(val).then((res) => {
        this.groupList = res.data.map((item) => ({
          label: item.name,
          name: item.name,
          id: item.id,
        }));
      });
    },  
    //增加
    handleAdd(item,index) { 
      if(!item.amount){
       this.$message({
          message: "核算班组金额不能为空",
          type: "warning",
        });
        return
      }
      this.details.push({ 
        amount: "",
        groupId: "",
      });
     
    },
  
    handleDelete(v, value) { 
      this.details.splice(value, 1);
    }, 
    onStageBlur(num, it) {  
    
      if (this.checkAmount(it.amount)) return; 
       
    
      let total = this.details.reduce(function (pre, cur, index, arr) {
        
        return NP.plus(pre, Number(moneyDelete(cur.amount)));
      }, 0); 
 
       
      it.amount=Number(moneyDelete(it.amount))
      this.calculateForm.processAumont=it.amount
      this.totalAmount=total
    },
    //校验金额
    checkAmount(value) { 
      let flag = false;
      let amount = moneyDelete(value);
      if (!amount) {
        this.$message({
          message: "金额不能为空",
          type: "warning",
        });
        flag = true;
      }
   
      if (!flag) { 
      if (!/^-?\d{1,5}(\.\d{1,2})?$/.test(moneyDelete(amount))) {
          this.$message({
            message: "分配金额仅支持小数点前面仅支持5位数,小数点后面仅支持2位数",
            type: "warning",
          });
          flag = true;
        }
      }
      return flag;
    },
    //分配
    async deductDetail() {  
      const params = {
        months: [this.calculateForm.accountingMonth],
        factoryId: this.calculateForm.factoryId,
        staffCode: this.calculateForm.staffCode, 
      }; 
     try {
       this.$api.plateTypeInformation.payrollManagement.monthSalary(params).then(({ data }) => { 
        this.details =[{
          amount:  data&&data[0]&&data[0].groupPiece|| "",
          groupId: data&&data[0]&&data[0].groupId || ""
        }]
      }) 
     } catch (error) {
       this.details = new Array(1).fill().map(() => {
        return { 
          amount: "",
          groupId: "",
        };
      });
     }
 
    },
    
 
    handleCancel() { 
      this.localValue = false;
    },
    handleConfirm() { 
      let total= this.totalAmount || 0
      let amounts= this.calculateForm.amount  || 0 
   
      this.$refs.calculateForm.validate((valid) => { 
        if (!valid) return;
        this.isLoading = true;
        for (const [key, val] of Object.entries(this.calculateForm)) {
          if (typeof val !== "undefined" && val !== null && val !== "") {
            this.filterParam[key] = val;
          }
        }
        
        let params = {
          accountingMonth:this.calculateForm.accountingMonth, 
          factoryId:this.calculateForm.factoryId, 
          details:this.details,
          totalAmount:this.totalAmount
        };
        console.log(params,'数据')
         this.isLoading = false;
         this.$emit("confirmGroupIds", params);
        // let fullApi =
        //   this.title == "新增"
        //     ? "addMesPieceWageDivision"
        //     : "editMesPieceWageDivision";
        // let msg = this.title == "新增" ? "新增成功" : "编辑成功";
        // this.$api.plateTypePieceWageSystem[fullApi](params)
        //   .then(() => {
        //     this.$notify({
        //       title: "成功",
        //       message: msg,
        //       type: "success",
        //     });
        //     this.$emit("cancel", "confirm");
        //   })
        //   .finally(() => {
        //     this.isLoading = false;
        //   });
      });
    }, 
  },
};
</script>

<style lang="stylus" scoped>
>>>.el-form-item__label {
  text-align: right;
}

.processCode {
  >>>.el-form-item__label {
    &::before {
      content: '*';
      color: #F23D20;
      margin-right: 4px;
      visibility: hidden;
    }
  }
}

>>>.el-input-group__append {
  background: #24c69a;
  color: #fff;

  .el-button {
    padding: 5px 10px;
  }
}
 

.el-col {
  padding-right: 10px;
}
.details_btn{
  margin-left:20px
}
.el-upload__tip {
  padding-left: 10px;
  display: inline-block;
  color: #24c69a;
}

>>>.el-textarea__inner {
  padding-right: 50px;
}

.el-textarea {
  >>>.el-input__count {
    right: 20px !important;
  }
}

.details {
  display: flex;
  padding-bottom: 18px;

  &_content { 
    display: flex; 

    >.el-select, >.el-input, >span {
    
    }
  }

  &_btn {
    width: 20%;

    >.el-button {
      mar;
    }
  }
}

.deduction {
  width: 100%;
  margin: 0;
  padding: 0;

  li {
    span {
      padding-right: 10px;
    }
  }
}
 .details_content{
  >>>.el-select{
      width:200px; 
      margin-right:50px
  }
  >>>.el-input{
    width:200px
  }
  >>>.el-input__inner {
     width:200px; 
  }
 }
</style>
