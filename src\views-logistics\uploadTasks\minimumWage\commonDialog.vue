<template>
  <qDialog
    :visible="isVisible"
    title="批量删除"
    :innerScroll="false"
    width="400px"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel">
    <p>是否确认批量删除</p>
    <p>已勾选{{list.length}}条数据
    </p>
  </qDialog>
</template>

<script>
export default {
  name: "editDialog",
  props: {
    isVisible: {
      type: Boolean,
      required: true
    },
    list: Array
  },
  methods: {
    handleCancel() {
      this.$emit('cancel', 'cancel')
    },
    handleConfirm() {
      this.$api.logisticsDataUpload.guaranteedSalary.batchDelete({id:"",ids:this.list}).then(() => {
        this.$notify.success({
          title: '成功',
          message: '批量删除成功',
        });
        this.$emit('cancel', 'confirm')
      })
    },
  }
}
</script>

<style lang="scss" scoped>
</style>