<template>
  <!-- 厂服扣款 -->
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          label-position="left"
          size="mini"
        >
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="员工姓名"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input
              clearable
              v-model.trim="searchForm.staffCode"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenStaffCodes"
                  titleName="厂牌编号"
                />
              </template>
            </el-input>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary"  @click="onSearch">
            查询
          </el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-left>
          <ul>
            <li>
              <span>核算工厂:</span><span>{{ info.factoryName }}</span>
            </li>
            <li>
              <span>核算月份:</span><span>{{ info.accountingMonth }}</span>
            </li>
            <li>
              <span>人数:</span><span>{{ debitInfo.people }}</span>
            </li>
            <li>
              <span>厂服扣款:</span
              ><span>{{ debitInfo.uniformAmount | moneyFormat }}</span>
            </li>
          </ul>
      </template>
      <template v-slot:header-right>
        <el-button v-show="permission" size="small" type="primary" @click="handleImport">
          导入
        </el-button>
      </template>
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :data="tableData"
        :height="maxTableHeight"
        style="width: 100%"
      >
        <el-table-column type="index" width="40"> </el-table-column>
        <el-table-column label="员工姓名" prop="staffName" show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="厂牌编号" prop="staffCode" show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="中心" prop="orgCentre" show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="部门/分厂" show-overflow-tooltip prop="department">
        </el-table-column>
        <el-table-column label="职务" prop="job" show-overflow-tooltip> </el-table-column>
        <el-table-column label="套数" prop="suitNum" show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="厂服扣款" prop="uniformAmount" show-overflow-tooltip>
          <template slot-scope="{ row: { uniformAmount } }">
            {{ uniformAmount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column label="备注说明" prop="remark" show-overflow-tooltip>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <el-pagination
          :current-page="pageNum"
          :page-size="pageSize"
          :total="total"
          :page-sizes="[50, 100, 200]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onSizeChange"
          @current-change="onNumChange"
        >
        </el-pagination>
      </template>
    </table-panel>
    <Import
      :visible="ImportVisible"
      @cancel="cancel"
      @confirm="confirm"
      :importInfo="importInfo"
    />
  </content-panel>
</template>
<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";

export default {
  name: "Factoryservicededuction",
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      searchForm: {
        staffCode: "",
        staffName: "",
      },
      tableData: [],
      loading: false,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      filterParam: {},
      params: {},
      factoryId: "",
      permission: "",
      resizeOffset: 55,
      debitInfo: { sysTotal: 0, totalSocialSecurity: 0 },
      info: {},
      ImportVisible: false,
    };
  },
  watch: {
    $route: {
      handler(value) {
        if (value.path.includes("factoryservicededuction")&&value.path.includes("customized")) {
          this.info = JSON.parse(this.$Base64.decode(value.query.data));
          this.factoryId = this.info.factoryId;
          this.importInfo = {
            reportName: "importFactoryUniform",
            paramMap: {
              columnValue: "厂服扣款",
              factoryId: this.factoryId,
              accountingMonth: this.info.accountingMonth,
              id: this.info.id,
            },
          };
          this.getList();
          this.getStatistic();
          this.getPermission();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    //获取厂服扣款列表
    getList() {
      this.loading = true;
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        filterData: {
          factoryId: this.factoryId,
          accountingMonth: this.info.accountingMonth,
          ...this.filterParam,
          ...this.params,
        },
      };
      this.$api.dataUpload.factoryUniform
        .getFactoryUniform(params)
        .then(({ data: { list, total } }) => {
          this.tableData = list || [];
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //是否具有待办任务操作权限
    getPermission() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.info.accountingMonth,
        type: "16",
      };
      this.$api.workbench.agencyPermission(params).then((res) => {
        this.permission = res.data;
      });
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
      this.getStatistic();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    onSizeChange(pageSize) {
      this.pageSize = pageSize;
      // 切换每页条数后从第一页查询
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    // 获取底部数据信息
    getStatistic() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.info.accountingMonth,
        ...this.filterParam,
        ...this.params,
      };
      this.$api.dataUpload.factoryUniform
        .factoryUniformStatistics(params)
        .then(({ success, data }) => {
          if (success) {
            this.debitInfo = data
              ? data
              : (this.debitInfo = {
                  sysTotal: 0,
                  totalSocialSecurity: 0,
                });
          }
        });
    },
    handleImport() {
      this.title = "导入";
      this.ImportVisible = true;
    },
    cancel(value) {
      this.ImportVisible = value;
    },
    confirm(value) {
      this.ImportVisible = value;
    },
    renderHeader(h, { column, index }) {
      let l = column.label.length;
      let f = 14; //每个字大小，其实是每个字的比例值，大概会比字体大小差不多大一点，
      column.minWidth = f * l; //字大小乘个数即长度 ,注意不要加px像素，这里minWidth只是一个比例值，不是真正的长度 //然后将列标题放在一个div块中，注意块的宽度一定要100%，否则表格显示不完全
      return h("div", { class: "table-head", style: { width: "100%" } }, [column.label]);
    },
  },
};
</script>
<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}
ul {
  height:34px
  list-style: none;
  display: flex;
  align-items: center;
  padding: 0;
  margin: 0
  li{
    margin-right: 10px;
  }
}

i {
  font-style: normal;
}


</style>
