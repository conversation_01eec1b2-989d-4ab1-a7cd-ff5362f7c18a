<template>
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          size="mini"
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          label-position="right"
        >
          <el-form-item label="工厂名称:" prop="factoryId">
            <el-select
              v-model="searchForm.factoryId"
              filterable
              clearable
              placeholder="请选择工厂名称"
              @change="onSearch"
            >
              <el-option
                v-for="item in tabList"
                :key="item.value"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="流程编号:" prop="code">
            <el-input
              clearable
              v-model.trim="searchForm.code"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('codes', $event)"
                  @focusEvent="focusEvent('codes', $event)"
                  ref="childrenCodes"
                  titleName="流程编号"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="员工姓名"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input
              v-model.trim="searchForm.staffCode"
              size="mini"
              clearable
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenStaffCodes"
                  titleName="厂牌编号"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="借支类型:" prop="type">
            <el-select
              v-model="searchForm.type"
              filterable
              clearable
              placeholder="请选择借支类型"
              @change="onSearch"
            >
              <el-option
                v-for="item in debitOptions"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="任务状态:" prop="status">
            <el-select
              v-model="searchForm.status"
              filterable
              clearable
              placeholder="请选择任务状态"
              @change="onSearch"
            >
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="金额范围:" prop="amountRangeList">
            <el-select
              v-model="searchForm.amountRangeList"
              filterable
              clearable
              placeholder="请选择金额范围"
              @change="onSearch"
              multiple
            >
              <el-option
                v-for="item in payList"
                :key="item.value"
                :label="item.label"
                :value="item"
              >
              </el-option>
            </el-select>
          </el-form-item>
             <!-- 组织路径 -->
             <el-form-item label="组织路径:" prop="staffDept">
            <el-input
              v-model="searchForm.staffDept"
              size="mini"
              clearable
              maxlength="100"
              placeholder="请输入组织路径关键词查询"
            >
            </el-input>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button
            size="small"
            type="primary"

            @click="onSearch"
          >
            查询
          </el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-left>
        <el-tabs v-model="activeTab">
          <el-tab-pane
            name="0"
            :label="`未处理(${
              (debitInfo.stsVO && getTotal(debitInfo.stsVO.notHandle)) || 0
            })`"
          >
          </el-tab-pane>
          <el-tab-pane
            name="1"
            :label="`已处理(${
              (debitInfo.stsVO && getTotal(debitInfo.stsVO.handled)) || 0
            })`"
          >
          </el-tab-pane>
          <el-tab-pane
            name="2"
            :label="`已完成(${
              (debitInfo.stsVO && getTotal(debitInfo.stsVO.completed)) || 0
            })`"
          >
          </el-tab-pane>
          <!-- <el-tab-pane name="3" label="变动数据"> </el-tab-pane> -->
        </el-tabs>
      </template>
      <template v-slot:header-right>
        <div ref="btnRight">
          <el-checkbox
            v-model="selectall"
            type="primary"
            label="全选"
            border
            size="mini"
            style="margin-right: 10px"
            v-if="['0'].includes(activeTab)"
            class="select"
          ></el-checkbox>
          <el-button
            v-if="activeTab === '0'"
            v-permission="
              'was-customized$informationAccount$logistics$debitAccount$batchConfirmation'
            "
            size="small"
            type="primary"
            @click="batch"
          >
            批量确认
          </el-button>
          <el-button size="small" type="primary" @click="handleExport">
            导出
          </el-button>
          <!-- <el-button size="small" type="primary" @click="handleImport"> 导入 </el-button> -->
        </div>
      </template>
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :height="maxTableHeight"
        :data="tableData"
        :row-class-name="tableRowClassName"
        @selection-change="handleSelectionChange"
        :row-key="getRowKeys"
      >
        <el-table-column
          v-show="activeTab !== '3'"
          width="40"
          type="selection"
          :reserve-selection="true"
        >
        </el-table-column>
        <el-table-column
          v-if="activeTab !== '3'"
          prop="factoryName"
          label="工厂名称"
          width="130"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="code"
          label="流程编号"
          width="150"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="borrowStaffName"
          label="员工姓名"
          width="100"
          align="left"
        >
        </el-table-column>
        <el-table-column
          prop="borrowStaffCode"
          label="厂牌编号"
          width="130"
          align="left"
        >
        </el-table-column>
        <el-table-column
          prop="staffDept"
          label="组织路径"
          width="130"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <!-- <el-table-column
          v-if="activeTab == '3'"
          prop="staffDept"
          label="部门"
          align="left"
        >
        </el-table-column> -->
        <el-table-column prop="type" label="借支类型" width="130" align="left">
        </el-table-column>
        <el-table-column
          prop="processAmount"
          label="申请金额"
          width="100"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            {{ row.processAmount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          prop="realAmount"
          label="审定金额"
          width="100"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            {{ row.realAmount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          prop="actualAmount"
          label="实付金额"
          width="100"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            {{ row.actualAmount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="activeTab != '3'"
          prop="payedAmount"
          label="已还款"
          width="100"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            {{ row.payedAmount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="activeTab != '3'"
          prop="payingAmount"
          label="待还款"
          width="100"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            {{ row.payingAmount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="activeTab !== '3'"
          prop="status"
          label="任务状态"
          width="80"
          align="left"
        >
        </el-table-column>
        <el-table-column
          prop="updateTime"
          label="修改时间"
          width="150"
          align="left"
        >
          <template slot-scope="scope">
            {{ scope.row.updateTime | dateFormat }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="activeTab !== '3'"
          prop="comments"
          label="备注说明"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="acceptTime"
          label="接收时间"
          width="150"
          align="left"
        >
          <template slot-scope="scope">
            {{ scope.row.acceptTime | dateFormat }}
          </template>
        </el-table-column>
        <el-table-column
          label=""
          :width="activeTab != '3' ? '180' : '100'"
          align="left"
          fixed="right"
        >
          <template slot-scope="scope">

            <el-button
              style="margin-left: 0"
              size="small"
              type="text"
              v-permission="
                'was-customized$informationAccount$logistics$debitAccount$stages'
              "
              v-show="scope.row.status && scope.row.status == '未处理'"
              @click="handleStages(scope.row)"
            >
              处理
            </el-button>
            <el-button
              style="margin-left: 0"
              size="small"
              type="text"
              v-permission="
                'was-customized$informationAccount$logistics$debitAccount$repaymentDetails'
              "
              v-show="scope.row.status && scope.row.status != '未处理'"
              @click="handleDetails(scope.row)"
            >
              查看详情
            </el-button>
            <template v-if="scope.row.status != '未处理'">
              <el-button
              style="margin-left: 0"
              size="small"
              type="text"
              v-permission="
                'was-customized$informationAccount$customized$debitAccount$back'
              "
              v-show="scope.row.status == '已处理'"
              @click="handleBack(scope.row)"
            >
              退回
            </el-button>
            <el-button
              style="margin-left: 0"
              size="small"
              type="text"
              v-permission="
                'was-customized$informationAccount$customized$debitAccount$back'
              "
              v-show="scope.row.status == '已完成' && scope.row.isAccounting == 0"
              @click="handleBack(scope.row)"
            >
              退回
            </el-button>
            </template>
            <el-button
              style="margin-left: 0"
              size="small"
              type="text"
              v-permission="
                'was-customized$informationAccount$logistics$debitAccount$submit'
              "
              v-show="activeTab == '3'"
              @click="handleConfirm(scope.row)"
            >
              确认
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <ul>
          <li>待还款人数:{{ debitInfo.sysTotal }}</li>
          <li>待还款金额:{{ debitInfo.totalPayingAmount | moneyFormat }}</li>
        </ul>
        <div style="text-align: right">
          <el-pagination
            @size-change="onSizeChange"
            @current-change="onNumChange"
            :current-page="pageNum"
            :page-size="pageSize"
            :total="total"
            :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper"
          >
          </el-pagination>
        </div>
      </template>
    </table-panel>
    <stage-dialog
      v-if="visible"
      :visible="visible"
      :formData="formData"
      @cancel="handleCancel"
    ></stage-dialog>
    <details-dialog
      v-if="isVisible"
      :visible="isVisible"
      :formData="detailsData"
      @cancel="detailsCancel"
    ></details-dialog>
    <common-dialog
      v-if="commonVisible"
      :visible="commonVisible"
      :title="title"
      :formData="commonData"
      @cancel="commonCancel"
      :selectall="selectall"
      :filterParam="filterParam"
      :activeTab = activeTab
    ></common-dialog>
    <!-- <Import
      v-if="importVisible"
      :visible="importVisible"
      :importInfo="importInfo"
      @cancel="cancel"
      @confirm="confirm"
    /> -->
    <!-- 批量核算 -->
    <accoun-dialog
      v-if="accounDialogVisible"
      :isVisible="accounDialogVisible"
      :commonData="commonData"
      @cancel="accounCancel"
      :selectall="selectall"
      :filterParam="filterParam"
      @success="successPage"
      :total="total"
    ></accoun-dialog>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import stageDialog from "./components/stageDialog";
import detailsDialog from "./components/detailsDialog";
import commonDialog from "./components/commonDialog";
import moment from "moment";
import accounDialog from "./components/accounDialog";
export default {
  name: "LogisticsdebitLedger",
  components: {
    stageDialog,
    detailsDialog,
    commonDialog,
    accounDialog,
  },
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      selectall: false,
      searchForm: {
        type: "",
        staffCode: "",
        staffName: "",
        factoryId: "",
        code: "",
        amountRangeList: "",
         // 组织路径
         staffDept: "",
      },
      debitOptions: Object.freeze([
        {
          name: "因公借支",
          value: "1",
        },
        {
          name: "员工生活费借支",
          value: "2",
        },
        {
          name: "工伤生活费借支",
          value: "3",
        },
        {
          name: "工伤医疗费借支",
          value: "4",
        },
      ]),
      activeTab: "0",
      // statusOptions: Object.freeze([
      //   {
      //     name: "未处理",
      //     value: "0",
      //   },
      //   {
      //     name: "已处理",
      //     value: "1",
      //   },
      //   {
      //     name: "已完成",
      //     value: "2",
      //   },
      // ]),
      tabList: [],
      idList: [],
      codes: [],
      debitInfo: {},
      filterParam: {},
      params: {},
      tableData: [],
      formData: {},
      detailsData: {},
      commonData: {},
      loading: false,
      resizeOffset: 68,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      visible: false,
      isVisible: false,
      commonVisible: false,
      accounDialogVisible: false,
      title: "",
      payList: [
        {
          label: "0~100元",
          value: "0",
          max: 100,
          min: 0,
        },
        {
          label: "100~300元",
          value: "1",
          max: 300,
          min: 100,
        },
        {
          label: "300~500元",
          value: "2",
          max: 500,
          min: 300,
        },
        {
          label: "500元以上",
          value: "3",
          max: "",
          min: 500,
        },
      ],
      // importInfo: {
      //   reportName: "logisticsBorrowingImport",
      //   paramMap: {
      //     columnValue: "物流-借支台账",
      //   },
      // },
      tableKey: "",
    };
  },
  created() {
    this.$nextTick(() => {
      this.$api.logisticsInformation.debitAccount
        .listLogisticsFactorys()
        .then(({ data }) => {
          this.tabList = data || [];
        });
    });
  },
  watch: {
    $route: {
      handler(value) {
        if (
          value.path.includes("debitLedger") &&
          value.path.includes("logistics")
        ) {
          this.getList();
          this.getDebitList();
        }
      },
      deep: true,
      immediate: true,
    },
    activeTab: {
      handler() {
        this.selectall = false;
        this.$refs.tableRef.clearSelection();
        this.onSearch();
        this.getDebitList();
      },
    },
    selectall: {
      handler() {
        if (this.selectall) {
          this.$refs.tableRef.toggleAllSelection();
        } else {
          this.$refs.tableRef.clearSelection();
        }
      },
    },
  },
  methods: {
    getRowKeys(row) {
      return row.id;
    },
    //获取借支台账列表
    getList() {
      this.loading = true;
      this.tableKey = Math.random();
      if (this.activeTab == "3") {
        this.$api.logisticsInformation.debitAccount
          .obsDebitChangeData({
            pageNum: this.pageNum,
            pageSize: this.pageSize,
            filterData: {
              ...this.filterParam,
              status: this.activeTab,
              moduleId: 1,
            },
          })
          .then(({ data: { list, total } }) => {
            this.tableData = list || [];
            this.total = total;
            if (this.selectall) {
              this.$refs.tableRef.toggleAllSelection();
            }
          })
          .finally(() => {
            this.loading = false;
          });
      } else {
        this.$api.logisticsInformation.debitAccount
          .getDebitAccountList({
            pageNum: this.pageNum,
            pageSize: this.pageSize,
            filterData: {
              ...this.filterParam,
              status: this.activeTab,
            },
          })
          .then(({ data: { list, total } }) => {
            this.tableData = list || [];
            this.total = total;

            if (this.selectall) {
              this.tableData.forEach((row) => {
                this.$refs.tableRef.toggleRowSelection(row, true);
              });
            }
          })
          .finally(() => {
            this.loading = false;
          });
      }
    },

    //获取借支台账统计
    getDebitList() {
      this.$api.logisticsInformation.debitAccount
        .debitLedgerListStatistics({
          factoryId: this.factoryId,
          ...this.filterParam,
          status: this.activeTab,
        })
        .then(({ data }) => {
          this.debitInfo = data || {};
        });
    },
    getTotal(num) {
      if (isNaN(Number(num))) return 0;
      return Number(num) > 9999 ? "9999+" : num;
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.$refs.childrenCodes.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (key === "amountRangeList" && val.length) {
          this.filterParam.amountRangeList = val.map((item) => {
            return {
              maxAmount: item.max,
              minAmount: item.min,
            };
          });
        } else if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0)
          this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
      this.getDebitList();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    tableRowClassName({ row, rowIndex }) {
      return this.idList.includes(row.id) ? "table-SelectedRow-bgcolor" : "";
    },

    //多选
    handleSelectionChange(val) {
      this.idList = (val && val.map((item) => item.id)) || [];
      this.codes = (val && val.map((item) => item.code)) || [];
    },
    //批量确认
    batch() {
      if (this.idList.length === 0 && !this.selectall) {
        this.$message({
          message: "请先勾选需要批量确认的内容",
          type: "warning",
        });
        return;
      }
      this.title = "批量确认";
      this.accounDialogVisible = true;
      this.commonData = {
        ids: this.idList,
        codes: this.codes,
        // payMonth: !this.searchForm.factoryId
        //   ? moment().subtract(1, "month").format("YYYY-MM")
        //   : "",
        payMonth: moment().subtract(1, "month").format("YYYY-MM"),
      };
    },
    //退回
    handleBack({ id }) {
      this.title = "退回";
      this.commonVisible = true;
      this.commonData = { id };
    },
    //处理
    handleStages(row) {
      this.visible = true;
      this.formData = {
        id: row.id || "",
        factoryId: row.factoryId || "",
      };
    },
    // 查看详情
    handleDetails({ id }) {
      this.isVisible = true;
      this.detailsData = {
        id,
      };
    },
    //确认
    handleConfirm(row) {
      this.$confirm("是否确认该条数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$api.logisticsInformation.debitAccount
          .handleChangeData({ id: row.id })
          .then((res) => {
            this.$notify.success({
              title: "成功",
              message: "确认成功",
            });
            this.getList();
            this.getDebitList();
          });
      });
    },
    //导入
    // handleImport() {
    //   this.importVisible = true;
    // },
    accounCancel() {
      this.accounDialogVisible = false;
    },
    successPage() {
      this.accounDialogVisible = false;
      this.$refs.tableRef.clearSelection();
      this.onSearch();
    },
    //导出
    handleExport() {
      let params = {};
      if (Object.keys(this.filterParam).length) {
        params = {
          ...this.filterParam,
        };
      }
      this.$api.common
        .doExport("logisticsExportborrowing", {
          ...params,
          ...this.params,
          status: this.activeTab,
        })
        .then((res) => {
          this.$message.success("导出操作成功，请前往导出记录查看详情");
        });
    },
    handleCancel(type) {
      this.visible = false;
      if (type == "cancel") return;
      this.getList();
      this.getDebitList();
    },
    detailsCancel() {
      this.isVisible = false;
    },
    commonCancel(type) {
      this.commonVisible = false;
      if (type == "cancel") return;
      this.getList();
      this.getDebitList();
    },
    // cancel(value) {
    //   this.importVisible = value;
    // },
    // confirm(value) {
    //   this.importVisible = value;
    // },
    onSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(val) {
      this.pageNum = val;
      this.getList();
    },
  },
};
</script>

<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}

>>>.el-form-item__label {
  text-align: left;
}

.el-table {
  >>> .table-SelectedRow-bgcolor {
    .el-table__cell {
      background-color: #0bb78e29 !important;
    }
  }
}

ul {
  list-style: none;
  display: flex;
  padding: 0;
}

i {
  font-style: normal;
}

>>>.table-panel-footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  ul {
    display: flex;

    li {
      padding: 0 5px;
    }
  }
}

>>>.el-tabs__nav-wrap::after {
  height: 0;
}
.select{
  background:#0bb78e
  color:#fff
}
>>>.el-checkbox.is-bordered.el-checkbox--mini{
    height:25px !important
}
>>>.el-checkbox__input.is-checked+.el-checkbox__label{
    color:white !important
}
>>>.el-checkbox__input.is-checked .el-checkbox__inner{
   border-color:white !important
}
</style>
