<template>
  <qDialog
    :visible="isVisible"
    :title="title"
    :innerHeight="200"
    width="400px"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <el-form
      :model="addForm"
      ref="addForm"
      :rules="rules"
      label-width="92px"
      size="small"
    >
      <el-form-item label="工厂名称:" prop="factoryName">
        <el-input
          v-model="addForm.factoryName"
          clearable
          placeholder="请输入工厂名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="Mes工厂:" prop="mesFactoryName">
        <el-input
          v-model="addForm.mesFactoryName"
          clearable
          placeholder="请输入Mes工厂"
        ></el-input>
      </el-form-item>
      <el-form-item label="工厂编码:"></el-form-item>
      <el-form-item label="状态:" prop="status">
        <el-radio-group v-model="addForm.status">
          <el-radio label="0">启用</el-radio>
          <el-radio label="1">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
  </qDialog>
</template>

<script>
export default {
  name: "addDialog",
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    editForm: Object,
  },
  data() {
    return {
      // 表单数据
      addForm: {
        factoryName: "",
        mesFactoryName: "",
        code: "",
        status: "0",
      },
      rules: {
        factoryName: [{ required: true, message: "请输入工厂名称", trigger: "blur" }],
      },
    };
  },
  methods: {
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      this.$emit("cancel", "confirm");
    },
  },
};
</script>

<style lang="stylus" scoped></style>
