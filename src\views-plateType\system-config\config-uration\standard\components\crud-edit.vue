<template>
  <content-panel>
    <qDialog
      :visible="visible"
      :innerScroll="false"
      :innerHeight="500"
      :title="title"
      width="500px"
      :isLoading="isLoading"
      @cancel="handleCancel"
      @confirm="handleConfirm"
      :before-close="handleCancel"
    >
      <el-form
        :model="addForm"
        :rules="rules"
        ref="addForm"
        size="small"
        label-width="173px"
      >
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="核算工厂:" prop="factoryId">
              <el-select
                v-model="addForm.factoryId"
                filterable
                clearable
                style="width: 100%"
                placeholder="请选择核算工厂"
                @change="changeFactoryId"
              >
                <el-option
                  v-for="item in factoryList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="核算类型:" prop="relationSet">
              <el-select
                v-model="addForm.relationSet"
                filterable
                clearable
                style="width: 100%"
                placeholder="请选择核算类型"
              >
                <el-option
                  v-for="item in accountingtype"
                  :disabled="item.disabled"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item
              label="核算班组(工序):"
              prop="shiftGroupStr"
              :show-message="!addForm.shiftGroupStr"
            >
              <el-input
                disabled
                clearable
                placeholder="请选择核算班组(工序)"
                v-model="addForm.shiftGroupStr"
                type="text"
              >
                <template slot="append">
                  <el-button @click="selectlist">选择</el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="计件工序:">
              <el-input
                disabled
                clearable
                placeholder="请选择计件工序"
                type="text"
              >
                <template slot="append">
                  <el-button
                    @click="changePiece"
                    :disabled="true"
                    class="selectcolor"
                    >选择</el-button
                  >
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="入职年限:" prop="condition">
              <el-select
                v-model="addForm.condition"
                filterable
                clearable
                style="width: 100%"
                placeholder="请选择入职年限"
              >
                <el-option
                  v-for="item in yearList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="补贴标准:" prop="criterion">
              <el-input
                clearable
                placeholder="请输入金额"
                v-model="addForm.criterion"
                type="number"
                class="no-spin-buttons"
              >
                <template slot="append">元/天</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </qDialog>
    <add-Data
      v-if="isvisible"
      :visible.sync="isvisible"
      :processesList="processesList"
      :checkData="addForm.shiftGroup"
      @confirmGroupIds="confirmGroupIds"
    />
    <add-piece
      v-if="isvisiblePiece"
      :visible.sync="isvisiblePiece"
      :factoryId="addForm.factoryId"
      :checkData="addForm.pieceProcess"
      @confirmPiece="confirmPiece"
    />
  </content-panel>
</template>
<script>
import { assignValue } from "@/utils";
import addData from "./add-data.vue";
import addPiece from "./add-piece.vue";
export default {
  components: { addData, addPiece },
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    modifyData: {
      type: Object,
      default: {},
    },
    editForm: Object,
    modifyNum: Number,
  },
  computed: {
    title() {
      return this.modifyNum == 1
        ? "新增"
        : this.modifyNum == 2
        ? "编辑"
        : "复制";
    },
    processesList() {
      if (this.factoryList.length && this.addForm.factoryId) {
        return this.factoryList.find((v) => v.id == this.addForm.factoryId)
          .process;
      }
    },
  },
  data() {
    return {
      addForm: {
        factoryId: "",
        relationSet: "",
        relationSetStr: "",
        shiftGroup: [],
        condition: "",
        criterion: "",
        shiftGroupStr: "",
        id: "",
      },
      isModifyType: false,
      rules: {
        factoryId: [
          { required: true, message: "核算工厂不能为空", trigger: "change" },
        ],
        shiftGroupStr: [
          { required: true, message: "核算班组不能为空", trigger: "blur" },
        ],
        relationSet: [
          { required: true, message: "核算类型不能为空", trigger: "change" },
        ],

        // pieceProcessStr: [
        //   { required: true, message: "计件工序不能为空", trigger: "blur" },
        // ],
        condition: [
          { required: true, message: "入职年限不能为空", trigger: "change" },
        ],
        criterion: [
          {
            required: true,
            pattern: /^(0|[0-9]{1,5})((\.\d{0,2})*)$/,
            message: "小数点前面仅支持5位数,小数点后面仅支持2位数",
            trigger: "blur",
          },
        ],
      },
      factoryList: [],
      isLoading: false,
      isvisible: false,
      isvisiblePiece: false,
      accountingtype: [
        { label: "计件工序查询", value: 0, disabled: true },
        { label: "核算班组查询", value: 1, disabled: false },
        { label: "关联查询", value: 2, disabled: true },
      ],
      yearList: [
        { label: "3个月内", value: "entryMonths>0&&entryMonths<3" },
        { label: "3月至1年内", value: "entryMonths>=3&&entryMonths<12" },
        { label: "6个月内", value: "entryMonths>0&&entryMonths<6" },
        { label: "6个月至1年内", value: "entryMonths>=6&&entryMonths<12" },
        { label: "1年至2年内", value: "entryMonths>=12&&entryMonths<24" },
        { label: "2年至3年内", value: "entryMonths>=24&&entryMonths<36" },
        { label: "3年至3年以上", value: "entryMonths>=36" },
        { label: "按出勤天数直接核算", value: "1==1" },
      ],
    };
  },
  created() {
    this.getFactoryList();
    this.init();
  },
  methods: {
    getFactoryList() {
      this.$api.plateTypeSystemManage.getBasicPermission
        .getQuFactory({ moduleId: 3 })
        .then((res) => {
          this.factoryList = res.data || [];
        });
    },
    init() {
      if (this.modifyData) {
        this.initModifyData();
      }
    },
    initModifyData() {
      if (this.modifyNum == 3) {
        //复制不需要id
        this.modifyData.id = "";
      }
      assignValue(this.addForm, this.modifyData);
    },
    changeFactoryId() {
      this.addForm.shiftGroupStr = "";
      this.addForm.shiftGroup = [];
    },
    //选择核算班组
    selectlist() {
      if (!this.addForm.factoryId) {
        this.$message.error("请先选择核算工厂");
        return;
      }
      this.isvisible = true;
    },
    //选择计件工序
    changePiece() {
      return;
      if (!this.addForm.factoryId) {
        this.$message.error("请先选择核算工厂");
        return;
      }
      if (!this.addForm.relationSet) {
        this.$message.error("请先选择核算标准");
        return;
      }
      this.isvisiblePiece = true;
    },
    confirmGroupIds(data) {
      let names = data.map((v) => {
        return v.name;
      });
      this.addForm.shiftGroupStr = names.join(",");
      this.addForm.shiftGroup = data.map((v) => {
        return v.id;
      });
    },
    confirmPiece(data) {
      let names = data.map((v) => {
        return v.processName;
      });
      this.addForm.pieceProcessStr = names.join(",");
      this.addForm.pieceProcess = data.map((v) => {
        return v.id;
      });
    },

    handleConfirm() {
      this.$refs.addForm.validate((valid) => {
        if (!valid) return;
        this.isLoading = true;
        let params = {
          ...this.addForm,
          taskType: "ENVIRONMENT_SUBSIDY",
        };
        // modifyNum:1新增 2编辑 3复制
        this.$api.plateTypeSystemConfig
          .configsaveOrUpdate(params)
          .then(() => {
            this.$notify({
              title: "成功",
              message: this.title + "成功",
              type: "success",
            });
            this.$emit("cancel", "confirm");
          })
          .finally(() => {
            this.isLoading = false;
          });
      });
    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
  },
};
</script>

<style lang="stylus" scoped>
.staffCode {
  >>>.el-input-group__append {
    background: #24c69a;
    color: #fff;

    .el-button {
      padding: 5px 20px;
    }
  }
}
.selectcolor{
  color: #fff;
  background-color: #fff;

}
.el-row {
  &::before, &::after {
    display: none;
  }

  display: flex;
  justify-content: space-between;
}

.el-form-item {
  display: flex;

  >>>.el-form-item__label {
    text-align: right;

  }

  >>>.el-form-item__content {
    width: 100%;
    margin-left: 0 !important;
  }
}

.assignedFactory {
  >>>.el-form-item__label {
    &::before {
      content: '*';
      color: #F23D20;
      margin-right: 4px;
      visibility: hidden;
    }
  }
}
>>>.el-date-editor {
  width: 100%;
}
.illustrate{
  color:#FF8900;
  padding:10px;
  margin-left:15px

}
>>>.el-input-group__append button.el-button{
  color: #fff;
  background-color: #0bb78e;
  border: 4px solid #0bb78e;
}
>>>.el-input.is-disabled .el-input__inner{
  background-color: #fff;
}
::v-deep .no-spin-buttons input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

::v-deep .no-spin-buttons input::-webkit-outer-spin-button {
  -webkit-appearance: none !important;
}

::v-deep .no-spin-buttons input[type="number"] {
  -moz-appearance: textfield !important;
}
</style>
