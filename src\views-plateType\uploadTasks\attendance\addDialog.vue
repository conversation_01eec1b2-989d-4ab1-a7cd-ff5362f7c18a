<template>
  <qDialog :visible="visible" :innerScroll="false" :innerHeight="500" :title="title" :isLoading="isLoading"
    width="1000px" @cancel="handleCancel" @confirm="handleConfirm" :before-close="handleCancel">
    <el-form :model="addForm" :rules="rules" ref="addForm" size="small">
      <el-row>
        <el-col :span="11">
          <el-form-item class="staffName" label="员工姓名:" label-width="140px">
            {{ addForm.staffName }}
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item v-if="title == '新增'" label="厂牌编号:" label-width="170px" prop="staffCode">
            <el-input class="staffCode" v-model="addForm.staffCode" clearable placeholder="请输入厂牌编号">
              <template slot="append">
                <el-button type="primary" @click="searchEmployee">
                  查询
                </el-button>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item v-else class="staffName" label-width="170px" label="厂牌编号:">{{
            addForm.staffCode
          }}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item class="staffName" label="核算工厂:" label-width="140px">
            {{ addForm.factoryName }}
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item v-if="title == '新增'" label="核算班组:" label-width="170px" prop="groupId">
            <el-select clearable v-model="addForm.groupId" placeholder="请选择核算班组">
              <el-option v-for="item in processesOption" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-else class="staffName" label-width="170px" label="核算班组:">{{
            addForm.groupId
          }}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item class="staffName" label="核算月份:" label-width="140px">
            {{ addForm.accountingMonth }}
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <!-- 必填 -->
          <el-form-item label="员工系数:" label-width="170px" prop="coefficient">
            <el-input oninput="value=value
                  .replace(/[^.\d]/g, '')
                  .replace(/^\./g, '0.')
                  .replace(/(\..*)\./g, '$1')
                  .replace(/(\.\d{2})\d+/g, '$1')
                " v-model.trim="addForm.coefficient" clearable placeholder="请输入员工系数">
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row>
        <el-col :span="11">
          <el-form-item class="staffName" label-width="140px" label="子工序:" prop="subProcess">
            <el-input v-model.trim="addForm.subProcess" clearable placeholder="请输入子工序">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item class="staffName" label="员工类别:" label-width="170px" prop="productionCategory">
            <el-select v-model="addForm.productionCategory" clearable placeholder="请选择员工类别" filterable class="item">
              <el-option v-for="item in productionCategorysEnum" :key="item.value" :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row> -->
      <el-row>
        <el-col :span="11">
          <el-form-item class="staffName" label-width="140px" label="主管:" prop="supervisor">
            <el-input show-word-limit :maxlength="20" v-model.trim="addForm.supervisor" clearable placeholder="请输入主管">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="11">
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item class="staffName" label="本厂出勤天数:" label-width="150px">
            {{ addForm.totalWorkDay }}
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item class="staffName" label="本厂加班小时:" label-width="170px">
            {{ addForm.totalOvertime }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item label="工作日出勤:" label-width="140px" prop="workdayWork">
            <el-input
              oninput="value=value.replace(/[^\d.]/g, '');if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+4)}"
              v-model.trim="addForm.workdayWork" clearable @blur="totalDay('workdayWork')"
              @clear="totalDay('workdayWork')" placeholder="请输入工作日出勤">
              <template slot="append">天</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="工作日延时加班:" label-width="170px" prop="workdayOvertime">
            <el-input
              oninput="value=value.replace(/[^\d.]/g, '');if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
              v-model.trim="addForm.workdayOvertime" clearable @blur="totalTime('workdayOvertime')"
              @clear="totalTime('workdayOvertime')" placeholder="请输入工作日延时加班">
              <template slot="append">小时</template></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item label="周末出勤:" label-width="140px" prop="weekWork">
            <el-input
              oninput="value=value.replace(/[^\d.]/g, '');if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+4)}"
              v-model.trim="addForm.weekWork" clearable @blur="totalDay('weekWork')" @clear="totalDay('weekWork')"
              placeholder="请输入周末出勤">
              <template slot="append">天</template></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="周末延时加班:" label-width="170px" prop="weekOvertime">
            <el-input
              oninput="value=value.replace(/[^\d.]/g, '');if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
              v-model.trim="addForm.weekOvertime" clearable @blur="totalTime('weekOvertime')"
              @clear="totalTime('weekOvertime')" placeholder="请输入周末延时加班">
              <template slot="append">小时</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item label="节假日出勤:" label-width="140px" prop="holidayWork">
            <el-input
              oninput="value=value.replace(/[^\d.]/g, '');if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+4)}"
              v-model.trim="addForm.holidayWork" clearable @blur="totalDay('holidayWork')"
              @clear="totalDay('holidayWork')" placeholder="请输入节假日出勤">
              <template slot="append">天</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="节假日延时加班:" label-width="170px" prop="holidayOvertime">
            <el-input
              oninput="value=value.replace(/[^\d.]/g, '');if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
              v-model.trim="addForm.holidayOvertime" clearable @blur="totalTime('holidayOvertime')"
              @clear="totalTime('holidayOvertime')" placeholder="请输入节假日延时加班">
              <template slot="append">小时</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item class="staffName" label="备注:" label-width="120px">
            <el-input type="textarea" resize="none" rows="5" v-model.trim="addForm.comments">
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </qDialog>
</template>

<script>
import NP from 'number-precision';
import { productionCategorysEnum } from '@/utils/constant';
export default {
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    editForm: [Object],
  },
  data() {
    return {
      addForm: {
        staffName: "",
        staffCode: "",
        factoryName: "",
        groupId: "",
        totalWorkDay: "",
        coefficient: '',//员工系数
        supervisor: '',//主管
        totalOvertime: "",
        workdayWork: "",
        workdayOvertime: "",
        weekWork: "",
        holidayWork: "",
        holidayOvertime: "",
        accountingMonth: "",
        comments: "",
        weekOvertime: "",
        // subProcess: '',
        // productionCategory: '',
      },
      staffCode: "",
      factoryId: "",
      tabList: [],
      productionCategorysEnum: productionCategorysEnum,
      rules: {
        coefficient: [
          {
            required: true,
            message: "员工系数不能为空",
            trigger: "blur"
          },
          {
            pattern: /^\d(\.\d{0,2})?$/,
            message: "小数点前面仅支持1位数,小数点后面仅支持2位数",
            trigger: "blur"
          }
        ],
        workdayWork: [
          { required: true, message: "工作日出勤不能为空", trigger: "blur" },
          {
            pattern: /^(0|[0-9]{1,2})((\.\d{0,3})*)$/,
            message: "小数点前面仅支持2位数,小数点后面仅支持3位数",
            trigger: "blur",
          },
        ],
        weekWork: [
          { required: true, message: "周末出勤不能为空", trigger: "blur" },
          {
            pattern: /^(0|[0-9]{1,2})((\.\d{0,3})*)$/,
            message: "小数点前面仅支持2位数,小数点后面仅支持3位数",
            trigger: "blur",
          },
        ],
        holidayWork: [
          { required: true, message: "节假日出勤不能为空", trigger: "blur" },
          {
            pattern: /^(0|[0-9]{1,2})((\.\d{0,3})*)$/,
            message: "小数点前面仅支持2位数,小数点后面仅支持3位数",
            trigger: "blur",
          },
        ],
        workdayOvertime: [
          { required: true, message: "工作日延时加班不能为空", trigger: "blur" },
          {
            pattern: /^(0|[0-9]{1,3})((\.\d{0,2})*)$/,
            message: "小数点前面仅支持3位数,小数点后面仅支持2位数",
            trigger: "blur",
          },
        ],
        weekOvertime: [
          { required: true, message: "周末延时加班不能为空", trigger: "blur" },
          {
            pattern: /^(0|[0-9]{1,3})((\.\d{0,2})*)$/,
            message: "小数点前面仅支持3位数,小数点后面仅支持2位数",
            trigger: "blur",
          },
        ],
        holidayOvertime: [
          { required: true, message: "节假日延时加班不能为空", trigger: "blur" },
          {
            pattern: /^(0|[0-9]{1,3})((\.\d{0,2})*)$/,
            message: "小数点前面仅支持3位数,小数点后面仅支持2位数",
            trigger: "blur",
          },
        ],
      },
      isLoading: false,
    };
  },
  async created() {
    const { factoryName, factoryId, accountingMonth } = JSON.parse(
      this.$Base64.decode(this.$route.query.data)
    );
    this.factoryId = factoryId;
    this.addForm = {
      ...this.addForm,
      ...this.editForm,
      factoryName,
      accountingMonth,
    };
    await this.$api.plateTypeSystemManage.getBasicPermission
      .getQuFactory({ moduleId: 3 })
      .then((res) => {
        this.tabList = res.data || [];
      });
  },
  computed: {
    processesOption() {
      return (
        this.tabList.length > 0 &&
        this.addForm.factoryName &&
        this.tabList.find((item) => item.name === this.addForm.factoryName).process || []
      );
    },
  },
  methods: {
    //查询员工信息
    searchEmployee() {
      if (!this.addForm.staffCode) {
        this.$message.warning("请输入厂牌编号");
        return;
      }
      this.$api.information.employee
        .employeeDetails({ staffCode: this.addForm.staffCode })
        .then(({ data }) => {
          this.addForm.staffName = data.staffName || "";
        });
    },
    //总出勤天数统计
    totalDay(name) {
      if (!this.addForm[name]) {
        let total = NP.plus(Number(this.addForm.workdayWork), Number(this.addForm.weekWork), Number(this.addForm.holidayWork));
        this.addForm.totalWorkDay = total;
        return;
      }
      this.$refs.addForm.validateField(
        ["workdayWork", "weekWork", "holidayWork"],
        (valid) => {
          if (valid) return;
          let total = NP.plus(Number(this.addForm.workdayWork), Number(this.addForm.weekWork), Number(this.addForm.holidayWork));
          this.addForm.totalWorkDay = total;
        }
      );
    },
    //总加班小时统计
    totalTime(name) {
      if (!this.addForm[name]) {
        let total = NP.plus(Number(this.addForm.workdayOvertime), Number(this.addForm.weekOvertime), Number(this.addForm.holidayOvertime));
        this.addForm.totalOvertime = total;
        return;
      }
      this.$refs.addForm.validateField(
        ["workdayOvertime", "weekOvertime", "holidayOvertime"],
        (valid) => {
          if (valid) return;
          let total = NP.plus(Number(this.addForm.workdayOvertime), Number(this.addForm.weekOvertime), Number(this.addForm.holidayOvertime));
          this.addForm.totalOvertime = total;
        }
      );
    },
    handleCancel() {
      this.$emit("cancel", {
        type: "cancel",
      });
    },
    handleConfirm() {
      this.$refs.addForm.validate((valid) => {
        if (!valid) return;
        if (!this.addForm.staffName) {
          this.$message.warning("员工姓名为空,请先查询员工姓名");
          return;
        }
        this.isLoading = true;
        let params = {
          ...this.addForm,
          factoryId: this.factoryId,
        };
        delete params.factoryName;
        // if (params.subProcess.trim() === '') delete params.subProcess;
        // if (params.productionCategory.trim() === '') delete params.productionCategory;
        if (this.title == "新增") {
          this.$api.plateTypeDataUpload.employeeAttendance.addAttendance(params).then(() => {
            this.$notify({
              title: "成功",
              message: "新增成功",
              type: "success",
            });
            this.$emit("cancel", {
              type: "confirm",
            });
          }).finally(() => {
            this.isLoading = false;
          });
        } else {
          delete params.updateTime;
          let groupId = this.addForm.groupId && this.processesOption.find(
            (item) => item.name == this.addForm.groupId
          ).id || "";
          this.$api.plateTypeDataUpload.employeeAttendance
            .reviseEmployee({
              ...params,
              id: this.editForm.id,
              groupId,
            })
            .then((res) => {
              this.$notify({
                title: "成功",
                message: "编辑成功",
                type: "success",
              });
              this.$emit("cancel", {
                type: "confirm",
              });
            }).finally(() => {
              this.isLoading = false;
            });
        }
      });
    },
  },
};
</script>

<style lang="stylus" scoped>
.staffCode {
  >>>.el-input-group__append {
    background: #24c69a;
    color: #fff;

    .el-button {
      padding: 5px 20px;
    }
  }
}

.el-row {
  &::before, &::after {
    display: none;
  }

  display: flex;
  justify-content: space-between;
}

.el-form-item {
  display: flex;

  >>>.el-form-item__label {
    text-align: left;
  }

  >>>.el-form-item__content {
    width: 100%;
    margin-left: 0 !important;

    .el-select {
      width: 100%;
    }
  }
}

.staffName {
  >>>.el-form-item__label {
    &::before {
      content: '*';
      color: #F23D20;
      margin-right: 4px;
      visibility: hidden;
    }
  }
}
</style>
