<template>
  <div>
    <p
      style="padding-left:40px;color:#333;font-weight: bold">
      提交校验:</p>
    <ul class="finished">
      <li v-for="item in list"
        :key="item.content">
        <span>{{item.content}}</span>
        <i :class="[item.icon?'el-icon-success':'el-icon-error']"
          :style="{'color':!item.icon?'rgba(236, 128, 141, 1)':'#0BB78E'}"></i>
      </li>
    </ul>
    <p
      :style="{'padding-left':'40px','color':!isAllTrue?'rgba(163, 0, 20, 0.9960784)':'#0BB78E'}">
      {{isAllTrue?title:"验证失败，请调整后再试！"}}
    </p>
  </div>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      required: true
    },
    isAllTrue: {
      type: Boolean,
      required: true
    },
    title: String
  },
}
</script>

<style lang="stylus" scoped>
ul {
  li {
    list-style: none;
    line-height: 40px;
    display: flex;
    align-items: center;

    >i {
      margin-left: 30px;
      font-size: 30px;
    }
  }
}
</style>