import { asyncRoutes, constantRoutes } from '@/router'

export function setPermitList(list) {
  if (list) {
    localStorage.setItem('permitList', JSON.stringify(list));
  }
}

export function getPermitList() {
  let permitList = [];
  const permitStr = localStorage.getItem('permitList');
  try {
    permitList = JSON.parse(permitStr)
  } catch (error) {
    
  }

  return permitList;
}

export function removePermitList() {
  localStorage.removeItem('permitList')
}

/**
 * Use meta.role to determine if the current user has permission
 * @param roles
 * @param route
 */
export function hasPermission(route) {
  // route的meta没配置permit，默认不显示
  if (!route.meta || !route.meta.permit) {
    return false;
  }

  const permit = route.meta.permit;

  // 如果是布尔类型，直接返回
  if (typeof permit === 'boolean') {
    return permit
  }

  const permissionList = getPermitList();
  const target = permissionList.find((item) => item.indexOf(permit) > -1)
  if (target) {
    return true;
  }
  return false;
}

/**
 * Filter asynchronous routing tables by recursion
 * @param routes asyncRoutes
 * @param roles
 */
export function filterAsyncRoutes(routes) {
  const res = []

  routes.forEach(route => {
    const tmp = { ...route }
    if (hasPermission(tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children)
      }
      res.push(tmp)
    }
  })

  return res
}

const state = {
  routes: [],
  addRoutes: []
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    state.routes = constantRoutes.concat(routes)
  }
}

const actions = {
  generateRoutes({ commit }, roles) {
    return new Promise(resolve => {
      const accessedRoutes = filterAsyncRoutes(asyncRoutes)
      commit('SET_ROUTES', accessedRoutes)
      resolve(accessedRoutes)
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
