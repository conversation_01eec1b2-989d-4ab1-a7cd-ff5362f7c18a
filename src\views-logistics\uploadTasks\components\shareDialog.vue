<template>
  <qDialog
    :visible="visible"
    :innerScroll="false"
    title="设置分摊"
    width="1000px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <el-form size="small" :model="shareForm">
      <el-form-item label="区域:">{{ shareForm.area }}</el-form-item>
      <el-form-item label="核算班组:">{{ shareForm.groupName }}</el-form-item>
      <el-form-item label="不参与人:" class="event">
        <search-box class="search-box">
          <el-form
            :inline="true"
            :model="searchForm"
            ref="searchForm"
            size="mini"
          >
            <el-form-item label="姓名:" prop="staffName">
              <el-input
                v-model="searchForm.staffName"
                clearable
                @keyup.enter.native="onSearch"
                placeholder="请输入姓名"
              >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="员工姓名"
                />
              </template>
            </el-input>

            </el-form-item>
            <el-form-item label="工号:" prop="staffCode">
              <el-input
                v-model="searchForm.staffCode"
                clearable
                @keyup.enter.native="onSearch"
                placeholder="请输入工号"
              >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenStaffCodes"
                  titleName="员工姓名"
                />
              </template>
              </el-input>
            </el-form-item>
          </el-form>
          <template v-slot:right>
            <el-button size="small" type="primary" @click="onSearch">
              查询</el-button
            >
            <el-button size="small" type="warning" @click="resetSearchForm">
              重置</el-button
            >
          </template>
        </search-box>
        <el-row :gutter="10">
          <el-col :span="14">
            <p style="font-weight: bold">该班考勤组人员</p>
            <el-table
              stripe
              border
              v-loading="loading"
              ref="multipleTable"
              highlight-current-row
              height="300px"
              :data="tableData"
              @select="handleSelect"
              @select-all="handleSelectAll"
            >
              <el-table-column type="selection" width="40"> </el-table-column>
              <el-table-column
                prop="staffName"
                label="姓名"
                align="center"
                show-overflow-tooltip
              >
              </el-table-column>
              <el-table-column
                prop="staffCode"
                label="工号"
                align="center"
                show-overflow-tooltip
              >
              </el-table-column>
            </el-table>
            <el-pagination
              background
              small
              layout="total,prev, pager, next"
              :total="total"
              :current-page="pageNum"
              @current-change="onNumChange"
            >
            </el-pagination>
          </el-col>
          <el-col :span="10">
            <p style="font-weight: bold">不参与人员</p>
            <el-table
              stripe
              border
              highlight-current-row
              height="300px"
              v-loading="loading"
              :data="flatSelectionData"
            >
              <el-table-column
                prop="staffName"
                label="姓名"
                align="center"
                show-overflow-tooltip
              >
              </el-table-column>
              <el-table-column
                prop="staffCode"
                label="工号"
                align="center"
                show-overflow-tooltip
              >
              </el-table-column>
              <el-table-column label="操作" align="center">
                <template slot-scope="{ row }">
                  <el-button
                    type="text"
                    size="small"
                    @click="handleDelete(row)"
                  >
                    删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-form-item>
    </el-form>
  </qDialog>
</template>
<script>
export default {
  name: "shareDialog",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    formData: Object,
  },
  data() {
    return {
      searchForm: {
        staffName: "",
        staffCode: "",
      },
      shareForm: {
        area: "",
        groupName: "",
      },
      loading: false,
      tableData: [],
      filterParam: {},
      pageNum: 1,
      total: 0,
      allSelectionData: [],
      isLoading: false,
    };
  },
  async created() {
    this.shareForm = {
      ...this.shareForm,
      ...this.formData,
    };
    await this.listNotShareStaff();
    this.getList();
  },
  computed: {
    flatSelectionData() {
      let arr = this.allSelectionData;
      let arrIds = Array.from(new Set(arr.map((v) => v.staffCode)));
      let newArr = [];
      arrIds.forEach((staffCode) => {
        newArr.push(arr.find((v) => v.staffCode === staffCode));
      });
      return newArr;
    },
  },
  methods: {
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    //获取班组下人员列表
    async getList() {
      this.loading = true;
      await this.$api.logisticsDataUpload.pieceRateWage
        .listAttndStaffByGroup({
          pageNum: this.pageNum,
          pageSize: 10,
          filterData: {
            ...this.formData,
            ...this.filterParam,
          },
        })
        .then(({ data: { list, total } }) => {
          this.tableData = list || [];
          this.total = total || 0;
          this.showCheckedNode();
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //获取未参与分摊员工列表
    listNotShareStaff() {
      return this.$api.logisticsDataUpload.pieceRateWage
        .listNotShareStaff({
          factoryId: this.formData.factoryId,
          accountingMonth: this.formData.accountingMonth,
          groupId: this.formData.groupId,
          wageItem: this.formData.wageItem,
        })
        .then(({ data }) => {
          this.allSelectionData =
            (data &&
              data.map((item) => ({
                staffCode: item.staffCode || "",
                staffName: item.staffName || "",
              }))) ||
            [];
        });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.filterParam = {};
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
    },
    // 回显已选中节点
    showCheckedNode() {
      this.$nextTick((_) => {
        const data = this.tableData.filter((v) =>
          this.flatSelectionData.find((k) => k.staffCode === v.staffCode)
        );
        data.forEach((row) => {
          this.$refs.multipleTable.toggleRowSelection(row);
        });
      });
    },
    handleSelect(data, row) {
      let currentData = this.allSelectionData;
      const checkStatus = !currentData.find(
        (v) => v.staffCode === row.staffCode
      );
      if (checkStatus) {
        // 勾选
        this.allSelectionData = [...currentData, ...data];
      } else {
        // 取消勾选
        currentData = currentData.filter((v) => v.staffCode !== row.staffCode);
        this.allSelectionData = currentData;
      }
    },
    handleSelectAll(data) {
      let currentData = this.allSelectionData;
      if (data.length === 0) {
        let clearData = this.tableData;
        let checkedData = currentData.filter(
          (v) => !clearData.find((k) => k.staffCode === v.staffCode)
        );
        this.allSelectionData = checkedData;
      } else {
        this.allSelectionData = [...currentData, ...data];
      }
    },
    handleDelete(row) {
      this.allSelectionData = this.allSelectionData.filter(
        (item) => item.staffCode != row.staffCode
      );
      this.$refs.multipleTable.toggleRowSelection(
        this.tableData.find((item) => item.staffCode == row.staffCode)
      );
    },
    onNumChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      this.isLoading = true;
      let params = {
        factoryId: this.formData.factoryId,
        accountingMonth: this.formData.accountingMonth,
        groupId: this.formData.groupId,
        staffCodes:
          (this.flatSelectionData &&
            this.flatSelectionData.map((v) => v.staffCode)) ||
          [],
        wageItem: this.formData.wageItem,
      };
      this.$api.logisticsDataUpload[this.formData.type]
        .apportion(
          !this.$route.path.includes("handyman")
            ? params
            : {
                ...params,
                category: this.formData.category,
              }
        )
        .then(() => {
          this.$notify.success({
            title: "成功",
            message: "设置分摊成功",
          });
          this.onSearch();
        })
        .finally(() => {
          this.$emit("cancel", "confirm");
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
  },
};
</script>

<style lang="stylus" scoped>
.search-box {
  margin-bottom: 10px;
}

.event {
  display: flex;

  >>>.el-form-item__content {
    flex: 1;
    line-height: 0;
  }
}
</style>
