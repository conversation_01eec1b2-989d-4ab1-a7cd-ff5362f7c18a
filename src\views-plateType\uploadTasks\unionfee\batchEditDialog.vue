<template>
  <qDialog
    :visible="isVisible"
    title="批量编辑"
    :innerScroll="false"
    width="400px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <p>是否批量开启或关闭工会费扣款？</p>
    <span>状态:</span>
    <el-radio-group v-model="onOff" style="margin-left: 20px">
      <el-radio label="on">开启</el-radio>
      <el-radio label="off">关闭</el-radio>
    </el-radio-group>
  </qDialog>
</template>

<script>
export default {
  name: "batchEditDialog",
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    formData: Object,
  },
  data() {
    return {
      isLoading: false,
      onOff: "on",
    };
  },
  methods: {
    handleCancel() {
      this.$emit("success", "cancel");
    },
    handleConfirm() {
      this.isLoading = true;
      const { factoryId, accountingMonth } = JSON.parse(this.$Base64.decode(this.$route.query.data));
      let params = {
        ids: this.formData.idList,
        // isAll: 0,
        factoryId,
        accountingMonth,
        onOff: this.onOff,
      };

      this.$api.plateTypeDataUpload.unionFee
        .batchOnOrOffUnionFees(params)
        .then((res) => {
          this.$notify.success({
            title: "成功",
            message: "批量编辑成功",
          });
          this.$emit("success",'confirm');
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped></style>
