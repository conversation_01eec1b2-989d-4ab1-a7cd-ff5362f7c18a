<template>
  <qDialog
    :visible="isVisible"
    :innerScroll="true"
    :isAuto="true"
    title="批量核算"
    width="400px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <el-form label-width="102px" style="margin-bottom: 20px;">
      <p>当前共选中条数:{{ selectall?total:info.idList.length }}</p>
      <el-row>
        <el-col :span="24">
          <el-form-item label="是否核算:">
            <el-radio v-model="stagesForm.isAccounting" :label="1">是</el-radio>
            <el-radio v-model="stagesForm.isAccounting" :label="0">否</el-radio>
          </el-form-item>
        </el-col>
      </el-row>
      <template v-if="stagesForm.isAccounting == 0">
        <el-row>
          <el-col :span="24">
            <el-form-item label="选择原因:">
              <el-select
                v-model="stagesForm.resaonType"
                filterable
                clearable
                placeholder="请选择原因"
              >
                <el-option
                  v-for="item in reasonList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="stagesForm.resaonType == 3">
          <el-col :span="24">
            <el-form-item label="其他说明:">
              <el-input
                type="textarea"
                placeholder="请输入内容"
                v-model="stagesForm.remark"
                maxlength="45"
                show-word-limit
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <template v-else>
         <el-row>
          <el-col :span="24">
            <el-form-item
              label="实际执行月份" 
            >
              <el-date-picker
                v-model="stagesForm.actualAccountingMonth"
                type="month"
                placeholder="选择日期"  
                value-format="yyyy-MM"
                size="mini"
                :clearable="false"
              >
              </el-date-picker> 
            </el-form-item>
          </el-col>
        </el-row>
    
    </template>
    </el-form>
  </qDialog>
</template>

<script>
import moment from "moment";
export default {
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    filterParam: {
      type: Object,
      required: {},
    },
    total:{
      type:Number,
    },
    info: [Object],
    selectall: {
      type: Boolean,
      required: false,
    },
  },
  data() {
    return {
      stagesForm:{isAccounting: 1,remark:"",actualAccountingMonth: moment().subtract(1, "month").format("YYYY-MM")}, 
      isLoading: false, 
      reasonList: [
        { label: "无需核算", value: "无需核算" }, 
        { label: "人力资源部", value: "人力资源部" }, 
        { label: "其他原因 ", value: "3" },
      ],
    };
  },
  methods: { 
    handleCancel() {
      this.$emit("cancel", {
        type: "cancel",
        isVisible: false,
      });
    },
    handleConfirm() { 
      if(this.stagesForm.isAccounting == 0){
        //无需核算
        if(!this.stagesForm.resaonType){
          this.$message.error("请选择原因");
          return
        }
        if(this.stagesForm.resaonType == 3 && !this.stagesForm.remark ){
          this.$message.error("请填写其他原因");
          return
        }
        this.isLoading = true
        let params = {
          isAll:0,
          ids: this.info.idList,
          isAccounting: this.stagesForm.isAccounting,
        }
        if (this.selectall) {
            //全选
            params = {
              isAll: 1,
              ids: this.info.idList,
              ...this.filterParam,
              isAccounting: this.stagesForm.isAccounting,
              
            };
          } 
          if(this.stagesForm.resaonType == 3){
                params.noAccountingRemarks=  '其他原因:' + this.stagesForm.remark    
            }else{
                params.noAccountingRemarks=this.stagesForm.resaonType
            }
        this.$api.plateTypeInformation.orderBomRevision.orderBomRevisionBatchAccounting(params)
          .then(() => {
            this.$notify.success({
              title: "成功",
              message: "操作成功！",
            });
            this.$emit("success");
          })
          .finally(() => {
            this.isLoading = false;
          });
          return
      }
      this.isLoading = true;
      let params = {
          isAll:0,
          ids: this.info.idList,
          actualAccountingMonth: this.stagesForm.actualAccountingMonth,
          isAccounting: this.stagesForm.isAccounting,
        }
        if (this.selectall) {
            //全选
            params = {
              isAll: 1,
              ids: this.info.idList,
              ...this.filterParam,
              actualAccountingMonth: this.stagesForm.actualAccountingMonth,
              isAccounting: this.stagesForm.isAccounting,
            };
          }  
      this.$api.plateTypeInformation.orderBomRevision
        .orderBomRevisionBatchAccounting(params)
        .then(({ data }) => {
          this.$notify.success({
            title: "成功",
            message: data,
          });
          this.$emit("success");
        })
        .finally(() => {
          this.isLoading = false;
        });
    },  
  },
};
</script>

<style lang="stylus" scoped>
.el-date-editor.el-input{
  width: 130px
}
>>>.el-form-item{
  margin-bottom: 5px !important;
}
>>>.el-input__inner {
  height 32px
  line-height 32px
}
.el-input{
  width 50%
}
</style>
