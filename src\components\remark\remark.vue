<template>
  <div style="max-width: 100%;">
    <el-link type="actived" :underline="false" @click="showRemark" v-if="String(content).length>100">查看</el-link>
    <el-tooltip class="item" effect="dark" placement="top" :open-delay="600" v-else>
      <div style="overflow: hidden; text-overflow: ellipsis;">{{content}}</div>
      <div slot="content">
        <div style="max-width: 400px;">{{content}}</div>
      </div>
    </el-tooltip>

    <v-dialog v-model="dialogVisible" :config="dialogConfig">
      <div slot="content">
        <div class="remark">{{content}}</div>
      </div>
    </v-dialog>
  </div>
</template>

<script>
export default {
  name: 'remark',
  props: ['content'],
  data() {
    return {
      dialogVisible: false,
      dialogConfig: {
        title: '查看',
        showCancel: false
      }
    }
  },
  methods: {
    showRemark() {
      this.dialogVisible = true;
    }
  }
}
</script>

<style lang="stylus" scoped>
  .remark
    text-align justify
    display inline-block
    word-break break-all
    line-height 1.6
    max-height 500px
    overflow scroll
</style>