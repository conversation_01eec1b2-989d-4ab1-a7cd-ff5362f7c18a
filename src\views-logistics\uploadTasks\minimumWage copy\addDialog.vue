<template>
  <qDialog :visible="visible"
    :title="title"
    :innerScroll="false"
    width="1200px"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel">
    <el-form ref="addForm"
      :model="addForm"
      :rules="rules">
      <el-row :gutter="15">
        <el-col :span="8">
          <el-form-item
            label="员工姓名:">
            {{addForm.staffName}}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="厂牌编号:"
            prop="staffCode">
            <el-input
              v-model.trim="addForm.staffCode"
              clearable
              placeholder="请输入厂牌编号">
              <template
                slot="append">
                <el-button
                  type="primary"
                  @click="searchEmployee">
                  查询
                </el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <section>
      <el-row :gutter="10">
        <el-col :span="4">
          <span>考勤班组</span>
        </el-col>
        <el-col :span="3">
          <span>基数</span>
        </el-col>
        <el-col :span="3">
          <span>总出勤天数</span>
        </el-col>
        <el-col :span="3">
          <span>总加班天数</span>
        </el-col>
        <el-col :span="5">
          <span>计算规则</span>
        </el-col>
        <el-col :span="3">
          <span>调整金额</span>
        </el-col>
        <el-col :span="3">
          <span>保底工资</span>
        </el-col>
      </el-row>
      <el-row :gutter="10"
        v-for="item in list"
        :key="item.processId"
        class="content">
        <el-col :span="4">
          <div
            style="padding:1px 0">
            {{item.processName}}
          </div>
        </el-col>
        <el-col :span="3">
          <el-input
            oninput="value=value.replace(/[^\d.]/g, '')"
            v-model="item.base"
            clearable
            @blur="baseChange('blur',item)"
            @clear="baseChange('clear',item)"
            placeholder="请输入基数">
            >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-input
            oninput="value=value.replace(/[^\d.]/g, '');if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+4)}"
            v-model="item.totalWorkDay"
            clearable
            @blur="totalWorkDayChange('blur','totalWorkDay',item)"
            @clear="totalWorkDayChange('clear','totalWorkDay',item)"
            placeholder="请输入总出勤天数">
            >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-input
            oninput="value=value.replace(/[^\d.]/g, '');if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+4)}"
            v-model="item.totalOverDay"
            clearable
            @blur="totalWorkDayChange('blur','totalOverDay',item)"
            @clear="totalWorkDayChange('clear','totalOverDay',item)"
            placeholder="请输入总加班天数">
            ></el-input>
        </el-col>
        <el-col :span="5">
          <el-select
            v-model="item.calculationRules"
            placeholder="请选择计算规则">
            <el-option
              label="基数/28*总出勤天数"
              value="0"></el-option>
            <el-option
              label="基数/28*(总出勤天数+总加班天数)"
              value="1"></el-option>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-input
            v-model="item.amount"
            clearable
            placeholder="请输入调整金额"></el-input>
        </el-col>
        <el-col :span="3">
          <div
            style="padding:1px 0">
            {{item.minimumSalary | moneyFormat}}
          </div>
        </el-col>
      </el-row>
    </section>
  </qDialog>
</template>

<script>
import { moneyFormat, moneyDelete } from "@/utils";
export default {
  name: "editDialog",
  props: {
    title: {
      type: String,
      required: true
    },
    visible: {
      type: Boolean,
      required: true
    },
    editForm: Object
  },
  data() {
    return {
      addForm: {
        staffName: "",
        staffCode: "",
      },
      rules: {
        staffCode: [
          { required: true, message: '请输入厂牌编号', trigger: 'blur' },
        ],
      },
      list: []
    }
  },
  created() {
    this.list = Array.from({ length: 2 }).map((item, index) => ({
      processId: Math.random().toString().substring(3, 10),
      processName: "班组" + (index + 1),
      base: moneyFormat(4000),
      totalWorkDay: 0,
      totalOverDay: 0,
      calculationRules: '0',
      amont: 0,
      minimumSalary: 0,
    }))
  },
  methods: {
    //查询员工信息
    searchEmployee() {
      this.$refs.addForm.validateField(["staffCode"], (valid) => {
        if (valid) return;
        this.$api.information.employee
          .employeeDetails({ staffCode: this.addForm.staffCode })
          .then(({ data }) => {
            this.addForm.staffName = data && data.staffName || '';
          });
      });
    },
    //校验基数
    baseChange(name, item) {
      if (!item.base || name == 'clear') {
        item.base = 0
        return
      }
      if (!/^\+?[1-9]\d*$/.test(moneyDelete(item.base))) {
        this.$message.error('请输入正整数！');
        item.base = 0;
      }
      item.base = moneyFormat(moneyDelete((item.base)));
    },
    handleCancel() {
      this.$emit('cancel', 'cancel')
    },
    handleConfirm() {
      this.$refs.addForm.validate(valid => {
        if (!valid) return
        this.$emit('cancel', 'confirm')
      })
    },
  }
}
</script>

<style lang="stylus" scoped>
.el-form-item {
  display: flex;
  align-items: center;
}

>>>.el-form-item__content {
  flex: 1;
}

.content {
  margin: 15px 0 15px 0;
  display: flex;
  align-items: center;
}

>>>.el-select {
  width: 100%;
}

>>>.el-input-group__append {
  background: #24c69a;
  color: #fff;

  .el-button {
    padding: 5px 20px;
  }
}
</style>