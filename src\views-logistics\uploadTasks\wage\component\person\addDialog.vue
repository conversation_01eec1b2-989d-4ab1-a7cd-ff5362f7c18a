<template>
  <qDialog
    :visible="visible"
    :title="title"
    :innerScroll="false"
    width="500px"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <el-form
      ref="addForm"
      inline
      :model="addForm"
      label-width="125px"
      :rules="rules"
    >
      <el-form-item label="厂牌编号:" prop="staffCode">
        <el-input
          class="staffCode"
          v-model="addForm.staffCode"
          clearable
          placeholder="请输入厂牌编号"
          size="small"
          style="width: 220px"
        >
          <template slot="append">
            <el-button type="primary" @click="searchEmployee"> 查询 </el-button>
          </template>
        </el-input>
        <span class="error_info" v-show="isShowIdCard">{{ showMessage }}</span>
      </el-form-item>
      <el-form-item label="员工姓名:">
        {{ addForm.staffName }}
      </el-form-item>
      <el-form-item label="核算班组:" prop="groupId">
        <el-select
          v-model="addForm.groupId"
          filterable
          clearable
          placeholder="请选择核算班组 "
        >
          <el-option
            v-for="item in groupList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="计件工资:" prop="pieceWage">
        <el-input
          oninput="value=value.replace(/[^\d.]/g, '');if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
          v-model="addForm.pieceWage"
          placeholder="请输入计件工资"
          clearable
          style="width: 220px"
        >
          <template slot="append">元</template>
        </el-input>
      </el-form-item>
      <el-form-item label="大组长划出工资:" prop="leaderOutgoingWage">
        <el-input
          oninput="value=value.replace(/[^\d.]/g, '');if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
          v-model="addForm.leaderOutgoingWage"
          placeholder="请输入大组长划出工资"
          clearable
          style="width: 220px"
        >
          <template slot="append">元</template>
        </el-input>
      </el-form-item>
      <el-form-item label="划出工资:" prop="outgoingWage">
        <el-input
          oninput="value=value.replace(/[^\d.]/g, '');if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
          v-model="addForm.outgoingWage"
          placeholder="请输入划出工资:"
          clearable
          style="width: 220px"
        >
          <template slot="append">元</template>
        </el-input>
      </el-form-item>
      <el-form-item label="总计计件工资:">
        {{ totalWage }}
      </el-form-item>
    </el-form>
  </qDialog>
</template>
<script>
import { assignValue } from "@/utils";
export default {
  name: "addDialog",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    modifyData: Object,
  },
  computed: {
    title() {
      return this.modifyData ? "编辑" : "新增";
    },
    totalWage() {
      const { pieceWage, leaderOutgoingWage, outgoingWage } = this.addForm;
      console.log(
        "leaderOutgoingWage",
        leaderOutgoingWage ? leaderOutgoingWage : 0
      );
      let num =
        pieceWage -
        (leaderOutgoingWage ? leaderOutgoingWage : 0) -
        (outgoingWage ? outgoingWage : 0);
      return num.toFixed(2);
    },
  },
  data() {
    return {
      groupList: [],
      addForm: {},
      isShowIdCard: false,
      showMessage: "",
      rules: {
        staffCode: [
          { required: true, message: "厂牌编号不能为空", trigger: "blur" },
        ],
        groupId: [
          { required: true, message: "核算班组不能为空", trigger: "change" },
        ],
        pieceWage: [
          { required: true, message: "计件工资不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      const { factoryId, accountingMonth } = JSON.parse(this.$Base64.decode(this.$route.query.data));
      this.addForm = {
        factoryId,
        accountingMonth,
        pieceWage: "",
        groupId: "",
        id: "",
        leaderOutgoingWage: "",
        outgoingWage: "",
        pieceWage: "",
        staffCode: "",
        staffName: "",
      };
      if (this.modifyData) {
        //编辑
        this.initModifyData();
      }
    },
    initModifyData() {
      assignValue(this.addForm, this.modifyData);
      this.getSelection();
    },
    //查询员工信息
    searchEmployee() {
      this.isShowIdCard = false;
      if (!this.addForm.staffCode) {
        this.$message.warning("请输入厂牌编号");
        return;
      }
      let params = {
        accountingMonth: this.addForm.accountingMonth,
        factoryId: this.addForm.factoryId,
        staffCode: this.addForm.staffCode,
      };
      this.$api.logisticsDataUpload.pieceRateWage
        .employeeQueryStaff(params)
        .then(({ data }) => {
          if (data) {
            this.$set(this.addForm, "staffName", data.staffName || "");
            this.isShowIdCard = false;
            this.getSelection(true);
          } else {
            this.showMessage = "该人员无考勤数据！";
            this.isShowIdCard = true;
            this.$nextTick(() => {
              this.$refs.addForm.clearValidate();
              this.$refs.addForm.resetFields();
              this.addForm.staffName=""
            });
          }
        });
    },
    getSelection(flag) {
      let params = {
        factoryId: this.addForm.factoryId,
        accountingMonth: this.addForm.accountingMonth,
        staffCode: this.addForm.staffCode,
      };
      this.$api.logisticsWorkbench
        .listGroupByAttend(params)
        .then(({ data }) => {
          this.groupList = data || [];
          if (flag) {
            //点击厂牌编号的查询才默认核算班组
            this.addForm.groupId = data[0].id;
          }
        });
    },

    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      this.isShowIdCard = false;
      this.$refs.addForm.validate((valid) => {
        if (!valid) return;
        let params = {
          ...this.addForm,
        };
        let api = this.modifyData ? "editPersonWage" : "addPersonWage";
        let msg = this.modifyData ? "编辑成功" : "新增成功";
        this.$api.logisticsDataUpload.pieceRateWage[api](params).then(() => {
          this.$notify.success({
            title: "成功",
            message: msg,
          });
          this.$emit("cancel", "confirm");
        });
      });
    },
  },
};
</script>

<style lang="stylus" scoped>
.el-form-item {
  display: flex;
  margin-bottom:15px;
  >>>.el-form-item__label {
    text-align: right;
  }

}
.staffCode {
  >>>.el-input-group__append {
    background: #24c69a;
    color: #fff;
    .el-button {
      padding: 5px 10px;
    }
  }
}
.error_info{
    color: #F23D20;
    font-size: 12px;
    line-height: 1;
    padding-top: 4px;
    position: absolute;
    top: 100%;
    left: 0;
}
</style>
