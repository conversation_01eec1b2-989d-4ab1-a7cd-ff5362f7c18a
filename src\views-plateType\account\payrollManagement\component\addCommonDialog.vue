<template>
  <content-panel>
  <qDialog :visible="editVisible" :innerScroll="true" :innerHeight="height" :title="editTitle" :showFooter="showFooter"
    :width="width" :modal-append-to-body="false" append-to-body @cancel="handleCancel" @confirm="handleConfirm"
    :before-close="handleCancel">
    <div v-if="editTitle == '选择月份'">
      <el-form :model="monthForm" ref="monthForm" :rules="monthRules" label-width="88px" size="small">
        <el-form-item label="选择月份:">
          <el-select v-model="monthForm.accountingMonth" filterable clearable multiple collapse-tags
            placeholder="请选择月份">
            <el-option v-for="month in monthList" :key="month" :label="month" :value="month">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div v-else>
      <div class="edit_header">
        <el-form>
          <el-row>
            <el-col :span="row">
              <el-form-item label="员工姓名:">
                {{ basicInfo.staffName }}
              </el-form-item>
            </el-col>
            <el-col :span="row">
              <el-form-item label="厂牌编号:">
                {{ basicInfo.staffCode }}
              </el-form-item>
            </el-col>
            <el-col :span="row" v-if="editTitle != '编辑'"> </el-col>
          </el-row>
          <el-row>
            <el-col :span="row">
              <el-form-item label="工厂名称:">
                {{ basicInfo.factoryName }}
              </el-form-item>
            </el-col>
            <el-col :span="row">
              <el-form-item label="结算月份:">
                {{ basicInfo.accountingMonth }}
              </el-form-item>
            </el-col>
            <el-col v-if="editTitle != '编辑'" :span="row">
              <el-form-item label="制表人员:">
                {{ basicInfo.tableName }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <el-table border stripe :data="tableData" style="width: 100%" :span-method="objectSpanMethod">
        <el-table-column type="index" width="50"> </el-table-column>
        <el-table-column prop="name" label="数据名称" width="80" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span v-if="!row.isHiddenName">{{ row.name === '个人计件' ? "计件工资" : row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="value" label="系统数值" show-overflow-tooltip width="120">
          <template slot-scope="{ row }">
            <span v-if="row.name === '个人计件'">
              {{ '个人：' + filterValue(row) }}
            </span>
            <span v-else-if="row.name === '集体计件'">
              {{ '集体：' + filterValue(row) }}
            </span>
            <span v-else-if="row.name === '计件工资'">
              {{ '汇总：' + filterValue(row) }}
            </span>
            <span v-else>
              {{ filterValue(row) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="结算数值" width="180" show-overflow-tooltip>
          <template slot-scope="scope">
            <div style="display: flex; align-items: center; gap: 5px;">
              <span v-if="scope.row.name === '个人计件'">
                个人:
              </span>
              <span v-if="scope.row.name === '集体计件'">
                集体:
              </span>
              <span v-if="scope.row.name === '计件工资'">
                汇总:
              </span>

              <el-input class="input_box" v-if="
                editTitle == '编辑' &&
                scope.row.name != '合计扣款' &&
                scope.row.name != '实发工资' &&
                scope.row.name != '计件工资'&&
                scope.row.name != '集体计件'
              " oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+4)}" ref="inputRef" clearable
                v-model.trim="scope.row.settlementAmount" @blur="onBlur(scope.$index, scope.row)"
                @clear="clearData(scope.$index, scope.row)">
              </el-input>

              <span v-else-if="editTitle == '编辑' &&
                scope.row.name == '计件工资' || scope.row.name == '集体计件'" style="padding:10px 5px 10px 5px;">{{ scope.row.settlementAmount }}</span>
              <span v-else :style="{
                paddingLeft:'5px',
                color:
                  editTitle != '编辑'
                    ? filterData(scope.row.settlementAmount) ==
                      filterData(scope.row.sysAmount)
                      ? '#0BB78E'
                      : 'red'
                    : '',
              }">{{ scope.row.settlementAmount }}</span>
              <span @click="selectProcesses"  v-if="editTitle == '编辑' &&scope.row.name === '集体计件'" style="color: #24c69a;cursor: pointer;">选择工序</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column v-if="editTitle != '编辑'" label="修改人">
          <template slot-scope="{ row }">
            {{ row.updateName }}
          </template>
        </el-table-column>
        <el-table-column v-if="editTitle != '编辑'" label="修改时间">
          <template slot-scope="{ row }">
            {{ row.updateTime }}
          </template>
        </el-table-column>
        <el-table-column label="备注说明" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <el-input v-if="editTitle == '编辑' && !row.isHiddenRemark" class="input_box" v-model="row.remark"
              type="textarea" resize="none" rows="2" show-word-limit maxlength="300" clearable placeholder="请输入备注说明">
            </el-input>
            <span v-else-if="!row.isHiddenRemark"> {{ row.remark }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </qDialog>
   <add-Data
      v-if="isvisible"
      :visible.sync="isvisible"
      :editForm="editForm" 
      @confirmGroupIds="confirmGroupIds"
    />
  </content-panel>
</template>

<script>
import moment from "moment";
import { moneyFormat, moneyDelete } from "@/utils";
import addData from "./add-data.vue";
import NP from "number-precision";
import { Select } from "element-ui";
export default {
  components: { addData },
  props: {
    editVisible: {
      type: Boolean,
      required: true,
    },
    editTitle: {
      type: String,
      required: true,
    },
    addInfo: [Object],
  },
  data() {
    return {
      groupIds:[],
      factoryId:"",
      groupNames:[],
      editForm:{},
      isvisible: false,
      height: 0,
      width: "",
      row: 0,
      showFooter: true,
      monthForm: {
        accountingMonth: "",
      },
      monthRules: {},
      tableData: [
        {
          name: "出勤天数",
          value: "attendances",
          attendances: "",
          settlementAmount: "",
          remark: "",
        },
        {
          name: "个人计件",
          value: "personPiece",
          personPiece: "",
          settlementAmount: "",
          remark: "",
          isPiecework: true,
          rowspan: 3, // 标记需要合并3行
          isFirstPiecework: true // 标记是第一行
        },
        {
          name: "集体计件",
          value: "groupPiece",
          groupPiece: "",
          settlementAmount: "",
          remark: "",
          isPiecework: true,
          isHiddenName: true, // 隐藏名称列
          isHiddenRemark: true // 隐藏备注列
        },
        {
          name: "计件工资",
          value: "totalPiece",
          totalPiece: "",
          settlementAmount: "",
          remark: "",
          isPiecework: true,
          isHiddenName: true, // 隐藏名称列
          isHiddenRemark: true // 隐藏备注列
        },
        {
          name: "工资总额",
          value: "salary",
          salary: "",
          settlementAmount: "",
          remark: "",
        },

        {
          name: "其他扣款",
          value: "otherDeduct",
          otherDeduct: "",
          settlementAmount: "",
          remark: "",
        },
        {
          name: "厂服扣款",
          value: "uniformDeduct",
          uniformDeduct: "",
          settlementAmount: "",
          remark: "",
        },
        {
          name: "生活费",
          value: "living",
          living: "",
          settlementAmount: "",
          remark: "",
        },
        {
          name: "保险",
          value: "insurance",
          insurance: "",
          settlementAmount: "",
          remark: "",
        },
        {
          name: "工会",
          value: "labour",
          labour: "",
          settlementAmount: "",
          remark: "",
        },
        {
          name: "借支",
          value: "loan",
          loan: "",
          settlementAmount: "",
          remark: "",
        },
        {
          name: "自离扣款",
          value: "leaveDeduct",
          leaveDeduct: "",
          settlementAmount: "",
          remark: "",
        },
        {
          name: "合计扣款",
          value: "totalDeduct",
          totalDeduct: "",
          settlementAmount: "",
          remark: "",
        },
        {
          name: "实发工资",
          value: "actualSalary",
          actualSalary: "",
          settlementAmount: "",
          remark: "",
        },
      ],
      basicInfo: {},
      monthList: [],
    };
  },
  watch: {
    editTitle: {
      handler(value) {
        switch (value) {
          case "选择月份":
            this.width = "400px";
            this.height = 100;
            this.showFooter = true;
            break;
          case "编辑":
            this.width = "700px";
            this.row = 12;
            this.height = 600;
            this.showFooter = true;
            break;

          default:
            this.width = "900px";
            this.row = 8;
            this.height = 400;
            this.showFooter = false;
            break;
        }
      },
      immediate: true,
    },
  },
  created() {
    this.monthList = this.getMonthList();
    if (this.editTitle == "选择月份") {
      this.monthForm.accountingMonth = this.addInfo.obj.isClearMonth
        ? []
        : this.addInfo.months;
    }
     
    this.$bus.$off("plateTypeList");
    this.$bus.$on("plateTypeList", (data) => {
      const { editData, obj } = data;
      this.editForm= {
        ...obj,
        ...editData[0]
      }  
      this.factoryId=obj.factoryId
      const arr = editData[0];



      this.tableData = this.tableData.reduce((pre, cur) => {
        let item;
        for (const key in arr) {
          if (cur.value === key) {

            if(key==='groupPiece'){
               item = {
                ...cur,
                [cur.value]: arr[cur.value] && arr[cur.value].sysAmount,
                sysAmount: arr[cur.value] && arr[cur.value].sysAmount,
                details:arr[cur.value] && arr[cur.value].details,
                settlementAmount:
                  cur.name == "出勤天数"
                    ? arr[cur.value] && arr[cur.value].settlementAmount
                    : moneyFormat(arr[cur.value] && arr[cur.value].settlementAmount),
                oldSettlementAmount: moneyFormat(arr[cur.value] && arr[cur.value].settlementAmount),
                updateName: arr[cur.value] && arr[cur.value].updateName,
                updateTime: arr[cur.value] && arr[cur.value].updateTime,
                remark: cur.isFirstPiecework ? arr[cur.value] && arr[cur.value].remark || "" : "", // 只有第一行计件工资显示备注
              };
            }else{
                item = {
                ...cur,
                [cur.value]: arr[cur.value] && arr[cur.value].sysAmount,
                sysAmount: arr[cur.value] && arr[cur.value].sysAmount, 
                settlementAmount:
                  cur.name == "出勤天数"
                    ? arr[cur.value] && arr[cur.value].settlementAmount
                    : moneyFormat(arr[cur.value] && arr[cur.value].settlementAmount),
                oldSettlementAmount: moneyFormat(arr[cur.value] && arr[cur.value].settlementAmount),
                updateName: arr[cur.value] && arr[cur.value].updateName,
                updateTime: arr[cur.value] && arr[cur.value].updateTime,
                remark: cur.isFirstPiecework ? arr[cur.value] && arr[cur.value].remark || "" : "", // 只有第一行计件工资显示备注
              };
            }
            
            break;
          }
        }
        if (item) {
          pre.push(item);
        }
        return pre;
      }, []);

      // 计算基础工资：工资总额 - 计件工资汇总
      const salaryData = this.tableData.find(item => item.name === '工资总额');
      const totalPiece = this.tableData.find(item => item.name === '计件工资');
      let baseSalary = 0;
      if (salaryData && totalPiece) {
        const totalSalary = Number(moneyDelete(salaryData.settlementAmount || '0'));

        const totalPiecework = Number(moneyDelete(totalPiece.settlementAmount || '0'));
        baseSalary = NP.minus(totalSalary, totalPiecework);
      }
      this.tableData = this.tableData.map(item => {
        if (item.name === '工资总额') {
          return {
            ...item,
            baseSalary: moneyFormat(baseSalary), // 添加基础工资字段
          };
        }
        return item;
      });
      // ... existing code ...
      data &&
        (this.basicInfo = {
          factoryName: obj.factoryName,
          staffCode: obj.staffCode,
          staffName: obj.staffName,
          accountingMonth: obj.accountingMonth,
          tableName: obj.tableName,
          count: obj.count,
        });
    });
  },
  methods: {
    selectProcesses(){
      this.isvisible=true
    },
    confirmGroupIds(data) {
      this.isvisible=false 
        this.onBlur(2,
          {
            name:'集体计件',
            sysAmount: "0.00",
            details:data.details,
            settlementAmount:moneyFormat(data.totalAmount), 
          }
        )
        
    },
    filterValue(row) {
      const { name, sysAmount } = row;
      if (name == "出勤天数") {
        return sysAmount;
      }

      // 处理计件工资的三个子项
      if (row.isPiecework) {
        const value = row[row.value]; // 获取对应的值
        return moneyFormat(value || sysAmount);
      }

      return moneyFormat(sysAmount);
    },
    // 合并单元格方法
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 数据名称列合并
      if (columnIndex === 1) { // 数据名称列
        if (row.isFirstPiecework) {
          return {
            rowspan: 3,
            colspan: 1
          };
        } else if (row.isHiddenName) {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
      // 备注说明列合并
      if (columnIndex === this.getRemarkColumnIndex()) { // 备注说明列
        if (row.isFirstPiecework) {
          return {
            rowspan: 3,
            colspan: 1
          };
        } else if (row.isHiddenRemark) {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
    },

    // 获取备注列的索引
    getRemarkColumnIndex() {
      // 根据editTitle判断备注列的位置
      return this.editTitle != '编辑' ? 6 : 4;
    },

    getMonthList() {
      // 获取当前时间
      const now = moment();
      // 获取两年前的时间
      const twoYearsAgo = now.clone().subtract(2, "years");
      // 构建一个数组，用于存储所有的月份
      const months = [];
      // 循环遍历每个月份
      for (let m = twoYearsAgo.clone(); m.isSameOrBefore(now); m.add(1, "month")) {
        // 将当前月份添加到数组中
        months.push(m.format("YYYY-MM"));
      }
      return months.reverse();
    },
    filterData(value) {
      return moneyDelete(value);
    },
    filterValue({ name, sysAmount }) {
      return name == "出勤天数" ? sysAmount : moneyFormat(sysAmount);
    },
    init(index) {
      this.tableData = this.tableData.map((m, n) => {
        if (n == index) {
          m = {
            ...m,
            settlementAmount: m.oldSettlementAmount,
          };
        }
        return m;
      });
    },
    clearData(index, row) {
      let tableData = this.tableData.map((m, n) => {
        if (n == index) {
          m = {
            ...m,
            settlementAmount: "0.00",
          };
        }
        return m;
      });

      // 重新计算计件工资汇总
      const personalPiecework = tableData.find(item => item.name === '个人计件');
      const groupPiecework = tableData.find(item => item.name === '集体计件');

      if (personalPiecework && groupPiecework) {
        const personalAmount = Number(moneyDelete(personalPiecework.settlementAmount || '0'));
        const groupAmount = Number(moneyDelete(groupPiecework.settlementAmount || '0'));
        const totalPiecework = NP.plus(personalAmount, groupAmount);

        this.tableData = tableData.map(item => {
          if (item.name === '计件工资') {
            return {
              ...item,
              settlementAmount: moneyFormat(totalPiecework)
            };
          }
          return item;
        });
      }

      // 工资总额相关计算
      const salaryComponents = ['个人计件', '集体计件', '工资总额'];

      if (salaryComponents.includes(row.name)) {
        const salaryItem = this.tableData.find(item => item.name === '工资总额');
        const pieceworkTotal = this.tableData.find(item => item.name === '计件工资');

        if (salaryItem && pieceworkTotal) {
          let finalTotalSalary;
          let newBaseSalary;

          if (['个人计件', '集体计件'].includes(row.name)) {
            // 计件工资修改时：使用baseSalary + 当前计件工资汇总
            const baseSalary = Number(moneyDelete(salaryItem.baseSalary || '0'));
            const pieceworkAmount = Number(moneyDelete(pieceworkTotal.settlementAmount || '0'));

            // 工资总额 = 基础工资 + 当前计件工资汇总
            finalTotalSalary = NP.plus(baseSalary, pieceworkAmount);
            newBaseSalary = baseSalary; // 基础工资不变
          } else if (row.name === '工资总额') {
            // 工资总额直接修改时：重新计算基础工资
            finalTotalSalary = Number(moneyDelete(salaryItem.settlementAmount || '0'));
            const pieceworkAmount = Number(moneyDelete(pieceworkTotal.settlementAmount || '0'));

            // 基础工资 = 工资总额 - 计件工资汇总
            newBaseSalary = NP.minus(finalTotalSalary, pieceworkAmount);
          }

          // 统一校验：确保工资总额不小于计件工资汇总
          const pieceworkAmount = Number(moneyDelete(pieceworkTotal.settlementAmount || '0'));

          if (finalTotalSalary < pieceworkAmount) {
            finalTotalSalary = pieceworkAmount;
            newBaseSalary = 0; // 如果工资总额小于计件工资，基础工资为0
            if (row.name === '工资总额') {
              this.$message.warning('工资总额不能小于计件工资汇总，已自动调整');
            }
          }

          // 更新工资总额和基础工资

          this.tableData = this.tableData.map(item => {
            if (item.name === '工资总额') {
              return {
                ...item,
                settlementAmount: moneyFormat(finalTotalSalary),
                baseSalary: moneyFormat(newBaseSalary) // 同时更新基础工资
              };
            }
            return item;
          });

        }
      }

      let excludeList = ["出勤天数", "工资总额", "合计扣款", "实发工资", "个人计件", "集体计件", "计件工资"];
      const salary = this.tableData.find((item) => item.name == "工资总额").settlementAmount;

      const total = this.tableData
        .filter((item) => !excludeList.includes(item.name))
        .reduce(
          (pre, cur) => {
            pre.totalDeduct = NP.plus(pre.totalDeduct, Number(moneyDelete(cur.settlementAmount)));
            pre.actualSalary = NP.minus(Number(moneyDelete(salary)), pre.totalDeduct);
            return pre;
          },
          { totalDeduct: 0, actualSalary: 0 }
        ); 

      this.tableData = this.tableData.map((item) => {
        Object.keys(total).forEach((key) => {
          if (item.value == key) {
            item.settlementAmount = moneyFormat(total[key]);
          }
        });
        return item;
      });

    },

    onBlur(index, row) {
      console.log(index, row,'index, row')
      if (!row.settlementAmount) return;
      if (row.name == "出勤天数") {
        if (!/^\d{1,2}(\.\d{1,3})?$/.test(moneyDelete(row.settlementAmount))) {
          this.$message({
            message: "小数点前面仅支持2位数,小数点后面仅支持3位数",
            type: "warning",
          });
          this.init(index);
        }
        return;
      }
      if (!/^-?\d{1,5}(\.\d{1,2})?$/.test(moneyDelete(row.settlementAmount))) {
        this.$message({
          message: "小数点前面仅支持5位数,小数点后面仅支持2位数",
          type: "warning",
        });
        this.init(index);
        return;
      }
      this.tableData = this.tableData.map((m, n) => {
        if (n == index) {
          m = {
            ...m,
            settlementAmount: moneyFormat(moneyDelete(row.settlementAmount)),
          };
        }
        return m;
      });

      // 计算计件工资汇总（个人计件 + 集体计件）
      const personPiece = this.tableData.find(item => item.name === '个人计件');
      const groupPiece = this.tableData.find(item => item.name === '集体计件');
      const totalPiece = this.tableData.find(item => item.name === '计件工资');

      if (personPiece && groupPiece && totalPiece) {
        const personalAmount = Number(moneyDelete(personPiece.settlementAmount || '0'));
        const groupAmount = Number(moneyDelete(groupPiece.settlementAmount || '0'));
        const totalPiecework = NP.plus(personalAmount, groupAmount);

        // 更新计件工资汇总
        this.tableData = this.tableData.map(item => {
          if (item.name === '计件工资') {
            return {
              ...item,
              settlementAmount: moneyFormat(totalPiecework)
            };
          }
          return item;
        });
      }

      // 工资总额相关计算
      const salaryComponents = ['个人计件', '集体计件', '工资总额'];

      if (salaryComponents.includes(row.name)) {
        const salaryItem = this.tableData.find(item => item.name === '工资总额');
        const pieceworkTotal = this.tableData.find(item => item.name === '计件工资');

        if (salaryItem && pieceworkTotal) {
          let finalTotalSalary;
          let newBaseSalary;

          if (['个人计件', '集体计件'].includes(row.name)) {
            // 计件工资修改时：使用baseSalary + 当前计件工资汇总
            const baseSalary = Number(moneyDelete(salaryItem.baseSalary || '0'));
            const pieceworkAmount = Number(moneyDelete(pieceworkTotal.settlementAmount || '0'));

            // 工资总额 = 基础工资 + 当前计件工资汇总
            finalTotalSalary = NP.plus(baseSalary, pieceworkAmount);
            newBaseSalary = baseSalary; // 基础工资不变
          } else if (row.name === '工资总额') {
            // 工资总额直接修改时：重新计算基础工资
            finalTotalSalary = Number(moneyDelete(salaryItem.settlementAmount || '0'));
            const pieceworkAmount = Number(moneyDelete(pieceworkTotal.settlementAmount || '0'));

            // 基础工资 = 工资总额 - 计件工资汇总
            newBaseSalary = NP.minus(finalTotalSalary, pieceworkAmount);
          }

          // 统一校验：确保工资总额不小于计件工资汇总
          const pieceworkAmount = Number(moneyDelete(pieceworkTotal.settlementAmount || '0'));
          if (finalTotalSalary < pieceworkAmount) {
            finalTotalSalary = pieceworkAmount;
            newBaseSalary = 0; // 如果工资总额小于计件工资，基础工资为0
            if (row.name === '工资总额') {
              this.$message.warning('工资总额不能小于计件工资汇总，已自动调整');
            }
          }

          // 更新工资总额和基础工资
          this.tableData = this.tableData.map(item => {
            if (item.name === '工资总额') {
              return {
                ...item,
                settlementAmount: moneyFormat(finalTotalSalary),
                baseSalary: moneyFormat(newBaseSalary) // 同时更新基础工资
              };
            }
             if (item.name === '集体计件') {
              return {
                ...item,
                details:row.details
              };
            }
            return item;
          });
        }
      }

      // 统一过滤列表，排除不参与扣款计算的项目
      let excludeList = ["出勤天数", "工资总额", "合计扣款", "实发工资", "个人计件", "集体计件", "计件工资"];
      const salary = this.tableData.find((item) => item.name == "工资总额").settlementAmount;

      const total = this.tableData
        .filter((item) => !excludeList.includes(item.name))
        .reduce(
          (pre, cur) => {
            pre.totalDeduct = NP.plus(pre.totalDeduct, Number(moneyDelete(cur.settlementAmount)));
            pre.actualSalary = NP.minus(Number(moneyDelete(salary)), pre.totalDeduct);
            return pre;
          },
          { totalDeduct: 0, actualSalary: 0 }
        );

      this.tableData = this.tableData.map((item) => {
        Object.keys(total).forEach((key) => {
          if (item.value == key) {
            item.settlementAmount = moneyFormat(total[key]);
          }
        });
        return item;
      }); 

    },
    handleCancel() {
      this.$emit("cancel", {
        type: "cancel",
        isVisible: false,
      });
    },
    handleConfirm() {
      switch (this.editTitle) {
        case "选择月份":
          if (!this.monthForm.accountingMonth.length) {
            return this.$message.error("请选择月份");
          }
          let params = {
            months: this.monthForm.accountingMonth,
            factoryId: this.addInfo.obj.factoryId,
            staffCode: this.addInfo.obj.staffCode,
            type: this.addInfo.obj.type
          };
          this.$api.plateTypeInformation.payrollManagement.monthSalary(params).then(({ data }) => {
            let months = params.months;
            let list = [];
            if (data.filter((item) => item != null).length > 0) {
              data
                .filter((item) => item != null)
                .map((item) => {
                  let findIndex = months.findIndex(
                    (month) => month == item.accountingMonth
                  );
                  months.splice(findIndex, 1);
                });
            }
            months.forEach((month) => {
              list.push({
                accountingMonth: month,
                actualSalary: "0.00",
                personPiece: "0.00",
                groupPiece: "0.00",
                totalPiece: "0.00",
                attendances: "0.00",
                factoryId: "0.00",
                insurance: "0.00",
                labour: "0.00",
                living: "0.00",
                loan: "0.00",
                otherDeduct: "0.00",
                salary: "0.00",
                staffCode: "0.00",
                totalDeduct: "0.00",
                uniformDeduct: "0.00",
                leaveDeduct: "0.00",
              });
            });
            this.$bus.$emit("plateTypeMonth", {
              editData: [...data, ...list].filter((item) => item != null),
              obj: { ...this.addInfo },
            });
          });
          break;
        case "编辑":
          let mapObj = {
            attendances: {
              itemType: "1",
              sortNo: "1",
            },
            salary: {
              itemType: "2",
              sortNo: "2",
            },
            otherDeduct: {
              itemType: "3",
              sortNo: "3",
            },
            uniformDeduct: {
              itemType: "4",
              sortNo: "4",
            },
            living: {
              itemType: "5",
              sortNo: "5",
            },
            insurance: {
              itemType: "6",
              sortNo: "6",
            },
            labour: {
              itemType: "7",
              sortNo: "7",
            },
            loan: {
              itemType: "8",
              sortNo: "8",
            },
            totalDeduct: {
              itemType: "9",
              sortNo: "9",
            },
            actualSalary: {
              itemType: "10",
              sortNo: "10",
            },
            leaveDeduct: {
              itemType: "11",
              sortNo: "11",
            },
            personPiece: {
              itemType: "12",
              sortNo: "12",
            },
            groupPiece: {
              itemType: "13",
              sortNo: "13",
            },
            totalPiece: {
              itemType: "14",
              sortNo: "14",
            },
          };
          let details = []; 
          this.tableData.forEach((item) => {
            for (const key in mapObj) {
              if (item.value === key) {
                if(item.value==='groupPiece'){
                   mapObj[key] = {
                  ...mapObj[key],
                  sysAmount: moneyDelete(item[key]) || "0.00",
                  settlementAmount: item.settlementAmount
                    ? moneyDelete(item.settlementAmount)
                    : "0.00",
                  details:item.details,
                  remark: item.remark || "",
                };
                }else{
                   mapObj[key] = {
                  ...mapObj[key],
                  sysAmount: moneyDelete(item[key]) || "0.00",
                  settlementAmount: item.settlementAmount
                    ? moneyDelete(item.settlementAmount)
                    : "0.00",
                  remark: item.remark || "",
                };
                }
                
              }
            }
          });
          details.push({
            accountingMonth: this.basicInfo.accountingMonth,
            count: this.basicInfo.count,
            ...mapObj,
          });  
          this.$bus.$emit("plateTypeTable", details);
          break;

        default:
          break;
      }
      this.$emit("cancel", {
        type: "confirm",
        isVisible: false,
      });
    },
  },
};
</script>

<style lang="stylus" scoped>
>>>.el-select {
  width: 100%;
}

.edit_header {
  >>>.el-form-item {
    margin-bottom: 5px;
  }
}

>>>.input_box {
  margin: 3px 0;
}

>>>.el-textarea__inner {
  padding-right: 45px;
}
>>> .el-input--suffix .el-input__inner{
  padding-left:10px
}
.el-textarea {
  >>>.el-input__count {
    right: 20px !important;
  }
}
</style>
