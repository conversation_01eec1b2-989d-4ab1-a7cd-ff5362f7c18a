<template>
  <qDialog
    :visible="visible"
    :innerScroll="false"
    :innerHeight="300"
    :title="title"
    width="400px"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <!-- <div v-show="title == '批量确认'">
      是否批量核算当前选中内容，设定在执行月份中全部还清？
    </div> -->
    <div v-if="activeTab == '1'">
      退回后,流程状态变更为未处理,已还款的还款详情无法修改,未还款的还款详情可以编辑,是否退回
    </div>
    <div v-if="activeTab == '2'">退回后，已处理内容会被删除</div>
  </qDialog>
</template>

<script>
export default {
  name: "commmonDialog",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    formData: Object,
    filterParam: {
      type: Object,
      required: {},
    },
    selectall: {
      type: Boolean,
      required: false,
    },
    activeTab:{
      type: String,
      required: true,
    },
  },
  data(){
    return {
      isLoading:false
    }
  },
  methods: {
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      if (this.title == "退回") {
      let retutnApi = this.activeTab == "1" ? "back" : "rollBackNoAccounting";

        this.$api.logisticsInformation.debitAccount[retutnApi]({ id: this.formData.id })
          .then(() => {
            this.$notify.success({
              title: "成功",
              message: "退回成功！",
            });
            this.$emit("cancel", "confirm");
          });
      } else {
        let params = {
          ids: this.formData.ids,
          codes: this.formData.codes,
          status: 0,
          payMonth: this.formData.payMonth,
          isAll:this.selectall?1:0
        };
        if (this.selectall) {
        //全选
        params = {
          status: 0,
          payMonth: this.formData.payMonth,
          isAll: 1,
          ...this.filterParam,
        };
      }
        this.$api.logisticsInformation.debitAccount
          .batchConfirmDebitLedger(params)
          .then(({ data }) => {
            if (data.length > 0) {
              let staffCode = `员工编号:${data
                .map((item) => item.staffCode)
                .join(",")}`;
              this.$notify.error({
                title: "通知",
                message: staffCode + "数据还款信息生成失败," + "请手动生成还款信息!",
              });
              this.$emit("cancel", "confirm");
            } else {
              this.$notify.success({
                title: "成功",
                message: "批量确认成功！",
              });
              this.$emit("cancel", "confirm");
            }
          });
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
