<template>
  <!-- 汇总财务表 -->
  <content-panel>
    <template v-slot:search>
      <search-box
        class="search-box">
        <el-form size="mini"
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          label-position="right">
          <el-form-item
            label="核算月份:"
            prop="accountingMonth">
            <el-date-picker
              @change="onSearch"
              v-model="searchForm.accountingMonth"
              type="month"
              placeholder="请选择日期"
              :clearable="false"
              :picker-options="pickerOptions">
            </el-date-picker>
          </el-form-item>
        </el-form>
        <template
          v-slot:right>
          <el-button
            size="small"
            type="primary"

            @click="onSearch">
            查询</el-button>
          <el-button
            size="small"
            type="warning"
            @click="resetSearchForm">
            重置</el-button>
        </template>
      </search-box>
    </template>
    <table-panel
      ref="tablePanel">
      <template
        v-slot:header-right>
        <el-button
          size="small"
          type="primary"
          @click="handleExport">
          导出</el-button>
      </template>
      <div
        style="margin-bottom:10px">
        <div
          class="header_tableName">
          {{ filterName.factory }}汇总财务表
        </div>
        <div
          class="header_date">
          日期:{{ filterName.date }}
        </div>
        <section>
          <div
            class="header_content_left">
            编制部门:生产财务本部</div>
          <div
            class="header_content_right">
            单位:元</div>
        </section>
      </div>
      <el-table stripe border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :data="tableData"
        :height="maxTableHeight"
        :span-method="objectSpanMethod"
        style="width: 100%"
        class="topTable"
        :default-sort="{ prop: 'pieceWage', order: 'descending' }">
        <el-table-column
          v-for="(item,index) in tableHeader"
          :key="index"
          :label="item.columnName"
          :width="item.width"
          align="center"
          :prop="item.fieldName"
          show-overflow-tooltip>
          <template
            slot-scope="{ row }">
            <span
              style="display: block; width: 100%"
              v-if="['adjust', 'add', 'reduce'].includes(row.serialNumber)">{{ filterTitle(row.serialNumber) }}</span>
            <span
              v-else-if="!isNaN(Number(row[item.fieldName])) && item.fieldName != 'serialNumber'">{{ filterData(row[item.fieldName])}}</span>
            <div
              v-else-if="row[item.fieldName]=='线下核算'">
              <span>{{ row[item.fieldName]}}</span>
              <el-tooltip
                content="线下核算行有汇总金额计算，列的实发金额不参与计算"
                placement="top">
                <i
                  class="el-icon-question"></i>
              </el-tooltip>
            </div>
            <div v-else-if="row[item.fieldName]=='净余留工资'">
              <span>{{ row[item.fieldName]}}</span>
              <el-tooltip
              content="净余留=本月划出+线下核算+提取余留-本月划入+其他划入-其他划出"
              placement="top">
              <i
                class="el-icon-question"></i>
            </el-tooltip>
            </div>
            <span
              v-else>{{ row[item.fieldName] }}
            </span>
          </template>
        </el-table-column>
      </el-table>
    </table-panel>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import { moneyFormat, calculateTableWidth } from "@/utils";
import moment from "moment";
export default {
  name: "softwareSummary",
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      searchForm: {
        accountingMonth: moment().subtract(1, "months").format("YYYY-MM"),
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      tableData: [], //表格数据
      loading: false,
      filterParam: {},
      //表格表头信息
      tableHeader: [],
      resizeOffset: 93,
      list: [],
      adjustList: [],
      reduceTableData: []
    };
  },
  created() {
    this.onSearch();
  },
  computed: {
    filterName() {
      let accountingMonth = this.searchForm.accountingMonth
      return {
        factory: `${moment(accountingMonth).format("YYYY年MM月")} 软体本部`,
        date: `${moment(accountingMonth)
          .startOf("month")
          .format("YYYY年MM月DD日")} - ${moment(accountingMonth)
            .endOf("month")
            .format("YYYY年MM月DD日")}`,
      };
    },
  },
  methods: {
    //获取汇总财务表
    getList() {
      this.loading = true;
      this.$api.softwareReportManagement
        .financialSummary({
          ...this.filterParam,
        })
        .then(
          ({
            data: {
              positiveTerm,
              negativeTerm,
              salaryItems,
              adjustTerm,
              negativeTotalObj,
              positiveTotalObj,
              adjustTotalObj,
              headItems,
              detailList
            },
          }) => {
            let obj = this.filterDetailList(headItems, detailList || [])
            this.list = [
              {
                serialNumber: "add",
                itemName: "",
                itemValue: "",
                remark: "",
              },
            ]
              .concat(positiveTerm)
              .concat([
                {
                  serialNumber: "合计",
                  itemName: "",
                  ...positiveTotalObj,
                },
              ]);
            this.adjustList = [
              {
                serialNumber: "adjust",
                itemName: "",
                itemValue: "",
                remark: "",
              },
            ]
              .concat(adjustTerm)
              .concat([
                {
                  serialNumber: "净余留工资",
                  itemName: "",
                  ...adjustTotalObj,
                },
              ]);
            this.reduceTableData = [
              {
                serialNumber: "reduce",
                itemName: "",
                itemValue: "",
                remark: "",
              },
            ]
              .concat(negativeTerm)
              .concat([
                {
                  serialNumber: "合计",
                  itemName: "",
                  ...negativeTotalObj,
                },
                {
                  serialNumber: "实发工资",
                  itemName: "",
                  ...salaryItems
                },
                {
                  serialNumber: "其中:上卡",
                  itemName: "",
                  ...obj.bankCardObj
                },
                {
                  serialNumber: "其中:现金",
                  itemName: "",
                  ...obj.cashObj
                },
                {
                  serialNumber: "其中:延发",
                  itemName: "",
                  ...obj.delayedObj
                },
                {
                  serialNumber: "其中:自离",
                  itemName: "",
                  ...obj.leaveSelfObj
                },
                {
                  serialNumber: "其中:辞职已结",
                  itemName: "",
                  ...obj.resignationObj
                },
              ]);
            this.tableData = [...this.list, ...this.adjustList, ...this.reduceTableData];
            let items = {
              serialNumber: "100",
            };
            this.tableHeader = headItems.map((item) => {
              if (Object.keys(items).includes(item.fieldName)) {
                Object.keys(items).forEach((key) => {
                  if (key == item.fieldName) {
                    item.width = items[item.fieldName];
                  }
                });
              } else {
                item.width = this.flexWidth(
                  item.fieldName,
                  this.tableData,
                  item.columnName
                );
              }
              return item;
            });
            let totalWidth = this.tableHeader.reduce((pre, cur) => {
              return (pre += Number(cur.width));
            }, 0);
            if (totalWidth <= this.$refs.tableRef.$el.clientWidth) {
              this.tableHeader.forEach((item) => {
                delete item.width;
              });
            }
            // this.getSum()
          }
        )
        .finally(() => {
          this.loading = false;
        });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.filterParam = {};
      this.onSearch();
    },
    //搜索
    onSearch() {
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (key == "accountingMonth" && val) {
          this.filterParam.accountingMonth = moment(val).format("YYYY-MM");
        } else if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      this.pageNum = 1;
      this.getList();
    },
    filterNum() {
      let num = [];
      new Array(7).fill().forEach((item, index) => {
        num.push(this.tableData.length - index - 1);
      });
      return num;
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      //表格合并行
      if (
        rowIndex === 0 ||
        rowIndex == this.list.length ||
        rowIndex == this.list.length + this.adjustList.length
      ) {
        if (columnIndex === 0) {
          return {
            rowspan: 1,
            colspan: this.tableHeader.length,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }
      if (
        rowIndex === this.list.length - 1 ||
        this.filterNum().includes(rowIndex) ||
        rowIndex == this.list.length + this.adjustList.length - 1
      ) {
        //重点在else
        if (columnIndex === 0) {
          return {
            rowspan: 1,
            colspan: 2,
          };
        } else if (columnIndex == 1) {
          return {
            rowspan: 0, //清除就是这俩属性设置为0
            colspan: 0, //清除就是这俩属性设置为0
          };
        }
      }
    },
    filterTitle(value) {
      let params = {
        add:
          "增加项目" +
          (this.list.length < 2 ? this.list.length : `(1-${this.list.length - 2})`),
        adjust:
          "调整项目" +
          (this.adjustList.length < 2
            ? this.adjustList.length
            : `(1-${this.adjustList.length - 2})`),
        reduce:
          "减少项目" +
          (this.reduceTableData.length < 2
            ? this.reduceTableData.length
            : `(1-${this.reduceTableData.length - 8})`),
      };
      return params[value];
    },
    //计算表格列宽度
    flexWidth(...args) {
      return calculateTableWidth(...args);
    },
    filterData(value) {
      if (!value) return '-'
      return moneyFormat(value)
    },
    filterDetailList(headItem, list) {
      let headItems = headItem.filter(item => !['serialNumber', 'itemName'].includes(item.fieldName))
      let detailList = headItems.reduce((pre, cur) => Object.assign(pre, { [cur.fieldName]: 0 }), {})
      let obj = {
        bankCardObj: { ...detailList },
        cashObj: { ...detailList },
        delayedObj: { ...detailList },
        leaveSelfObj: { ...detailList },
        resignationObj: { ...detailList },
      }
      list.forEach(item => {
        for (const i in obj) {
          for (let key in obj[i]) {
            if (key == item.factoryAlias) {
              switch (i) {
                case 'bankCardObj':
                  obj[i][key] = item.bankCardAmount || 0
                  break;
                case 'cashObj':
                  obj[i][key] = item.cashAmount || 0
                  break;
                case 'delayedObj':
                  obj[i][key] = item.delayedAmount || 0
                  break;
                case 'leaveSelfObj':
                  obj[i][key] = item.leaveSelfAmount || 0
                  break;

                default:
                  obj[i][key] = item.resignationAmount || 0
                  break;
              }
            }
          }
        }
      })
      return obj
    },
    //合计
    getSum(it) {
      const sums = [];
      for (const key in it) {
        const values = this.reduceTableData.map((item) => Number(item[key]));
        if (!values.every((value) => isNaN(value))) {
          let num = this.reduceTableData
            .filter((item) => item.number != "合计" && item.number != "实发工资")
            .reduce((prev, curr) => {
              const value = Number(curr[key]);
              if (!isNaN(value)) {
                return prev + curr[key];
              } else {
                return prev;
              }
            }, 0);
          sums.push(num);
        }
      }
      return sums.filter((item) => typeof item == "number" && item > 0);
    },
    //导出
    handleExport() {
      this.$api.softwareReportManagement
        .exportFinancialSummary({...this.filterParam})
        .then((res) => {
            this.$message.success("导出操作成功，请前往导出记录查看详情");
        });
    },
  },
};
</script>
<style lang="stylus" scoped>
ellipsis() {
  font-size: 16px;
  font-weight: 600;
}

.header_tableName {
  ellipsis();
  font-size: 22px;
  text-align: center;
  margin-bottom: 8px;
}

.header_date {
  ellipsis();
  font-size: 18px;
  text-align: center;
}


>>>.table-panel-footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  >ul {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      white-space: nowrap;
      padding: 0 10px;
    }
  }
}

section {
  display: flex;
  justify-content: space-between;

  .header_content_left {
    ellipsis();
  }

  .header_content_right {
    ellipsis();
  }
}
</style>
