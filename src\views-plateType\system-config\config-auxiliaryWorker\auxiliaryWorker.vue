<template>
  <content-panel class="panel-tabs">
    <el-row class="tabs-row">
      <el-col :span="22">
        <el-tabs v-model="activeTab" class="tabs">
         <el-tab-pane v-for="tab in getTabPermission" :key="tab.name" :name="tab.name" :label="tab.label"></el-tab-pane>
        </el-tabs>
      </el-col> 
    </el-row>
     <component :is="activeTab" ref="SortwarePieceworkWageRef"></component>
    <updatePieceworkWageDialog ref="updatePieceworkWageDialogRef" v-if="updateVisible" @confirm="onUpdateConfirm"
      @cancel="onUpdateCancel" :factoryList="tabList" :visible="updateVisible">
    </updatePieceworkWageDialog>
  </content-panel>
</template>

<script>
import {getPermitList} from '@/store/modules/permission'
import AttendanceCalculation from './attendanceCalculation/attendanceCalculation.vue';
import ProductionCalculation from './productionCalculation/productionCalculation.vue'; 
import defaultNmae from "./defaultNmae.vue"; 
const tabPermissions=[
  {
    name: 'attendanceCalculation',
    label: '按出勤计算',
    permission: 'was-customized$systemConfig$plateType$auxiliaryWorker$attendanceCalculation'
  },
  {
    name: 'productionCalculation',
    label: '按产量计算',
    permission:  'was-customized$systemConfig$plateType$auxiliaryWorker$productionCalculation'
  }
] 
export default {
  name: 'PlateTypeAuxiliaryWorker',
  components: {
    AttendanceCalculation,
    ProductionCalculation,   
    defaultNmae
  },
  data() {
    return {
      activeTab: 'defaultNmae',
      updateVisible: false, 
      tabList: []
    };
  },
  created() {
    this.$api.softwareSystemManage.getBasicPermission.getBasicPermissionAll().then((res) => {
      this.tabList = res.data.map((item) => ({
        label: item.name,
        name: item.name,
        id: item.id,
      }));
    });
  }, 
  watch: { 
     getTabPermission: {
        handler(newVal) { 
          if (newVal.length > 0 && this.activeTab=='defaultNmae') {
          this.activeTab = newVal[0].name;
        } 
      },
      deep: true,
      immediate: true,
    },
},
  computed: {
    // 获取当前tab的权限
    getTabPermission() {
      const permitList = getPermitList();  
      return tabPermissions.filter(tab => permitList.includes(tab.permission));
    },
  },
  methods: {
    //导出
    handleExport() {
      let exportForm = JSON.parse(JSON.stringify(this.$refs.SortwarePieceworkWageRef.filterParam));
      this.$api.common
        .doExport(this.activeTab == 'PieceworkWage' ? 'plankPieceWageSystemExport' : this.activeTab == 'GroupDetail' ? 'plankPieceWageGroup' : this.activeTab == 'PersonalDetails' ? 'plankPieceWagePerson' : 'plankPieceWageSystemTotalExport', { ...exportForm })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success("导出操作成功，请前往导出记录查看详情");
          }
        });
    }
  },
};
</script>

<style lang="stylus" scoped>
>>>.table-panel-footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .table_footer {
    overflow: auto;

    >ul {
      display: flex;
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        white-space: nowrap
        padding: 0 10px;
      }
    }
  }
}
.panel-tabs
    position: relative;
.panel-tabs
  >>> .main-area
    padding-top 0
.tabs-row
    display:flex;
    align-items:center;
.tabs-row:after
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 2px;
    background-color: #E4E7ED;
    z-index: 1;
.table-btn-area
    display:flex;
    justify-content:flex-end;
    margin-right:12px;
.tabs
  >>> .el-tabs__header
    margin-bottom 5px
    .el-tabs__nav-wrap::after{
      display:none;
    }
</style>
