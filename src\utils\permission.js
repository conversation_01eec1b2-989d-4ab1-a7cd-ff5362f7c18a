import store from '@/store'

/**
 * @param {Array} value
 * @returns {Boolean}
 * @example see @/views/permission/directive.vue
 */
function checkPermission(value) {
  if (value && value instanceof Array && value.length > 0) {
    const permits = store.getters && store.getters.permits;

    const hasPermission = permits.some(permit => {
      return permit.indexOf(value) > -1;
    })

    if (!hasPermission) {
      return false
    }
    return true
  } else {
    console.error(`need roles! Like v-permission="roleEdit"`)
    return false
  }
}

export {checkPermission};