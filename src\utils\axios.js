import axios from 'axios';
import { baseUrl, httpUrl, TOKEN_EXPIRED_CODE } from '@/utils/constant';
import { setToken, getToken } from '@/utils/auth';
import { isBlob,trimObjectProperties } from '@/utils';
import routeDescMap from '@/utils/route-desc'; 
import { Notification } from 'element-ui';
import store from '@/store';

function logout() {
  Notification.warning({
    message: '当前会话已过期，请重新登录',
    duration: 3000,
    onClose: () => {
      store.dispatch('user/logout').then(() => {
        window.location.replace(`${httpUrl}#/login`);
      });
    }
  });
}

function getInitConfig() {
  const config = {
    baseURL: baseUrl,
    headers: {
      Authorization: getToken(),
      'Content-Type': 'application/json;charset=utf-8'
    }
  };
  return config;
}

function getMenuPath() {
  let locationHash = location.hash.substring(1);
  const questionMarkIndex = locationHash.indexOf('?');
  if (questionMarkIndex > -1) {
    locationHash = locationHash.substring(0, questionMarkIndex);
  }
  return routeDescMap[locationHash] || '';
}

const axiosInstance = axios.create(getInitConfig());

axiosInstance.interceptors.request.use(
  config => {
    config.headers.Referer2 = location.href;
    config.headers.menu = getMenuPath();
    config.headers.Authorization = getToken();  
    
    // 处理请求体数据
    if (config.data) {
       const isFormData = config.data instanceof FormData ||   (typeof FormData === 'function' && FormData.prototype.isPrototypeOf(config.data)) || (typeof config.data.append === 'function');
      if (!isFormData && typeof config.data === 'object') {
        // 普通对象处理：去除空值和空字符串
        config.data = trimObjectProperties(config.data);
        
        if (config.data.filterData && typeof config.data.filterData === 'object') {
          config.data.filterData = trimObjectProperties(config.data.filterData);
        }
        
        if (config.data.paramMap && typeof config.data.paramMap === 'object') {
          config.data.paramMap = trimObjectProperties(config.data.paramMap);
        }
      }
      // FormData 类型不进行任何处理
    } 
    return config;
  },
  err => {
    return Promise.reject(err);
  }
);


axiosInstance.interceptors.response.use(
  response => {
    const accessToken = response.headers['access_token'];
    if (accessToken) {
      // console.log('setToken', accessToken);
      setToken(accessToken);
    }

    const { data } = response;
    if (isBlob(data)) {
      return response;
    }

    // 未返回data属性，或code不是 200
    if (!data) {
      const errorMsg = data.message || '异常的响应数据';
      Notification.error({
        message: errorMsg,
        dangerouslyUseHTMLString: true // 支持 HTML 渲染
      });
      return Promise.reject(errorMsg);
    }

    // token过期，退出登录
    if (+data.code === TOKEN_EXPIRED_CODE) {
      logout();
    } else if (data.code && +data.code !== 200) {
      const errorMsg = data.message || '异常的响应数据';
      Notification.error({
        message: errorMsg,
        dangerouslyUseHTMLString: true // 支持 HTML 渲染
      });
      return Promise.reject(errorMsg);
    }

    // TODO 此处可根据code做错误处理，如超时退出 未鉴权
    return data;
  },
  error => {
    let errorInfo = error.response;
    let accessToken = '';
    let code = '';

    if (errorInfo) {
      accessToken = errorInfo.headers['access_token'];
      code = errorInfo.data && errorInfo.data.code;
    }

    // 错误响应有token时，更新token
    if (accessToken) {
      // console.log('setToken-in-error', accessToken);
      setToken(accessToken);
    }

    if (!errorInfo) {
      errorInfo = {
        data: {
          message: error && error.message || '未知错误'
        }
      };
    }

    // token过期
    if (+code === TOKEN_EXPIRED_CODE) {
      logout();
      return Promise.reject(error);
    }

    // 全局错误通知
    Notification.error({
      message: !errorInfo.data.message ? error.message : errorInfo.data.message,
      dangerouslyUseHTMLString: true // 支持 HTML 渲染
    });
    return Promise.reject(error);
  }
);

export default axiosInstance;