<template>
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form :inline="true" :model="searchForm" ref="searchForm" label-position="right" size="mini">
          <el-form-item label="工厂:" prop="factoryId">
            <el-select v-model="searchForm.factoryId" filterable clearable placeholder="请选择工厂" @change="onSearch">
              <el-option v-for="item in tabList" :key="item.id" :label="item.label" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态:" prop="enable">
            <el-select v-model="searchForm.enable" filterable clearable placeholder="请选择状态" @change="onSearch">
              <el-option label="全部" value=""> </el-option>
              <el-option label="开启" value="1"> </el-option>
              <el-option label="禁用" value="0"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="核算大工序:" prop="bigGroupName">
            <el-input v-model.trim="searchForm.bigGroupName" clearable placeholder="请输入核算大工序">
            </el-input>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary" @click="onSearch">
            查询</el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置</el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-right>
        <div ref="btnRight" style="display: flex; justify-content: space-between">
          <el-button @click="handleAdd" size="small" type="primary">新增
          </el-button>
          <el-button size="small" type="primary" @click="handleExport">
            导出</el-button>
        </div>
      </template>
      <el-table stripe border v-loading="loading" ref="tableRef" highlight-current-row :data="tableData"
        :height="maxTableHeight" style="width: 100%">
        <el-table-column prop="factoryName" label="工厂名称" width="230" align="left">
        </el-table-column>
        <el-table-column prop="name" label="大工序名称" width="300" align="left">
        </el-table-column>
        <el-table-column prop="code" label="大工序编码" width="300" align="left">
        </el-table-column>
        <el-table-column width="120" prop="enable" show-overflow-tooltip label="状态" align="left">
          <template slot-scope="scope">
            <div>
              <el-switch @change="onSwitch(scope.row)" inactive-color="#ff4949" v-model="scope.row.enable"
                :active-text="scope.row.enable == 1 ? '启用' : '禁用'" :active-value="1" :inactive-value="0">
              </el-switch>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="sortIndex" label="排序" width="60" align="left">
        </el-table-column>
        <el-table-column prop="remark" label="备注" align="left" show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button size="small" type="text" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="text" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="pageNum"
        :page-size="pageSize" :total="total" :page-sizes="[30, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper">
      </el-pagination>
    </table-panel>
    <add-dialog v-if="addVisible" :visible="addVisible" :title="title" :editForm="editForm"
      @cancel="handleCancel"></add-dialog>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import { moneyFormat, moneyDelete, calculateTableWidth } from "@/utils";
import moment from "moment";
import addDialog from "./addDialog";
export default {
  name: "plateTypeLargeprocess",
  mixins: [tableMixin, pagePathMixin],
  components: { addDialog },
  data() {
    return {
      searchForm: {
        factoryId: "",
        enable: "",
        bigGroupName: "",
      },
      tabList: [],
      tableData: [],
      loading: false,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      filterParam: {},
      addVisible: false,
      title: "",
      editForm: {},
    };
  },
  created() {
    this.$api.plateTypeSystemManage.getBasicPermission
      .getQuFactory({ moduleId: 3 })
      .then((res) => {
        if (res.code === 200 && res.data) {
          this.tabList = res.data.map((item) => ({
            label: item.name,
            name: item.name,
            id: item.id,
            process: item.process,
          }));
        }
      });
    this.getList();
  },
  methods: {
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0)
          this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
    },
    getList() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        filterData: {
          ...this.searchForm,
        },
      };
      this.$api.plateTypeSystemManage.largeprocess
        .listBigGroup(params)
        .then((res) => {
          this.tableData = res.data.list;
          this.total = res.data.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    onSwitch({ id, enable }, value) {
      this.$api.plateTypeSystemManage.getBasicPermission
        .groupEnable({ id, enable })
        .then(({ success }) => {
          if (enable == 1) {
            this.$notify.success({
              title: "成功",
              message: "启用成功",
            });
          } else if (enable == 0) {
            this.$notify.success({
              title: "成功",
              message: "禁用成功",
            });
          }
        })
        .finally(() => {
          this.getList();
        });
    },
    //新增
    handleAdd() {
      this.title = "新增";
      this.addVisible = true;
      this.editForm = {};
    },
    //编辑
    handleEdit(row) {
      const { name, id, enable, parentId, sortIndex } = row;
      this.title = "编辑";
      this.addVisible = true;
      this.editForm = {
        id: id,
        parentId,
        groupName: name || "",
        enable,
        remark: row.remark || "",
        code: row.code || "",
        factoryId: row.factoryId || "",
        sortIndex: sortIndex
      };
    },
    //计算表格列宽度
    flexWidth(...args) {
      return calculateTableWidth(...args);
    },
    //删除
    handleDelete({ id }) {
      this.$confirm("此操作将永久删除该条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$api.plateTypeSystemManage.largeprocess
            .deleteBigGroup({ id })
            .then((res) => {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getList();
            });
        })
        .catch(() => { });
    },
    //导出
    handleExport() {
      let params = {
        factoryId: this.searchForm.factoryId,
        ...this.filterParam,
      };
      this.$api.common.doExport("exportPlankBigGroup", { ...params }).then((res) => {
        if (res.code == 200) {
          this.$message.success("导出操作成功，请前往导出记录查看详情");
        }
      });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    handleCancel(type) {
      this.addVisible = false;
      if (type == "cancel") return;
      this.getList();
    },
    //分页   页数条数改变函数
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    handleCurrentChange(val) {
      this.pageNum = val;
      this.getList();
    },
  },
};
</script>

<style lang="stylus" scoped></style>
<style>
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

input[type="number"] {
  -moz-appearance: textfield;
}
</style>
