// 请求cookie键名
const Authorizition = 'accessToken';
// 会话过期错误码
const TOKEN_EXPIRED_CODE = 10001;

const domain = window.location.origin;
// 接口baseURL
const baseUrl = process.env.BUILD_ENV === 'local' ? '/api' : `${domain}/gw/`;
const httpUrl = `${domain}/`;

const numberCompareEnum = {
  greater: '>',        // 大于
  less: '<',           // 小于
  equal: '=',          // 等于
  greaterOrEqual: '>=', // 大于等于
  lessOrEqual: '<=',    // 小于等于
  notEqual: '!=',       // 不等于
  range: 'between'          // 介于A-B
};
const productionCategorysEnum = [
  {
    value: '生产辅助类',
    label: '生产辅助类',
  },
  {
    value: '生产作业类',
    label: '生产作业类',
  },
  {
    value: '班组长',
    label: '班组长',
  },
  {
    value: '辅助类',
    label: '辅助类',
  },
  {
    value: '技工类',
    label: '技工类',
  },
  {
    value: '业务类',
    label: '业务类',
  },
  {
    value: '管理类',
    label: '管理类',
  }
];

const exceptionEnum = [
  { label: "计件为空", value: "计件为空" },
  { label: "划出工资大于3000", value: "划出工资大于3000" },
  { label: "日均应发差异率大于20%", value: "日均应发差异率大于20%" },
  { label: "日均实发差异率大于20%", value: "日均实发差异率大于20%" },
  { label: "日均计件差异率大于20%", value: "日均计件差异率大于20%" },
  { label: "总出勤天数小于15天", value: "总出勤天数小于15天" },
];
export { Authorizition, baseUrl, httpUrl, TOKEN_EXPIRED_CODE, numberCompareEnum, productionCategorysEnum, exceptionEnum };
