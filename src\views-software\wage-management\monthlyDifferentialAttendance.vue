<template>
  <content-panel>
    <!-- 月度差异考勤报表 -->
    <template v-slot:search>
      <search-box class="search-box">
        <el-form size="mini" :inline="true" :model="searchForm" label-width="90px" ref="searchForm" class="rangeTime"
          label-position="right">
          <el-form-item label="核算工厂:" prop="factoryId">
            <el-select v-model="searchForm.factoryId" filterable clearable placeholder="请选择核算工厂"
              @change="onChangeFactory">
              <el-option v-for="item in tabList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="核算班组:" prop="groupId">
            <el-select v-model="searchForm.groupId" filterable clearable placeholder="请选择核算班组"
              @change="onChangeProcess">
              <el-option v-for="item in process.processList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input clearable v-model.trim="searchForm.staffName" size="mini" @keyup.enter.native="onSearch">
              <template slot="append">
                <search-batch @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)" ref="childrenStaffNames" titleName="员工姓名" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input v-model.trim="searchForm.staffCode" size="mini" clearable @keyup.enter.native="onSearch">
              <template slot="append">
                <search-batch @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)" ref="childrenStaffCodes" titleName="厂牌编号" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item   label="核算月份:" prop="accountingMonth">
            <el-date-picker :clearable="false" v-model="searchForm.accountingMonth" value-format="yyyy-MM" type="month"
              placeholder="核算月份"@change="onSearch()" clearable>
            </el-date-picker> 
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary" @click="onSearch">
            查询
          </el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <el-table stripe border v-loading="loading" ref="tableRef" highlight-current-row :height="maxTableHeight"
        :data="tableData" key="tableKey">
          <!-- 渲染处理后的表头 -->
       <div v-for="(item, index) in columnList"  :key="tableKey">
          <!-- 如果是普通列 -->
          <el-table-column  :prop="item.fieldName" :label="item.columnName" :width="item.width"
            :fixed="item.fixed" align="center" show-overflow-tooltip>
            <template slot="header"> 
              <div >{{ item.columnName }}
              </div>
            </template>
            <template slot-scope="{ row }"> 
              <span  >{{  row[item.fieldName] }}</span>
            </template>

          </el-table-column> 
        </div>
      </el-table>
      <template v-slot:footer>
        <div class="table_footer">
         
        </div>
        <div style="text-align: right">
          <el-pagination @size-change="onSizeChange" @current-change="onNumChange" :current-page="pageNum"
            :page-size="pageSize" :total="total" :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>
        </div>
      </template>
    </table-panel> 
  </content-panel>
</template>

<script>
import moment from "moment";
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import { moneyFormatZh,calculateTableWidth } from "@/utils"; 


export default {
  name: "softwarePieceworkWage",
  mixins: [tableMixin, pagePathMixin], 
  data() {
    return {
      searchForm: {
        staffCode: "",
        groupId: '',
        staffName: "",
        factoryId: "",
        accountingMonth: moment().subtract(1, "month").format("YYYY-MM")
      },
      pieceworkWageInfo: {},//统计信息
      tabList: [], 
      filterParam: {},
      params: {},
      tableData: [],  
      loading: false,
      resizeOffset: 55,
      pageSize: 50,
      columnList: [], // 原始表头数据
      pageNum: 1,
      total: 0,
      tableKey: "",
    };
  },
  computed: {
    process() {
      return {
        processList:
          (this.searchForm.factoryId &&
            this.tabList.find((item) => item.id == this.searchForm.factoryId)
              .process) ||
          [],
      };
    },
  },
  created() {
    this.$api.softwareSystemManage.getBasicPermission.getBasicPermissionAll().then((res) => {
      this.tabList = res.data.map((item) => ({
        label: item.name,
        name: item.name,
        id: item.id,
         ...item
      }));
    }); 
    this.onSearch();
  },
  filters: {
    filterData(value) {
      return !value ? "-" : moneyFormatZh(value);
    },
    filterDataPiece(value) {
      return !value ? "0" : moneyFormatZh(value);
    },
  },
  methods: {
    //获取表头
    getMonethHeader() {
      this.$api.softwarePieceWageSystem.getMonthAttendHeader({
        ...this.filterParam 
      }).then(({data}) => { 
          let items = {
            factoryName: "100", 
            groupName:"100",
            staffName: "100",
            avgWorkDays:"260",
            lowAttendanceRatio:"180"
          }; 
          this.columnList = data.map((item) => {
            if (Object.keys(items).includes(item.fieldName)) {
              Object.keys(items).forEach((key) => {
                if (key == item.fieldName) {
                  item.width = items[item.fieldName];
                }
              });
            } else {
              item.width = this.flexWidth(
                item.fieldName,
                this.tableData,
                item.columnName
              );
            }
            if (["核算工厂", "核算班组", "员工姓名"].includes(item.columnName)) {
              item.fixed = "left";
            }
            return item;
          });
      });
    },
    onChangeFactory() {
      this.onSearch(); 
    },
    onChangeProcess() {
      this.onSearch();
    }, 
    //获取月度考勤差异分析列表
    async getList() {
      this.loading = true;
      await this.getMonethHeader();
      this.loading = true;
      this.$api.softwarePieceWageSystem
        .getMonthAttendList({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: {
            ...this.filterParam,
            ...this.params,
          },
        })
        .then(({ data: { list, total } }) => {
          this.tableData = list;
          this.total = total;
          this.tableKey = Math.random() + Date.now();
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.searchForm.accountingMonth = moment().subtract(1, "month").format("YYYY-MM") || '';
      this.params = {};
      this.onSearch();

    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (key == 'accountingMonth' && val) {
          this.filterParam.accountingMonth = moment(val).format('YYYY-MM') || '';
        }  else if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
     //计算表格列宽度
    flexWidth(...args) {
      return calculateTableWidth(...args);
    },
    onSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(val) {
      this.pageNum = val;
      this.getList();
    },
  },
};
</script>

<style lang="stylus" scoped>
ul {
  list-style: none;
  display: flex;
  align-items: center;
  padding: 0;
  margin: 0;
  margin-top:10px
  li{
    margin-right: 10px;
  }
}
</style>
