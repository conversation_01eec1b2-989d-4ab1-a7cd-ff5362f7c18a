<template>
  <qDialog
    :visible="visible"
    :innerScroll="true"
    :innerHeight="650"
    title="查看详情"
    width="1000px"
    :modal-append-to-body="false"
    append-to-body
    :isShowCancelBtn="false"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <common title="员工信息">
      <el-form
        :model="staffInfo"
        label-width="106px"
        ref="staffInfo"
        size="small"
      >
        <el-row>
          <el-col :span="8">
            <el-form-item label="员工姓名:" prop="staffName">
              {{ staffInfo.staffName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="厂牌编号:" prop="staffCode">
              {{ staffInfo.staffCode }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="身份证号:" prop="idCard">
              <template>
                <span>{{ staffInfo.idCard }}</span>
                <i
                  v-if="staffInfo.idCard"
                  :class="[
                    'iconfont',
                    showType ? 'icon-guanbi' : ' icon-yincangmima',
                  ]"
                  @click="toggle"
                ></i>
              </template>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="工厂名称:" prop="factoryId">
              {{ staffInfo.factoryId }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="工序名称:" prop="processId">
              {{ staffInfo.processId }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </common>
    <common title="表单信息">
      <el-form
        :model="formData"
        label-width="106px"
        ref="formData"
        size="small"
      >
        <el-row>
          <el-col :span="8">
            <el-form-item label="单据编号:" prop="code">
              {{ formData.no }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="制表人员:" prop="idCard">
              {{ formData.tableName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="办理日期:" prop="staffName">
              {{ formData.handleTime }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="工资表类型:" prop="type">
              {{ formData.type }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="审核人员:">
              {{ formData.auditName }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </common>
    <div>
      <div class="header">
        <span>工资单详情</span>
        <div class="btn">
          <el-button size="small" type="primary" @click="modifyRecord">
            修改记录
          </el-button>
        </div>
      </div>
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        :height="200"
        highlight-current-row
        :data="tableList"
        :row-class-name="tableRowClassName"
        @select-all="handleSelectionAll"
        @selection-change="handleSelectionChange"
      >
        <el-table-column width="40" type="selection"> </el-table-column>
        <el-table-column
          prop="accountingMonth"
          label="年/月"
          width="75"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column label="津贴项目" align="center" show-overflow-tooltip>
          <el-table-column
            label="工资总额"
            prop="totalWage"
            align="left"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <span
                :style="{
                  color:
                    filterData(row.totalWage.sysAmount) ==
                    filterData(row.totalWage.settlementAmount)
                      ? '#0BB78E'
                      : 'red',
                }"
              >
                {{ row.totalWage.settlementAmount | moneyFormat }}</span
              >
            </template>
          </el-table-column>
          <el-table-column
            label="奖励"
            prop="rewards"
            align="left"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <span
                :style="{
                  color:
                    filterData(row.rewards.sysAmount) ==
                    filterData(row.rewards.settlementAmount)
                      ? '#0BB78E'
                      : 'red',
                }"
              >
                {{ row.rewards.settlementAmount | moneyFormat }}</span
              >
            </template>
          </el-table-column>
          <el-table-column
            label="补贴"
            prop="subsidy"
            align="left"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <span
                :style="{
                  color:
                    filterData(row.subsidy.sysAmount) ==
                    filterData(row.subsidy.settlementAmount)
                      ? '#0BB78E'
                      : 'red',
                }"
              >
                {{ row.subsidy.settlementAmount | moneyFormat }}</span
              >
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column
          :label="`应得工资\n总额`"
          prop="salary"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <span
              :style="{
                color:
                  filterData(row.salary.sysAmount) ==
                  filterData(row.salary.settlementAmount)
                    ? '#0BB78E'
                    : 'red',
              }"
            >
              {{ row.salary.settlementAmount | moneyFormat }}</span
            >
          </template>
        </el-table-column>
        <el-table-column label="扣款项目" align="center" show-overflow-tooltip>
          <el-table-column
            label="处罚"
            prop="punishment"
            align="left"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <span
                :style="{
                  color:
                    filterData(row.punishment.sysAmount) ==
                    filterData(row.punishment.settlementAmount)
                      ? '#0BB78E'
                      : 'red',
                }"
              >
                {{ row.punishment.settlementAmount | moneyFormat }}</span
              >
            </template>
          </el-table-column>
          <el-table-column
            label="成本赔偿"
            prop="costCompensation"
            align="left"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <span
                :style="{
                  color:
                    filterData(row.costCompensation.sysAmount) ==
                    filterData(row.costCompensation.settlementAmount)
                      ? '#0BB78E'
                      : 'red',
                }"
              >
                {{ row.costCompensation.settlementAmount | moneyFormat }}</span
              >
            </template>
          </el-table-column>
          <el-table-column
            label="低耗扣款"
            prop="lowCostDeduction"
            align="left"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <span
                :style="{
                  color:
                    filterData(row.lowCostDeduction.sysAmount) ==
                    filterData(row.lowCostDeduction.settlementAmount)
                      ? '#0BB78E'
                      : 'red',
                }"
              >
                {{ row.lowCostDeduction.settlementAmount | moneyFormat }}</span
              >
            </template>
          </el-table-column>
          <el-table-column
            label="平板扣款"
            prop="tabletDeduction"
            align="left"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <span
                :style="{
                  color:
                    filterData(row.tabletDeduction.sysAmount) ==
                    filterData(row.tabletDeduction.settlementAmount)
                      ? '#0BB78E'
                      : 'red',
                }"
              >
                {{ row.tabletDeduction.settlementAmount | moneyFormat }}</span
              >
            </template>
          </el-table-column>
          <el-table-column
            label="工会费"
            prop="unionFees"
            align="left"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <span
                :style="{
                  color:
                    filterData(row.unionFees.sysAmount) ==
                    filterData(row.unionFees.settlementAmount)
                      ? '#0BB78E'
                      : 'red',
                }"
              >
                {{ row.unionFees.settlementAmount | moneyFormat }}</span
              >
            </template>
          </el-table-column>
          <el-table-column
            label="社保"
            prop="socialSecurity"
            align="left"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <span
                :style="{
                  color:
                    filterData(row.socialSecurity.sysAmount) ==
                    filterData(row.socialSecurity.settlementAmount)
                      ? '#0BB78E'
                      : 'red',
                }"
              >
                {{ row.socialSecurity.settlementAmount | moneyFormat }}</span
              >
            </template>
          </el-table-column>
          <el-table-column
            label="公积金"
            prop="reserveFund"
            align="left"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <span
                :style="{
                  color:
                    filterData(row.reserveFund.sysAmount) ==
                    filterData(row.reserveFund.settlementAmount)
                      ? '#0BB78E'
                      : 'red',
                }"
              >
                {{ row.reserveFund.settlementAmount | moneyFormat }}</span
              >
            </template>
          </el-table-column>
          <el-table-column
            label="考勤扣款"
            prop="attendanceDeduction"
            align="left"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <span
                :style="{
                  color:
                    filterData(row.attendanceDeduction.sysAmount) ==
                    filterData(row.attendanceDeduction.settlementAmount)
                      ? '#0BB78E'
                      : 'red',
                }"
              >
                {{
                  row.attendanceDeduction.settlementAmount | moneyFormat
                }}</span
              >
            </template>
          </el-table-column>
          <el-table-column
            label="工装扣款"
            prop="uniformDeduction"
            align="left"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <span
                :style="{
                  color:
                    filterData(row.uniformDeduction.sysAmount) ==
                    filterData(row.uniformDeduction.settlementAmount)
                      ? '#0BB78E'
                      : 'red',
                }"
              >
                {{ row.uniformDeduction.settlementAmount | moneyFormat }}</span
              >
            </template>
          </el-table-column>
          <el-table-column
            label="个人所得税"
            prop="individualIncomeTax"
            align="left"
            width="100"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <span
                :style="{
                  color:
                    filterData(row.individualIncomeTax.sysAmount) ==
                    filterData(row.individualIncomeTax.settlementAmount)
                      ? '#0BB78E'
                      : 'red',
                }"
              >
                {{
                  row.individualIncomeTax.settlementAmount | moneyFormat
                }}</span
              >
            </template>
          </el-table-column>
          <el-table-column
            label="自离扣款"
            prop="leaveDeduct"
            align="left"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <span
                :style="{
                  color:
                    filterData(row.leaveDeduct.sysAmount) ==
                    filterData(row.leaveDeduct.settlementAmount)
                      ? '#0BB78E'
                      : 'red',
                }"
              >
                {{ row.leaveDeduct.settlementAmount | moneyFormat }}</span
              >
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column
          :label="`实发工资\n(元)`"
          prop="actualSalary"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <span
              :style="{
                color:
                  filterData(row.actualSalary.sysAmount) ==
                  filterData(row.actualSalary.settlementAmount)
                    ? '#0BB78E'
                    : 'red',
              }"
            >
              {{ row.actualSalary.settlementAmount | moneyFormat }}</span
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="table-footer">
        <div id="table_footer">
          <div class="footer_left">
            <span>合计人民币（小写）:</span>
            <span>{{ smallNum }}</span>
          </div>
          <div class="footer_left">
            <span>合计人民币（大写）:</span>
            <span>{{ bigNum }}</span>
          </div>
        </div>
        <div class="form_content">
          <span style="display: block; padding: 10px 0"
            >备注说明:1.工资总额=应发工资-奖励;2.应得工资总额=工资总额+奖励。</span>
          <span>{{ remarks }}</span>
        </div>
      </div>
    </div>
  </qDialog>
</template>
<script>
import common from "./component/common";
import salaryDetails from "./component/salaryDetails";
import { getSum, dealBigMoney } from "./common";
import { moneyFormat } from "@/utils";
export default {
  name: "addPayroll",
  mixins: [salaryDetails],
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    detailsInfo: [Object],
  },
  components: { common },
  data() {
    return {
      //员工信息
      staffInfo: {
        staffName: "",
        staffCode: "",
        idCard: "",
        idCardOrigin: "",
        idCardDecoded: "",
        factoryId: "",
        processId: "",
      },
      //表单信息
      formData: {
        no: "",
        tableName: "",
        handleTime: "",
        type: "",
        auditName: "",
      },
      //工资条类型
      payrollOptions: Object.freeze([
        {
          name: "辞职工资",
          value: "1",
        },
        {
          name: "自离工资",
          value: "2",
        },
        {
          name: "延发工资",
          value: "3",
        },
        {
          name: "其他工资",
          value: "4",
        },
      ]),
      idCardRsa: "",
      remarks: "",
      commonTitle: "",
      commonVisible: false,
      showType: false,
    };
  },
  created() {
    this.getDetail();
  },
  methods: {
    //特殊工资单详情
    async getDetail() {
      const { data } =
        await this.$api.logisticsInformation.payrollManagement.salaryDetail({
          id: this.detailsInfo.id,
        });
      this.idCardRsa = data.idCardRsa;
      this.remarks = data.remark;
      this.staffInfo = {
        staffName: data.staffName || "",
        staffCode: data.staffCode || "",
        idCard: data.idCard,
        idCardOrigin: data.idCard || "",
        idCardDecoded: "",
        factoryId: data.factoryName || "",
        processId: data.processName || "",
      };
      this.formData = {
        no: data.no || "",
        tableName: data.tableName || "",
        handleTime: data.handleTime || "",
        type:
          data.type &&
          this.payrollOptions.find((item) => item.value == data.type).name,
        auditName: data.auditName || "",
      };
      data.list.forEach((v) => {
        //没有自离扣款手动添加
        if (!v.leaveDeduct) {
          v.leaveDeduct = {
            settlementAmount: "0.00",
            sysAmount: "0.00",
          };
        }
      });
      this.tableList =
        (data.list &&
          data.list.sort(
            (a, b) =>
              new Date(a.accountingMonth).getTime() -
              new Date(b.accountingMonth).getTime()
          )) ||
        [];
      this.smallNum = moneyFormat(getSum(this.tableList));
      this.bigNum = dealBigMoney(this.smallNum);
    },
    //身份证解码
    toggle() {
      if (this.showType) {
        this.staffInfo.idCard = this.staffInfo.idCardOrigin;
        this.showType = false;
      } else {
        if (this.staffInfo.idCardDecoded) {
          this.staffInfo.idCard = this.staffInfo.idCardDecoded;
          this.showType = true;
          return;
        }
        this.$api.logisticsInformation.employee
          .decrypt(this.idCardRsa)
          .then((res) => {
            this.staffInfo.idCard = res.data;
            this.staffInfo.idCardDecoded = res.data;
            this.showType = true;
          });
      }
    },
    handleCancel() {
      this.$emit("cancel", {
        type: "cancel",
        isVisible: false,
      });
    },
    handleConfirm() {
      this.$emit("cancel", {
        type: "cancel",
        isVisible: false,
      });
    },
  },
};
</script>

<style lang="stylus" scoped>
>>>.el-form-item__label {
  text-align: left;
}

>>>.el-input-group__append {
  background: #24c69a;
  color: #fff;

  .el-button {
    padding: 5px 10px;
  }
}

>>>.el-select {
  width: 100%;
}

.el-col {
  padding-right: 10px;
}

>>>.el-table__header .el-table-column--selection {
  .el-checkbox {
    visibility: hidden;
    z-index: 99999;
  }
}

.processCode {
  >>>.el-form-item__label {
    &::before {
      content: '*';
      color: #F23D20;
      margin-right: 4px;
      visibility: hidden;
    }
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;

  >span {
    color: #24c69a;
    font-weight: bold;
    font-size: 14px;
  }
}

.table-footer {
  margin-top: 10px;

  #table_footer {
    display: flex;
    justify-content: space-between;
  }
}

>>>.el-table__body {
  .table_row {
    .el-table__cell {
      background: pink !important;
    }
  }
}
>>>.el-table th.el-table__cell > .cell {
    white-space: pre;
}
</style>
