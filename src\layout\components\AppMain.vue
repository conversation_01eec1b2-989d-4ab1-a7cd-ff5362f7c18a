<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view :key="key" />
      </keep-alive>
    </transition>
  </section>
</template>

<script>
export default {
  name: 'AppMain',
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.$route.path
    }
  }
}
</script>

<style lang="stylus" scoped>
.app-main
  /* 50= navbar  50  */
  height: calc(100% - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;

.fixed-header+.app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 82 = navbar + tags-view = 50 + 32 */
    height: calc(100% - 65px);
  }

  .fixed-header+.app-main {
    padding-top: 65px;
  }
}
</style>

