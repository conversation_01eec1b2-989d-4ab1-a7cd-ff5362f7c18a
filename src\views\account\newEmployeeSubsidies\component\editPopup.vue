<template>
  <qDialog
    :visible="visible"
    :innerScroll="true"
    :innerHeight="420"
    :title="title"
    width="850px"
    :isLoading="isLoading"
    @cancel="editCancel"
    @confirm="handleConfirm"
    :before-close="editCancel"
  >
    <el-form
      :model="addForm"
      ref="addForm"
      label-width="120px"
      :rules="rules"
      size="small"
    >
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item class="processCode" label="报名日期:">
            {{ addForm.startDate }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="processCode" label="应聘人员:">
            {{ addForm.staffName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item class="processCode" label="厂牌编号:">
            {{ addForm.staffCode }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item
            class="processCode"
            label="应聘人身份证号:"
            label-width="134px"
          >
            {{ addForm.idCard }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="processCode" label="应聘岗位:">
            {{ addForm.post }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item class="processCode" label="应聘组织:">
            {{ addForm.orgAbbr }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="processCode" label="现工序:">
            {{ addForm.curProcess }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item class="processCode" label="现子工序:">
            {{ addForm.curSubProcess }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="processCode" label="现班组:">
            {{ addForm.curShiftGroup }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item
            class="processCode"
            label="应聘组织全路径:"
            label-width="134px"
          >
            {{ addForm.orgPath }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item class="processCode" label="现部门全路径:">
            {{ addForm.curOrgPath }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="补贴类型:" prop="subsidyType">
            <el-select
              v-model="addForm.subsidyType"
              filterable
              clearable
              placeholder="请选择补贴类型"
            >
              <el-option
                v-for="item in subsidy"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="熟练程度:"
            prop="masteryLevel"
            v-if="addForm.subsidyType != '无补贴'"
          >
            <el-select
              v-model="addForm.masteryLevel"
              filterable
              clearable
              placeholder="请选择熟练程度"
            >
              <el-option
                v-for="item in skilledList"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </qDialog>
</template>

<script>
export default {
  name: "detailsPopup",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    formData: Object,
    title: String,
  },
  data() {
    return {
      addForm: {
        masteryLevel: "",
        subsidyType: "",
      },
      isLoading: false,
      rules: {
        subsidyType: [
          { required: true, message: "请选择补贴类型", trigger: "change" },
        ],
        masteryLevel: [
          { required: true, message: "请选择熟手程度", trigger: "change" },
        ],
      },
      subsidy: Object.freeze([
        {
          name: "新手补贴",
          value: "新手补贴",
        },
        {
          name: "新员工补贴",
          value: "新员工补贴",
        },
        {
          name: "熟手补贴",
          value: "熟手补贴",
        },
        {
          name: "无补贴",
          value: "无补贴",
        },
      ]),
    };
  },
  computed: {
    skilledList() {
      // this.addForm.masteryLevel = "";
      let name = this.addForm.subsidyType;

      if (name == "新手补贴" || name == "新员工补贴") {
        return [{ name: "生手", value: "生手" }];
      }
      if (name == "熟手补贴") {
        return [
          { name: "熟手A", value: "熟手A" },
          { name: "熟手B", value: "熟手B" },
          { name: "标准", value: "标准" },
        ];
      }
    },
  },
  created() {
    const {
      orgAbbr,
      id,
      startDate,
      staffName,
      staffCode,
      idCard,
      post,
      curProcess,
      curSubProcess,
      curShiftGroup,
      orgPath,
      curOrgPath,
      subsidyType,
      masteryLevel,
    } = this.formData;
    this.addForm = {
      orgAbbr,
      id,
      startDate,
      staffName,
      staffCode,
      idCard,
      post,
      curProcess,
      curSubProcess,
      curShiftGroup,
      orgPath,
      curOrgPath,
      subsidyType,
      masteryLevel,
    };
  },
  methods: {
    editCancel() {
      this.$emit("cancel", {
        type: "cancel",
        visible: false,
      });
    },
    handleConfirm() {
      this.$refs.addForm.validate((valid) => {
        if (!valid) return;
        this.isLoading = true;
        let params = {
          masteryLevel:this.addForm.subsidyType == '无补贴' ? "" : this.addForm.masteryLevel,
          subsidyType:this.addForm.subsidyType,
          id:this.addForm.id,
          startDate:this.addForm.startDate,
          staffCode:this.addForm.staffCode,
        };
        this.$api.information.newEmployeeSubsidies
          .newstaffedit(params)
          .then((data) => {
            this.$notify.success({
              title: "成功",
              message: "编辑成功",
            });
            this.$emit("cancel", {
              type: "confirm",
              visible: false,
            });
          })
          .finally(() => {
            this.isLoading = false;
          });
      });
    },
  },
};
</script>

<style lang="stylus" scoped>
.processCode {
  >>>.el-form-item__label {
    &::before {
      content: '*';
      color: #F23D20;
      margin-right: 4px;
      visibility: hidden;
    }
  }
}
</style>
