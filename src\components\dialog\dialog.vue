<template>
  <div>
    <el-dialog
      :title="option.title"
      :visible.sync="dialogVisible"
      :width="option.width"
      :append-to-body="true"
      :close-on-click-modal="option.closeOnClickModal"
      @closed="handleClosed"> <!--:destroy-on-close="true" 此属性不可用，会导致父组件无法获取model内的数据。-->
      <slot name="content"></slot>
      <slot name="footer" slot="footer" class="dialog-footer"></slot>
      <span slot="footer" class="dialog-footer" v-if="option.showButton">
        <el-button @click="cancel" size="small" v-if="option.showCancel">{{option.cancelText}}</el-button>
        <el-button type="primary" @click="confirm" size="small" v-if="option.showConfirm">{{option.confirmText}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  model: {
    prop: 'visible',
    event: 'visibleUpdate'
  },
  props: {
    visible: {
      default() {
        return false
      },
      type: Boolean
    },
    config: {
      default() {
        return {}
      },
      type: Object
    }
  },
  data() {
    return {
      dialogVisible: false,
      option: {
        title: '提示',
        width: '50%',
        cancelText: '取消',
        confirmText: '确定',
        showButton: true,
        showConfirm: true,
        showCancel: true,
        //dialog btn 回调，true 需要手动在$emit抛出的function内关闭, false 自动关闭
        cancelCallback: false,
        confirmCallback: false,
        closeOnClickModal: false
      }
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val;
      },
      immediate: true
    },
    config: {
      handler(val) {
        this.initConfig();
      },
      immediate: true
    }
  },
  methods: {
    //遍历config
    initConfig() {
      Object.keys(this.config).forEach(key => {
        this.option[key] = this.config[key];
      });
    },
    cancel() {
      if(this.option.cancelCallback) {
        this.$emit('dialogCancel', null);
      }else{
        this.dialogVisible = false;
      }
    },
    confirm() {
      if(this.option.confirmCallback) {
        this.$emit('dialogConfirm', null);
      }else{
        this.dialogVisible = false;
      }
    },
    handleClosed() {
      this.$emit('visibleUpdate', false);
    }
  }
}
</script>

<style lang="stylus" scoped>
  >>> .el-dialog__header
    background #24c69a

  >>> .el-dialog__header
    padding 10px !important
    position relative
    .el-dialog__title
      font-size 14px
      color #ffffff
    .el-dialog__headerbtn
      top 50%
      transform translate(0,-50%)
      .el-icon
        color #ffffff

  >>> .el-dialog__body
    padding 15px
</style>