import api from "@/api";
import { setPageMap, removePageMap } from '@/utils/page-cache.js';

function getFullPathByBFS(list) {
  const pageMap = {};
  const addPage = (path, walkPath) => {
    if (path) {
      path = path.split('?')[0];
      pageMap[path] = walkPath;
    }
  };

  const queue = [...list];
  while (queue.length) {
    const current = queue.shift();
    if (!current.walkPath) {
      current.walkPath = current.name;
      addPage(current.path, current.walkPath);
    }
    if (current.children && current.children.length) {
      current.children.forEach(item => {
        item.walkPath = current.walkPath ? `${current.walkPath}/${item.name}` : item.name;
        addPage(item.path, item.walkPath);
      });
      queue.push(...current.children);
    }
  }
  setPageMap(pageMap);
}


const state = {
  menus: [],
  currentMenuID: "",
};

const mutations = {
  SET_MENUS: (state, menus) => {
    state.menus = menus;
  },
  GET_MENUS: (state, name) => {
    state.name = name;
  },
  SET_CURRENT_MENU: (state, menuId = "") => {
    state.currentMenuID = menuId;
  },
};

const actions = {
  async getMenus({ commit, state }) {
    if (state.menus && state.menus.length) {
      return Promise.resolve(state.menus);
    }

    const buildAppNode = (appData = []) => {
      if (!appData.length) {
        return null;
      }

      appData = appData.map((v) => ({
        name: v.subAppName,
        path: v.url || "",
        id: "myApp" + v.subAppId,
        children: [],
      }));
      return {
        id: "myApp",
        name: "我的应用",
        path: "",
        icon: "el-icon-menu",
        children: [
          {
            id: "myAppSub",
            name: "我的应用",
            path: "",
            icon: "",
            children: appData,
          },
        ],
      };
    };

    const appRes = await api.common.getMyApps()
      .catch(console.log);
    const appNode = buildAppNode(appRes && appRes.data || []);
    removePageMap();
    return api.common.getMenus().then(({ data, success, message }) => {
      if (Array.isArray(data)) {
        let menus = data[0] && data[0].children || [];
        getFullPathByBFS(menus);
        if (appNode) {
          menus = [appNode, ...menus,];
        }
        if (data[0]) {
          // 当前appNode
          menus = [
            {
              id: 'curApp',
              name: data[0].nodeName,
              path: '',
              icon: 'el-icon-monitor',
              children: null
            },
            ...menus
          ];
        }
        commit('SET_MENUS', menus);
      }
    })
      .catch((error) => {
        console.log("获取menu错误:", error);
      });
  },
  // 路由切换时，更改左侧导航的选中模块
  moveMenu({ commit, state }, moduleName) {
    const menu = state.menus.find((item) => item.nodeName === moduleName);
    if (menu) {
      commit("SET_CURRENT_MENU", menu.id);
    }
  },
  updateCurrentMenu({ commit }, menuId) {
    sessionStorage.setItem("currentMenuID", menuId);
    commit("SET_CURRENT_MENU", menuId);
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
