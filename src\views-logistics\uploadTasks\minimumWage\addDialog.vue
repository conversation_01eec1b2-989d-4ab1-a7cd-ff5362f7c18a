<template>
  <qDialog :visible="visible"
    :title="title"
    :innerScroll="true"
    width="800px"
    :innerHeight="300"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel">
    <el-form ref="addForm"
      :model="addForm"
      :rules="rules">
      <el-row :gutter="15">
        <el-col :span="10">
          <el-form-item
            label="员工姓名:">
            {{addForm.staffName}}
          </el-form-item>
        </el-col>
        <el-col :span="14">
          <el-form-item
            label="厂牌编号:"
            :prop="title=='新增'?'staffCode':''">
            <el-input
              v-if="title=='新增'"
              v-model.trim="addForm.staffCode"
              clearable
              placeholder="请输入厂牌编号">
              <template
                slot="append">
                <el-button
                  type="primary"
                  @click="searchEmployee">
                  查询
                </el-button>
              </template>
            </el-input>
            <span
              v-else>{{addForm.staffCode}}</span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <section
      v-loading="loading">
      <el-row :gutter="10">
        <el-col :span="6">
          <span>考勤班组</span>
        </el-col>
        <el-col :span="6">
          <span>总出勤天数</span>
        </el-col>
        <el-col :span="6">
          <span>总加班天数</span>
        </el-col>
        <el-col :span="6">
          <span>保底工资</span>
        </el-col>
      </el-row>
      <el-row :gutter="10"
        v-for="item in list"
        :key="item.groupId"
        class="content">
        <el-col :span="6">
          <div
            style="padding:1px 0">
            {{item.groupName}}
          </div>
        </el-col>
        <el-col :span="6">
          <div
            style="padding:1px 0">
            {{item.totalWorkDay || 0}}
          </div>
        </el-col>
        <el-col :span="6">
          <div
            style="padding:1px 0">
            {{item.totalOvertime || 0}}
          </div>
        </el-col>
        <el-col :span="6">
          <el-input
            oninput="value=value.replace(/[^\d.]/g, '');if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
            v-model="item.minimumWage"
            clearable
            @blur="baseChange('blur',item)"
            @clear="baseChange('clear',item)"
            placeholder="请输入保底工资">
            >
          </el-input>
        </el-col>
      </el-row>
    </section>
  </qDialog>
</template>

<script>
import { moneyFormat, moneyDelete } from "@/utils";
export default {
  name: "editDialog",
  props: {
    title: {
      type: String,
      required: true
    },
    visible: {
      type: Boolean,
      required: true
    },
    editForm: Object
  },
  data() {
    return {
      addForm: {
        staffName: "",
        staffCode: "",
      },
      rules: {
        staffCode: [
          { required: true, message: '请输入厂牌编号', trigger: 'blur' },
        ],
      },
      loading: false,
      list: []
    }
  },
  created() {
    this.addForm = {
      ...this.addForm,
      ...this.editForm
    }
    if (this.title == '编辑') {
      this.getDetail()
    }
  },
  methods: {
    //保底工资获取明细
    async getDetail() {
      this.loading = true
      const { factoryId, accountingMonth } = JSON.parse(this.$Base64.decode(this.$route.query.data))
      let params = {
        accountingMonth,
        factoryId,
        staffCode: this.addForm.staffCode,
      }
      await this.$api.logisticsDataUpload.guaranteedSalary.getDetail(this.title == '新增' ? params : { id: this.editForm.id, ...params, }).then(({ data }) => {
        this.addForm = {
          ...this.addForm,
          staffCode: data && data.staffCode || '',
          staffName: data && data.staffName || ''
        }
        this.list = data && data.list.map(item => ({ ...item, minimumWage: item.minimumWage && moneyFormat(item.minimumWage) || 0 })) || []
      }).finally(() => {
        this.loading = false
      });

    },
    //查询员工信息
    searchEmployee() {
      this.$refs.addForm.validateField(["staffCode"], (valid) => {
        if (valid) return;
        this.getDetail()
      });
    },
    //校验保底工资
    baseChange(name, item) {
      if (!item.minimumWage || name == 'clear') {
        item.minimumWage = 0
        return
      }
      if (!/^\d{1,5}(\.\d{1,2})?$/.test(moneyDelete(item.minimumWage))) {
        this.$message.error('小数点前面仅支持5位数,小数点后面仅支持2位数');
        item.minimumWage = 0;
      }
      item.minimumWage = moneyFormat(moneyDelete((item.minimumWage)));
    },
    handleCancel() {
      this.$emit('cancel', 'cancel')
    },
    handleConfirm() {
      this.$refs.addForm.validate(valid => {
        if (!valid) return
        const { factoryId, accountingMonth } = JSON.parse(this.$Base64.decode(this.$route.query.data))
        let params = {
          factoryId,
          accountingMonth,
          staffCode: this.addForm.staffCode,
          list: this.list && this.list.map(item => ({ ...item, minimumWage: item.minimumWage && moneyDelete(item.minimumWage) || 0 })) || []
        }
        if (this.title == '新增') {
          this.$api.logisticsDataUpload.guaranteedSalary
            .addStaffMiniWage(params).then(() => {
              this.$notify.success({
                title: '成功',
                message: '新增成功',
              });
              this.$emit('cancel', 'confirm')
            })
        } else {
          this.$api.logisticsDataUpload.guaranteedSalary
            .editStaffMiniWage({id: this.editForm.id,...params}).then(() => {
              this.$notify.success({
                title: '成功',
                message: '编辑成功',
              });
              this.$emit('cancel', 'confirm')
            })
        }

      })
    },
  }
}
</script>

<style lang="stylus" scoped>
.el-form-item {
  display: flex;
  align-items: center;
}

>>>.el-form-item__content {
  flex: 1;
}

.content {
  margin: 15px 0 15px 0;
  display: flex;
  align-items: center;
}

>>>.el-select {
  width: 100%;
}

>>>.el-input-group__append {
  background: #24c69a;
  color: #fff;

  .el-button {
    padding: 5px 20px;
  }
}
</style>