<template>
  <!-- 同比分析人工效率分析表 -->
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          size="mini"
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          label-position="right"
        >
          <el-form-item label="核算工厂:" prop="factoryId">
            <el-select
              v-model="searchForm.factoryId"
              filterable
              placeholder="请选择工厂"
              @change="onSearch"
              clearable
            >
              <el-option
                v-for="item in tabList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="核算大工序:" prop="bigProcessId">
            <el-select
              v-model="searchForm.bigProcessId"
              filterable
              clearable
              placeholder="请选择核算大工序"
              @change="onSearch"
            >
              <el-option
                v-for="item in process.bigProcessList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="核算月份:" prop="accountingMonth">
            <el-date-picker
              @change="onSearch"
              v-model="searchForm.accountingMonth"
              type="month"
              placeholder="请选择日期"
              :clearable="false"
              :picker-options="pickerOptions"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button
            size="small"
            type="primary"

            @click="onSearch"
          >
            查询</el-button
          >
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置</el-button
          >
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <div class="header_tableName">生产财务本部板木分厂人工效率分析表</div>
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :data="tableData"
        :height="maxTableHeight"
        :span-method="objectSpanMethod"
      >
        <el-table-column
          prop="factoryName"
          align="center"
          label="分厂"
          width="150"
          fixed
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="bigProcessName"
          align="center"
          label="工序"
          width="100"
          fixed
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          :label="this.filterAmountto"
          align="center"
          class-name="customOne"
        >
          <el-table-column
            prop="averagePersonSum"
            class-name="customOne"
            min-width="105"
            align="center"
            label="平均人数"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.averagePersonSum}}
            </template>
          </el-table-column>
          <el-table-column
            prop="totalWorkDaySum"
            class-name="customOne"
            label="本厂出勤"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.totalWorkDaySum}}
            </template>
          </el-table-column>
          <el-table-column
            prop="backManDaysSum"
            class-name="customOne"
            label="外厂/部出勤"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.backManDaysSum}}
            </template>
          </el-table-column>
          <el-table-column
            prop="totalAttendanceSum"
            class-name="customOne"
            label="出勤合计"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.totalAttendanceSum}}
            </template>
          </el-table-column>
          <el-table-column
            prop="totalOutputSum"
            class-name="customOne"
            label="产量(套)"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="totalOutputValueSum"
            class-name="customOne"
            label="产值"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.totalOutputValueSum}}
            </template>
          </el-table-column>
          <el-table-column
            prop="totalPieceWageSum"
            class-name="customOne"
            label="计件工资(线上+线下)"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.totalPieceWageSum}}
            </template>
          </el-table-column>
          <el-table-column
            prop="outWorkWageSum"
            class-name="customOne"
            label="外派工资"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.outWorkWageSum}}
            </template>
          </el-table-column>
          <el-table-column
            prop="floatingSalarySum"
            class-name="customOne"
            label="上浮工资"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.floatingSalarySum}}
            </template>
          </el-table-column>
          <el-table-column
            prop="applySalarySum"
            class-name="customOne"
            label="增补/借支总额"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.applySalarySum}}
            </template>
          </el-table-column>
          <el-table-column
            prop="salarySum"
            class-name="customOne"
            label="应发工资总额"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.salarySum}}
            </template>
          </el-table-column>
          <el-table-column
            prop="actualSalarySum"
            class-name="customOne"
            label="实发工资总额"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.actualSalarySum}}
            </template>
          </el-table-column>
          <el-table-column
            prop="fineSum"
            class-name="customOne"
            label="罚扣款"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.fineSum}}
            </template>
          </el-table-column>
          <el-table-column
            prop="eachTotalPieceWageSum"
            class-name="customOne"
            label="单套产品计件工资"
            min-width="140"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.eachTotalPieceWageSum}}
            </template>
          </el-table-column>
          <el-table-column
            prop="attendanceRateSum"
            class-name="customOne"
            label="出勤率"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="avgOutputSum"
            class-name="customOne"
            label="人均日产量(套)"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.avgOutputSum}}
            </template>
          </el-table-column>
          <el-table-column
            prop="avgOutputValueSum"
            class-name="customOne"
            label="人均日产值"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.avgOutputValueSum}}
            </template>
          </el-table-column>
          <el-table-column
            prop="avgTotalPieceWageSum"
            class-name="customOne"
            label="人均日计件工资"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.avgTotalPieceWageSum}}
            </template>
          </el-table-column>
          <el-table-column
            prop="avgSalarySum"
            class-name="customOne"
            label="人均日应发工资"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.avgSalarySum}}
            </template>
          </el-table-column>
          <el-table-column
            prop="avgActualSalarySum"
            class-name="customOne"
            label="人均日实发工资"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.avgActualSalarySum}}
            </template>
          </el-table-column>
          <el-table-column
            prop="avgFineSum"
            class-name="customOne"
            label="人均日扣款金额"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.avgFineSum}}
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column
          :label="this.filterLastMonth"
          align="center"
          class-name="customOne"
        >
          <el-table-column
            prop="averagePersonPre"
            class-name="customOne"
            min-width="105"
            align="center"
            label="平均人数"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="totalWorkDayPre"
            class-name="customOne"
            label="本厂出勤"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="backManDaysPre"
            class-name="customOne"
            label="外厂/部出勤"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="totalAttendancePre"
            class-name="customOne"
            label="出勤合计"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="totalOutputPre"
            class-name="customOne"
            label="总产量(套)"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="totalOutputValuePre"
            class-name="customOne"
            label="总产量(值)"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="totalPieceWagePre"
            class-name="customOne"
            label="计件工资(线上+线下)"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="outWorkWagePre"
            class-name="customOne"
            label="外派工资"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="floatingSalaryPre"
            class-name="customOne"
            label="上浮工资"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="applySalary"
            class-name="customOne"
            label="增补/借支总额"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="salaryPre"
            class-name="customOne"
            label="应发工资总额"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="actualSalaryPre"
            class-name="customOne"
            label="实发工资总额"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="accidentCompensationPre"
            class-name="customOne"
            label="质量事故赔偿金额"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="compensationAmountPre"
            class-name="customOne"
            label="成本赔偿"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="consumptionAmountPre"
            class-name="customOne"
            label="低耗品"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="reworkAmountPre"
            class-name="customOne"
            label="返工扣款"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="finePre"
            class-name="customOne"
            label="罚款"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="eachTotalPieceWagePre"
            class-name="customOne"
            label="每套计件"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="attendanceRatePre"
            class-name="customOne"
            label="出勤率"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="avgOutputPre"
            class-name="customOne"
            label="人均日产量(套)"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="avgOutputValuePre"
            class-name="customOne"
            label="人均日产量(值)"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="avgTotalPieceWagePre"
            class-name="customOne"
            label="人均日计件工资"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="avgSalaryPre"
            class-name="customOne"
            label="人均日应发工资"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="avgActualSalaryPre"
            class-name="customOne"
            label="人均日实发工资"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="avgAccidentCompensationPre"
            class-name="customOne"
            label="人均日质量事故赔偿金额"
            min-width="180"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="avgCompensationAmountPre"
            class-name="customOne"
            label="人均日成本赔偿"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="avgConsumptionAmountPre"
            class-name="customOne"
            label="人均日低耗品"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="avgReworkAmountPre"
            class-name="customOne"
            label="人均日返工扣款"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="avgFinePre"
            class-name="customOne"
            label="人均日罚款"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
        </el-table-column>
        <el-table-column
          :label="this.filterMonth"
          align="center"
          class-name="customOne"
        >
          <el-table-column
            prop="averagePerson"
            class-name="customOne"
            min-width="105"
            align="center"
            label="平均人数"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="totalWorkDay"
            class-name="customOne"
            label="本厂出勤"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="backManDays"
            class-name="customOne"
            label="外厂/部出勤"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="totalAttendance"
            class-name="customOne"
            label="出勤合计"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="totalOutput"
            class-name="customOne"
            label="总产量(套)"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="totalOutputValue"
            class-name="customOne"
            label="总产量(值)"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="totalPieceWage"
            class-name="customOne"
            label="计件工资(线上+线下)"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="outWorkWage"
            class-name="customOne"
            label="外派工资"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="floatingSalary"
            class-name="customOne"
            label="上浮工资"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="applySalary"
            class-name="customOne"
            label="增补/借支总额"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="salary"
            class-name="customOne"
            label="应发工资总额"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="actualSalary"
            class-name="customOne"
            label="实发工资总额"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="accidentCompensation"
            class-name="customOne"
            label="质量事故赔偿金额"
            min-width="150"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="compensationAmount"
            class-name="customOne"
            label="成本赔偿"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="consumptionAmount"
            class-name="customOne"
            label="低耗品"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="reworkAmount"
            class-name="customOne"
            label="返工扣款"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="fine"
            class-name="customOne"
            label="罚款"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="eachTotalPieceWage"
            class-name="customOne"
            label="每套计件"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="attendanceRate"
            class-name="customOne"
            label="出勤率"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="avgOutput"
            class-name="customOne"
            label="人均日产量(套)"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="avgOutputValue"
            class-name="customOne"
            label="人均日产量(值)"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="avgTotalPieceWage"
            class-name="customOne"
            label="人均日计件工资"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="avgSalary"
            class-name="customOne"
            label="人均日应发工资"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="avgActualSalary"
            class-name="customOne"
            label="人均日实发工资"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="avgAccidentCompensation"
            class-name="customOne"
            label="人均日质量事故赔偿金额"
            min-width="180"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="avgCompensationAmount"
            class-name="customOne"
            label="人均日成本赔偿"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="avgConsumptionAmount"
            class-name="customOne"
            label="人均日低耗品"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="avgReworkAmount"
            class-name="customOne"
            label="人均日返工扣款"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="avgFine"
            class-name="customOne"
            label="人均日罚款"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
        </el-table-column>
      </el-table>
    </table-panel>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import {  calculateTableWidth } from "@/utils";
import moment from "moment";
export default {
  name: "yoyAnalysis",
  mixins: [tableMixin, pagePathMixin],
  data() {
    return {
      searchForm: {
        factoryId: "",
        accountingMonth: moment().subtract(1, "months").format("YYYY-MM"),
        bigProcessId: "",
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      tabList: [],
      tableData: [], //表格数据
      loading: false,
      filterParam: {},
      spanMap: {},
      spanIndex: 0,
      spanIndex2: 0,

    };
  },
  async created() {
    await this.getFactory();
    // this.searchForm.factoryId  = this.tabList[0].id
    this.onSearch();
  },
  computed: {
    filterMonth() {
      let accountingMonth = this.searchForm.accountingMonth;
      const currentMonth = moment(accountingMonth);
      const lastYearMonth = currentMonth.clone().subtract(1, "years");
      const currentMonthStr = currentMonth.format("YYYY年MM月");
      const lastYearMonthStr = lastYearMonth.format("YYYY年MM月");
      return `${currentMonthStr}与${lastYearMonthStr}同期变动率`;
    },

    filterAmountto() {
      let accountingMonth = this.searchForm.accountingMonth;
      const currentYear =  moment(accountingMonth).format("YYYY年");
      const lastYear = moment(moment(accountingMonth).subtract(1, "years")).format("YYYY年");
      return `${currentYear}与${lastYear}同期变动率`;
    },
    filterLastMonth() {
      let accountingMonth = this.searchForm.accountingMonth;
      let currentDate = new Date(accountingMonth);
      currentDate.setMonth(currentDate.getMonth() - 1);
      let lastMonthYear = currentDate.getFullYear()-1;
      let lastMonthMonth = String(currentDate.getMonth() + 1).padStart(2, '0');
      let currentMonthYear = currentDate.getFullYear();
      let currentMonthMonth = String(currentDate.getMonth() + 1).padStart(2, '0');

      return `${currentMonthYear}年${currentMonthMonth}月与${lastMonthYear}年${lastMonthMonth}月同期变动率`;
    },

    process() {
      return {
        bigProcessList:
          (this.searchForm.factoryId &&
            this.tabList.find((item) => item.id == this.searchForm.factoryId)
              .bigProcess) ||
          [],
      };
    },
  },
  methods: {
    //获取工厂
    getFactory() {
      return this.$api.plateTypeSystemManage.getBasicPermission
        .getBasicPermissionAll({ moduleId: 3 })
        .then(({ data }) => {
          this.tabList = data || [];
        });
    },
    //获取工资分析表
    getList() {
      this.loading = true;
      this.$api.plateTypeReportManagement
        .yoyEfficiencylist({ ...this.filterParam })
        .then((res) => {
          this.tableData = res.data;
          this.computeSpanData(this.tableData);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 遍历表格数据，计算合并行数据
    computeSpanData(list) {
      this.spanMap = {};
      list.forEach((item, index) => {
        this.recordSpanData(index, list);
      });
    },
    // 计算每一行的合并行数据
    recordSpanData(index, data) {
      if (index === 0) {
        this.spanMap[index] = {
          level1: 1,
          level2: 1,
        };
        this.spanIndex = 0;
        this.spanIndex2 = 0;
      } else {
        // 一级合并
        if (data[index].factoryName === data[index - 1].factoryName) {
          let spanRow = this.spanMap[this.spanIndex];
          spanRow.level1 += 1;
          // 二级合并
          let level2 = 1;
          if (
            data[index].factoryName === data[index - 1].factoryName
          ) {
            spanRow = this.spanMap[this.spanIndex2];
            spanRow.level2 += 1;
            // 当前行参与二级合并，当前行level2 = 0
            level2 = 0;
          } else {
            this.spanIndex2 = index;
          }
          this.spanMap[index] = {
            level1: 0,
            level2,
          };
        } else {
          this.spanMap[index] = {
            level1: 1,
            level2: 1,
          };
          this.spanIndex = index;
          this.spanIndex2 = index;
        }
      }
    },
    objectSpanMethod({row, column, rowIndex, columnIndex }) {
      const spanRow = this.spanMap[rowIndex];
      if (!spanRow) {
        return;
      }

      if (columnIndex == 0) {
        return {
          rowspan: spanRow.level1,
          colspan: 1
        };
      }
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.filterParam = {};
      this.onSearch();
    },
    //搜索
    onSearch() {
      let month = this.searchForm.accountingMonth;
      this.searchForm.accountingMonth = month
        ? month
        : moment().subtract(1, "months").format("YYYY-MM");
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (key == "accountingMonth" && val) {
          this.filterParam.accountingMonth = moment(val).format("YYYY-MM");
        } else if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      this.pageNum = 1;
      this.getList();
    },
    //计算表格列宽度
    flexWidth(...args) {
      return calculateTableWidth(...args);
    },
  }
};
</script>
<style lang="stylus" scoped>
ellipsis() {
  font-size: 16px;
  font-weight: 600;
}

.header_tableName {
  ellipsis();
  font-size: 22px;
  text-align: center;
  margin-bottom: 8px;
  margin-top: 34px;
}

>>>.el-table__cell {
  .cell {
    width: 100% !important;
    padding: 0 8px !important;
  }
}

>>>.el-table__fixed {
  height: auto !important;
  bottom: 17px !important;
}
</style>
