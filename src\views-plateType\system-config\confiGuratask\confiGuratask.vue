<template>
  <content-panel class="panel-tabs">
    <el-tabs v-model="activeTab" class="tabs">
      <el-tab-pane label="全勤奖" name="management"></el-tab-pane>
      <el-tab-pane label="环境补贴" name="environment"></el-tab-pane>
      <el-tab-pane label="高温补贴" name="hightemper"></el-tab-pane>
    </el-tabs>
    <component :is="componentName"> </component>
  </content-panel>
</template>

<script>
import managementsystem from "./component/managementsystem";
import environmentalsubsidies from "./component/environmentalsubsidies";
import hightemperaturesubsidy from "./component/hightemperaturesubsidy";

export default {
  name: "PlateTypeconfiguratask",
  components: {
    managementsystem,
    environmentalsubsidies,
    hightemperaturesubsidy
  },
  data() {
    return {
      activeTab: "management",
      componentName: "",
      tabList: Object.freeze([
        {
          name: "management",
          component: managementsystem,
        },
        {
          name: "environment",
          component: environmentalsubsidies,
        },
        {
          name: "hightemper",
          component: hightemperaturesubsidy,
        },
      ]),
    };
  },
  watch: {
    activeTab: {
      handler(value) {
        this.componentName = this.tabList.find(
          (item) => item.name === value
        ).component;
      },
      immediate: true,
    },
  },
};
</script>

<style lang="stylus" scoped>
.panel-tabs {
  >>> .main-area {
    padding-top: 0;
  }
}

.tabs {
  >>> .el-tabs__header {
    margin-bottom: 5px;
  }
}
</style>
