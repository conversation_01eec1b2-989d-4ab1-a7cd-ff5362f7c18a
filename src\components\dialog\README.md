# dialog 弹窗组件

# v-model 窗口显示状态 Boolean

# config 配置
title                 弹窗标题                               默认[提示]
width                 窗口宽度                               默认[50%]
showButton            是否显示窗口底部按钮                    默认[true]
showConfirm           是否显示确认按钮                        默认[true]
showCancel            是否显示取消按钮                        默认[true]
cancelText            取消按钮文本                            默认[取消]
confirmText           确认按钮文本                            默认[确定]

cancelCallback        是否绑定取消按钮点击事件回调方法          默认[false]
confirmCallback       是否绑定确认按钮点击事件回调方法          默认[false]
closeOnClickModal     点击modal层是否自动关闭窗口              默认[false]

# 注释
回调方法参数设置为true，点击按钮不会自动关闭窗口，需要手动关闭。

# event
dialogCancel
dialogConfirm

# slot
content               内容区域
footer                底部区域

# example

<template>
  <div>
    <v-dialog v-model="dialogVisible">
      <div slot="content">luoshiyang</div>
    </v-dialog>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        dialogVisible: false
      }
    }
  }
</script>