<template>
  <content-panel>
    <template v-slot:search>
      <search-box
        class="search-box">
        <el-form
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          label-position="right"
          size="mini">
          <el-form-item
            label="工厂名称:"
            prop="factoryName">
            <el-input
              v-model.trim="searchForm.factoryName"
              size="mini"
              clearable
              placeholder="请输入工厂名称"
              @clear='onSearch'
              @keyup.enter.native="onSearch">
            </el-input>
          </el-form-item>
          <el-form-item
            label="启用状态:"
            prop="enable">
            <el-select
              v-model="searchForm.enable"
              filterable
              clearable
              placeholder="请选择启用状态"
              @change="onSearch">
              <el-option
                v-for="item in enableOptions"
                :key="item.id"
                :label="item.label"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template
          v-slot:right>
          <el-button
            size="small"
            type="primary"

            @click="onSearch">
            查询
          </el-button>
          <el-button
            size="small"
            type="warning"
            @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel
      ref="tablePanel">
      <el-table stripe border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :data="tableData"
        :height="maxTableHeight"
        style="width: 100%">
        <el-table-column
          type="index"
          align="left">
        </el-table-column>
         <el-table-column
          width="150"
          prop="name"
          show-overflow-tooltip
          label="工厂名称"
          align="left">
        </el-table-column>
        <el-table-column
          prop="groupName"
          label="核算班组"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          width="120"
          prop="enable"
          show-overflow-tooltip
          label="启用状态"
          align="left">
          <template
            slot-scope="scope">
            <div>
              <el-switch
                @change="onSwitch(scope.row,'基础信息')"
                inactive-color="#ff4949"
                v-model="scope.row.enable"
                :active-text="scope.row.enable == 1 ? '启用' : '禁用'"
                :active-value="1"
                :inactive-value="0">
              </el-switch>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          width="150"
          prop="updateTime"
          label="修改时间"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          width="120"
          label=""
          align="left">
          <template
            slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              @click="teamBtn(scope.row)">
              班组管理
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div
          style="text-align: right">
          <el-pagination
            @size-change="onSizeChange"
            @current-change="onNumChange"
            :current-page="pageNum"
            :page-size="pageSize"
            :total="total"
            :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>
        </div>
      </template>
    </table-panel>
    <team-management
      v-if="visible"
      :visible="visible"
      :formData="formData"
      @add="handleAdd"
      @cancel="handleCancel"></team-management>
    <add-dialog
      v-if="isVisible"
      :isVisible="isVisible"
      :title="title"
      :editForm="editForm"
      @cancel="addCancel"></add-dialog>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import teamManagement from './components/teamManagement';
import AddDialog from './components/addDialog';
export default {
  components: { teamManagement, AddDialog },
  name: "BasicInformation",
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      searchForm: {
        factoryName: '',
        enable: ""
      },
      //启用
      enableOptions: [
        {
          label: "是",
          id: "1",
        },
        {
          label: "否",
          id: "0",
        },
      ],
      title: "",
      tableData: [],
      loading: false,
      filterParam: {},
      pageSize: 50,
      pageNum: 1,
      total: 0,
      visible: false,
      isVisible: false,
      // table高度
      resizeOffset: 55,
      formData: {},
      editForm: {}
    };
  },
  created() {
    this.getList()
  },
  methods: {
    //获取所有工厂列表
    getList() {
      this.loading = true
      this.$api.systemManage.getBasicPermission.getAllFactory({
        pageSize: this.pageSize, pageNum: this.pageNum, filterData: { ...this.filterParam }
      }).then(({ data: { list, total } }) => {
        this.tableData = list
        this.total = total
      }).finally(() => {
        this.loading = false;
      });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.filterParam = {}
      this.onSearch();
    },
    //搜索
    onSearch() {
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      this.pageNum = 1;
      this.getList();
    },
    //班组管理
    teamBtn(row) {
      this.visible = true;
      this.formData = {
        factoryId: row.id || "",
        factoryName: row.name || ""
      }
    },
    handleAdd({ title, formData }) {
      this.title = title
      this.isVisible = true
      this.editForm = formData
    },
    //切换启用状态
    onSwitch({ id, enable }, value) {
      this.$api.systemManage.getBasicPermission.enable({ id, enable }).then(({ success }) => {
        if (enable == 1) {
          this.$notify.success({
            title: "成功",
            message: "启用成功",
          });
        } else if (enable == 0) {
          this.$notify.success({
            title: "成功",
            message: "禁用成功",
          });
        }
      }).finally(() => {
        this.getList()
      })
    },
    handleCancel() {
      this.visible = false
    },
    addCancel() {
      this.isVisible = false
    },
    onSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
    },
    onNumChange(val) {
      this.pageNum = val;
    },
  },
};
</script>

<style lang="stylus" scoped>
>>>.inputClass {
  .el-input__inner {
    height: 24px;
    line-height: 24px;
    padding: 2px 4px;
  }
}

.title {
  font-weight: bold;

  .tips {
    color: #00b891;
    font-weight: normal;
    display: inline-block;
    margin-left: 10px;
  }
}

.employee_info {
  padding: 5px;
  border: 1px solid #ccc;
}

>>>.el-select {
  width: 100%;
}
</style>