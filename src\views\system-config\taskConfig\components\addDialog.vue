<template>
  <qDialog :visible="visible"
    :title="title"
    :innerScroll="true"
    width="800px"
    :innerHeight="500"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel">
    <div class="title">基本信息
    </div>
    <el-form ref="addForm"
      :model="addForm"
      label-position="right"
      label-width="85px"
      :rules="rules"
      size="small">
      <el-form-item
        class="otherPlanName"
        label="任务名称:"
        prop="otherPlanName">
        <el-input type="text"
          v-model="addForm.otherPlanName"
          placeholder="请输入文本">
        </el-input>
      </el-form-item>
      <el-form-item
        label="所属角色:"
        prop="roleId">
        <el-select
          v-model="addForm.roleId"
          placeholder="请选择选项">
          <el-option
            v-for="item in roleList"
            :key="item.id"
            :label="item.roleName"
            :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="备注说明:">
        <el-input
          type="textarea"
          maxlength="300"
          resize="none"
          rows="5"
          show-word-limit
          v-model="addForm.comments"
          placeholder="请输入文本">
        </el-input>
      </el-form-item>
    </el-form>
    <div
      style="display: flex; justify-content: space-between">
      <div class="title">
        数据配置
        <span
          class="tips">Tips:数据名称最多添加50项</span>
      </div>
      <div>
        <el-button
          size="small"
          type="primary"
          @click="handleAdd">
          新增
        </el-button>
        <el-button
          size="small"
          type="primary"
          :class="[!isSave?'':'edit_active']"
          @click="handleSave">
          {{isSave?'保存':'编辑'}}
        </el-button>
      </div>
    </div>
    <div class="items">
      <el-table stripe border
        v-loading="loading"
        ref="tableRef"
        row-key="id"
        highlight-current-row
        :data="tableData"
        :height="300">
        <el-table-column
          prop="dataItemName"
          label="数据名称"
          width="150"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="备注说明"
          prop="comments"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
      </el-table>
    </div>
  </qDialog>
</template>

<script>
import Sortable from 'sortablejs';
export default {
  name: "addDialog",
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    title: {
      type: String,
      required: true
    },
    editForm: Object
  },
  data() {
    return {
      addForm: {
        otherPlanName: "",
        roleId: "",
        comments: "",
      },
      rules: {
        otherPlanName: [
          { required: true, message: "请输入任务名称", trigger: "blur" },
        ],
        type: [
          { required: true, message: "请选择任务类型", trigger: "change" },
        ],
        roleId: [{ required: true, message: "请选择角色", trigger: "change" }],
        isActive: [
          { required: true, message: "请选择所属状态", trigger: "change" },
        ],
      },
      roleList: [], //所属角色
      isSave: false,
      loading: false,
      tableData: [],
      tableKey: "",
      isLoading:false
    }
  },
  created() {
    this.getRoloeInfo()
    if (this.title == '编辑') {
      this.tableData = this.editForm.wageItems
      delete this.editForm.wageItems
    }
    this.addForm = {
      ...this.addForm,
      ...this.editForm
    }
    this.$bus.$off('customizedList')
    this.$bus.$on('customizedList', (data) => {
      this.tableData = [...this.tableData, ...data]
    })
  },
  watch: {
    visible: {
      handler(val) {
        if (val)
          this.$nextTick(() => {
            this.rowDrop()
          })
      },
      immediate: true
    }
  },
  methods: {
    // 行拖拽
    rowDrop() {
      // 要侦听拖拽响应的DOM对象
      const tbody = document.querySelector('.items .el-table__body-wrapper tbody');
      new Sortable(tbody, {
        animation: 1000,
        // 结束拖拽后的回调函数
        onEnd: ({ newIndex, oldIndex }) => {
          const currRow = this.tableData.splice(oldIndex, 1)[0]
          this.tableData.splice(newIndex, 0, currRow)
        },
        onMove: () => {
          return this.isSave
        }
      })
    },
    //获取角色信息
    getRoloeInfo() {
      this.$api.roleInfo.getRoleInfoAll({
        moduleId:1
      }).then(({ data }) => {
        this.roleList = data;
      });
    },
    //新增
    handleAdd() {
      this.$bus.$emit('customizedAdd')
    },
    //编辑
    handleSave() {
      this.isSave = !this.isSave
    },
    handleCancel() {
      this.$emit('cancel', 'cancel')
    },
    handleConfirm() {
      if (this.title == "新增") {
        this.$refs.addForm.validate((valid) => {
          if (!valid) return
          if (!this.tableData.length) {
            this.$message({
              message: "请添加工资项配置",
              type: "warning",
            });
            return;
          }
          this.isLoading = true
          let params = {
            ...this.addForm,
            items: this.tableData,
          };
          this.$api.systemConfig.taskConfigAdd(params).then((res) => {
            this.$message({
              type: "success",
              message: "新增成功!",
            });
            this.$emit('cancel', 'confirm')
          }).finally(()=>{
              this.isLoading = false;
          })
        });
      } else {
        this.$refs.addForm.validate((valid) => {
          let roleId = ""
          if (/^(?:[\u3400-\u4DB5\u4E00-\u9FEA\uFA0E\uFA0F\uFA11\uFA13\uFA14\uFA1F\uFA21\uFA23\uFA24\uFA27-\uFA29]|[\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0])+$/.test(this.addForm.roleId)) {
            roleId = this.roleList.find(item => item.roleName == this.addForm.roleId).id
          } else {
            roleId = this.addForm.roleId
          }
          if (!valid) return
          if (!this.tableData.length) {
            this.$message({
              message: "请添加工资项配置",
              type: "warning",
            });
            return;
          }
          this.isLoading = true
          let params = {
            id: this.editForm.id,
            ...this.addForm,
            roleId,
            items: this.tableData,
          };
          this.$api.systemConfig.taskConfigEdit(params).then((res) => {
            this.$message({
              type: "success",
              message: "编辑成功!",
            });
            this.$emit('cancel', 'confirm')
          }).finally(()=>{
            this.isLoading = false;
          });
        });
      }
    }
  }
}
</script>

<style lang="stylus" scoped>
border_style() {
  padding: 10px;
  border: 1px solid #ccc;
  margin: 10px 0;
}

.title {
  font-weight: bold;

  .tips {
    color: #00b891;
    font-weight: normal;
    display: inline-block;
    margin-left: 10px;
  }
}

.el-form {
  border_style();

  >>> .el-select {
    width: 35%;
  }

  .otherPlanName {
    >>>.el-input {
      width: 35%;
    }
  }
}

>>>.el-textarea__inner {
  padding-right: 50px;
}

.el-textarea {
  >>>.el-input__count {
    right: 20px !important;
  }
}

.items {
  border_style();
}

.edit_active {
  background: #ff6b31;
  border-color: #ff6b31;
}
</style>