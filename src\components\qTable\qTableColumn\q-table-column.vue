<template>
  <el-table-column
    v-bind="$attrs"
    v-on="$listeners"
    :prop="prop" 
    :label="label">
    <template #header v-if="initOptions.length">
      <el-popover :ref="'filter-pop-' + prop"
        popper-class="filter-pop"
        placement="bottom"
        :width="300"
        trigger="click"
        @show="onPopShow"
        @after-enter="afterPopShow">
        <template #reference>
          <div class="header-label">
            <span>{{label}}</span>
            <i class="el-icon-search"></i>
          </div>
        </template>
        <div class="filter-panel">
          <div class="filter-panel-sort-wrap" v-if="useSort">
            <el-button type="primary" :class="{active: sortType == 'up'}" plain icon="el-icon-sort-up" size="mini" @click="doSort('up')">升序</el-button>
            <el-button type="primary" :class="{active: sortType == 'down'}"  plain icon="el-icon-sort-down" size="mini" @click="doSort('down')">降序</el-button>
          </div>
          <div class="filter-panel-number-wrap" v-if="isNumberFilter">
            <span>数字筛选</span>
            <el-select 
              class="number-select"
              v-model="numCondition" 
              size="mini"
              placeholder="" 
              clearable>
              <el-option v-for="item in numberConditions" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value"/>
            </el-select>
            <el-input-number 
              class="number-input"
              v-model="numVal" 
              :controls="false" 
              step-strictly 
              size="mini"/>
            <el-input-number 
              v-if="isRange"
              class="number-input"
              v-model="numVal2" 
              :controls="false" 
              step-strictly 
              size="mini"/>
          </div>
          <template v-if="!numCondition">
            <div class="filter-panel-input-wrap">
              <el-input
                class="filter-input"
                placeholder="请输入内容"
                size="mini"
                v-model="inputVal"
                @input="onFilter">
                <i slot="append" class="el-icon-search"></i>
              </el-input>
            </div>
            <div class="filter-panel-select-wrap">
              <el-checkbox class="condition-item" v-model="allChecked" @change="onCheckAllChange">全选</el-checkbox>
              <virtual-list 
                ref="conditionList"
                class="virtual-list"
                :data-key="'value'"
                :data-sources="conditionOptions"
                :data-component="itemComponent"
                :estimate-size="19"
                :item-class="'virtual-list-item'"
              />
            </div>
          </template>
          
          <div class="filter-panel-btn-wrap">
            <el-button size="mini" @click="resetFilter">重置</el-button>
            <el-button size="mini" @click="closePop">取消</el-button>
            <el-button type="primary" size="mini" @click="confirmFilter">确定</el-button>
          </div>
        </div>
      </el-popover>
    </template>
    <template slot-scope="scope">
      <slot v-bind:row="scope.row">{{scope.row[prop]}}</slot>
    </template>
  </el-table-column>
</template>

<script>
import {debounce} from '@/utils'
import VirtualList from 'vue-virtual-scroll-list'
import CheckItem from './check-item'
import bus from '@/utils/bus'
export default {
  name: 'QTableColumn',
  componentName: 'QTableColumn',
  inheritAttrs: false,
  components: {
    VirtualList,
    CheckItem
  },
  props: {
    label: String,
    prop: String,
    // text number
    filterType: {
      type: String,
      default: 'text'
    },
    filterOptions: {
      type: Array,
      default() {
        return [];
      }
    },
    useSort: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    isNumberFilter() {
      return this.filterType === 'number';
    },
    isRange() {
      return this.numCondition == 7;
    }
  },
  watch: {
    filterOptions: {
      handler(val) {
        if (Array.isArray(val)) {
          this.initCheck();
        }
      },
      immediate: true
    }
  },
  data() {
    return {
      inputVal: '',
      checkedList: [],
      confirmCheckedList: [],
      conditionOptions: [],
      initOptions: [],
      onFilter: () => {},
      allChecked: true,
      numberConditions: [
        {label: '大于', value: 1},
        {label: '小于', value: 2},
        {label: '等于', value: 3},
        {label: '大于等于', value: 4},
        {label: '小于等于', value: 5},
        {label: '不等于', value: 6},
        {label: '介于', value: 7},
      ],
      numCondition: '',
      numVal: undefined,
      numVal2: undefined,
      isInited: false,
      sortType: '',
      // 做过一次搜索
      hasFiltered: false,

      isFirstShow: true,
      itemComponent: CheckItem,
      lastFilter: {
        number: [],
        sort: ''
      }
    }
  },
  methods: {
    init() {
      this.onFilter = debounce(this.filterOption, 500);
      bus.$on('checkBoxValueChange', (value, checked) => {
        const targetItem = this.conditionOptions.find((item) => item.value === value);
        if (targetItem) {
          targetItem.checked = checked;
          this.setCheckedList();
        }
      });
    },
    initCheck() {
      this.initOptions = this.filterOptions.map(item => ({
        ...item,
        checked: true
      }));
      this.conditionOptions = this.initOptions;
      this.onCheckAllChange(true);
    },
    resetModel() {
      this.sortType = '';
      this.numCondition = '';
      this.numVal = undefined;
      this.numVal2 = undefined;
      this.inputVal = '';
      this.allChecked = true;
      this.conditionOptions = this.initOptions;
      this.onCheckAllChange(true);
    },
    resetFilter() {
      this.hasFiltered = false;
      this.closePop();
      this.resetModel();
      this.confirmFilter();
    },
    confirmFilter() {
      this.confirmCheckedList = [...this.checkedList];
      let emitVal = {
        prop: this.prop,
        filter: {
          checked: this.confirmCheckedList,
          number: [],
          sort: ''
        }
      };

      // 选择了数字筛选时，不进行选择筛选
      if (this.isNumberFilter && this.numCondition) {
        // 数字筛选时，不进行条件选择
        emitVal.filter.checked = [];
        this.confirmCheckedList = [];

        emitVal.filter.number.push(this.numCondition, this.numVal);
        if (this.isRange) {
          emitVal.filter.number.push(this.numVal2);
        }
      }

      // 排序
      if (this.useSort) {
        emitVal.filter.sort = this.sortType;
        // if (!this.hasFiltered) {
        //   delete emitVal.filter.checked;
        //   delete emitVal.filter.number;
        // }
      }

      // if (this.isNumberFilter && this.numCondition || this.confirmCheckedList.length) {
      //   this.hasFiltered = true;
      // }

      this.lastFilter = {
        number: [...emitVal.filter.number],
        sort: emitVal.filter.sort
      }
      
      this.$emit('confirmFilter', emitVal);
      this.closePop();
    },
    closePop() {
      const popRef = this.$refs['filter-pop-' + this.prop];
      if (popRef) {
        popRef.doClose();
      }
    },
    filterOption() {
      const lowerInputVal = this.inputVal.toLowerCase();
      if (!lowerInputVal) {
        this.conditionOptions = this.initOptions;
        this.onCheckAllChange(true);
        this.checkAllChecked();
        return;
      }

      this.conditionOptions = this.initOptions.filter(item => {
        const lowerItemLabel = item.label.toString().toLowerCase();
        return lowerItemLabel.includes(lowerInputVal);
      });
      this.onCheckAllChange(true);
      this.checkAllChecked();
    },
    // 切换选中【全部】时，设置其他项选中状态
    onCheckAllChange(checked) {
      this.conditionOptions.forEach(item => item.checked = checked);
      this.checkedList = checked ? this.conditionOptions.map(item => item.value) : [];
    },
    // 保存当前选中项
    setCheckedList() {
      this.checkedList = this.conditionOptions
        .filter(item => item.checked)
        .map(item => item.value);
      this.checkAllChecked();
    },
    checkAllChecked() {
      // 检查是否选中【全部】
      this.allChecked = this.checkedList.length === this.conditionOptions.length;
    },
    onPopShow() {
      this.conditionOptions = this.initOptions;
      this.inputVal = '';

      if (this.confirmCheckedList.length) {
        this.checkedList = [...this.confirmCheckedList];
        // 设置选中状态
        this.conditionOptions.forEach(item => {
          item.checked = this.checkedList.includes(item.value);
        });
      } else if (this.lastFilter.number.length) {
        this.numCondition = this.lastFilter.number[0] || '';
        this.numVal = this.lastFilter.number[1] || undefined;
        this.numVal2 = this.lastFilter.number[2] || undefined;
        this.onCheckAllChange(true);
        this.checkAllChecked();
      } else {
        this.resetModel();
      }
    },
    afterPopShow() {
      // 防止上次拉到底部，下次打开时显示空白
      if (this.isFirstShow) {
        this.isFirstShow = false;
      } else {
        this.scrollToTop();
      }
    },
    doSort(type) {
      if (!this.sortType || this.sortType != type) {
        this.sortType = type;
        this.confirmFilter();
      } else {
        this.closePop();
      }
    },
    scrollToTop() {
      const virtualList = this.$refs.conditionList;
      if (virtualList) {
        virtualList.scrollToOffset(1);
      }
    }
  },
  created() {
    this.init();
  }
}
</script>

<style lang="stylus" scoped>
.header-label
  display inline-flex
  align-items center
  line-height 23px
  cursor pointer
  padding 0
.filter-pop
  padding 10px 5px
.filter-panel
  .filter-input
    >>> .el-input-group__append
      padding 0 10px
  &-sort-wrap
    display flex
    align-items center
    justify-content space-around
    margin-bottom 10px
  &-number-wrap
    display flex 
    align-items center
    margin-bottom 10px
    >span 
      flex none 
      width 65px
    .number-select
      width 120px
    .number-input
      margin-left 5px
      width 80px
      >>> .el-input__inner
        padding 0 5px
  &-select-wrap
    box-sizing border-box
    padding 5px
    margin-top 10px
    width 100%
    // height 200px
    border 1px solid #DCDFE6
    // overflow auto
    .virtual-list
      width 100%
      height 200px
      overflow auto
    .condition-item
      display block
  &-btn-wrap
    margin-top 10px
    text-align right
.active
  color #fff
  background-color #0BB78E
  border-color #0BB78E
</style>