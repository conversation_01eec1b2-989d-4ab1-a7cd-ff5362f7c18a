import { getSum, dealBigMoney } from "../common";
import { formatNumber, moneyDelete, moneyFormat } from "@/utils";
export default {
  data() {
    return {
      loading: false,
      tableList: [],
      remarks: "",
      smallNum: 0,
      bigNum: "",
      idList: [],
      params: {},
    };
  },

  mounted() {
    this.getList();
    this.$bus.$off('customizedTable');
    this.$bus.$on("customizedTable", (data) => {
      this.tableList = this.tableList.map((item) => {
        if (data[0].accountingMonth == item.accountingMonth) {
          item = data[0];
        }
        return item;
      });
      this.smallNum = moneyFormat(getSum(this.tableList));
      this.bigNum = dealBigMoney(this.smallNum);
    });
  },
  methods: {
    filterData(value) {
      return moneyDelete(value);
    },
    //获取工资单列表
    getList() {
      let mapObj = {
        attendances: {
          itemType: "1",
          sortNo: "1",
        },
        salary: {
          itemType: "2",
          sortNo: "2",
        },
        otherDeduct: {
          itemType: "3",
          sortNo: "3",
        },
        uniformDeduct: {
          itemType: "4",
          sortNo: "4",
        },
        living: {
          itemType: "5",
          sortNo: "5",
        },
        insurance: {
          itemType: "6",
          sortNo: "6",
        },
        labour: {
          itemType: "7",
          sortNo: "7",
        },
        loan: {
          itemType: "8",
          sortNo: "8",
        },
        totalDeduct: {
          itemType: "9",
          sortNo: "9",
        },
        actualSalary: {
          itemType: "10",
          sortNo: "10",
        },
        leaveDeduct: {
          itemType: "11",
          sortNo: "11",
        },

      };
      this.$bus.$off('customizedMonth');
      this.$bus.$on("customizedMonth", (data) => {
        console.log('data', data);
        const { editData, obj } = data;
        let details = [];
        let list = [];
        this.tableList = [];
        let months = this.tableList.map((item) => item.accountingMonth);
        let editMonths = editData.map((item) => item.accountingMonth);
        editData.forEach((it) => {
          list.push(it);
          // if (!months.includes(it.accountingMonth)) {
          //   list.push(it);
          // }
        });
        this.tableList.forEach((item, index) => {
          if (!editMonths.includes(item.accountingMonth)) {
            this.tableList.splice(index, 1);
          }
        });
        this.loading = true;
        console.log('list', list);
        list &&
          list.forEach((item) => {
            Object.keys(item).forEach((keys) => {
              for (const key in mapObj) {
                if (keys === key) {
                  mapObj[key] = {
                    ...mapObj[key],
                    sysAmount: item[key] || "",
                    settlementAmount: item[key] || "",
                    remark: "",
                  };
                }
              }
            });
            details.push({ accountingMonth: item.accountingMonth, ...mapObj });
          });
        console.log('details', details);
        this.tableList = [...this.tableList, ...details].sort((a, b) => new Date(a.accountingMonth).getTime() - new Date(b.accountingMonth).getTime());
        console.log('this.tableList', this.tableList);
        this.smallNum = moneyFormat(getSum(this.tableList));
        this.bigNum = dealBigMoney(this.smallNum);
        this.loading = false;
      });
    },
    //选择月份
    seleteMonth() {
      this.$refs.staffInfo.validate((valid) => {
        if (!valid) return;
        if (!this.isExist) {
          this.$message.warning("请先查询员工是否存在？");
          return;
        }
        for (const [key, val] of Object.entries({
          type: this.formData.type,
          staffCode: this.staffInfo.staffCode,
          staffName: this.staffInfo.staffName,
          factoryId: this.staffInfo.factoryId,
          factoryName:
            this.tabList &&
            this.tabList.find((item) => item.id === this.staffInfo.factoryId)
              .name,
        })) {
          if (typeof val !== "undefined" && val !== null && val !== "") {
            this.params[key] = val;
          }
        }
        this.$bus.$emit("customizedClick", {
          title: "选择月份",
          isShow: true,
          data: {
            months: this.tableList.map((item) => item.accountingMonth),
            obj: {
              ...this.params,
              isClearMonth: this.isClearMonth,
            },
          },
        });
      });
    },
    setTitle(title) {
      if (!this.idList.length) {
        this.$message.warning("请先勾选工资单详情数据");
        return;
      }
      if (this.idList.length > 1) {
        this.$message.warning("请勿选中多行数据");
        // this.$refs.tablePayroll.clearSelection();
        return;
      }
      this.$bus.$emit("customizedClick", {
        title,
        isShow: true,
      });
      let factoryName;
      if (
        /^(?:[\u3400-\u4DB5\u4E00-\u9FEA\uFA0E\uFA0F\uFA11\uFA13\uFA14\uFA1F\uFA21\uFA23\uFA24\uFA27-\uFA29]|[\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0])+$/.test(
          this.staffInfo.factoryId
        )
      ) {
        factoryName = this.staffInfo.factoryId;
      } else {
        factoryName = this.tabList.find(
          (item) => item.id === this.staffInfo.factoryId
        ).name;
      }
      this.$nextTick(() => {
        this.$bus.$emit("customizedList", {
          editData: this.idList,
          obj: {
            accountingMonth: this.idList[0].accountingMonth,
            staffCode: this.staffInfo.staffCode,
            staffName: this.staffInfo.staffName,
            factoryName,
            tableName: this.formData.tableName,
            count: this.idList[0].count,
          },
        });
      });
    },
    //编辑
    handleEdit() {
      this.setTitle("编辑");
    },
    //修改记录
    modifyRecord() {
      this.setTitle("修改记录");
    },
    //删除
    handleDelete() {
      if (!this.idList.length) {
        this.$message.warning("请选择工资单详情数据");
        return;
      }
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        console.log(this.idList);
        const accountingMonth =
          this.idList && this.idList.map((item) => item.accountingMonth);
        accountingMonth.forEach((month) => {
          let findIndex = this.tableList.findIndex(
            (it) => month == it.accountingMonth
          );
          this.tableList.splice(findIndex, 1);
        });
      });
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.count && row.count > 1) {
        return "table_row";
      }
      return "";
    },
    handleSelectionAll() { },
    handleSelectionChange(val) {
      val && (this.idList = val);
    },
  },
};
