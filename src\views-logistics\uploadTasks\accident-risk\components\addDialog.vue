<template>
  <qDialog
    :visible="isVisible"
    :title="title"
    :innerScroll="false"
    width="600px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <el-form ref="addForm" :rules="rules" :model="addForm" label-width="100px">
      <el-form-item label="厂牌编号:" prop="assignStaffCode">
        <el-input clearable v-model.trim="addForm.assignStaffCode" v-if="title == '新增'">
          <template slot="append">
            <el-button type="primary" @click="query('assignStaffCode')">查询</el-button>
          </template>
        </el-input>
        <span v-else>{{addForm.staffCode}}</span>
        <span class="error_info" v-if="showTipsCode">该人员无考勤数据！</span>
      </el-form-item>
      <el-form-item label="身份证号:" prop="idCard">
        <el-input clearable v-model.trim="addForm.idCard" v-if="title == '新增'">
          <template slot="append">
            <el-button type="primary" @click="query('idCard')">查询</el-button>
          </template>
        </el-input>
        <template v-else>
          <span style="margin-right: 10px">{{addForm.idCard}}</span>
          <i
            v-if="addForm.idCard"
            :class="['iconfont', showType ? 'icon-guanbi' : ' icon-yincangmima']"
            @click="look"
          ></i>
        </template>
         <span class="error_info" v-if="showTipsIdCard">该人员无考勤数据！</span>
      </el-form-item>
      <el-form-item label="员工姓名:" prop="staffName">{{addForm.staffName}}</el-form-item>
      <el-form-item label="核算月份:" prop="accountingMonth">{{addForm.accountingMonth }}</el-form-item>
      <el-form-item label="分配厂牌:" prop="staffCode">
        <el-radio-group v-model="addForm.staffCode">
          <el-radio v-for="item in codeList" :label="item" :key="item">
            {{item}}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="意外险扣款:" prop="deductAmount">
        <el-input
          oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
          v-model="addForm.deductAmount"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="考勤状态:">
        <span :style="{ color: attendStatus == 'abnormal' ? 'red' : '#0BB78E' }">
          {{ attendStatus == 'abnormal' ? '异常': '正常'}}
        </span>
      </el-form-item>
      <el-form-item label="分配状态:">
        <span :style="{ color: allocationStatus == 'abnormal' ? 'red' : '#0BB78E' }">
          {{ allocationStatus == 'abnormal' ? '异常': '正常'}}
        </span>
      </el-form-item>
      <el-form-item label="备注:" prop="comments">
      <el-input
        type="textarea"
        v-model="addForm.comments"
        rows="3"
        maxlength="300"
        show-word-limit
        resize="none"
      >
      </el-input>
      </el-form-item>
    </el-form>
  </qDialog>
</template>

<script>
export default {
  name: "addDialog",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    id: String,
  },
  computed: {
    isVisible: {
      get() {
        return this.visible
      },
      set(newVal) {
        this.$emit('update:visible',newVal)
      }
    },
  },
  data() {
    return {
      codeList: [],
      showType: false,
      routeParames: [],
      showTipsCode: false,
      showTipsIdCard: false,
      addForm: {
        id: '',
        staffCode: '',
        idCard: '',
        staffName: '',
        accountingMonth: '',
        assignStaffCode: '',
        deductAmount: '',
        comments: '',
        idCardMd5: '',
      },
      idCardRsa: '',
      isLoading: false,
      encryptionIdCard: '',
      attendStatus: '',
      allocationStatus: '',
      rules: {
        assignStaffCode: [
          {
            required: true,
            message: "分配厂牌不能为空",
            trigger: "blur",
          },
        ],
        idCard: [
          {
            required: true,
            message: "身份证号不能为空",
            trigger: "blur",
          },
        ],
        staffCode: [
          {
            required: true,
            message: "厂牌编号不能为空",
            trigger: "blur",
          },
        ],
       
        deductAmount: [
          {
            validator: (rule, value, callback) => {
              if (!/^-?\d{1,5}(\.\d{1,2})?$/.test(value)) {
                return callback(new Error("小数点前面仅支持5位数,小数点后面仅支持2位数"));
              }
              callback();
            },
            trigger: "blur",
          },
        ],
      },
    };
  },
  mounted() {
    this.routeParames = JSON.parse(this.$Base64.decode(this.$route.query.data))
    if (this.title == '编辑') {
      this.getDetail()
    }
  },
  methods: {
    query(name) {
      this.$refs.addForm.validateField(name, (valid) => {
        if(!valid) {
          const { factoryId,accountingMonth} = this.routeParames
          let params = {}
          if (name == 'assignStaffCode') {
            params = {factoryId,accountingMonth, staffCode: this.addForm.assignStaffCode }
          } else {
            params = {factoryId,accountingMonth, idCard: this.addForm.idCard }
          }
          this.$api.logisticsDataUpload.employeeAttendance.queryStaff(params)
          .then(res => {
            if (res.success && res.data) {
              for(let [key,value] of Object.entries(this.addForm)) {
                if (key !== name) {
                  this.addForm[key] = res.data[key]
                }
              }
              this.codeList = res.data.staffCodes
              this.addForm.staffCode = res.data.staffCodes.length == 1 ? res.data.staffCodes[0] : ''
              this.showTipsCode = false
              this.showTipsIdCard = false
            } else {
              if (name == 'assignStaffCode') {
                this.showTipsCode = true
              } else {
                this.showTipsIdCard = true
              }
            }
          })
        }
      })
      
      // let params = {"moduleId":"4","factoryId":"TEST","accountingMonth":"2023-11","staffCode":"QY-********"}
    },
    look() {
      if (!this.showType) {
        this.$api.information.employee.decrypt(this.idCardRsa)
        .then(res => {
          if (res.success) {
            this.addForm.idCard = res.data
            this.showType = true
          }
        })
      } else {
        this.addForm.idCard = this.encryptionIdCard
         this.showType = false
      }
    },
    handleCancel() {
      this.isVisible = false
      this.$refs.addForm.resetFields();
    },
    handleConfirm() {
      this.showTipsCode = false
      this.showTipsIdCard = false
      const { staffCode,deductAmount } = this.addForm
      if (!staffCode) {
        this.$message.warning("请选择分配厂牌");
      } else if (!deductAmount) {
        this.$message.warning("请输入社保扣款");
      } else {
        this.isLoading = true
        this.addForm.factoryId = this.routeParames.factoryId
        this.$api.accidentRisk.addOrEdit(this.addForm)
          .then(res => {
            if (res.success) {
                this.$notify.success({
                title: "成功",
                message: `${this.title}成功`,
              });
              this.handleCancel()
              this.$emit('addOrEditSuccess')
            }
          }).finally(err => {
            this.isLoading = false
          })
      }
    },
    getDetail() {
      this.$api.accidentRisk.viewDetail(this.id).then(res => {
        if (res.success) {
          for(let [key,value] of Object.entries(this.addForm)) {
            this.addForm[key] = res.data[key]
          }
          const {staffCodes,idCardRsa,idCard,attendStatus,allocationStatus} = res.data
          this.codeList = staffCodes
          this.idCardRsa = idCardRsa
          this.encryptionIdCard =idCard
          this.attendStatus = attendStatus
          this.allocationStatus = allocationStatus
        }
      })
    }
  },
};
</script>

<style lang="stylus" scoped>
>>>.el-form-item__label:before{
    display: none
}
>>>.el-input-group__append {
  background: #24c69a;
  color: #fff;

  .el-button {
    padding: 5px 25px;
  }
}
.error_info{
    color: #F23D20;
    font-size: 12px;
    line-height: 1;
    padding-top: 4px;
    position: absolute;
    top: 100%;
    left: 0;
}
</style>
