<template>
  <qDialog
    :visible="isVisible"
    :innerScroll="true"
    :isAuto="true"
    :title="title"
    width="850px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <el-form
      :model="calculateForm"
      ref="calculateForm"
      label-width="120px"
      :rules="rules"
      size="small"
      :key="upKey"
    >
    <el-row>
        <el-col :span="24">
          <el-form-item label="是否核算:" prop="isAccounting">
            <el-radio-group v-model="calculateForm.isAccounting"  > 
               <el-radio    :label="1" >是</el-radio>
                <el-radio   :label="0" >否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <template v-if="calculateForm.isAccounting == 0">
        <el-row>
          <el-col :span="24">
            <el-form-item label="选择原因:">
              <el-select
                v-model="calculateForm.resaonType"
                filterable
                clearable
                placeholder="请选择原因"
              >
                <el-option
                  v-for="item in reasonList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="calculateForm.resaonType == 3">
          <el-col :span="24">
            <el-form-item label="其他说明:">
              <el-input
                type="textarea"
                placeholder="请输入内容"
                v-model="calculateForm.noAccountingRemarks"
                maxlength="45"
                show-word-limit
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <template v-else>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="流程编号:">
            {{ calculateForm.processCode }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="processCode" label="工厂名称:">
            {{ calculateForm.factoryName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="厂牌编号:" prop="staffCode">
            {{ calculateForm.staffCode }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="processCode" label="员工姓名:">
            {{ calculateForm.staffName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="执行年月:" prop="accountingMonth">
            {{ calculateForm.accountingMonth }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="申请日期:" class="processCode" prop="applyDate">
            {{ calculateForm.applyDate }}
          </el-form-item>
        </el-col> 
      </el-row> 
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="核算工序:" prop="processName">
            {{ calculateForm.processName }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="分配方式:" class="processCode" prop="allotMethod">
            {{ calculateForm.allotMethod }}
          </el-form-item>
        </el-col> 
      </el-row> 
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="类型:" prop="type">
            {{ calculateForm.type }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="金额:" class="processCode" prop="amount">
            {{ calculateForm.amount }}
          </el-form-item>
        </el-col> 
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="实际核算工厂:" prop="actualFactoryId">
               <el-select
                    v-model="calculateForm.actualFactoryId"
                    placeholder="请选择实际核算工厂"
                    filterable
                  >
                    <el-option
                      v-for="item in tabList"
                      :key="item.name"
                      :label="item.label"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="实际核算工序:"   prop="actualProcessId">
              <el-select
                    v-model="calculateForm.actualProcessId"
                    placeholder="请选择实际核算工序"
                    filterable
                  >
                    <el-option
                      v-for="item in processList"
                      :key="item.value"
                      :label="item.name"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
          </el-form-item>
        </el-col> 
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="实际执行年月:" prop="actualAccountingMonth">
              <el-date-picker 
                v-model="calculateForm.actualAccountingMonth"
                type="month"
                value-format="yyyy-MM"
                placeholder="选择实际执行年月" 
              >
              </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12"> 
          <el-form-item v-if="calculateForm.allotMethod=='person'||calculateForm.allotMethod=='个人'" label="实际核算厂牌:" prop="actualStaffCode">
            <el-select
                v-model="calculateForm.actualStaffCode"
                filterable 
                placeholder="请选择实际核算厂牌"
              >
                <el-option
                  v-for="item in deductionList"
                  :key="item.staffCode"
                  :label="item.staffCode"
                  :value="item.staffCode"
                >
                </el-option>
              </el-select>
          </el-form-item>
        </el-col> 
      </el-row>
      <el-row :gutter="10"> 
        <el-col :span="12">
            <el-form-item label="实际增扣金额:" prop="actualAmount"> 
              <el-input 
                oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
                v-model.trim="calculateForm.actualAmount"
                clearable
                @blur="onBlur"
                placeholder="请输入实际增扣金额" 
              >
               <template slot="append">元</template>
              </el-input> 
            </el-form-item>

        </el-col> 
      </el-row>  
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注说明:" prop="remark">
            <el-input

              type="textarea"
              v-model="calculateForm.remarks"
              resize="none"
              rows="3"
              show-word-limit
              maxlength="100"
              placeholder="请输入备注说明"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row> 
    </template>
    </el-form>
  </qDialog>
</template>

<script>
import moment from "moment";
import {  moneyDelete } from "@/utils";
import NP from "number-precision";
NP.enableBoundaryChecking(false);
export default {
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    editForm: Object,
  },
  computed: { 
  },
  data() {
    return {
      upKey:0, 
      detailsList: [],
      dateList: [], 
      isShowRealAmount: false,
      deductionList: [], // 厂牌下拉
      calculateForm: {
        resaonType:"",
        noAccountingRemarks:"", 
        processCode: "",
        factoryName:"",
        staffCode:"",
        staffName:"",
        isAccounting:1,
        accountingMonth:"",
        applyDate:"",
        processName:"",
        allotMethod:"",
        type:"",
        amount:"",
        actualFactoryId:"",
        actualProcessId:"",
        actualAccountingMonth:"",
        actualAmount:"",
        remarks:"",  
      },
      reasonList: [
        { label: "无需核算", value: "无需核算" }, 
         { label: "人力资源部", value: "人力资源部" }, 
        { label: "其他原因 ", value: "3" },
      ],
      proofs: [],
      fileList: [],
      filterParam: {},
      rules: { }, 
      tabList: [],
      isLoading: false,
      isBlur: false,
      isConfirm: false,
      unpaidDetails: [],
      num: 1,
      payingAmount: "",
      actualDeductAmount:"",
    };
  },
  async created() { 
      await this.$api.plateTypeSystemManage.getBasicPermission
        .getBasicPermissionAll()
        .then((res) => {
          this.tabList =
            res.data.map((item) => ({
              label: item.name,
              name: item.name,
              id: item.id,
              ...item
            })) || [];
        }); 
  },
  computed:{
    processList() { 
       return  (this.calculateForm.actualFactoryId &&
            this.tabList.find((item) => item.id == this.calculateForm.actualFactoryId)
              .process) ||
          [] 
    },
  },
  watch: { 
    editForm: {
      handler(value) {
         
        this.calculateForm = { ...this.calculateForm,...value}
        this.getAllStaffCode();
        if(this.calculateForm.allotMethod=='person'){
           this.rules={
           
        actualFactoryId: [
          { required: true, message: "请选择实际核算工厂", trigger: "change" },
        ],
        actualProcessId: [
          { required: true, message: "请选择实际核算工序", trigger: "change" },
        ],
        actualAccountingMonth: [
          { required: true, message: "请选择实际执行年月", trigger: "change" },
        ],
 
        actualStaffCode: [
          { required: true, message: "请输入实际核算厂牌", trigger: "change" },
        ],

        actualAmount: [
          { required: true, message: "请输入实际金额", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (!/^-?\d{1,9}(\.\d{1,2})?$/.test(moneyDelete(value))) {
                callback(
                  new Error("小数点前面仅支持9位数,小数点后面仅支持2位数")
                );
              }
              callback();
            },
            trigger: "blur",
          },
        ],
      
         }
        }else{
           this.rules={
           
        actualFactoryId: [
          { required: true, message: "请选择实际核算工厂", trigger: "change" },
        ],
        actualProcessId: [
          { required: true, message: "请选择实际核算工序", trigger: "change" },
        ],
        actualAccountingMonth: [
          { required: true, message: "请选择实际执行年月", trigger: "change" },
        ],
 
        
        actualAmount: [
          { required: true, message: "请输入实际金额", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (!/^-?\d{1,9}(\.\d{1,2})?$/.test(moneyDelete(value))) {
                callback(
                  new Error("小数点前面仅支持9位数,小数点后面仅支持2位数")
                );
              }
              callback();
            },
            trigger: "blur",
          },
        ],
      
         }
        }
        
         
      },
      immediate: true,
      deep: true,
    }, 
  },
  methods: {  
    getAllStaffCode() {
      let params = {
        staffCode: this.calculateForm.staffCode,
      };
      this.$api.plateTypeInformation.rewardLedger
        .getAllStaffCode(params)
        .then((res) => {
          this.deductionList = res.data;
          // 有且只有一条默认回显
          if (res.data && res.data.length == 1) {
            this.calculateForm.actualStaffCode = res.data[0].staffCode;
            this.$forceUpdate();
            this.upKey++;
          }
        });
    },
        //根据厂牌编号查询
    searchStaffCode() {
      this.$refs.calculateForm.validateField(["staffCode"], async (valid) => {
        if (valid) return;
        await this.$api.information.employee
          .employeeDetails({ staffCode: this.calculateForm.staffCode })
          .then(({ data }) => { 
            this.calculateForm = {
              ...this.calculateForm,
              staffName: (data && data.staffName) || "",
            };
          });
      });
    },
    onBlur() {
      this.calculateForm.actualAmount = moneyDelete(
        this.calculateForm.actualAmount
      ); 
    },  
 
    handleCancel() { 
      this.$emit("cancel", {
        type: "cancel",
        isVisible: false,
      });
    },
    handleConfirm() { 
      if(this.calculateForm.isAccounting == 0){
        if(!this.calculateForm.resaonType){
          this.$message.error("请选择原因");
          return
        }
        if(this.calculateForm.resaonType == 3 && !this.calculateForm.noAccountingRemarks ){
          this.$message.error("请填写其他原因");
          return
        }
        this.isLoading = true 
        let params = {
          id:this.calculateForm.id,
          isAccounting:this.calculateForm.isAccounting,
           
        }
        if(this.calculateForm.resaonType == 3){
            params.noAccountingRemarks=  '其他原因:' + this.calculateForm.noAccountingRemarks    
        }else{
            params.noAccountingRemarks=this.calculateForm.resaonType
        }
        this.$api.plateTypeInformation.orderBomRevision
          .orderBomRevisionAccounting(params)
          .then((data) => {
            this.$notify.success({
              title: "成功",
              message: "操作成功",
            });
            this.$emit("cancel", {
              type: "confirm",
              isVisible: false,
            });
          })
          .finally(() => {
            this.isLoading = false;
          });
        return
      }

      this.$refs.calculateForm.validate((valid) => {
        if (!valid) return;
          this.isLoading = true;
        let params = {
         ...this.calculateForm
        };
        this.$api.plateTypeInformation.orderBomRevision
          .orderBomRevisionAccounting(params)
          .then((data) => {
            this.$notify.success({
              title: "成功",
              message: "核算成功",
            });
            this.$emit("cancel", {
              type: "confirm",
              isVisible: false,
            });
          })
          .finally(() => {
            this.isLoading = false;
          });
      })
      
    }, 
  },
};
</script>

<style lang="stylus" scoped>
>>>.el-form-item__label {
  text-align: left;
}

.processCode {
  >>>.el-form-item__label {
    &::before {
      content: '*';
      color: #F23D20;
      margin-right: 4px;
      visibility: hidden;
    }
  }
}

>>>.el-input-group__append {
  background: #24c69a;
  color: #fff;

  .el-button {
    padding: 5px 10px;
  }
}

>>>.el-select, >>>.el-date-editor {
  width: 100%;
}

.el-col {
  padding-right: 10px;
}

.el-upload__tip {
  padding-left: 10px;
  display: inline-block;
  color: #24c69a;
}

>>>.el-textarea__inner {
  padding-right: 50px;
}

.el-textarea {
  >>>.el-input__count {
    right: 20px !important;
  }
}
.details {
  display: flex;
  padding-bottom: 18px;

  &_content {
    flex: 1;
    display: flex;
    justify-content: space-between;

    >.el-select, >.el-input, >span {
      flex: 1 0 30%;
    }
  }

  &_btn {
    width: 20%;

    >.el-button {
      mar;
    }
  }
}
.deduction {
  width: 100%;
  margin: 0;
  padding: 0;

  li {
    span {
      padding-right: 10px;
    }
  }
}
</style>
