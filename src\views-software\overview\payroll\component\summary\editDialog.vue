<template>
  <qDialog :visible="visible"
    :innerScroll="true"
    :innerHeight="300"
    title="手动核算编辑"
    width="1000px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel">
    <p>
      <span
        >线下核算合计:{{getTotal.amount }}</span>
      <span style="margin: 0 10px">提取余留合计:{{getTotal.extractResidual }}</span>
      <span>其他划入合计:{{getTotal.inAmount }}</span>
      <span style="margin-left:10px">其他划出合计:{{getTotal.outAmount }}</span>
    </p>
    <section>
      <div class="header">
        <div class="item">核算大工序
        </div>
        <div class="item">
          线下核算</div>
        <div class="item">
          提取余留</div>
        <div class="item">
          其他划入</div>
        <div class="item">
          其他划出</div>
      </div>
      <ul class="raw_data"
        v-for="item in tableHeader"
        :key="item.processId">
        <li class="item">
          {{ item.processName }}
        </li>
        <li class="item">
          <el-input
            onInput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
            v-model="item.amount"
            clearable
            placeholder=""
            @blur="onBlur(item,'amount')"
            @clear="item.amount='0.00'"></el-input>
        </li>
        <li class="item">
          <el-input
            onInput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
            v-model="item.extractResidual"
            clearable
            placeholder=""
            @blur="onBlur(item,'extractResidual')"
            @clear="item.extractResidual='0.00'"></el-input> 
        </li>
        <li class="item">
          <el-input
            onInput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
            v-model="item.inAmount"
            clearable
            placeholder=""
            @blur="onBlur(item,'inAmount')"
            @clear="item.inAmount='0.00'"></el-input>
        </li>
        <li class="item">
          <el-input
            onInput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
            v-model="item.outAmount"
            clearable
            placeholder=""
            @blur="onBlur(item,'outAmount')"
            @clear="item.outAmount='0.00'"></el-input>
        </li>
      </ul>
    </section>
  </qDialog>
</template>

<script>
import { moneyFormat, moneyDelete } from "@/utils";
import NP from "number-precision";
export default {
  name: "editFinancial",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      editForm: {
        bankCardAmount: "",
        cashAmount: "",
        delayedAmount: "",
        leaveSelfAmount: "",
        resignationAmount: "",
      },
      filterParam: {},
      tableHeader: [],
      tableData: [],
      list: [],
      total: 0,
      isLoading: false
    };
  },
  created() {
    this.getDetail();
  },
  computed: {
    getTotal() {
      let total = this.tableHeader.reduce(
        (pre, cur) => ({
          amount: NP.plus(pre.amount, Number(moneyDelete(cur.amount))),
          extractResidual: NP.plus(pre.extractResidual, Number(moneyDelete(cur.extractResidual))),
          inAmount: NP.plus(pre.inAmount, Number(moneyDelete(cur.inAmount))),
          outAmount: NP.plus(pre.outAmount, Number(moneyDelete(cur.outAmount))),
        }),
        { amount: 0, extractResidual: 0, inAmount:0, outAmount:0 }
      );
      for (const key in total) {
        total[key] = moneyFormat(total[key]);
      }
      return total;
    }
  },
  methods: {
    //详情
    getDetail() {
      this.$api.softwareWorkbench
        .viewDetail({
          factoryId: JSON.parse(this.$route.query.data).factoryId,
          accountingMonth: JSON.parse(this.$route.query.data).accountingMonth,
        })
        .then(({ data }) => {
          this.tableHeader =
            data.map((item) => ({
              ...item,
              amount: item.amount != null && moneyFormat(item.amount) || '0.00',
              extractResidual: item.extractResidual != null && moneyFormat(item.extractResidual) || '0.00',
              inAmount: item.inAmount != null && moneyFormat(item.inAmount) || '0.00',
              outAmount: item.outAmount != null && moneyFormat(item.outAmount) || '0.00',
            })) || [];
        });
    },
    onBlur(item, name) {
      if (!this.setTitle(item[name]).type) {
        this.$message.warning(this.setTitle(item[name]).message);
        item[name] = '0.00'
        return
      }
      item[name] = moneyFormat(moneyDelete(item[name]));
    },
    setTitle(amount) {
      if (amount == "")
        return {
          message: "金额不能为空",
          type: false,
        };
      if (!/^-?\d{1,7}(\.\d{1,2})?$/.test(moneyDelete(amount)))
        return {
          message: "小数点前面仅支持7位数,后面仅支持2位数",
          type: false,
        };
      return {
        message: "",
        type: true,
      };
    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      this.isLoading = true;
      const { factoryId, accountingMonth } = JSON.parse(this.$route.query.data);
      let list = this.tableHeader.map((item) => ({
        factoryId,
        accountingMonth,
        processId: item.processId,
        amount: moneyDelete(item.amount),
        extractResidual: moneyDelete(item.extractResidual),
        inAmount: moneyDelete(item.inAmount),
        outAmount: moneyDelete(item.outAmount),
      }));
      this.$api.softwareWorkbench.editManualAccounting(list).then(() => {
        this.$notify.success({
          title: "成功",
          message: "编辑成功",
        });
        this.$emit("cancel", "confirm");
      }).finally(()=>{
            this.isLoading = false;
      });
    },
  },
};
</script>

<style lang="stylus" scoped>
$border = 1px solid #ccc;

>>>.el-row {
  margin: 0 !important;
}

ul {
  padding: 0;
}

section {
  border: solid 1px #ccc;
  padding: 15px;

  .header, .raw_data {
    display: flex;
  }

  .item {
    flex: 1;
  }

  .raw_data {
    align-items: center;

    li {
      line-height: 20px;

      .el-input {
        width: 80%;
      }
    }
  }
}
</style>
