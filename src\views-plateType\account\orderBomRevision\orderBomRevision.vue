<template>
  <content-panel class="panel-tabs">
    <el-row class="tabs-row">
      <el-col :span="16">
        <el-tabs v-model="activeTab" class="tabs">
         <el-tab-pane v-for="tab in getTabPermission" :key="tab.name" :name="tab.name" :label="tab.label">
             <span slot="label" v-if="tab.label!=='分配汇总表'">{{
              tab.number != null ? `${tab.label}(${tab.number})` : tab.label
            }}</span>
            <span slot="label" v-else>{{
             tab.label
            }}</span>
         </el-tab-pane>
        </el-tabs>
      </el-col>
      
      <el-col :span="8" class="table-btn-area" v-show="activeTab&& activeTab !== 'defaultNmae'">
        <el-checkbox
            v-model="selectall" 
            type="primary"
            v-if="activeTab=='processed'||activeTab=='untreated'"
            label="全选"
            border
            size="mini"
            style="margin-right: 10px"
            class="select"
          ></el-checkbox>
        <el-button  v-permission="'was-customized$informationAccount$plateType$orderBomRevision$allocationUpdate'"  @click="updateView" v-show="activeTab == 'allocationSum'" size="small" type="primary"  >
            更新分配
          </el-button> 
          <el-button
            v-show="activeTab == 'processed'"
            size="small"
            type="primary"
             v-permission="'was-customized$informationAccount$plateType$orderBomRevision$import'"
            @click="handleImport"
          >
            导入
          </el-button>
          <el-button 
            v-show="activeTab == 'processed'"
            size="small"
             v-permission="'was-customized$informationAccount$plateType$orderBomRevision$add'"
            type="primary"
            @click="handleAdd"
          >
            新增
          </el-button>
          <el-button
            v-show="activeTab == 'untreated'"
            size="small"
            type="primary"
             v-permission="'was-customized$informationAccount$plateType$orderBomRevision$batchAccounting'"
            @click="batchClculate"
          >
            批量核算
          </el-button>
          <el-button
            v-show="activeTab == 'processed'"
             v-permission="'was-customized$informationAccount$plateType$orderBomRevision$batchReturn'"
            size="small"
            type="primary"
            @click="batchBack"
          >
            批量退回
          </el-button>
          <el-button
            v-show="activeTab == 'untreated'"
            v-permission="'was-customized$informationAccount$plateType$orderBomRevision$batchDelete'"
            size="small"
            type="primary"
            @click="batchDelete"
          >
            批量删除
          </el-button>
           <el-button size="small" type="primary" @click="handleExport">
            导出
          </el-button>
      </el-col>
    </el-row>
    <!-- <keep-alive> -->
       <component @clearSelection="clearSelection" @updateNumber="updateNumber"   :is="activeTab" ref="PlateTypeOrderBomRevisionRef"></component>
     <!-- </keep-alive>  -->
  </content-panel>
</template>

<script>
import {getPermitList} from '@/store/modules/permission'
import AllocationSum from './components/allocationSum.vue';
import Processed from './components/processed.vue';
import Completed from './components/completed.vue'; 
import Untreated from './components/untreated.vue'; 
import defaultNmae from "./components/defaultNmae.vue"; 
const tabPermissions=[
    {
    name: 'untreated',
    label: '未处理',
    permission:  'was-customized$informationAccount$plateType$orderBomRevision$untreated'
  },
  {
    name: 'processed',
    label: '已处理',
    permission: 'was-customized$informationAccount$plateType$orderBomRevision$processed'
  },
  {
    name: 'completed',
    label: '已完成', 
    permission: 'was-customized$informationAccount$plateType$orderBomRevision$completed'
  },
   {
    name: 'allocationSum',
     label: '分配汇总表', 
    permission: 'was-customized$informationAccount$plateType$orderBomRevision$allocationSum'
  }
] 
export default {
  name: 'OrderBomRevision',
  components: {
    AllocationSum,
    Processed,
    Completed,
    Untreated,
    defaultNmae
  },
  data() {
    return {
      activeTab: 'defaultNmae',
      updateVisible: false,
      selectall:false, 
      completed:0,
      hasProcessed:0,
      unProcessed:0,
    };
  }, 
  created() { 
        this.handleStatistic()  
  }, 
  watch: { 
    getTabPermission: {
      handler(newVal) { 
        if (newVal.length > 0 && this.activeTab=='defaultNmae') {
        this.activeTab = newVal[0].name;
      } 
    },
    deep: true,
    immediate: true,
  }, 
  activeTab: {
      handler(newVal) {  
        if (this.$refs&&this.$refs.PlateTypeOrderBomRevisionRef) {  
          this.handleStatistic()
          this.selectall=false
      } 
    },
    deep: true,
    immediate: true,
  }, 
   selectall: {
      handler(value) {
        this.$refs.PlateTypeOrderBomRevisionRef.selectall=value
        if (this.selectall) {
          this.$refs.PlateTypeOrderBomRevisionRef.$refs.tableRef.toggleAllSelection();
        } else {
          this.$refs.PlateTypeOrderBomRevisionRef.$refs.tableRef.clearSelection();
        }
      },
    },
},
  computed: { 
    numberMap(){
      return{
          completed:this.completed,
          processed:this.hasProcessed,
          untreated:this.unProcessed
      }
    },
       // 获取当前tab的权限
    getTabPermission() {
      const permitList = getPermitList();  
      return tabPermissions.filter(tab => permitList.includes(tab.permission)).map(item => { 
              return {
                  ...item,
                  number: this.numberMap[item.name] || 0
              };
          });
    },
  },
  methods: { 
    updateNumber(){
      this.handleStatistic()
    },
  //导出
    handleExport() {
      let exportForm = JSON.parse(JSON.stringify(this.$refs.PlateTypeOrderBomRevisionRef.filterParam));
      this.$api.common
        .doExport(this.activeTab == 'allocationSum' ? 'plankExportBomWageAllot' : 'plankExportOrderBomWage', { ...exportForm })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success("导出操作成功，请前往导出记录查看详情");
          }
        });
    }, 
    //查询统计
    handleStatistic(){
        let exportForm = {}
          if (this.$refs&&this.$refs.PlateTypeOrderBomRevisionRef) {  
            exportForm=JSON.parse(JSON.stringify(this.$refs.PlateTypeOrderBomRevisionRef.filterParam));
          }
        delete exportForm.handleStatus
         this.$api.plateTypeInformation.orderBomRevision.orderBomReStatistic({
          ...exportForm,
         
        }).then((res) => {
           if (res.code == 200) {  
            
            this.completed =res.data&&res.data.completed  
            this.hasProcessed =res.data&&res.data.hasProcessed
            this.unProcessed =res.data&&res.data.unProcessed  
          }
        });
    },
    clearSelection(){
      this.selectall=false
    },
       //刷新
    updateView() {
      this.$refs.PlateTypeOrderBomRevisionRef.updateVisible = true;
    },
    //导入
    handleImport() {
       this.$refs.PlateTypeOrderBomRevisionRef.handleImport()
    },
     //新增
    handleAdd() {
       this.$refs.PlateTypeOrderBomRevisionRef.handleAdd()
    },
    //批量核算
    batchClculate(){
      this.$refs.PlateTypeOrderBomRevisionRef.batchClculate()
    },
    //批量退回
    batchBack(){
      this.$refs.PlateTypeOrderBomRevisionRef.batchBack()
    },
    //批量删除
    batchDelete(){
      console.log('删除')
       this.$refs.PlateTypeOrderBomRevisionRef.batchDelete()
    }
  },
};
</script>

<style lang="stylus" scoped>
.select{
  background:#0bb78e
  color:#fff!important;
}
   >>>.el-checkbox__input.is-checked+.el-checkbox__label {
    color: #fff!important;
 }
.panel-tabs
    position: relative;
.panel-tabs
  >>> .main-area
    padding-top 0
.tabs-row
    display:flex;
    align-items:center;
.tabs-row:after
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 2px;
    background-color: #E4E7ED;
    z-index: 1;
.table-btn-area
    display:flex;
    justify-content:flex-end;
    margin-right:12px;
.tabs
  >>> .el-tabs__header
    margin-bottom 5px
    .el-tabs__nav-wrap::after{
      display:none;
    }
</style>
