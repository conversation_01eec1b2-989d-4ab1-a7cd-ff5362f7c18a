<template>
  <content-panel>
    <!-- 未打卡扣款 -->
    <template v-slot:search>
      <search-box
        class="search-box">
        <el-form size="mini"
          :inline="true"
          :model="searchForm"
          ref="searchForm">
          <el-form-item
            label="员工姓名:"
            prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch">
              <template
                slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="员工姓名" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item
            label="厂牌编号:"
            prop="staffCode">
            <el-input
              v-model.trim="searchForm.staffCode"
              size="mini"
              clearable
              @keyup.enter.native="onSearch">
              <template
                slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenStaffCodes"
                  titleName="厂牌编号" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item
            label="核算班组:"
            prop="processId">
            <el-select
              v-model="searchForm.processId"
              filterable
              clearable
              placeholder="请选择核算班组 "
              @change="onSearch">
              <el-option
                v-for="item in procedureOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template
          v-slot:right>
          <el-button
            size="small"
            type="primary"

            @click="onSearch">
            查询
          </el-button>
          <el-button
            size="small"
            type="warning"
            @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel
      ref="tablePanel">
      <template
        v-slot:header-right>
        <div ref="btnRight"
          style="display: flex; justify-content: space-between">
          <el-button
            v-if="permission"
            size="small"
            type="primary"
            @click="handleImport">
            导入
          </el-button>
          <el-button
            v-if="permission"
            v-permission="
              'was-customized$workBench$logistics$workOverview$puncandeductAdd'"
            size="small"
            type="primary"
            @click="handlerAdd">
            新增
          </el-button>
        </div>
      </template>
      <el-table stripe border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :height="maxTableHeight"
        :data="tableData">
        <el-table-column
          prop="staffName"
          label="员工姓名"
          width="100"
          align="left">
        </el-table-column>
        <el-table-column
          prop="staffCode"
          label="厂牌编号"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="accountingMonth"
          label="核算月份"
          align="left">
        </el-table-column>
        <el-table-column
          prop="accountingProcess"
          label="核算班组"
          align="left">
        </el-table-column>
        <el-table-column
          prop="unClockTimes"
          label="未打卡次数"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="unClockAmount"
          label="未打卡扣款"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="updateTime"
          label="修改时间"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          v-if="permission"
          label=""
          align="left"
          width="150">
          <template
            slot-scope="scope">
            <el-button
              size="small"
              type="text"
              @click="handlerEdit(scope.$index, scope.row)">
              编辑
            </el-button>
            <el-button
              v-if="permission"
              v-permission="
                'was-customized$workBench$logistics$workOverview$puncandeductDelete'"
              size="small"
              type="text"
              @click="handleDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div
          class="table_footer">
          <ul>
            <li>
              <span>核算工厂:</span><span>{{ info.factoryName }}</span>
            </li>
            <li>
              <span>核算月份:</span><span>{{ info.accountingMonth }}</span>
            </li>
            <li>
              <span>人数:</span><span>{{ puncandeductInfo.people }}</span>
            </li>
            <li>
              <span>未打卡次数:</span><span>{{ puncandeductInfo.unClockTimes }}</span>
            </li>
            <li>
              <span>总未打卡扣款:</span><span>{{ puncandeductInfo.unClockDeduct | moneyFormat }}</span>
            </li>
          </ul>
        </div>
        <div
          style="text-align: right">
          <el-pagination
            @size-change="onSizeChange"
            @current-change="onNumChange"
            :current-page="pageNum"
            :page-size="pageSize"
            :total="total"
            :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>
        </div>
      </template>
    </table-panel>
    <add-dialog v-if="visible"
      :visible="visible"
      :title="title"
      :editForm="editForm"
      @cancel="handleCancel"></add-dialog>
    <Import
      v-if="ImportVisible"
      :visible="ImportVisible"
      @cancel="cancel"
      @confirm="confirm"
      :importInfo="importInfo" />
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import addDialog from "./addDialog";
export default {
  name: "LogisticsPuncandeduct",
  mixins: [tableMixin,pagePathMixin],
  components: { addDialog },
  data() {
    return {
      searchForm: {
        staffCode: "",
        staffName: "",
        processId: "",
      },
      editForm: {},
      procedureOptions: [],
      puncandeductInfo: {}, //未打卡扣款统计
      info: {},
      factoryId: "",
      tableData: [],
      loading: true,
      resizeOffset: 53,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      permission: "",
      title: "",
      filterParam: {},
      params: {},
      visible: false,
      ImportVisible: false,
      importInfo: {},
    };
  },
  watch: {
    $route: {
      handler(value) {
        if (value.path.includes("puncandeduct")&&value.path.includes("logistics")) {
          this.info = JSON.parse(this.$Base64.decode(value.query.data));
          this.factoryId = this.info.factoryId;
          this.$nextTick(() => {
            this.$api.logisticsSystemManage.getBasicPermission
              .listByFactoryId({
                factoryId: this.factoryId,
              })
              .then(({ data }) => {
                this.procedureOptions = data || [];
              });
          });
          this.importInfo = {
            reportName: "logisticsImportunclock",
            paramMap: {
              columnValue: "物流-未打卡扣款",
              factoryId: this.factoryId,
              accountingMonth: this.info.accountingMonth,
              id: this.info.id,
            },
          };
          this.getList();
          this.getStatistic();
          this.getPermission();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    //获取未打卡扣款列表
    getList() {
      this.loading = true;
      this.$api.logisticsDataUpload.noPunchDeduction
        .getNoPunchDeduction({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: {
            factoryId: this.factoryId,
            accountingMonth: this.info.accountingMonth,
            ...this.filterParam,
            ...this.params,
          },
        })
        .then(({ data: { list, total }, success }) => {
          if (success) {
            this.tableData = list;
            this.total = total;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //是否具有代办任务操作权限
    getPermission() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.info.accountingMonth,
        type: "12",
      };
      this.$api.logisticsWorkbench.agencyPermission(params).then((res) => {
        this.permission = res.data;
      });
    },
    //获取统计信息
    getStatistic() {
      this.$api.logisticsDataUpload.noPunchDeduction
        .statisticsNoPunchDeduction({
          accountingMonth: this.info.accountingMonth,
          factoryId: this.factoryId,
          ...this.filterParam,
          ...this.params,
        })
        .then(({ data, success }) => {
          if (success) {
            this.puncandeductInfo = data || {};
          }
        });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
      this.getStatistic();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    //新增
    handlerAdd() {
      this.title = "新增";
      this.visible = true;
      this.editForm = {};
    },
    //编辑
    handlerEdit(index, row) {
      this.title = "编辑";
      this.visible = true;
      this.editForm = { ...row };
    },
    //导入
    handleImport() {
      this.title = "导入";
      this.ImportVisible = true;
    },
    //删除
    handleDelete(row) {
      this.$confirm("此操作将永久删除该条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$api.logisticsDataUpload.noPunchDeduction
            .deleteUnClock({ id: row.id })
            .then((res) => {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getList();
              this.getStatistic();
            });
        })
        .catch(() => { });
    },
    onSizeChange(pageSize) {
      this.pageSize = pageSize;
      // 切换每页条数后从第一页查询
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    handleCancel(type) {
      this.visible = false;
      if (type == "cancel") return;
      this.getList();
      this.getStatistic();
    },
    cancel(value) {
      this.ImportVisible = value;
    },
    confirm(value) {
      this.ImportVisible = value;
    },
  },
};
</script>

<style lang="stylus" scoped>
// >>> .el-input__suffix {
// .el-icon-circle-close:before {
// margin-left: -70px;
// }
// }
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}

// .table-panel {
// position: relative;

// >>>.btn_right {
// position: absolute;
// top: 8px;
// right: 0;
// z-index: 2;
// }
// }
>>>.el-tabs__nav-wrap::after {
  height: 0;
}

ul {
  list-style: none;
  display: flex;
  padding: 0;
}

i {
  font-style: normal;
}

>>>.table-panel-footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .table_footer {
    overflow: auto;

    >ul {
      display: flex;
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        white-space: nowrap;
        padding: 0 10px;
      }
    }
  }
}
</style>
