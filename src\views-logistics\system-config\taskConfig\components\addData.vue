<template>
  <qDialog :visible="visible"
    title="新增数据"
    :innerScroll="false"
    width="800px"
    :innerHeight="500"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel">
    <div
      style="display:flex;  justify-content: space-between;">
      <div class="items">
        <el-form
          style="width:100%">
          <el-form-item
            label=" 待分配:"
            style="display:flex;margin-bottom:0">
            <el-input
              v-model="content"
              clearable>
              <template
                slot="append">
                <el-button
                  size="small"
                  type="primary">
                  查询
                </el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <div class="items">
        已分配:
      </div>
    </div>
    <div class="content">
      <div
        class="left-content">
        <el-checkbox
          :indeterminate="indeterminate"
          v-model="checkAll"
          @change="checkAllChange">
          全选
        </el-checkbox>
        <el-checkbox-group
          v-model="list"
          @change="checkedPermission">
          <el-checkbox
            v-for="item in allocatedList"
            :label="item"
            :key="item.id">
            {{ item.dataItemName }}
          </el-checkbox>
        </el-checkbox-group>
      </div>
      <div
        class="right-content">
        <div
          v-for="item in empty"
          :key="item.id"
          style="padding:5px 0">
          {{ item.dataItemName }}
        </div>
      </div>
    </div>
  </qDialog>
</template>

<script>
export default {
  name: "addData",
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    editForm: Object
  },
  data() {
    return {
      content: "",
      indeterminate: false,
      checkAll: false,
      list: [],
      jurisdiction: [],
      empty: []
    }
  },
  created() {
    this.branchInformation()
    this.addForm = {
      ...this.addForm,
      ...this.editForm
    }
  },
  computed: {
    allocatedList() {
      return this.content ? this.jurisdiction.filter(item => item.dataItemName.includes(this.content)) : this.jurisdiction
    }
  },
  methods: {
    branchInformation() {
      this.$api.logisticsSystemConfig.listUnassignedItem().then(({ data }) => {
        this.jurisdiction = data || [];
      });
    },
    checkAllChange(val) {
      this.empty = this.list = val ? this.jurisdiction : []
      this.indeterminate = false
    },
    checkedPermission(val) {
      this.list = this.empty = val
      this.checkAll = this.list.length == this.jurisdiction.length
      this.indeterminate = val.length > 0 && val.length < this.jurisdiction.length;
    },
    handleCancel() {
      this.$emit('cancel', 'cancel')
    },
    handleConfirm() {
      this.$bus.$emit('logisticsList', this.empty)
      this.$emit('cancel', 'cancel')
    }
  }
}
</script>

<style lang="stylus" scoped>
.items {
  width: 48%;
  display: flex;
  align-items: center;

  >.el-input {
    flex: 1;
  }
}

>>>.el-form-item__content {
  flex: 1;
}

>>>.el-input-group__append {
  background: #24c69a;

  .el-button {
    color: #fff;
  }
}

.content {
  display: flex;
  padding-top: 20px;
  justify-content: space-between;

  .left-content, .right-content {
    width: 44%;
    border: 1px solid #eee;
    padding: 14px;
    height: 300px;
    border-radius: 8px;
    overflow: auto;
  }
}
</style>