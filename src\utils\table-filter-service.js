import {numberCompareEnum} from '@/utils/constant'
import moment from 'moment'

function cached (fn) {
  var cache = Object.create(null); // 创建空对象作为缓存对象
  return function cachedFn (str) {
    var hit = cache[str];
    return hit || (cache[str] = fn(str)) // 每次执行时缓存对象有值则不需要执行函数方法，没有则执行并缓存起来
  }
}

// 缓存会保存每次进行小写转换的结果
var toLowerCase = cached(function (str) {
  return str.toLowerCase();
});

class TableFilterService {
  constructor(tableData) {
    this._data = tableData;
    // 过滤字段及对应的值
    this.filterMap = {};
    // 排序字段及顺序，只按一个字段排序
    this.sortMap = {
      field: '',
      order: '',
      isNumber: false
    };
  }

  // filterProps:
  // {
  //   type, textValue, numberValues, selectValues
  // }
  filterByField(field, filterProps) {
    this.filterMap[field] = filterProps;
    return this.doFilter();
  }

  sortByField(field, order, isNumber = false) {
    this.sortMap = {
      field,
      order,
      isNumber
    };
    const filteredData = this.doFilter();
    return filteredData.sort((a, b) => {
      const aVal = isNumber ? (+a[this.sortMap.field] || 0) : (a[this.sortMap.field] || '');
      const bVal = isNumber ? (+b[this.sortMap.field] || 0) : (b[this.sortMap.field] || '');
      if (aVal < bVal) {
        return this.sortMap.order === 'down' ? 1 : -1;
      }
      if (aVal > bVal) {
        return this.sortMap.order === 'down' ? -1 : 1;
      }
      return 0;
    });
  }

  doFilter() {
    return this._data.filter(row => {
      let condition = true;
      for (const [filterField, filterProps] of Object.entries(this.filterMap)) {
        const {type, textValue, numberValues, selectValues, dateValues} = filterProps;
        const fieldValue = row[filterField];
        if (type === 'text') {
          condition = condition && this.filterText(fieldValue, textValue);
        } else if (type === 'number') {
          condition = condition && this.filterNumber(fieldValue, numberValues);
        } else if (type === 'select') {
          condition = condition && this.filterSelect(fieldValue, selectValues);
        }  else if (type === 'date') {
          condition = condition && this.filterDate(fieldValue, dateValues);
        }
      }
      return condition;
    });
  }

  // 过滤文本
  filterText(fieldValue, textValue) {
    if (textValue.trim() === '空' || textValue.trim() === '无') {
      return fieldValue === null || fieldValue === '';
    }

    const lowerCaseFieldValue = (fieldValue || '').toString().toLowerCase();
    const splitedTextValues = textValue.trim().toLowerCase().replace(/\s+/g, ' ').split(' ');
    let result = true;
    splitedTextValues.forEach(item => {
      result = result && lowerCaseFieldValue.includes(item);
    });
    return result;
  }

  // 过滤数字
  filterNumber(fieldValue, numberValues) {
    const [condition, val1, val2] = numberValues;

    // 等于空
    if (condition === numberCompareEnum.equal && val1 === undefined) {
      return fieldValue === null;
    }

    if (condition && val1 === undefined && val2 === undefined) {
      return false;
    }

    let result = false;
    switch (condition) {
      case numberCompareEnum.greater:
        result = +fieldValue > val1;
        break;
      case numberCompareEnum.less:
        result = +fieldValue < val1;
        break;
      case numberCompareEnum.equal:
        result = +fieldValue === val1;
        break;
      case numberCompareEnum.greaterOrEqual:
        result = +fieldValue >= val1;
        break;
      case numberCompareEnum.lessOrEqual:
        result = +fieldValue <= val1;
        break;
      case numberCompareEnum.notEqual:
        result = +fieldValue !== val1;
        break;
      case numberCompareEnum.range:
        if (val1 === undefined && val2 !== undefined) {
          result = +fieldValue <= val2;
        } else if (val1 !== undefined && val2 === undefined) {
          result = +fieldValue >= val1
        } else {
          result = +fieldValue >= val1 && +fieldValue <= val2;
        }
        break;
      default:
        break;
    }
    return result;
  }

  // 下拉列表，多选
  filterSelect(fieldValue, selectValues) {
    let result = false;
    selectValues.forEach(item => {
      if (item === '空') {
        result = result || fieldValue === null || fieldValue === '';
      } else {
        result = result || fieldValue === item;
      }
    });
    return result;
  }

  filterDate(fieldValue, dateValues) {
    if (!fieldValue) {
      return false;
    }
    let [dateVal1, dateVal2] = dateValues;
    if (!dateVal1 && !dateVal2) {
      return true;
    }

    fieldValue = moment(fieldValue).valueOf();
    if (dateVal1 && !dateVal2) {
      dateVal1 = moment(dateVal1).hour(0).minute(0).second(0).valueOf();
      return fieldValue >= dateVal1;
    } else if (!dateVal1 && dateVal2) {
      dateVal2 = moment(dateVal2).hour(23).minute(59).second(59).valueOf();
      return fieldValue <= dateVal2;
    } else {
      dateVal1 = moment(dateVal1).hour(0).minute(0).second(0).valueOf();
      dateVal2 = moment(dateVal2).hour(23).minute(59).second(59).valueOf();
      return fieldValue >= dateVal1 && fieldValue <= dateVal2;
    }
  }

  resetField(field) {
    delete this.filterMap[field];
    return this.doFilter();
  }

  resetAll() {
    this.filterMap = {};
    this.sortMap = {};
    return this._data;
  }

  updateData(tableData) {
    this._data = tableData;
  }
}

export default TableFilterService;