<template>
  <qDialog
    :visible="visible"
    title="数据权限"
    :innerScroll="false"
    width="800px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <div class="dialog-info">
      {{ title }}
    </div>
    <div class="dialog-title">
      <div>所有权限</div>
      <div>已有权限</div>
    </div>

    <div class="content">
      <div class="left-content">
        <div class="left">
          <el-checkbox
            style="margin-left: 20px"
            :indeterminate="indeterminate"
            v-model="checkAll"
            @change="checkAllChange"
          >
            全选</el-checkbox
          >
          <el-cascader-panel
            :options="optionsList"
            :props="props"
            v-model="checkPanelList"
            @change="changePanel"
            ref="cascaderAddr"
          ></el-cascader-panel>
        </div>
      </div>
      <div class="right-content">
        <div>
          <div v-for="item in selectedList" :key="item.id" class="haveInfo">
            {{ item.name }}

            <!-- <span v-if="item.name == '定制一厂'">
              <span style="margin-left: 30px" v-for="obj in selectedLine" :key="obj.id">{{ obj.name }}</span>
            </span> -->
          </div>
        </div>
      </div>
    </div>
  </qDialog>
</template>

<script>
export default {
  name: "editDialog",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    editForm: Object,
  },
  data() {
    return {
      checkPanelList: [],
      optionsList: [],
      props: {
        multiple: true,
        checkStrictly: true,
        label: "name",
        value: "id",
        children: "productLines",
      },
      checkAll: false,
      allPermission: [],
      checkPermission: [],
      list: [],
      lineList: [],
      selectedList: [],
      isLoading: false,
      ima: [],
      productLineId: [],
      selectedLine: [],
      isAll: false,
    };
  },
  computed: {
    title() {
      return `${this.editForm.staffName || ""}  ${
        this.editForm.staffCode || ""
      }`;
    },
    indeterminate(){
      if(!this.checkPanelList.length) return false
      return this.checkPanelList.length != this.allPermission.length 
    },

  },
  async created() {
    await this.getBasic();
  },
  methods: {
    getBasic() {
      return this.$api.systemManage.getBasicPermission
        .getBasicPermissionAll1()
        .then(({ data }) => {
          data.forEach((v) => {
            if (v.name != "定制一厂") {//只有定制一厂需要二级
              v.productLines = null;
            }
          });
          this.optionsList = data;
          this.selectedList = []
          let somArr = [];
          this.editForm.permissions.forEach((v) => {
            if (!v) return;
            somArr.push([v.factoryId]);
            if (!this.selectedList.some((obj) => obj.id == v.factoryId)) {
              this.selectedList.push({ id: v.factoryId, name: v.factoryName });
                  }
          });
          this.editForm.productLines.forEach((v) => {
            if (!v) return;
            somArr.push([v.factoryId, v.id]);
            this.selectedList.push(v);
            this.selectedLine.push({factoryId:v.factoryId,id:v.id})
          });
          this.checkPanelList = somArr;
          this.setallPermission()

        });
    },
    setallPermission(){
      this.optionsList.forEach((v) => {
          this.allPermission.push([v.id]);
          if (v.productLines) {
            v.productLines.forEach((i) => {
              this.allPermission.push([i.factoryId, i.id]);
            });
          }
        });
        if(this.checkPanelList.length == this.allPermission.length){
      this.checkAll = true
      }else{
        this.checkAll = false
      }
    },
    changeproductLine(val) {
      // this.selectedLine = val;
    },
    //全选
    checkAllChange(val) {
       
       if(this.checkPanelList.length != this.allPermission.length){
          this.checkAll = true
          this.checkPanelList = this.allPermission
          this.changePanel();
       }else{
        this.checkAll = false
        this.checkPanelList = []
        this.selectedList = []
       }


    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      let params = {
        staffId: this.editForm.staffId,
        factoryIds: this.selectedList.filter((item) => !item.factoryId).map((item) => item.id).join(","), 
        
      };
      params.details = this.selectedLine.map((item) => {
        return {
          factoryId: item.factoryId,
          productLineId: item.id,
        };
      });

      this.isLoading = true;
      this.$api.agencyLog
        .editPermission(params)
        .then((res) => {
          this.$notify.success({
            title: "成功",
            message: "添加成功",
          });
          this.$emit("cancel", "confirm");
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    changePanel(e) {
      this.selectedList = [];
      this.selectedLine = [];
      this.checkPanelList.forEach((v) => {
        if (v.length > 1) {
          //有产线
          this.optionsList.forEach((p) => {
            if (p.productLines) {
              p.productLines.forEach((u) => {
                if (v[1] == u.id) {
                  if (!this.selectedLine.some((obj) => obj.id == u.id)) {
                    this.selectedLine.push(u);
                  }
                  if (!this.selectedList.some((obj) => obj.id == u.id)) {
                    this.selectedList.push(u);
                  }
                }
              });
            }
          });
        } else {//无产线
          v.forEach((i) => {
            this.optionsList.forEach((item) => {
              if (i == item.id) {
                if (!this.selectedList.some((obj) => obj.id == item.id)) {
                  this.selectedList.push(item);
                }
              }
            });
          });
        }
      });
      if(this.checkPanelList.length == this.allPermission.length){
      this.checkAll = true
      }else{
        this.checkAll = false
      }
    },
  },
};
</script>

<style lang="stylus" scoped>
.dialog-info {
  font-size: 14px;
  color: #000;
  font-weight: bold;
  height: 20px;
  border-bottom: 1px solid #eee;
}

.dialog-title {
  margin: 30px 0 10px 0;
  height: 26px;
  background-color: #eee;
  display: flex;
  align-items: center;
  font-size: 12px;
  text-indent: 20px;

  >div {
    flex: 1;
  }
}

.content {
  display: flex;

  .left-content {
    display: flex;
  }

  .left-content, .right-content {
    width: 45%;
    border: 1px solid #eee;
    // padding: 14px;
    height: 200px;
    border-radius: 8px;
    overflow: auto;
  }
  .left-content{
    padding:5px 14px 14px 5px;
  }

  .right-content {
    margin-left: 20px;
    font-size: 14px;
    display: flex;
    padding:10px;

    .haveInfo {
      line-height: 20px;
    }
  }
}

>>>.el-checkbox__label {
  font-size: 14px;
  line-height: 20px;
}

.el-checkbox-group {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
}

>>>.el-cascader-menu__wrap {
  height: 223px;
}
</style>
