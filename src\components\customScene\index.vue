<template>
  <!-- 自定义列切换组件 -->
  <div>
    <!-- `${configureName === item.configureName ? 'zdyActive' : ''}` -->
    <div class="heraderCenter">
      <el-popover placement="bottom" width="200" trigger="click" ref="popper">
        <div class="zdyTitle">自定义列选择</div>
        <el-input v-model="searchcj" placeholder="输入关键字搜索" style="margin-top:30px;" @input="searchInput"></el-input>
        <div class="zdyBox">
          <div class="zdyItem" v-for="(item, index) in newSearchslectedColumns" :key="index" @click="searchClick(index, item)" :class="`${btomIndex === index ? 'active' : ''}`">
            {{ item.name }}
          </div>
        </div>
        <div class="imgBox" slot="reference">
          <img src="../../assets/images/search_menu.png" class="search_img" @click="modelMask" />
        </div>
      </el-popover>
      <!-- <div class="leftBtoom" @click="leftBtoom" v-if="this.rightSpan > 0">
        <img src="../../assets/header-left.png" class="imgSize" />
      </div>
      <div class="lb_box">
        <div class="btoomBox" :style="`margin-left:${margin};transition: .2s;`">
          <div
            class="moren"
            v-for="(item, index) in newSlectedColumns"
            :key="index"
          >
            <el-button
              :id="item.configureName"
              :class="`width ${btomIndex === index ? 'active' : ''}`"
              @click="list(index, item)"
              >{{ item.configureName }}</el-button
            >
          </div>
        </div>
      </div>
      <div class="rightBtoom" @click="rightBtoom" v-if="this.rightSpan > 0">
        <img src="../../assets/header-right.png" class="imgSize" />
      </div> -->
    </div>
    <el-dialog :visible.sync="dialogVisible" append-to-body width="0%" ref="modelFlag">
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "CustomScene",
  props: {
    searchslectedColumns: Array,
    slectedColumns: Array,
    configureName: String,
    rightSpan: Number
  },
  watch: {
    // watch监听,重新复制，在子组件内修改父组件传过来的数据
    searchslectedColumns: function (val, oldVal) {
      this.newSearchslectedColumns = val;
    },
    slectedColumns: function (val, oldVal) {
      this.newSlectedColumns = val;
    },
    rightSpan: function (val, oldVal) {
      this.rightNum = val;
    }
  },
  data() {
    return {
      dialogVisible: false,
      searchcj: "",
      margin: "",
      newSearchslectedColumns: null,
      newSlectedColumns: null,
      leftSpan: 0,
      btomIndex: -1,
      rightNum: 0,
      sum: 0,
      loading: null,
      flag: null
    };
  },
  methods: {
    searchInput() {
      if (this.searchcj) {
        this.newSearchslectedColumns = this.newSlectedColumns.filter(item =>
          item.configureName.includes(this.searchcj)
        );
      } else {
        this.newSearchslectedColumns = JSON.parse(
          JSON.stringify(this.newSlectedColumns)
        );
      }
    },
    modelMask() {
      this.dialogVisible = true;
    },
    searchClick(index, item) {
      this.$emit("searchClick", index, item);
      this.dialogVisible = false
       this.$refs.popper.doClose()
      this.btomIndex = index
    },
    list(index, item) {
      this.$emit("list", index, item);
    },
    leftBtoom() {
      if (this.leftSpan == 0) return;
      this.leftSpan -= 1;
      this.rightNum += 1;
      this.sum = this.sum + 80;
      this.margin = this.sum + "px";
    },
    rightBtoom() {
      if (this.rightNum == 0) return;
      this.leftSpan += 1;
      this.rightNum -= 1;
      this.sum = this.sum - 80;
      this.margin = this.sum + "px";
    }
  },
  created() {
    console.log("configureName", this.configureName);
  }
};
</script>
<style lang="stylus" scoped>
>>> .el-dialog__header {
  display: none;
}

.heraderCenter {
  /* margin-top: 8px; */
  padding: 0px 5px;
  height: 45px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  /* background: #fff; */
  border-radius: 3px;
  position: absolute;
  right: 85px;
  text-align: center;
  /* border: 1px solid red; */
  /* width: 500px; */
}

.imgBox {
  padding: 0px 5px;
  box-sizing: border-box;
  position: relative;
  top: 2px;
  left: 6px;
  background: #e67b00;
  color: white;
  cursor: pointer;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
}

.heraderCenter span {
  color: #fff;
  position: relative;
  left: -15px;
}

.zdyTitle {
  width: 100%;
  height: 30px;
  color: white;
  background: #2eb58d;
  text-align: center;
  line-height: 30px;
  position: absolute;
  top: 0px;
  left: 0px;
}

.zdyBox {
  width: 100%;
  height: 300px;
  margin-top: 10px;
  border-radius: 5px;
  border: 1px solid #ccc;
  overflow: auto;
}

.zdyItem {
  cursor: pointer;
  padding: 5px;
  margin: 5px;
  font-size: 12px;
}

.zdyActive {
  background: #c8eee5;
  border: 1px solid #0bb78e;
  border-radius: 5px;
  color: #0bb78e;
}

.search_img {
  height: 21px;
  /* cursor: pointer;
  position: relative;
  right: 10px;
  top: 4px; */
  /* border: 1px solid red; */
}

.leftBtoom {
  flex: 1;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  cursor: pointer;
}

.imgSize {
  width: 12px;
  height: 20px;
}

.lb_box {
  width: 88%;
  overflow: hidden;
  display: flex;
  align-items: center;
  height: 45px;
  line-height: 45px;
}

.btoomBox {
  width: auto;
  display: flex;
  align-items: center;
  height: 45px;
  line-height: 45px;
}

.moren {
  display: inline-block;
  width: 100%;
  height: 30px;
  line-height: 30px;
  /* position: relative; */
  padding: 0px 10px 0 0;
}

.moren button {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.active {
  background: #c8eee5;
  border: 1px solid #0bb78e;
  border-radius: 5px;
  color: #0bb78e;
}

.rightBtoom {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  cursor: pointer;
}

input:focus {
  outline: 0;
  border-color: #0bb78e;
}

.width {
  /* width: 100%; */
  height: 30px;
}
</style>
