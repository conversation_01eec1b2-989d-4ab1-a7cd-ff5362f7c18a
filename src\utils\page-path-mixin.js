import { setPageName, getPageName } from '@/utils/page-cache';
import { isString } from '@/utils';
export default {
  data() {
    return {
      currentWalkPath: ''
    };
  },
  created() {
    const { fromPath } = this.$route.query;
    let pageName = '';

    // 无fromPath为一级路由，一级路由直接从缓存配置中读取
    if (!fromPath) {
      pageName = getPageName(this.$route.path);
      if (!pageName) {
        setPageName(this.$route.path, this.$route.meta.title || '');
      }
    } else {
      pageName = `${getPageName(fromPath)}/${this.$route.meta.title || ''}`;
      setPageName(this.$route.path, pageName);
    }
  },
  methods: {
    openSubPage(link) {
      if (isString(link)) {
        this.$router.push({
          path: link,
          query: {
            fromPath: this.$route.path
          }
        });
      } else {
        const { query } = link;
        this.$router.push({
          ...link,
          query: {
            ...query,
            fromPath: this.$route.path
          }
        });
      }
    }
  }
};