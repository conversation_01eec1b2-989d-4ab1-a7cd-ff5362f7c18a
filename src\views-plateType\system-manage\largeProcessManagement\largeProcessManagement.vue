<template>
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          label-position="left"
          size="mini"
        >
          <el-form-item label="工厂名称:" prop="description">
            <el-input
              v-model="searchForm.searchValue"
              size="mini"
              clearable
              @keyup.enter.native="onSearch"
              placeholder="请输入工厂名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="工序名称:">
            <el-input
              v-model="searchForm.searchValue"
              size="mini"
              clearable
              @keyup.enter.native="onSearch"
              placeholder="请输入工序名称"
            ></el-input>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary" @click="onSearch">查询</el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">重置</el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-right>
        <el-button size="small" type="primary" @click="handleAdd"> 新增</el-button>
      </template>
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :data="tableData"
        :height="maxTableHeight"
        style="width: 100%"
      >
        <el-table-column
          prop="operateTime"
          label="大工序名称"
          align="left"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="staffName"
          label="关联工厂"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column prop="operateUser" label="状态" align="left"> </el-table-column>
        <el-table-column label="操作" align="left" width="100">
          <template slot-scope="{ row }">
            <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageNum"
        :page-size="pageSize"
        :total="total"
        :page-sizes="[50, 100, 200]"
        layout="total, sizes, prev, pager, next, jumper"
      >
      </el-pagination>
    </table-panel>
    <add-dialog
      v-if="visible"
      :isVisible="visible"
      :title="title"
      :editForm="editForm"
      @cancel="handleCancel"
    ></add-dialog>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import addDialog from "./addDialog";
export default {
  name: "PlateTypeLargeProcessManagement",
  mixins: [tableMixin,pagePathMixin],
  components: { addDialog },
  data() {
    return {
      searchForm: {},
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      options: [],
      tableData: [],
      loading: false,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      filterParam: {},
      resizeOffset: 45,
      visible: false,
      title: "",
      editForm: {},
    };
  },
  created() {
    this.getList();
  },
  mounted() {},

  methods: {
    //搜索
    onSearch() {
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (key === "date" && val) {
          if (val.length) {
            this.searchForm.startTime = val[0] || "";
            this.searchForm.endTime = val[1] || "";
          }
        } else if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      this.pageNum = 1;
      this.getList();
    },
    getList() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        filterData: {
          ...this.searchForm,
        },
      };
      this.$api.agencyLog
        .logList(params)
        .then((res) => {
          this.tableData = res.data.list;
          this.total = res.data.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //重置
    resetSearchForm() {
      this.searchForm = {
        endTime: "",
        startTime: "",
        account: "",
      };
      this.onSearch();
    },
    //分页   页数条数改变函数
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    handleCurrentChange(val) {
      this.pageNum = val;
      this.getList();
    },
    //新增
    handleAdd() {
      this.title = "新增";
      this.visible = true;
      this.editForm = {};
    },
    //编辑
    handleEdit(row) {
      this.title = "编辑";
      this.visible = true;
      this.editForm = {};
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    handleCurrentChange(val) {
      this.pageNum = val;
      this.getList();
    },
    handleCancel() {
      this.visible = false;
    },
  },
};
</script>

<style lang="stylus" scoped>
.el-form--label-left {
  .el-form-item {
    margin-right: 16px;
  }

  >>>.el-form-item__label {
    text-align: right !important;
  }
}
</style>
