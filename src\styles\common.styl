// 搜索区域全局样式
.search-box
  .search-left
    .el-form-item
      margin-bottom 5px !important
      .el-form-item__label
        padding-right 5px
      .el-date-editor.el-input__inner,
      .el-input
        width 240px
.rangeTime
  .el-input
    width 240px!important
.range .el-date-editor--date
  width 110px!important
.range .el-date-editor--date .el-input__prefix
  left: 0px!important
.range .el-date-editor--date .el-input__inner,.el-input
  width 110px

.range .el-date-editor--date .el-input__inner
  padding-left 20px!important
  padding-right 25px!important

.range .el-date-editor--month
  width 110px!important
.range .el-date-editor--date .el-input__prefix
  left: 0px!important
.range .el-date-editor--date .el-input__inner,.el-input
  width 110px

.range .el-date-editor--date .el-input__inner
  padding-left 20px!important
  padding-right 25px!important

.separator{
    flex: 1;
    text-align: center;
    font-size: 12px
    width:20px;
    display: inline-block;
    line-height: 28px;
  }
.headerRight{
  background-color:#fff;
  display:flex;
  justify-content:flex-end;
  align-items: center;
  padding: 0 0 10px 20px;
  border-bottom: 2px solid #E4E7ED;
}
// 操作列
.action-column
  .el-button
    padding 5px
  .el-button+.el-button
    margin-left 5px !important

.form-no-label
  .el-form-item__content
    margin-left 0 !important

// vexTable 样式覆盖
.vxe-table
  font-family Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, STHeiTi, sans-serif !important
  &.vxe-table--render-default
    color #333
  .vxe-header--column
    color #666
    background #f1f4f7
  .vxe-header--column, .vxe-body--column
    padding 8px 0 !important
    line-height 1.15
    height auto !important
  // 斑马纹背景色
  &.vxe-table--render-default .vxe-body--row.row--stripe
    background-color #F6FAFA
  &.vxe-table--render-default .vxe-body--row.row--hover,
  &.vxe-table--render-default .vxe-body--row.row--hover.row--stripe
    background-color #E9F6ED
  .vxe-cell--checkbox
    .vxe-checkbox--icon
      font-size 14px !important
    .vxe-checkbox--icon:before
      border-width 1px !important
  .is--indeterminate.vxe-cell--checkbox .vxe-checkbox--icon:before,
  .is--checked.vxe-cell--checkbox .vxe-checkbox--icon:before
    border-color #0bb78e !important
    background-color #0bb78e !important
  .vxe-cell--checkbox:not(.is--disabled):hover .vxe-checkbox--icon:before
    border-color #0bb78e !important
.vxe-loading
  background-color rgba(0,0,0,0) !important

.vxe-table{
  tr,th{
    color:#666
    border-right: 1px solid #EBEEF5
  }
    th{
        background: #f1f4f7 !important
    }

  }
  .row--stripe{
    background-color: #f6fafa !important;
  }
  .row--hover{
    background-color: #e9f6ed!important;
  }
  .vxe-table--render-default .vxe-body--column.col--ellipsis{
    height:35px !important
  }
  .vxe-cell--checkbox:hover  .vxe-checkbox--icon{
    color: #0bb78e !important;
  }
  .vxe-table  .vxe-cell--title .vxe-cell--checkbox .vxe-icon-checkbox-indeterminate{
    color: #0bb78e !important;
  }
  .vxe-table .is--checked.vxe-cell--checkbox .vxe-checkbox--icon{
    color: #0bb78e !important;
  }
  .vxe-table .is--checked.vxe-cell--checkbox .vxe-checkbox--icon:before {
    border-color: #0bb78e !important;
    background: none !important;
  }
  .vxe-loading>.vxe-loading--chunk, .vxe-loading>.vxe-loading--warpper{
    color: #0bb78e !important;
  }