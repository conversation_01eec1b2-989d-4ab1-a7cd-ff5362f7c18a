<template>
  <!-- 汇总财务表 -->
  <content-panel>
    <table-panel ref="tablePanel">
      <template v-slot:header-left>
        <div class="header_tableName">{{ filterName.factory }}汇总财务表</div>
        <div class="header_date">日期:{{ filterName.date }}</div>
        <section>
          <div class="header_content_left">编制部门:人力资源部薪酬绩效一科</div>
          <div class="header_content_right">单位:元</div>
        </section>
      </template>
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :data="tableData"
        :height="maxTableHeight"
        :span-method="objectSpanMethod"
        style="width: 100%"
        class="topTable"
        :default-sort="{ prop: 'pieceWage', order: 'descending' }"
      >
        <el-table-column
          v-for="item in tableHeader"
          :key="item.fieldName"
          :label="item.label"
          :width="item.width"
          align="center"
          :prop="item.fieldName"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <span
              style="display: block; width: 100%"
              v-if="['adjust', 'add', 'reduce'].includes(row.serialNumber)"
              >{{ filterTitle(row.serialNumber) }}</span
            >
            <span
              v-else-if="!isNaN(Number(row[item.name])) && item.name != 'serialNumber'"
              >{{ filterData(row[item.name])}}</span
            >
             <div v-else-if="row[item.name]=='线下核算'">
              <span>{{ row[item.name]}}</span>
              <el-tooltip
              content="线下核算行有汇总金额计算，列的实发金额不参与计算"
              placement="top">
              <i
                class="el-icon-question"></i>
            </el-tooltip>
            </div>
            <span v-else>{{ row[item.name] }} </span>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <ul>
          <li>
            <span>核算月份:</span><span>{{ info.accountingMonth }}</span>
          </li>
          <li>
            <span>人数:</span><span>{{ statiSticInfo.peopleNum || 0}}</span>
          </li>
        </ul>
      </template>
    </table-panel>
    <edit-financial
      v-if="visible"
      :visible="visible"
      :editInfo="editForm"
      @cancel="handleCancel"
    >
    </edit-financial>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import { moneyFormat } from "@/utils";
import moment from "moment";
import editFinancial from "./editFinancial.vue";
export default {
  components: { editFinancial },
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      collectionIsShow: false,
      collectionIsShows: false,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      list: [],
      tableData: [], //表格数据
      reduceTableData: [],
      adjustList: [],
      loading: false,
      resizeOffset: 50,
      filterParam: {},
      factoryId: "",
      //表格表头信息
      tableHeader: [
        { label: "序号", name: "serialNumber", width: 80 },
        { label: "项目", name: "itemName" },
        { label: "金额", name: "itemValue" },
        { label: "备注", name: "belongProcessName" },
      ],
      statiSticInfo: {},
      info: {},
      basicInfo: {},
      visible: false,
      editForm: {},
    };
  },
  created() {},
  computed: {
    filterName() {
      let { accountingMonth, factoryName } = this.info;
      return {
        factory: `${moment(accountingMonth).format("YYYY年MM月")} ${factoryName}`,
        date: `${moment(accountingMonth)
          .startOf("month")
          .format("YYYY年MM月DD日")} - ${moment(accountingMonth)
          .endOf("month")
          .format("YYYY年MM月DD日")}`,
      };
    },
  },
  watch: {
    $route: {
      handler(value) {
        if (value.path.includes("payroll")&&value.path.includes("logistics")) {
          this.info = JSON.parse(value.query.data);
          this.factoryId = this.info.factoryId;
          this.getList();
          this.getDebitList();
        }
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {
    this.$bus.$off("logisticsEditFinancial")
    this.$bus.$on("logisticsEditFinancial", () => {
      this.visible = true;
    });
  },
  methods: {
    //获取汇总财务表
    getList() {
      this.loading = true;
      this.$api.logisticsWorkbench
        .financialSheet({
          factoryId: this.factoryId,
          accountingMonth: this.info.accountingMonth,
        })
        .then(
          ({
            data: {
              positiveTerm,
              negativeTerm,
              negativeTermSum,
              positiveTermSum,
              salaryItems,
              detailSheetVO,
              adjustTerm,
              adjustTermSum,
            },
          }) => {
            this.editForm = detailSheetVO;
            this.list = [
              {
                serialNumber: "add",
                itemName: "",
                itemValue: "",
                remark: "",
              },
            ]
              .concat(positiveTerm)
              .concat([
                {
                  serialNumber: "合计",
                  itemName: "",
                  itemValue: positiveTermSum,
                  remark: "",
                },
              ]);
            this.reduceTableData = [
              {
                serialNumber: "reduce",
                itemName: "",
                itemValue: "",
                remark: "",
              },
            ]
              .concat(negativeTerm)
              .concat([
                {
                  serialNumber: "合计",
                  itemName: "",
                  itemValue: negativeTermSum,
                  remark: "",
                },
                {
                  serialNumber: "实发工资",
                  itemName: "",
                  itemValue: salaryItems,
                  remark: "",
                },
                {
                  serialNumber: "其中:上卡",
                  itemName: "",
                  itemValue: (detailSheetVO && detailSheetVO.bankCardAmount) || "",
                  remark: "",
                },
                {
                  serialNumber: "其中:现金",
                  itemName: "",
                  itemValue: (detailSheetVO && detailSheetVO.cashAmount) || "",
                  remark: "",
                },
                {
                  serialNumber: "其中:延发",
                  itemName: "",
                  itemValue: (detailSheetVO && detailSheetVO.delayedAmount) || "",
                  remark: "",
                },
                {
                  serialNumber: "其中:自离",
                  itemName: "",
                  itemValue: (detailSheetVO && detailSheetVO.leaveSelfAmount) || "",
                  remark: "",
                },
                {
                  serialNumber: "其中:辞职已结",
                  itemName: "",
                  itemValue: (detailSheetVO && detailSheetVO.resignationAmount) || "",
                  remark: "",
                },
              ]);
            // this.getSum()
            this.tableData = [...this.list, ...this.adjustList, ...this.reduceTableData];
          }
        )
        .finally(() => {
          this.loading = false;
        });
    },
    filterNum() {
      let num = [];
      new Array(7).fill().forEach((item, index) => {
        num.push(this.tableData.length - index - 1);
      });
      return num;
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      //表格合并行
      if (
        rowIndex === 0 ||
        rowIndex == this.list.length ||
        rowIndex == this.list.length + this.adjustList.length
      ) {
        if (columnIndex === 0) {
          return {
            rowspan: 1,
            colspan: 4,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }
      if (
        rowIndex === this.list.length - 1 ||
        this.filterNum().includes(rowIndex) ||
        rowIndex == this.list.length + this.adjustList.length - 1
      ) {
        //重点在else
        if (columnIndex === 0) {
          return {
            rowspan: 1,
            colspan: 2,
          };
        } else if (columnIndex == 1) {
          return {
            rowspan: 0, //清除就是这俩属性设置为0
            colspan: 0, //清除就是这俩属性设置为0
          };
        }
      }
    },
    filterTitle(value) {
      let params = {
        add:
          "增加项目" +
          (this.list.length < 2 ? this.list.length : `(1-${this.list.length - 2})`),
        adjust:
          "调整项目" +
          (this.adjustList.length < 2
            ? this.adjustList.length
            : `(1-${this.adjustList.length - 2})`),
        reduce:
          "减少项目" +
          (this.reduceTableData.length < 2
            ? this.reduceTableData.length
            : `(1-${this.reduceTableData.length - 8})`),
      };
      return params[value];
    },
    filterData(value){
      if(!value) return '-'
      return moneyFormat(value)
    },
    //表格下方统计
    getDebitList() {
      this.$api.logisticsStatiStics
        .statisticsSalary({
          factoryId: this.factoryId,
          accountingMonth: this.info.accountingMonth,
        })
        .then(({ data }) => {
          this.statiSticInfo = data || {};
        });
    },
    //合计
    getSum(it) {
      const sums = [];
      for (const key in it) {
        const values = this.reduceTableData.map((item) => Number(item[key]));
        if (!values.every((value) => isNaN(value))) {
          let num = this.reduceTableData
            .filter((item) => item.number != "合计" && item.number != "实发工资")
            .reduce((prev, curr) => {
              const value = Number(curr[key]);
              if (!isNaN(value)) {
                return prev + curr[key];
              } else {
                return prev;
              }
            }, 0);
          sums.push(num);
        }
      }
      return sums.filter((item) => typeof item == "number" && item > 0);
    },
    handleCancel(type) {
      this.visible = false;
      if (type == "cancel") return;
      this.getList();
    },
    //导出
    handleExport() {
      let params = {};
      if (Object.keys(this.filterParam).length) {
        params = {
          ...this.filterParam,
        };
      }
      this.$api.common
        .doExport("exportAllot", {
          ...params,
          factoryId: this.info.factoryId,
          accountingMonth: this.info.accountingMonth,
        })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success("导出操作成功，请前往导出记录查看详情");
          }
        });
    },
  },
};
</script>
<style lang="stylus" scoped>
ellipsis() {
  font-size: 16px;
  font-weight: 600;
}

.header_tableName {
  ellipsis();
  font-size: 22px;
  text-align: center;
  margin-bottom: 8px;
}

.header_date {
  ellipsis();
  font-size: 18px;
  text-align: center;
}

>>>.el-table__cell:nth-of-type(1) {
  .el-tooltip {
    width: 100% !important;
    text-align: center;
  }
}

>>>.table-panel-footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  >ul {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      white-space: nowrap
      padding: 0 10px;
    }
  }
}

section {
  display: flex;
  justify-content: space-between;

  .header_content_left {
    ellipsis();
  }

  .header_content_right {
    ellipsis();
  }
}
</style>
