<template>
  <!-- 熟手补贴 -->
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          label-position="right"
          size="mini"
        >
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="员工姓名"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input
              v-model.trim="searchForm.staffCode"
              size="mini"
              clearable
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenStaffCodes"
                  titleName="厂牌编号"
                />
              </template>
            </el-input>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button
            size="small"
            type="primary"

            @click="onSearch"
          >
            查询</el-button
          >
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置</el-button
          >
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-left>
        <ul>
          <li>
            <span>核算工厂:</span><span>{{ info.factoryName }}</span>
          </li>
          <li>
            <span>核算月份:</span><span>{{ info.accountingMonth }}</span>
          </li>
          <li>
            <span>人数:</span>
            <span>{{ debitInfo.people }}</span>
          </li>

          <li>
            <span>熟手补贴金额:</span>
            <span>{{ debitInfo.subsidyAmount }}</span>
          </li>
        </ul>
      </template>
      <template
        v-slot:header-right
        v-if="info.status != '待提交' || info.roleName != '分厂文员'"
      >
        <template v-if="permission">
        <el-button size="small" type="primary" @click="handleUpdate">
          生成数据
        </el-button>
        <el-button size="small" type="primary" @click="handleAdd">
          新增</el-button
        >
        <el-button size="small" type="primary" @click="handleImport">
          导入</el-button
        >
        <el-button size="small" type="primary" @click="batchDelete">
          批量删除</el-button
        >
        </template>
        <el-button size="small" type="primary" @click="handleExport">
          导出</el-button
        >
      </template>

      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :data="tableData"
        :height="maxTableHeight"
        @selection-change="handleSelectionChange"
      >
        <el-table-column width="40" type="selection"></el-table-column>
        <el-table-column
          prop="staffName"
          label="员工姓名"
          align="left"
          width="90px"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="staffCode"
          label="厂牌编号"
          align="left"
          min-width="70px"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="accountingMonth"
          label="核算月份"
          align="left"
          width="90px"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="subsidyType"
          label="补贴类型"
          align="left"
          width="90px"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="skilledLevel"
          label="熟练程度"
          align="left"
          width="90px"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="groupName"
          label="核算班组(工序)"
          min-width="90px"
          align="left"
          show-overflow-tooltip
        >
          <template slot="header">
            <span>核算班组(工序)</span>
            <el-tooltip>
              <div slot="content">
                一个月以30天为标准核算,比如员工张三6月入职当月出勤20天,7月出勤15天,6月份不享受熟手补贴,7月份享受出勤满30天标准补贴。
              </div>
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          prop="totalWorkDay"
          label="考勤天数"
          width="90px"
          align="left"
          show-overflow-tooltip
        >
          <template slot="header">
            <span>考勤天数</span>
            <el-tooltip>
              <div slot="content">满足补贴条件的工序对应的考勤天数。</div>
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          prop="subsidyLevel"
          label="补贴标准"
          width="90px"
          align="left"
          show-overflow-tooltip
        >
          <template slot="header">
            <span>补贴标准</span>
            <el-tooltip>
              <div slot="content">
                熟手补贴在员工入职后第八个月内享受完(春节放假天数顺廷),未享受完的部分不再发放。例始:李某是2024年2月12日报名进厂的熟手,若李某到10月31日还未补贴完熟手补贴,则从
                2024年11月1日开始不再享受熟手补贴。
              </div>
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          prop="subsidyAmount"
          label="熟手补贴金额"
          width="130px"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="createTime"
          label="创建时间"
          min-width="100px"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="updateTime"
          label="修改时间"
          min-width="100px"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="remarks"
          label="备注"
          min-width="90px"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="操作"
          min-width="120px"
          align="center"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              @click="handleEdit(scope.$index, scope.row)"
            >
              编辑</el-button
            >

            <el-button
              slot="reference"
              type="text"
              size="mini"
              @click="handleDelete(scope.row)"
            >
              删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <el-pagination
          :current-page="pageNum"
          :page-size="pageSize"
          :total="total"
          :page-sizes="[50, 100, 200]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </template>
    </table-panel>
    <add-dialog
      v-if="addVisible"
      :visible="addVisible"
      :title="title"
      :editForm="editForm"
      @cancel="handleCancel"
      :info="info"
    ></add-dialog>
    <importDialog
      :showdownload="true"
      v-if="ImportVisible"
      :importInfo="importInfo"
      :visible.sync="ImportVisible"
      @after="onSearch"
    />
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import { moneyFormat, moneyDelete, calculateTableWidth } from "@/utils";
import addDialog from "./addDialog";
import importDialog from "./ImportOffline";

export default {
  name: "Softwareoldhandsubsidy",
  mixins: [tableMixin,pagePathMixin],
  components: {
    addDialog,
    importDialog,
  },
  data() {
    return {
      searchForm: {
        staffCode: "",
        staffName: "",
      },
      loading: false,
      tableData: [], //表格数据
      filterParam: {},
      tableInfo: [],
      pageSize: 50,
      pageNum: 1,
      total: 1,
      addVisible: false,
      permission: "",
      ImportVisible: false,
      info: {},
      editForm: {},
      selection: [],
      debitInfo: {},
      isVisible: false,
    };
  },
  watch: {
    $route: {
      handler(value) {
        if (
          value.path.includes("oldhandsubsidy") &&
          value.path.includes("software")
        ) {
          this.info = JSON.parse(this.$Base64.decode(value.query.data));
          this.factoryId = this.info.factoryId;
          this.importInfo = {
            reportName: "softwareSkilledSubsidyImport",
            title: "导入",
            paramMap: {
              columnValue: "板木-熟手补贴",
              factoryId: this.factoryId,
              accountingMonth: this.info.accountingMonth,
              id: this.info.id,
            },
          };
          this.onSearch();
          this.getPermission();
          this.getStatistic();
        }
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    this.getList();
  },
  mounted() {},
  methods: {
    handleSelectionChange(selection) {
      this.selection = selection;
    },
    //获取分页
    getList() {
      this.loading = true;
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        filterData: {
          accountingMonth: this.info.accountingMonth,
          factoryId: this.factoryId,
          ...this.filterParam,
          ...this.params,
        },
      };
      this.$api.softwareDataUpload.ldhandsubsidy
        .ldhandList(params)
        .then((res) => {
          this.tableData = res.data.list;
          this.total = res.data.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //获取统计信息
    getStatistic() {
      let params = {
        accountingMonth: this.info.accountingMonth,
        factoryId: this.factoryId,
        ...this.filterParam,
        ...this.params,
      };
      this.$api.softwareDataUpload.ldhandsubsidy
        .ldhandstatistics(params)
        .then((res) => {
          this.debitInfo = res.data || {};
        });
    },
    //是否具有待办任务操作权限
    getPermission() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.info.accountingMonth,
        type: "30",
      };
      this.$api.softwareWorkbench.agencyPermission(params).then((res) => {
        this.permission = res.data;
      });
    },
    //计算表格列宽度
    flexWidth(...args) {
      return calculateTableWidth(...args);
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0)
          this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
      this.getStatistic();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    //导入
    handleImport() {
      sessionStorage.setItem("tableInfo", JSON.stringify(this.tableInfo));
      this.title = "导入";
      this.ImportVisible = true;
    },
    //新增
    handleAdd() {
      this.title = "新增";
      this.addVisible = true;
      this.editForm = {};
    },
    //编辑
    handleEdit(index, row) {
      this.title = "编辑";
      this.addVisible = true;
      this.editForm = {
        staffCode: row.staffCode || "",
        staffName: row.staffName || "",
        groupId: row.groupId || "",
        accountingMonth: row.accountingMonth || "",
        subsidyAmount: row.subsidyAmount || "",
        id: row.id,
        remarks: row.remarks || "",
        groupName: row.groupName,
        actionType: row.actionType,
      };
    },

    //删除
    handleDelete(row) {
      let params = {
        id: row.id,
        factoryId: this.factoryId,
        accountingMonth: row.accountingMonth,
      };
      this.$confirm("此操作将永久删除该条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$api.softwareDataUpload.ldhandsubsidy
            .ldhanddelete(params)
            .then((res) => {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getList();
              this.getStatistic();
            });
        })
        .catch(() => {});
    },
    //批量删除
    batchDelete() {
      if (!this.selection.length) {
        this.$message({
          message: "请先勾选需要批量删除的内容",
          type: "warning",
        });
        return;
      }
      let params = {
        ids: this.selection.map((item) => item.id),
        factoryId: this.factoryId,
        accountingMonth: this.info.accountingMonth,
      };
      this.$confirm(`确认要删除选中数据吗？确认后，物理删除当前数据。`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$api.softwareDataUpload.ldhandsubsidy
            .ldhanddelete(params)
            .then((res) => {
              this.$notify.success({
                title: "成功",
                message: "批量删除成功",
              });
              this.getList();
              this.getStatistic();
            })
            .finally(() => {
              this.loading = false;
            });
        })
        .catch(() => {});
    },
    //导出
    handleExport() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.info.accountingMonth,
        taskType: "SKILLED_SUBSIDY",
        ...this.filterParam,
      };

      this.$api.common
        .doExport("exportSoftwareSkilledSubsid", { ...params })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success("导出操作成功，请前往导出记录查看详情");
          }
        });
    },
    //重新生成数据
    handleUpdate() {
      if (!this.total) {
        this.savaData();
        return;
      }
      this.$confirm("重新生成数据后，修改记录不保存。,是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.savaData();
      });
    },

    savaData() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.info.accountingMonth,
      };
      this.$api.softwareDataUpload.ldhandsubsidy
        .ldhandgenerate(params)
        .then((res) => {
          if (res.code == 200) {
            this.$message.success("重新生成数据成功");
            this.getList();
            this.getStatistic();
          }
        })
        .catch((res) => {
          // this.$message.error("重新生成数据失败,请稍后重新点击生成数据按钮!");
        });
    },

    handleCancel(type) {
      this.addVisible = false;
      if (type == "cancel") return;
      this.getList();
      this.getStatistic();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.pageNum = val;
      this.getList();
    },
    cancel(value) {
      this.ImportVisible = value;
    },
    confirm(value) {
      this.ImportVisible = value;
    },
  },
};
</script>

<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}
#item {
  margin: 0;
  padding: 5px;
}

ul {
  height: 34px;
  list-style: none;
  display: flex;
  align-items: center;
  padding: 0;
  margin: 0
  li{
    margin-right: 10px;
  }
}

i {
  font-style: normal;
}

.el-form-item__content {
  display: flex;
}
>>>.el-button--mini{
    padding:0px 10px
}
</style>
