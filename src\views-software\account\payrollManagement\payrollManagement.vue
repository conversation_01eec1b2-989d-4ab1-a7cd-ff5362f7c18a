<template>
  <!-- 特殊工资单 -->
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          size="mini"
          :inline="true"
          :model="searchForm"
          label-width="93px"
          ref="searchForm"
          label-position="right"
        >
          <el-form-item label="单据编号:" prop="no">
            <el-input
              v-model="searchForm.no"
              placeholder="请输入单据编号"
              @keyup.enter.native="onSearch"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="工厂名称:" prop="factoryId">
            <el-select
              v-model="searchForm.factoryId"
              filterable
              clearable
              placeholder="请选择工厂名称"
              @change="onSearch"
            >
              <el-option
                v-for="item in tabList"
                :key="item.value"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="员工姓名"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input
              v-model.trim="searchForm.staffCode"
              size="mini"
              clearable
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenStaffCodes"
                  titleName="厂牌编号"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="工资条类型:" prop="type">
            <el-select
              v-model="searchForm.type"
              filterable
              clearable
              placeholder="请选择工资条类型"
              @change="onSearch"
            >
              <el-option
                v-for="item in payrollOptions"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="制表人员:" prop="tableName">
            <el-input v-model="searchForm.tableName" placeholder="请输入制表人员">
            </el-input>
          </el-form-item>
          <el-form-item label="办理日期:" prop="handleTime">
            <el-date-picker
              v-model="searchForm.handleTime"
              type="date"
              placeholder="选择日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary"  @click="onSearch">
            查询
          </el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-left>
        <el-tabs v-model="activeName">
          <el-tab-pane v-for="item in items" :key="item.name" :name="item.value">
            <span
              v-permission="
                `was-customized$informationAccount$software$specialPayrollManagement$${item.type}`
              "
              slot="label"
              >{{
                item.number != null ? `${item.name}(${item.number})` : item.name
              }}</span
            >
          </el-tab-pane>
        </el-tabs>
      </template>
      <template v-slot:header-right>
        <div ref="btnRight">
          <el-button
            v-permission="
              'was-customized$informationAccount$software$specialPayrollManagement$add'
            "
            size="small"
            type="primary"
            @click="handleAdd"
          >
            新增
          </el-button>
          <el-button
            v-show="activeName == '0'"
            v-permission="
              'was-customized$informationAccount$software$specialPayrollManagement$submit'
            "
            size="small"
            type="primary"
            @click="handleBatchSubmit"
          >
            批量提交
          </el-button>
          <el-button
            v-show="activeName == '1'"
            v-permission="
              'was-customized$informationAccount$software$specialPayrollManagement$examine'
            "
            size="small"
            type="primary"
            @click="handleExamine"
          >
            批量审核
          </el-button>
        </div>
      </template>
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :height="maxTableHeight"
        :data="tableData"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          v-if="activeName == '0' || activeName == '1'"
          width="40"
          type="selection"
        >
        </el-table-column>
        <el-table-column
          prop="no"
          label="单据编号"
          width="130"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="factoryName"
          label="工厂名称"
          width="130"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column prop="staffName" label="员工姓名" width="100" align="left">
        </el-table-column>
        <el-table-column prop="staffCode" label="厂牌编号" width="130" align="left">
        </el-table-column>
        <el-table-column label="办理状态" width="100" align="left">
          <template slot-scope="{ row }">
            {{ items.find((item) => item.value == row.handleStatus).name }}
          </template>
        </el-table-column>
        <el-table-column label="工资条类型" width="100" align="left">
          <template slot-scope="{ row }">
            {{ payrollOptions.find((item) => item.value == row.type).name }}
          </template>
        </el-table-column>
        <el-table-column label="实发金额" width="100" align="left" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span
              :style="{ color: row.actualAmount == row.actualOrigin ? '#0BB78E' : 'red' }"
            >
              {{ row.actualAmount | moneyFormat }}</span
            >
          </template>
        </el-table-column>
        <el-table-column
          prop="tableName"
          label="制表人员"
          width="100"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="auditName"
          label="审核人员"
          width="100"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column label="办理日期" width="100" align="left" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.handleTime | shortDate }}
          </template>
        </el-table-column>
        <el-table-column
          prop="remark"
          label="备注说明"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column label="" width="350" align="left" fixed="right">
          <template slot-scope="scope">
            <el-button
              style="margin-left: 0"
              size="small"
              type="text"
              @click="handleDetails(scope.$index, scope.row)"
            >
              查看详情
            </el-button>
            <el-button
              style="margin-left: 0"
              size="small"
              type="text"
              v-permission="
                'was-customized$informationAccount$software$specialPayrollManagement$edit'
              "
              v-show="scope.row.handleStatus == '0' || scope.row.handleStatus == '3'"
              @click="handleEdit(scope.$index, scope.row)"
            >
              编辑
            </el-button>
            <el-button
              style="margin-left: 0"
              size="small"
              type="text"
              v-permission="
                'was-customized$informationAccount$software$specialPayrollManagement$cancellation'
              "
              v-show="
                scope.row.handleStatus == '0' ||
                scope.row.handleStatus == '2' ||
                scope.row.handleStatus == '3'
              "
              @click="handleCancel(scope.$index, scope.row)"
            >
              作废
            </el-button>
            <el-button
              style="margin-left: 0"
              size="small"
              type="text"
              v-permission="
                'was-customized$informationAccount$software$specialPayrollManagement$edit'
              "
              v-show="scope.row.handleStatus == '0' || scope.row.handleStatus == '3'"
              @click="handleSubmit(scope.$index, scope.row)"
            >
              提交
            </el-button>
            <el-button
              style="margin-left: 0"
              size="small"
              type="text"
              v-permission="
                'was-customized$informationAccount$software$specialPayrollManagement$through'
              "
              v-show="scope.row.handleStatus == '1'"
              @click="handleThrough(scope.$index, scope.row)"
            >
              通过
            </el-button>
            <el-button
              style="margin-left: 0"
              size="small"
              type="text"
              v-permission="
                'was-customized$informationAccount$software$specialPayrollManagement$through'
              "
              v-show="scope.row.handleStatus == '1'"
              @click="handleBack(scope.$index, scope.row)"
            >
              退回
            </el-button>
            <el-button
              style="margin-left: 0"
              size="small"
              type="text"
              v-permission="
                'was-customized$informationAccount$software$specialPayrollManagement$through'
              "
              v-show="scope.row.handleStatus == '2'"
              @click="handlePreview(scope.$index, scope.row)"
            >
              打印预览
            </el-button>
            <el-button
              style="margin-left: 0"
              size="small"
              type="text"
              v-permission="
                'was-customized$informationAccount$software$specialPayrollManagement$through'
              "
              v-show="scope.row.handleStatus == '3'"
              @click="handleReason(scope.$index, scope.row)"
            >
              退回原因
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div style="text-align: right">
          <el-pagination
            @size-change="onSizeChange"
            @current-change="onNumChange"
            :current-page="pageNum"
            :page-size="pageSize"
            :total="total"
            :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper"
          >
          </el-pagination>
        </div>
      </template>
    </table-panel>
    <add-payroll
      v-if="visible"
      :visible="visible"
      :title="title"
      :editForm="editForm"
      @cancel="addCancel"
    >
    </add-payroll>
    <payroll-details
      v-if="detailsVisible"
      :visible="detailsVisible"
      :detailsInfo="detailsInfo"
      @cancel="detailsVisible = false"
    >
    </payroll-details>
    <addCommonDialog
      v-if="editVisible"
      :editVisible="editVisible"
      :editTitle="editTitle"
      :addInfo="addInfo"
      @cancel="editCancel"
    >
    </addCommonDialog>
    <common-dialog
      v-if="commonVisible"
      :commonVisible="commonVisible"
      :commonTitle="commonTitle"
      :info="info"
      @cancel="commonCancel"
    >
    </common-dialog>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import { moneyFormat, moneyDelete } from "@/utils";
import moment from "moment";
import addPayroll from "./addPayroll";
import addCommonDialog from "./component/addCommonDialog";
import commonDialog from "./component/commonDialog";
import payrollDetails from "./payrollDetails";
export default {
  name: "SoftwarePayroll",
  mixins: [tableMixin,pagePathMixin],
  components: {
    addPayroll,
    addCommonDialog,
    commonDialog,
    payrollDetails,
  },
  data() {
    return {
      searchForm: {
        no: "",
        factoryId: "",
        staffCode: "",
        staffName: "",
        type: "",
        tableName: "",
        handleTime: "",
      },
      editForm: {},
      addInfo: {},
      //工资条类型
      payrollOptions: Object.freeze([
        {
          name: "辞职工资",
          value: "1",
        },
        {
          name: "自离工资",
          value: "2",
        },
        {
          name: "延发工资",
          value: "3",
        },
        {
          name: "其他工资",
          value: "4",
        },
      ]),
      items: [
        {
          name: "未提交",
          value: "0",
          type: "notSubmitted",
          number: 0,
        },
        {
          name: "审核中",
          value: "1",
          type: "review",
          number: 0,
        },
        {
          name: "退回",
          value: "3",
          type: "back",
          number: 0,
        },
        {
          name: "已审核",
          value: "2",
          type: "audited",
        },
        {
          name: "已作废",
          value: "4",
          type: "voided",
        },
      ],
      activeName: "0",
      tabList: [],
      filterParam: {},
      params: {},
      tableData: [],
      loading: false,
      // resizeOffset: 53,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      title: "",
      visible: false,
      editVisible: false,
      editTitle: "",
      commonTitle: "",
      commonVisible: false,
      detailsVisible: false,
      detailsInfo: {},
      info: {},
      idList: [],
    };
  },
  created() {
    this.$nextTick(() => {
      this.$api.softwareSystemManage.getBasicPermission
      .getBasicPermissionAll().then((res) => {
          this.tabList = res.data.map((item) => ({
            label: item.name,
            name: item.name,
            id: item.id,
          })) || [];
      });
    });
  },
  mounted() {
    this.$bus.$off('softwareClick')
    this.$bus.$on("softwareClick", ({ title, isShow, data }) => {
      this.editTitle = title;
      this.editVisible = isShow;
      this.addInfo = data;
    });
  },
  watch: {
    $route: {
      handler(value) {
        if (value.path.includes("specialPayrollMgent")&&value.path.includes("software")) {
          this.getList();
          this.getStatistics();
        }
      },
      deep: true,
      immediate: true,
    },
    activeName() {
      this.idList = []
      this.getList();
      this.getStatistics();
    },
  },
  methods: {
    //特殊工资单管理列表
    getList() {
      this.loading = true;
      this.tableData = [];
      this.$api.softwareInformation.payrollManagement
        .specialSalaryList({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: {
            handleStatus: this.activeName,
            ...this.filterParam,
            ...this.params,
          },
        })
        .then(({ data: { list, total } }) => {
          this.tableData = list || [];
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //获取统计信息
    async getStatistics() {
      let params = {
        handleStatus: this.activeName,
        ...this.filterParam,
        ...this.params,
      };
      const list = await this.$api.softwareInformation.payrollManagement.statistics(params);
      const data = list.data || {};
      if (Object.keys(data).length != 0) {
        this.items = this.items.map((item) => {
          for (const key in data) {
            if (item.type == key) {
              item.number = list.data[item.type];
            }
          }
          return item;
        });
      } else {
        this.items = [
          {
            name: "未提交",
            value: "0",
            type: "notSubmitted",
            number: 0,
          },
          {
            name: "审核中",
            value: "1",
            type: "review",
            number: 0,
          },
          {
            name: "退回",
            value: "3",
            type: "back",
            number: 0,
          },
          {
            name: "已审核",
            value: "2",
            type: "audited",
          },
          {
            name: "已作废",
            value: "4",
            type: "voided",
          },
        ];
      }
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (key === "staffName" && val) {
          this.filterParam.staffName = val;
        } else if (key === "staffCode" && val) {
          this.filterParam.staffCode = val;
        } else if (key === "handleTime" && val) {
          this.filterParam.handleTime = moment(val).format("YYYY-MM-DD");
        } else if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
      this.getStatistics();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    //新增
    handleAdd() {
      this.editForm = {};
      this.title = "新增";
      this.visible = true;
    },
    //批量提交
    handleBatchSubmit() {
      if (this.idList.length == 0) {
        this.$message.warning("请先勾选需要批量提交的内容");
        return;
      }
      this.commonTitle = "批量提交";
      this.commonVisible = true;
      this.info = { idList: this.idList }
    },
    //批量审核
    handleExamine() {
      if (this.idList.length == 0) {
        this.$message.warning("请先勾选需要批量审核的内容");
        return;
      }
      this.commonTitle = "批量审核";
      this.commonVisible = true;
      this.info = { idList: this.idList }
    },
    //查看详情
    handleDetails(index, row) {
      this.detailsInfo = {
        id: row.id,
      };
      this.detailsVisible = true;
    },
    //编辑
    handleEdit(index, row) {
      this.editForm = {
        id: row.id,
      };
      this.title = "编辑";
      this.visible = true;
    },
    //作废
    handleCancel(index, row) {
      this.commonTitle = "作废";
      this.commonVisible = true;
      this.info = row;
    },
    //提交
    handleSubmit(index, row) {
      this.commonTitle = "提交";
      this.commonVisible = true;
      this.info = row;
    },
    //通过
    handleThrough(index, row) {
      this.commonTitle = "通过";
      this.commonVisible = true;
      this.info = row;
    },
    //退回
    handleBack(index, row) {
      this.commonTitle = "退回";
      this.commonVisible = true;
      this.info = row;
    },
    //打印预览
    handlePreview(index, row) {
      this.commonTitle = "打印预览";
      this.commonVisible = true;
      this.info = row;
    },
    //退回原因
    handleReason(index, row) {
      this.commonTitle = "退回原因";
      this.commonVisible = true;
      this.info = row;
    },
    cancellation(type) {
      if (type && type == "cancel") return;
      this.getList();
      this.getStatistics();
    },
    addCancel(obj) {
      const { type, isVisible } = obj;
      this.visible = isVisible;
      this.cancellation(type);
    },
    editCancel(obj) {
      const { type, isVisible } = obj;
      this.editVisible = isVisible;
    },
    commonCancel(obj) {
      const { type, isVisible } = obj;
      this.commonVisible = isVisible;
      this.cancellation(type);
    },
    handleSelectionChange(val) {
     this.idList = val && val.map((item) => item.id) || [];
    },
    onSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(val) {
      this.pageNum = val;
      this.getList();
    },
  },
};
</script>

<style lang="stylus" scoped>
</style>
