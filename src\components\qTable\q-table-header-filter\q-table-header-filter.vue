<template>
  <el-popover :ref="'filter-pop-' + prop"
    popper-class="filter-pop"
    placement="bottom"
    :width="300"
    trigger="click"
    @show="onPopShow"
    @after-enter="afterPopShow">
    <div class="header-label" slot="reference">
      <slot>
        <span>{{label}}</span>
      </slot>
      <i class="el-icon-search"  :class="{'is-filtered': isFiltered}"></i>
      <i v-if="sortType == 'up'" class="el-icon-sort-up is-filtered"></i>
      <i v-if="sortType == 'down'" class="el-icon-sort-down is-filtered"></i>
    </div>
    <div class="filter-panel">
      <div class="filter-panel-sort-wrap" v-if="useSort">
        <el-button 
          type="primary"
          plain
          size="mini" 
          :class="{active: sortType == 'up'}"  
          icon="el-icon-sort-up"  
          @click="doSort('up')">升序</el-button>
        <el-button 
          type="primary" 
          plain 
          size="mini" 
          :class="{active: sortType == 'down'}"  
          icon="el-icon-sort-down" 
          @click="doSort('down')">降序</el-button>
      </div>
      <div class="filter-panel-number-wrap" v-if="isNumberFilter">
        <span>数字筛选</span>
        <el-select 
          class="number-select"
          v-model="numCondition" 
          size="mini"
          placeholder="" 
          clearable>
          <el-option v-for="item in numberConditions" 
            :key="item.value" 
            :label="item.label" 
            :value="item.value"/>
        </el-select>
        <el-input-number 
          class="number-input"
          v-model="numVal1" 
          :controls="false" 
          step-strictly 
          size="mini"/>
        <el-input-number 
          v-if="isRange"
          class="number-input"
          v-model="numVal2" 
          :controls="false" 
          step-strictly 
          size="mini"/>
      </div>
      <template v-if="!numCondition">
        <div class="filter-panel-input-wrap">
          <el-input
            class="filter-input"
            placeholder="请输入内容"
            size="mini"
            v-model="inputVal"
            @input="onFilter">
            <i slot="append" class="el-icon-search"></i>
          </el-input>
        </div>
        <div class="filter-panel-select-wrap">
          <el-checkbox class="condition-item" v-model="allChecked" @change="onCheckAllChange">全选</el-checkbox>
          <virtual-list 
            ref="conditionList"
            class="virtual-list"
            :data-key="'value'"
            :data-sources="conditionOptions"
            :data-component="itemComponent"
            :estimate-size="19"
            :item-class="'virtual-list-item'"
          />
        </div>
      </template>
      
      <div class="filter-panel-btn-wrap">
        <el-button size="mini" @click="resetFilter">重置</el-button>
        <el-button size="mini" @click="closePop">取消</el-button>
        <el-button type="primary" size="mini" @click="confirmFilter">确定</el-button>
      </div>
    </div>
  </el-popover>
</template>

<script>
import {debounce} from '@/utils'
import VirtualList from 'vue-virtual-scroll-list'
import CheckItem from './check-item'
import {numberCompareEnum} from '@/utils/constant'
export default {
  name: 'QTableHeaderFilter',
  components: {
    VirtualList,
    CheckItem
  },
  props: {
    label: String,
    prop: String,
    // filterType: text number
    filterType: {
      type: String,
      default: 'text'
    },
    getFilterOptions: Function,
    useSort: {
      type: Boolean,
      default: false
    },
    // 涉及排序字段只排一个，所以保存在父组件
    sortInfo: Object,
  },
  computed: {
    isTextFilter() {
      return this.filterType === 'text';
    },
    isNumberFilter() {
      return this.filterType === 'number';
    },
    isRange() {
      return this.numCondition === numberCompareEnum.range;
    },
    isFiltered() {
      return this.confirmCheckedList.length > 0 
        || (this.numCondition !== '' && this.numVal1 !== undefined);
    }
  },
  watch: {
    sortInfo: {
      handler({prop, sort}) {
        this.sortType = prop === this.prop ? sort : '';
      },
      deep: true
    }
  },
  data() {
    return {
      inputVal: '',
      checkedList: [],
      confirmCheckedList: [],
      filterOptions: [],
      initOptions: [],
      conditionOptions: [],
      onFilter: () => {},
      allChecked: true,
      numberConditions: [
        {label: '大于', value: numberCompareEnum.greater},
        {label: '小于', value: numberCompareEnum.less},
        {label: '等于', value: numberCompareEnum.equal},
        {label: '大于等于', value: numberCompareEnum.greaterOrEqual},
        {label: '小于等于', value: numberCompareEnum.lessOrEqual},
        {label: '不等于', value: numberCompareEnum.notEqual},
        {label: '介于', value: numberCompareEnum.range}
      ],
      numCondition: '',
      numVal1: undefined,
      numVal2: undefined,
      isInited: false,
      sortType: '',
      isFirstShow: true,
      itemComponent: CheckItem,
      lastNumberFilter: []
    }
  },
  methods: {
    init() {
      this.onFilter = debounce(this.filterOption, 500);
      this.$on('checkBoxValueChange', (value, checked) => {
        const targetItem = this.conditionOptions.find((item) => item.value === value);
        if (targetItem) {
          targetItem.checked = checked;
          this.setCheckedList();
        }
      });
    },
    resetModel() {
      this.sortType = '';
      this.numCondition = '';
      this.numVal1 = undefined;
      this.numVal2 = undefined;
      this.inputVal = '';
      this.allChecked = true;
      this.conditionOptions = this.initOptions;
      this.checkedList = [];

      this.confirmCheckedList = [];
      this.lastNumberFilter = [];
    },
    resetFilter() {
      this.closePop();
      this.resetModel();
      this.confirmFilter();
    },
    confirmFilter() {
      this.confirmCheckedList = [...this.checkedList];
      let emitVal = {
        prop: this.prop,
        checkedList: this.confirmCheckedList,
        numberFilter: []
      };

      // 选择了数字筛选时，不进行选择筛选
      if (this.isNumberFilter && this.numCondition && this.numVal1 !== undefined) {
        // 数字筛选时，不进行条件选择
        emitVal.checkedList = [];
        this.confirmCheckedList = [];
        emitVal.numberFilter.push(this.numCondition, this.numVal1, this.numVal2);
      }

      this.lastNumberFilter = [...emitVal.numberFilter];
      this.$emit('confirmFilter', emitVal);
      this.closePop();
    },
    closePop() {
      const popRef = this.$refs['filter-pop-' + this.prop];
      if (popRef) {
        popRef.doClose();
      }
    },
    filterOption() {
      const lowerInputVal = this.inputVal.toLowerCase();
      if (!lowerInputVal) {
        this.conditionOptions = this.initOptions;
        this.onCheckAllChange(true);
        this.checkAllChecked();
        return;
      }

      this.conditionOptions = this.initOptions.filter(item => {
        const lowerItemLabel = item.label.toString().toLowerCase();
        return lowerItemLabel.includes(lowerInputVal);
      });
      this.onCheckAllChange(true);
      this.checkAllChecked();
    },
    // 切换选中【全部】时，设置其他项选中状态
    onCheckAllChange(checked) {
      this.conditionOptions.forEach(item => item.checked = checked);
      this.checkedList = checked ? this.conditionOptions.map(item => item.value) : [];
    },
    // 保存当前选中项
    setCheckedList() {
      this.checkedList = this.conditionOptions
        .filter(item => item.checked)
        .map(item => item.value);
      this.checkAllChecked();
    },
    checkAllChecked() {
      // 检查是否选中【全部】
      this.allChecked = this.checkedList.length === this.conditionOptions.length;
    },
    initCheck() {
      this.initOptions = this.filterOptions.map(item => ({
        ...item,
        checked: true
      }));
      if (!this.confirmCheckedList.length) {
        this.conditionOptions = this.initOptions;
      } else {
        this.conditionOptions = this.initOptions.map(item => {
          const checked = this.confirmCheckedList.includes(item.value);
          return {
            ...item,
            checked
          }
        })
      }
      
      this.setCheckedList();
    },
    async onPopShow() {
      this.inputVal = '';
      if (typeof this.getFilterOptions === 'function') {
        this.filterOptions = await this.getFilterOptions(this.prop);
        // 初始化选中
        this.initCheck();
      }

      if (this.isNumberFilter && this.lastNumberFilter.length > 1) {
        this.numCondition = this.lastNumberFilter[0] || '';
        this.numVal1 = this.lastNumberFilter[1] || undefined;
        this.numVal2 = this.lastNumberFilter[2] || undefined;
      }
    },
    afterPopShow() {
      // 防止上次拉到底部，下次打开时显示空白
      if (this.isFirstShow) {
        this.isFirstShow = false;
      } else {
        this.scrollToTop();
      }
    },
    doSort(type) {
      if (!this.sortType || this.sortType != type) {
        this.sortType = type;
        this.confirmSort();
      } else {
        this.closePop();
      }
    },
    confirmSort() {
      this.$emit('confirmSort', {
        prop: this.prop,
        sort: this.sortType,
      });
      this.closePop();
    },
    scrollToTop() {
      const virtualList = this.$refs.conditionList;
      if (virtualList) {
        virtualList.scrollToOffset(1);
      }
    }
  },
  created() {
    this.init();
  }
}
</script>

<style lang="stylus" scoped>
.header-label
  display inline-flex
  align-items center
  line-height 23px
  cursor pointer
  padding 0
  .is-filtered
    color #0BB78E
    font-weight bold
.filter-pop
  padding 10px 5px
.filter-panel
  .filter-input
    >>> .el-input__inner
      border-bottom-left-radius 0
    >>> .el-input-group__append
      padding 0 10px
      border-bottom-right-radius 0
  &-sort-wrap
    display flex
    align-items center
    justify-content space-around
    margin-bottom 10px
  &-number-wrap
    display flex 
    align-items center
    margin-bottom 10px
    >span 
      flex none 
      width 65px
    .number-select
      width 120px
    .number-input
      margin-left 5px
      width 80px
      >>> .el-input__inner
        padding 0 5px
  &-select-wrap
    box-sizing border-box
    padding 5px
    width 100%
    border 1px solid #DCDFE6
    border-top 0
    .virtual-list
      width 100%
      height 200px
      overflow auto
      // &::-webkit-scrollbar
      //   display none
    .condition-item
      display block
  &-btn-wrap
    margin-top 10px
    text-align right
.active
  color #fff
  background-color #0BB78E
  border-color #0BB78E
</style>