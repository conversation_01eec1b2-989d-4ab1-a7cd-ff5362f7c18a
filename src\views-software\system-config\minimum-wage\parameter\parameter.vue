<template>
  <!-- 参数配置 -->
  <content-panel>
    <div class="config-header">
      <div class="header-left">
        <span>参数设置</span>
        <span class="switchMg">启用开关</span>
        <el-switch
          :disabled="!isModifyType"
          class="mg15"
          v-model="configForm.onOff"
          active-value="on"
          inactive-value="off"
        ></el-switch>
        <el-tooltip content="开启后配置生效,反之不生效" placement="right">
          <i class="el-icon-question"></i>
        </el-tooltip>
      </div>
      <div class="header-right">
        <el-button
          size="small"
          type="primary"
          @click="isModifyType = true"
          v-if="!isModifyType"
          >编辑</el-button
        >
        <template v-else>
          <el-button size="small" type="primary" @click="confirmSave"
            >保存</el-button
          >
          <el-button size="small" type="warning" @click="getList"
            >取消</el-button
          >
        </template>
      </div>
    </div>
    <div class="config-content">
      <span class="mg16">新员工从入职后第</span>
      <el-input
        :disabled="!isModifyType"
        v-model="configForm.stopMonths"
        type="text"
        onkeyup="value=value.replace(/[^0-6]/g, '')"
        maxlength="1"
        style="width: 60px; margin: 0px 2px"
      ></el-input>
      <span>个月停止补贴</span>
    </div>
    <div class="config-content">
      <span>无补贴人员日均保底补贴在对应保底标准中减少</span>
      <el-input
        :disabled="!isModifyType"
        v-model="configForm.cutMoneyEveryDay"
        type="text"
        maxlength="6"
        onkeyup="value=value.replace(/[^\d.]/g, '');if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+ 3)}"
        style="width: 60px; margin: 0px 2px"
      ></el-input>
      <span>元/天</span>
    </div>
    
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import moment from "moment";
export default {
  name: "parameter",
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      isModifyType: false,
      configForm: {
        onOff: "on",
        taskType: "MINIMUM_WAGE",
        cutMoneyEveryDay:"",
        id: "",
        stopMonths: "",
      },
    };
  },
  computed: {},
  methods: {

    getList() {
      this.isModifyType = false;
      let formData = new FormData();
      formData.append("taskType", "MINIMUM_WAGE");
      this.$api.softwareSystemManage.getBasicPermission
        .oldhandgetConfigGlobal(formData)
        .then((res) => {
          const { onOff, stopMonths, id,cutMoneyEveryDay } =
            res.data[0];
          this.configForm.onOff = onOff ? onOff : "on",
          this.configForm.id = id;
          this.configForm.stopMonths = stopMonths;
          this.configForm.cutMoneyEveryDay = cutMoneyEveryDay;
        });
    },
    confirmSave() {
      const { onOff, stopMonths, id,cutMoneyEveryDay } = this.configForm;
      if (!stopMonths) {
        this.$message.error("请输入多少月后停止补贴");
        return;
      }
      if (!cutMoneyEveryDay) {
        this.$message.error("请输入保底补贴标准减少多少元天");
        return;
      }
  
      let json = {

        stopMonths,
        cutMoneyEveryDay,
      };
      let params = {
        id,
        configJson: JSON.stringify(json),
        onOff,
        taskType: "MINIMUM_WAGE",
      };
      this.$api.softwareSystemManage.getBasicPermission
        .oldupdateConfigGlobal(params)
        .then((res) => {
          this.$message.success("操作成功");
          this.getList();
          this.isModifyType = false;
        });
    },
  },
  created() {
    this.getList();
  },
};
</script>
<style lang="stylus" scoped>
.content-panel  {
    height:100vh
}
.el-select{
  width:80px
  height:30px
}
.config-header{
  display:flex;
  justify-content:space-between;
  align-items:center;

  .header-left{
    font-weight:bold;
   .switchMg{
    margin:0px 10px 0px 20px;
    font-weight:400
   }
  }
}
.config-content{
  display:flex;
  align-items:center;
  margin:10px 0px
}
>>>.el-input__inner{
  height:30px;
  line-height:30px
}
>>> .el-range__icon {
  line-height:23px !important
}
>>>  .el-range__close-icon {
  line-height:23px !important
}
>>> .el-range-separator{
  line-height:23px !important
}

.config-bottom{
 display:flex;
 align-items:center;

}
.mg10{
  margin:0px 10px
}
</style>
