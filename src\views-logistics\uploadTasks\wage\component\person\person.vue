<template>
  <div>
    <table-panel ref="tablePanel">
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :height="maxTableHeight"
        :data="tableData"
      >
        <el-table-column
          prop="staffName"
          label="员工姓名"
          width="100"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="staffCode"
          label="厂牌编号"
          width="100"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="area"
          label="区域"
          width="100"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="groupName"
          label="核算班组"
          width="100"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="accountingMonth"
          label="核算月份"
          width="80"
          align="left"
        >
        </el-table-column>
        <el-table-column
          prop="totalWorkDay"
          label="总出勤天数"
          width="100"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="totalOvertimeDay"
          label="总加班天数"
          width="100"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="计件工资"
          width="100"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            {{ row.pieceWage | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          label="大组长划出工资"
          width="150"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            {{ row.leaderOutgoingWage | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          label="划出工资"
          width="100"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            {{ row.outgoingWage | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column width="120" align="left" show-overflow-tooltip>
          <template slot="header">
            <span>总计件工资</span>
            <el-tooltip
              content="总计件工资=计件工资-大组长划出工资-划出工资"
              placement="top"
            >
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
          <template slot-scope="{ row }">
            {{ row.totalPiece | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          prop="comments"
          label="备注说明"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="sysComments"
          label="系统备注"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="updateTime"
          label="修改时间"
          width="200"
          align="left"
        >
          <template slot-scope="{ row }">
            {{ row.updateTime | dateFormat }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="left"
          width="120"
          v-if="permission"
        >
          <template slot-scope="{ row }">
            <el-button
              v-show="row.sysComments == '个人'"
              type="text"
              size="small"
              @click="handleAdd(row)"
            >
              编辑</el-button
            >
            <el-button
              v-show="row.sysComments == '个人'"
              type="text"
              size="small"
              @click="handleDelete(row)"
            >
              删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div class="table_footer">
          <ul>
            <li>
              <span>核算月份:</span><span>{{ infoList.accountingMonth }}</span>
            </li>
            <li>
              <span>人数:</span><span>{{ infoList.peopleTotal }}</span>
            </li>
            <li>
              <span>计件工资:</span
              ><span>{{ infoList.pieceWage | moneyFormat }}</span>
            </li>
            <li>
              <span>大组长划出:</span
              ><span>{{ infoList.leaderOutgoingWage | moneyFormat }}</span>
            </li>
            <li>
              <span>划出工资:</span
              ><span>{{ infoList.outgoingWage | moneyFormat }}</span>
            </li>
            <li>
              <span>总计件工资:</span
              ><span>{{ infoList.totalPiece | moneyFormat }}</span>
            </li>
          </ul>
        </div>
        <el-pagination
          :current-page="pageNum"
          :page-size="pageSize"
          :total="total"
          :page-sizes="[50, 100, 200]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onSizeChange"
          @current-change="onNumChange"
        >
        </el-pagination>
      </template>
    </table-panel>
    <add-dialog
      v-if="addVisible"
      :visible="addVisible"
      @cancel="handleCancelAdd"
      :modifyData="modifyData"
    ></add-dialog>
  </div>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import addDialog from "./addDialog";
export default {
  name: "groups",
  components: { addDialog },
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      pageNum: 1,
      pageSize: 50,
      total: 0,
      loading: false,
      resizeOffset: 55,
      tableData: [],
      infoList: {},
      filterData: {},
      permission: false,
      addVisible: false,
      modifyData: {},
    };
  },
  methods: {
    init(data = {}, pageNum = 1, pieceWageFlag) {
      this.permission = pieceWageFlag;
      this.pageNum = 1;
      this.filterData = data;
      this.getList();
      this.getStatistic();
    },
    //获取班组列表
    getList() {
      this.loading = true;
      const { factoryId, accountingMonth } = JSON.parse(this.$Base64.decode(this.$route.query.data));
      this.$api.logisticsDataUpload.pieceRateWage
        .listPieceWagePerson({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: {
            factoryId,
            accountingMonth,
            ...this.filterData,
          },
        })
        .then(({ data: { list, total } }) => {
          this.tableData = list || [];
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //获取统计信息
    getStatistic() {
      const { factoryId, accountingMonth } = JSON.parse(this.$Base64.decode(this.$route.query.data));
      this.$api.logisticsDataUpload.pieceRateWage
        .personStatistics({
          factoryId,
          accountingMonth,
          ...this.filterData,
        })
        .then(({ data }) => {
          this.infoList =
            (data && {
              ...data,
              accountingMonth,
            }) ||
            {};
        });
    },
    //删除
    handleDelete(row) {
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$api.logisticsDataUpload.pieceRateWage
          .deletePieceWagePerson({
            id: row.id,
          })
          .then(() => {
            this.$notify.success({
              title: "成功",
              message: "删除成功",
            });
            this.getList();
            this.getStatistic();
          });
      });
    },
    onSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    handleCancelAdd(type) {
      this.addVisible = false;
      if (type == "confirm") {
        this.getList();
        this.getStatistic();
      }
    },
    // 新增or编辑
    handleAdd(row) {
      this.addVisible = true;
      this.modifyData = row || null;
    },
  },
};
</script>

<style lang="stylus" scoped>
ul {
  list-style: none;
  display: flex;
  padding: 0;
}

i {
  font-style: normal;
}

>>>.table-panel-footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .table_footer {
    overflow: auto;

    >ul {
      display: flex;
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        white-space: nowrap;
        padding: 0 10px;
      }
    }
  }
}
>>>.el-button--text{
     padding:5px !important
}
</style>
