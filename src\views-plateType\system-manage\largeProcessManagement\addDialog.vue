<template>
  <qDialog
    :visible="isVisible"
    :title="title"
    :innerHeight="260"
    width="400px"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <el-form
      :model="addForm"
      ref="addForm"
      :rules="rules"
      label-width="108px"
      size="small"
    >
      <el-form-item label="大工序名称:" prop="mesFactoryId">
        <el-input
          v-model="addForm.mesFactoryId"
          clearable
          placeholder="请输入工厂名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="关联工厂:" prop="factoryId">
        <el-select v-model="addForm.factoryId" clearable placeholder="请输入Mes工厂">
          <el-option
            v-for="item in factoryList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="Mes工厂:"></el-form-item>
      <el-form-item label="Mes大工序:">
        <el-select v-model="addForm.processId" clearable placeholder="请输入Mes工厂">
          <el-option
            v-for="item in processList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="大工序编码:"></el-form-item>
    </el-form>
  </qDialog>
</template>

<script>
export default {
  name: "addDialog",
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    editForm: Object,
  },
  data() {
    return {
      // 表单数据
      addForm: {
        mesFactoryId: "",
        factoryId: "",
        code: "",
        status: "0",
      },
      rules: {
        factoryId: [{ required: true, message: "请输入工厂名称", trigger: "change" }],
        mesFactoryId: [{ required: true, message: "请输入工厂名称", trigger: "blur" }],
      },
      factoryList: [],
      processList: [],
    };
  },
  methods: {
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      this.$emit("cancel", "confirm");
    },
  },
};
</script>

<style lang="stylus" scoped></style>
