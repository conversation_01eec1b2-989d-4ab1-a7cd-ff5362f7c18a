<template>
  <qDialog
    :visible="visible"
    :title="title"
    :innerScroll="false"
    width="500px"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <el-form ref="addForm" inline :model="addForm" :rules="rules">
      <el-form-item
        class="staffName"
        label="核算组织:"
        prop="accountingMonth"
        label-width="120px"
      >
        {{ addForm.factoryName }}
      </el-form-item>
      <el-form-item label="核算班组:" label-width="120px" prop="groupId">
        <el-select
          v-model="addForm.groupId"
          filterable
          clearable
          placeholder="请选择核算班组 "
          @change="changeGroup"
        >
          <el-option
            v-for="item in groupList"
            :key="item.id"
            :label="item.name"
            :value="item.id"

          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        class="staffName"
        label="区域:"
        prop="accountingMonth"
        label-width="120px"
      >
     {{ addForm.area }}
      </el-form-item>

      <el-form-item
        class="staffName"
        label="核算月份:"
        prop="accountingMonth"
        label-width="120px"
      >
        {{ addForm.accountingMonth }}
      </el-form-item>
      <el-form-item
        class="staffName"
        label="计件工资:"
        label-width="120px"
        prop="pieceWage"
      >
        <el-input v-model="addForm.pieceWage" size="small"></el-input>
      </el-form-item>
      <el-form-item class="staffName" label="备注说明:" label-width="120px">
        <el-input v-model="addForm.comments" size="small"></el-input>
      </el-form-item>
    </el-form>
  </qDialog>
</template>
<script>
import { assignValue } from "@/utils";
export default {
  name: "addDialog",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    modifyData: Object,
  },
  computed: {
    title() {
      return this.modifyData ? "编辑" : "新增";
    },
  },
  data() {
    return {
      groupList: [],
      addForm: {},
      rules: {
        groupId: [
          { required: true, message: "核算班组不能为空", trigger: "change" },
        ],
        pieceWage: [
          { required: true, message: "计件工资不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      const { factoryId, accountingMonth, factoryName } = JSON.parse(
        this.$Base64.decode(this.$route.query.data)
      );
      this.addForm = {
        factoryId,
        accountingMonth,
        factoryName,
        pieceWage: "",
        groupId: "",
        id: "",
        comments: "",
        area:""
      };
      if (this.modifyData) {
        //编辑
        this.initModifyData();
      }
      this.getSelection();
    },
    initModifyData() {
      assignValue(this.addForm, this.modifyData);
    },
    getSelection() {
      this.$api.logisticsWorkbench
        .availableGroups({
          factoryId: this.addForm.factoryId,
          accountingMonth: this.addForm.accountingMonth,
        })
        .then(({ data }) => {
          this.groupList = data || [];
        });
    },
    changeGroup(val){
      let obj =  this.groupList.find((item) => item.id == val);
      this.addForm.area = obj.factoryName
    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      this.$refs.addForm.validate((valid) => {
        if (!valid) return;
        let params = {
          ...this.addForm,
        };
        let api = this.modifyData ? "editGroupWage" : "addGroupWage";
        let msg = this.modifyData ? "编辑成功" : "新增成功";
        this.$api.logisticsDataUpload.pieceRateWage[api](params).then(() => {
          this.$notify.success({
            title: "成功",
            message: msg,
          });
          this.$emit("cancel", "confirm");
        });
      });
    },
  },
};
</script>

<style lang="stylus" scoped>
.el-form-item {
  display: flex;
  margin-bottom:15px;
  >>>.el-form-item__label {
    text-align: right;
  }
  >>>.el-form-item__content {
    width: 100%;
    margin-left: 0 !important;
  }
}
// .staffName {
//   >>>.el-form-item__label {
//     &::before {
//       content: '*';
//       color: #F23D20;
//       margin-right: 4px;

//     }
//   }
// }
</style>
