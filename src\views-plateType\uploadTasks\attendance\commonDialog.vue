<template>
  <qDialog
    :visible="isVisible"
    :innerScroll="false"
    :innerHeight="300"
    :title="title"
    width="400px"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel">
    <el-form
      v-if="title=='考勤审核'"
      :model="reviewForm"
      label-width="100px"
      ref="reviewForm">
      <el-form-item label="">
        <el-radio-group
          v-model="reviewForm.type">
          <el-radio
            label="0">通过</el-radio>
          <el-radio
            label="1">不通过</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        v-if="reviewForm.type=='1'"
        label="驳回原因:">
        <el-input
          v-model="reviewForm.remark"
          maxlength="20"
          show-word-limit></el-input>
      </el-form-item>
    </el-form>
    <p v-else>是否删除</p>
  </qDialog>
</template>

<script>
export default {
  props: {
    isVisible: {
      type: Boolean,
      required: true
    },
    title: {
      type: String,
      required: true
    },
  },
  data() {
    return {
      reviewForm: {
        type: '0',
        remark: "",
      }
    }
  },
  methods: {
    handleCancel() {
      this.$emit('review', 'cancel')
    },
    handleConfirm() {
      this.$emit('review', 'confirm')
    }
  }
}
</script>

<style lang="scss" scoped>
</style>