{"name": "admin", "version": "0.1.0", "private": true, "scripts": {"local": "cross-env BUILD_ENV=local vue-cli-service serve", "uat": "cross-env BUILD_ENV=uat vue-cli-service build", "build": "cross-env BUILD_ENV=prod vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@vue/babel-helper-vue-jsx-merge-props": "^1.2.1", "@vue/babel-preset-jsx": "^1.2.4", "axios": "^0.19.2", "core-js": "^3.6.4", "element-ui": "^2.15.14", "js-cookie": "^2.2.1", "modules-plugin-qy": "^1.1.104", "moment": "^2.24.0", "normalize.css": "^8.0.1", "number-precision": "^1.6.0", "path-to-regexp": "^6.1.0", "print-js": "^1.6.0", "vue": "^2.6.11", "vue-router": "^3.1.6", "vuedraggable": "^2.24.3", "vuex": "^3.1.3", "vxe-table": "^3.6.13", "xe-utils": "^3.5.13"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.3.0", "@vue/cli-plugin-eslint": "~4.3.0", "@vue/cli-plugin-router": "~4.3.0", "@vue/cli-plugin-vuex": "~4.3.0", "@vue/cli-service": "~4.3.0", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^10.1.0", "cross-env": "^7.0.2", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "stylus": "^0.54.7", "stylus-loader": "^3.0.2", "vue-template-compiler": "^2.6.11"}}