<template>
  <qDialog :visible="isVisible" :innerScroll="false" :title="title" width="850px" :isLoading="isLoading" @cancel="handleCancel" @confirm="handleConfirm"
    :before-close="handleCancel">
    <el-form :model="calculateForm" ref="calculateForm" label-width="120px" :rules="rules" size="small" :key="upKey">
      <el-row>
        <el-col :span="24">
          <el-form-item label="是否核算:" prop="isAttendance">
            <el-radio v-model="calculateForm.isAttendance" label="1">是</el-radio>
            <el-radio v-model="calculateForm.isAttendance" label="2">否</el-radio>
          </el-form-item>
        </el-col>
      </el-row>
      <template v-if="calculateForm.isAttendance == 2">
        <el-row>
          <el-col :span="24">
            <el-form-item label="选择原因:">
              <el-select
                v-model="calculateForm.resaonType"
                filterable
                clearable
                placeholder="请选择原因"
              >
                <el-option
                  v-for="item in reasonList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="calculateForm.resaonType == 3">
          <el-col :span="24">
            <el-form-item label="其他说明:">
              <el-input
                type="textarea"
                placeholder="请输入内容"
                v-model="calculateForm.remark"
                maxlength="45"
                show-word-limit
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <template v-else>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item class="processCode" label="奖惩编号:">
            {{ calculateForm.code }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="责任分厂:" prop="factoryName" class="processCode">
            {{ calculateForm.factoryName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="厂牌编号:" prop="staffCode" class="processCode">
            {{ calculateForm.staffCode }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="processCode" label="员工姓名:">
            {{ calculateForm.staffName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="执行年月:" prop="accountingMonth" class="processCode">
            {{ calculateForm.accountingMonth }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="奖惩日期:" prop="rewardDate" class="processCode">
            {{ calculateForm.rewardDate | shortDate }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="奖惩类型:" prop="type" class="processCode">
            {{ calculateForm.type }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="奖惩金额:" prop="amount" class="processCode">
            {{ calculateForm.amount }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item label="文件编号:" prop="fileNo" class="processCode">
            {{ calculateForm.fileNo }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="实际奖惩金额:" prop="actualAmount">
            <el-input oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}" v-model="calculateForm.actualAmount" clearable
              @blur="onBlur" placeholder="请输入奖惩金额">
              <template slot="append">元</template>
            </el-input>

          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="实际奖惩厂牌:" prop="actualStaffCode">
            <el-select v-model="calculateForm.actualStaffCode" filterable clearable placeholder="请选择实际扣款厂牌">
              <el-option v-for="item in deductionList" :key="item.staffCode" :label="item.staffCode" :value="item.staffCode">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="已奖惩金额:" prop="payedAmount" class="processCode">
            {{ calculateForm.payedAmount }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="processCode" label="待奖惩金额:">
            <span style="color: red">{{ calculateForm.payingAmount }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10" v-if="listDetails && listDetails.length">
          <el-col :span="12">
            <el-form-item class="processCode" label="奖惩详情:">
              <ul class="deduction">
              <li v-for="item in listDetails" :key="item.id">
                <span>{{ item.factoryName }}</span>
                <span>{{ item.repaymentTime }}</span>
                <span>{{ item.periodAmount }}</span>
                <span>{{ item.isPay }}</span>
              </li>
            </ul>
            </el-form-item>
          </el-col>
        </el-row>
      <!-- 分期行 -->
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item label="奖惩详情:" class="processCode">
            <span style="color: #afafaf; line-height: 40px">
              <span style="padding-right: 105px">实际核算工厂</span>
              <span style="padding-right: 105px">实际执行年月</span>
              <span>实际奖惩金额</span>
            </span>
            <div class="details" v-for="(item, index) in detailList" :key="item.date">
              <div class="details_content">
                <el-select v-model="item.factoryId" placeholder="请选择工厂名称" filterable>
                  <el-option v-for="item in tabList" :key="item.name" :label="item.label" :value="item.id">
                  </el-option>
                </el-select>
                <el-date-picker style="margin: 0 15px" v-model="item.accountingMonth" type="month" placeholder="选择日期" :picker-options="pickerOptions"
                  @change="seleteDate(item)">
                </el-date-picker>
                <span v-if="index == 0">{{ item.repaymentAmount }}</span>
                <el-input v-else class="repaymentAmount" v-model.trim="item.repaymentAmount" placeholder="请输入金额"
                  oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}" @blur="onStageBlur(index, item)">
                </el-input>
              </div>
              <div class="details_btn">
                <el-button type="text" @click="stages(index)" v-if="Number(calculateForm.actualAmount)">
                  分期
                </el-button>
                <el-button v-show="detailList.length > 1 && index != 0" type="text" @click="handleDelete(item, index)">
                  删除
                </el-button>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item class="processCode" label="奖惩原因:" prop="reason" >
            <el-input v-model="calculateForm.reason" clearable show-word-limit maxlength="45">
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item class="processCode" label="备注:" prop="comments">
            <el-input v-model="calculateForm.comments" clearable show-word-limit maxlength="45">
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </template>
    </el-form>
  </qDialog>
</template>

<script>
import moment from "moment";
import {  moneyDelete } from "@/utils";
import NP from "number-precision";
NP.enableBoundaryChecking(false);
export default {
  name: "calculateDialog",
  props: {
    title: {
      type: String,
      required: true,
    },
    isVisible: {
      type: Boolean,
      required: true,
    },
    editForm: Object,
  },
  data() {
    return {
      calculateForm: {
        code: "",
        factoryName: "",
        staffCode: "",
        staffName: "",
        accountingMonth: "",
        rewardDate: "",
        type: "",
        amount: "",
        fileNo: "",
        actualFactoryName: "",
        actualMonth: "",
        actualAmount: "",
        reason: "",
        comments: "",
        payedAmount:"",
        payingAmount:"",
        factoryId:"",
        actualStaffCode:'',
        isAttendance: "",
        remark: "",
      },
      reasonList: [
        { label: "无需核算", value: "无需核算" },
        // { label: "人力资源部", value: "人力资源部" },
        { label: "其他原因 ", value: "3" },
      ],
      deductionList: [], //扣款厂牌下拉
      detailList: [],
      pickerOptions: {
        disabledDate: (time) => {
          const date = new Date(time);
          const year = date.getFullYear();
          const month = date.getMonth() + 1;
          const checkYear = year + "-" + (month < 10 ? "0" + month : month);
          const disabledMonths = this.detailList.map((item) => {
            return item.accountingMonth;
          }); // 要禁用的月份数组
          return disabledMonths.includes(checkYear);
        },
      },
      rules: {
        actualStaffCode: [
          { required: true, message: "请选择实际奖惩厂牌", trigger: "change" },
        ],
        actualAmount: [
          { required: true, message: "请输入实际奖惩金额", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (!/^-?\d{1,5}(\.\d{1,2})?$/.test(moneyDelete(value))) {
                callback(new Error("小数点前面仅支持5位数,小数点后面仅支持2位数"));
              }
              callback();
            },
            trigger: "blur",
          },
        ],
      },
      //扣款类型
      payrollOptions: Object.freeze([
        {
          name: "成本赔偿",
          value: "1",
        },
        {
          name: "返工扣款",
          value: "2",
        },
        {
          name: "低耗品",
          value: "3",
        },
        {
          name: "刀具",
          value: "4",
        },
        {
          name: "未打卡扣款",
          value: "5",
        },
      ]),
      tabList: [],
      isLoading: false,
      payingAmount: "",
      isBlur: false,
      isConfirm: false,
      actualDeductAmount: "",
      num: 1,
      actualAmount:"",
      upKey:0,
      listDetails:[],
    };
  },
  created() {
    this.$api.softwareSystemManage.getBasicPermission.getBasicPermissionAll().then((res) => {
      this.tabList =
        res.data.map((item) => ({
          label: item.name,
          name: item.name,
          id: item.id,
        })) || [];
    });
    this.deductDetail();
  },
  methods: {

    //统计
    total(arr) {
      let total = arr.reduce(function (pre, cur, index, arr) {
        if (index === 0) {
          return pre + 0;
        }
        return NP.plus(pre, Number(moneyDelete(cur.repaymentAmount)));
      }, 0);
      return total;
    },
    addOneMonth(yearMonth) {
      var date = new Date(yearMonth);
      date.setMonth(date.getMonth() + 1);
      var newYearMonth = date.toISOString().substring(0, 7);
      return newYearMonth;
    },
    //选择月份
    seleteDate(item) {
      item.accountingMonth = moment(item.accountingMonth).format("YYYY-MM");
    },
    getAllStaffCode() {
      let params = {
        staffCode: this.calculateForm.staffCode,
      };
      this.$api.softwareWorkbench
        .getAllStaffCode(params)
        .then((res) => {
          this.deductionList = res.data;
          // 有且只有一条默认回显
          if( res.data &&  res.data.length == 1){
            this.calculateForm.actualStaffCode = res.data[0].staffCode
            this.$forceUpdate()
            this.upKey++
          }
        });
    },
      //校验金额
      checkAmount(value, name = "") {
      console.log("name", name);
      let flag = false;
      let amount = moneyDelete(value);
      if (!amount) {
        this.$message({
          message: "金额不能为空",
          type: "warning",
        });
        flag = true;
      }
      if (!flag) {
        if (!/^-?\d{1,5}(\.\d{1,2})?$/.test(amount)) {
          this.$message({
            message: "小数点前面仅支持5位数,小数点后面仅支持2位数",
            type: "warning",
          });
          flag = true;
          if (name) this.calculateForm[name] = this.payingAmount;
        }
      }
      return flag;
    },
    onStageBlur(num, it) {
      if (Number(it.repaymentAmount) > Number(this.calculateForm.payingAmount)) {
        this.$message.error("不能大于待奖惩金额")
        it.repaymentAmount = ""
        return
      }
      this.isBlur = true;
      this.isConfirm = false;
      if (this.checkAmount(it.repaymentAmount)) return;
      let detailList = this.detailList.map((item, index) => {
        item.repaymentAmount = moneyDelete(item.repaymentAmount);
        if (num === index) {
          item.repaymentAmount = item.repaymentAmount;
        }
        return item;
      });
      let total = detailList.reduce(function (pre, cur, index, arr) {
        if (index === 0) {
          return pre + 0;
        }
        return NP.plus(pre, Number(moneyDelete(cur.repaymentAmount)));
      }, 0);
      this.detailList = this.detailList.map((item, index) => {
        if (index == 0) {
          let repaymentAmount = (item.repaymentAmount =
            NP.minus(Number(moneyDelete(this.payingAmount)), total)
          );
          if (Number(moneyDelete(repaymentAmount)) < 0) {
            this.$message.error("操作错误,奖惩金额配置异常,请核查后重试!");
            this.isConfirm = false;

          } else {
            this.isConfirm = true;
          }
        }
        return {
          ...item,
          repaymentAmount: moneyDelete(item.repaymentAmount),
        };
      });
    },
    //分期
    stages(index) {
      if (this.detailList.length > 5) {
        this.$message.error("最多只能分6期");
        return;
      }
      let arr = JSON.parse(JSON.stringify(this.detailList));
      let lastIndex = arr.length - 1;
      this.num++;
      this.detailList.push({
        accountingMonth: this.addOneMonth(
          this.detailList[lastIndex].accountingMonth
        ),
        repaymentAmount: this.toFixedTwo(this.payingAmount / this.num),
        factoryId: this.calculateForm.factoryId
      });

      let detailList = this.detailList.map((item, index) => {
        return {
          ...item,
          repaymentAmount: this.toFixedTwo(this.payingAmount / this.num),
        };
      });
      this.detailList = detailList.map((item, index) => {
        if (Number(moneyDelete(this.payingAmount)) % this.num != 0) {
          if (index === 0) {
            let count = 0
            for (let i = 1; i < detailList.length; i++) {
              count += Number(detailList[i].repaymentAmount)
            }
            item.repaymentAmount = this.returnFloat(NP.minus(this.payingAmount ,count))
          }
        }
        return item;
      });
      this.isBlur = false;
    },
    toFixedTwo(num) {
      if (typeof num !== 'number' || Number.isNaN(num)) return num;
      let resultNum = Math.floor(num * 100) / 100
      return this.returnFloat(resultNum)
    },
    returnFloat(num) {
      num = num.toString(); // 转成字符串类型  如能确定类型 这步可省去
      if (num.indexOf(".") !== -1) {
        let [integerPart, decimalPart] = num.split(".");

        if (decimalPart.length > 2) {
          decimalPart = decimalPart.substring(0, 2);
        } else if (decimalPart.length === 1) {
          decimalPart += "0";
        }

        num = `${integerPart}.${decimalPart}`;
      } else {
        num += ".00";
      }
      return num;
    },
    handleDelete(v, value) {
      this.num--;
      this.detailList.splice(value, 1);
      if (this.detailList.length > 0) {
        let detailList = this.detailList.map((item, index) => {
          return {
            ...item,
            repaymentAmount:this.toFixedTwo(this.payingAmount / this.num),
          };
        });
        this.detailList = detailList.map((item, index) => {
          if (Number(moneyDelete(this.payingAmount)) % this.num != 0) {
            if (index === 0) {
              let count = 0
              for (let i = 1; i < detailList.length; i++) {
                count += Number(detailList[i].repaymentAmount)
              }
              item.repaymentAmount = this.returnFloat(NP.minus(this.payingAmount ,count))
            }
          }
          return item;
        });
      }
      if (this.detailList.length == 1) {
        this.num = 1;
      }
    },
    //奖惩台账详情
    async deductDetail() {
      const { data } = await this.$api.softwareInformation.rewardLedger.viewDetail({
        id: this.editForm.id,
      });
      data.isAttendance = data.isAttendance ? data.isAttendance : "1"; //默认是
      for (const key in this.calculateForm) {
        if (Object.hasOwnProperty.call(this.calculateForm, key)) {
          if (key == "amount" || key == "actualAmount") {
            this.calculateForm[key] = (data[key] && data[key]) || "";
          } else {
            this.calculateForm[key] = data[key] || "";
          }
        }
      }
      this.getAllStaffCode();
      this.actualAmount = data.actualAmount
      this.payingAmount = data.payingAmount
      this.listDetails = data.repaymentList
      // 一进来给这个数组赋值一条默认值数据
      this.detailList = new Array(1).fill().map(() => {
        return {
          accountingMonth: moment().subtract(1, "month").format("YYYY-MM"),
          repaymentAmount: data.payingAmount,
          factoryId: data.factoryId,
        };
      });
    },
    onBlur() {
      this.calculateForm.actualAmount = moneyDelete(this.calculateForm.actualAmount);
      this.$refs.calculateForm.validateField(["actualAmount"], (valid) => {
        if (!valid) {
          this.calculateForm = {
            ...this.calculateForm,
            actualAmount: this.calculateForm.actualAmount,
          };
        }
        if (
          this.calculateForm.actualAmount <
          Number(this.calculateForm.payedAmount)
        ) {
          this.$notify.error({
            title: "错误",
            message: "实际奖惩金额小于已奖惩金额",
          });
          this.calculateForm.actualAmount = this.actualAmount;
          return;
        }
        this.$api.softwareWorkbench
          .rewardUpdateActualAmount({
            id: this.editForm.id,
            actualAmount: this.calculateForm.actualAmount,
          })
          .then(async ({ success }) => {
            if (success) {
              this.$notify({
                title: "成功",
                message: "实际奖惩金额修改成功",
                type: "success",
              });
              await this.deductDetail({ id: this.editForm.id });
              this.num = 1;
            }
          });
      });
    },
    handleCancel() {
      this.$emit("cancel", {
        type: "cancel",
        isVisible: false,
      });
    },
    async handleConfirm() {
      if (this.calculateForm.isAttendance == 2) {
        if(!this.calculateForm.resaonType){
          this.$message.error("请选择原因");
          return
        }
        if(this.calculateForm.resaonType == 3 && !this.calculateForm.remark ){
          this.$message.error("请填写其他原因");
          return
        }
        this.isLoading = true;
        let params = {
          id: this.editForm.id,
          remark: this.calculateForm.resaonType == 3 ? '其他原因:' + this.calculateForm.remark : this.calculateForm.resaonType
        };
        this.$api.softwareInformation.punishment.noAccounting(params).then((data)=>{
          this.$notify.success({
              title: "成功",
              message: "操作成功",
            });
            this.$emit("cancel", {
              type: "confirm",
              isVisible: false,
            }).finally(() => {
            this.isLoading = false;
          });
        })
        return
      }
      if (!this.isConfirm && this.isBlur) {
        this.$message.error("操作错误,扣款金额配置异常,请核查后重试!");
        return;
      }
      this.$refs.calculateForm.validate((valid) => {
        if (!valid) return;
        // let actualFactoryId = this.tabList.find(
        //   (item) => item.name == this.calculateForm.actualFactoryName
        // ).id;
        this.isLoading = true
        let params = {
          id: this.editForm.id,
          factoryId:this.calculateForm.factoryId,
          // actualMonth: moment(this.calculateForm.actualMonth).format("YYYY-MM"),
          actualAmount: moneyDelete(this.calculateForm.actualAmount),
          reason: this.calculateForm.reason,
          comments: this.calculateForm.comments,
          repaymentList: this.detailList,
          actualStaffCode:this.calculateForm.actualStaffCode
        };
        this.$api.softwareInformation.rewardLedger.businessAccounting(params).then((data) => {
          this.$notify.success({
            title: "成功",
            message: "核算成功",
          });
          this.$emit("cancel", {
            type: "confirm",
            isVisible: false,
          });
        }).finally(() => {
          this.isLoading = false;
        });
      });
    },
  },
};
</script>

<style lang="stylus" scoped>
>>>.el-form-item__label {
  text-align: left;
}

.processCode {
  >>>.el-form-item__label {
    &::before {
      content: '*';
      color: #F23D20;
      margin-right: 4px;
      visibility: hidden;
    }
  }
}

>>>.el-input-group__append {
  .el-button {
    padding: 5px 10px;
  }
}

>>>.el-select, >>>.el-date-editor {
  width: 100%;
}

.el-upload__tip {
  padding-left: 10px;
  display: inline-block;
  color: #24c69a;
}

>>>.el-textarea__inner {
  padding-right: 50px;
}

.el-textarea {
  >>>.el-input__count {
    right: 20px !important;
  }
}

.details {
  display: flex;
  padding-bottom: 18px;

  &_content {
    flex: 1;
    display: flex;
    justify-content: space-between;

    >.el-select, >.el-input, >span {
      flex: 1 0 30%;
    }
  }

  &_btn {
    width: 20%;

    >.el-button {
      mar;
    }
  }
}
.deduction {
  width: 100%;
  margin: 0;
  padding: 0;

  li {
    span {
      padding-right: 10px;
    }
  }
}
</style>
