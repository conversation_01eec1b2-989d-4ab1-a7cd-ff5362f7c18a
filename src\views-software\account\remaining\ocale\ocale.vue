<template>
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form size="mini" :inline="true" :model="searchForm" ref="searchForm" label-position="right">
          <el-form-item label="核算工厂:" prop="factoryId">
            <el-select v-model="searchForm.factoryId" filterable clearable placeholder="请选择核算工厂" @change="onSearch">
              <el-option v-for="item in tabList" :key="item.id" :label="item.label" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary" @click="onSearch">
            查询</el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置</el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-right>
        <div ref="btnRight">
          <el-button size="small" type="primary" @click="handleExport">
            导出</el-button>
        </div>
      </template>
      <el-table stripe border key="tableKey" v-loading="loading" ref="tableRef" highlight-current-row
        :height="maxTableHeight" :data="tableData">
        <!-- item.width -->
        <el-table-column v-for="item in columnList" :key="item.fieldName" :label="item.columnName" align="left"
          show-overflow-tooltip>
          <template slot-scope="scope">
            <div v-if="item.type">
              <el-button size="small" type="text" @click="handleLook(scope.row)">
                查看明细
              </el-button>
              <el-button size="small" type="text" @click="handleResidual(scope.$index, scope.row)">
                余留调整
              </el-button>
            </div>
            <span v-else-if="item.fieldName != 'factoryname'">{{
              scope.row[item.fieldName] | moneyFormat
            }}</span>
            <span v-else>{{ scope.row[item.fieldName] }}</span>
          </template>
        </el-table-column>
      </el-table>
    </table-panel>
    <remaining-adjustment :months="months" v-if="visible" :visible="visible" :formData="formData"
      @cancel="handleCancel"></remaining-adjustment>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import moment from "moment";
import { moneyFormat, moneyDelete } from "@/utils";
import remainingAdjustment from "./remainingAdjustment.vue";
export default {
  components: { remainingAdjustment },
  name: "SoftwareRemaining",
  mixins: [tableMixin, pagePathMixin],
  data() {
    return {
      searchForm: {
        factoryId: "",
      },
      formData: {},

      //核算工厂
      tabList: [],
      visible: false,
      tableData: [],
      filterParam: {},
      resizeOffset: 12,
      months: [],
      loading: false,
      factoryId: "",
      tableKey: '',
      columnList: "",
    };
  },
  created() {
    this.$api.softwareSystemManage.getBasicPermission
      .getBasicPermissionAll().then((res) => {
        this.tabList = res.data.map((item) => ({
          label: item.name,
          name: item.name,
          id: item.id,
          process: item.process,
        })) || [];
      });
  },
  watch: {
    $route: {
      handler(value) {
        if (value.path.includes("remaining") && value.path.includes("software")) {
          this.getList();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    getList() {
      //获取余留管理列表
      this.loading = true;
      this.$api.softwareInformation.remaining
        .getRemainingList({
          ...this.filterParam
        })
        .then((res) => {
          if (res.code === 200) {
            this.tableData = res.data.dataList;
            this.months = res.data.monthList || [];
            this.columnList = res.data.headList.concat([
              {
                columnName: "",
                fieldName: "operation",
                type: "operation",
                fixed: "right",
              },
            ]);
            this.tableKey = Math.random();
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.filterParam = {};
      this.onSearch();
    },
    //搜索
    onSearch() {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      this.getList();
    },
    //查看明细
    handleLook(row) {
      this.openSubPage({
        path: "/software/account/remainingDetailed",
        query: {
          factoryId: row.factoryid,
        },
      });
    },
    //余留调整
    handleResidual(index, row) {
      this.visible = true;
      this.formData = {
        factoryId: row.factoryid || "",
      };
    },
    handleCancel(type) {
      this.visible = false;
      if (type == "cancel") return;
      this.getList();
    },
    //导出
    handleExport() {
      if (this.isExportRunning) {
        return;
      }
      this.isExportRunning = true;
      let params = {
        ...this.filterParam
      };
      this.$api.softwareInformation.remaining
        .exportRemainingList(params)
        .then((res) => {
          this.$message.success("导出操作成功，请前往导出记录查看详情");
        })
        .finally(() => {
          this.isExportRunning = false;
        });
    }
  },
};
</script>

<style lang="stylus" scoped></style>
