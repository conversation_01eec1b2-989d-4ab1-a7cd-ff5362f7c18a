<template>
  <qDialog
    :visible="isVisible"
    :innerScroll="true"
    :isAuto="true"
    title="批量核算"
    width="400px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <el-form label-width="82px" style="margin-bottom: 20px">
      <p>当前共选中条数:{{ selectall ? total : info.idList.length }}</p>
      <el-row>
        <el-col :span="24">
          <el-form-item label="是否核算:">
            <el-radio v-model="stagesForm.isAttendance" label="1">是</el-radio>
            <el-radio v-model="stagesForm.isAttendance" label="2">否</el-radio>
          </el-form-item>
        </el-col>
      </el-row>
      <template v-if="stagesForm.isAttendance == 2">
        <el-row>
          <el-col :span="24">
            <el-form-item label="选择原因:">
              <el-select
                v-model="stagesForm.resaonType"
                filterable
                clearable
                placeholder="请选择原因"
              >
                <el-option
                  v-for="item in reasonList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="stagesForm.resaonType == 3">
          <el-col :span="24">
            <el-form-item label="其他说明:">
              <el-input
                type="textarea"
                placeholder="请输入内容"
                v-model="stagesForm.remark"
                maxlength="45"
                show-word-limit
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <template v-else>
      <el-form-item
        :label="index ? '' : '分期月份'"
        v-for="(item, index) in dataList"
        :key="index"
      >
        <el-date-picker
          v-model="item.accountingMonth"
          type="month"
          placeholder="选择日期"
          :picker-options="pickerOptions"
          @change="seleteDate(item)"
          size="mini"
          :clearable="false"
        >
        </el-date-picker>
        <el-button type="text" @click="stages">增加</el-button>
        <el-button
          v-show="dataList.length > 1 && index != 0"
          type="text"
          @click="handleDelete(item, index)"
        >
          删除
        </el-button>
      </el-form-item>
    </template>
    </el-form>
  </qDialog>
</template>

<script>
import moment from "moment";
export default {
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    filterParam: {
      type: Object,
      required: {},
    },
    total: { type: Number },
    info: [Object],
    selectall: {
      type: Boolean,
      required: false,
    },
  },
  data() {
    return {
      stagesForm:{isAttendance: "1",remark:""},
      pickerOptions: {
        disabledDate: (time) => {
          const date = new Date(time);
          const year = date.getFullYear();
          const month = date.getMonth() + 1;
          const checkYear = year + "-" + (month < 10 ? "0" + month : month);
          const disabledMonths = this.dataList.map((item) => {
            return item.accountingMonth;
          }); // 要禁用的月份数组
          return disabledMonths.includes(checkYear);
        },
      },
      isLoading: false,
      dataList: [
        { accountingMonth: moment().subtract(1, "month").format("YYYY-MM") },
      ],
      reasonList: [
        { label: "无需核算", value: "无需核算" },
        // { label: "人力资源部", value: "人力资源部" },
        { label: "其他原因 ", value: "3" },
      ],
    };
  },
  methods: {
    //选择月份
    seleteDate(item) {
      item.accountingMonth = moment(item.accountingMonth).format("YYYY-MM");
    },
    handleCancel() {
      this.$emit("cancel", {
        type: "cancel",
        isVisible: false,
      });
    },
    handleConfirm() {
      if(this.stagesForm.isAttendance == 2){
        if(!this.stagesForm.resaonType){
          this.$message.error("请选择原因");
          return
        }
        if(this.stagesForm.resaonType == 3 && !this.stagesForm.remark ){
          this.$message.error("请填写其他原因");
          return
        }
        this.isloading=true
        const {resaonType,remark} = this.stagesForm
        let params = {
          ids: this.info.idList,
          remark: resaonType == 3 ? '其他原因:' + remark : resaonType
        }
        this.$api.information.costCompensation
          .noAccounting(params)
          .then(() => {
            this.$notify.success({
              title: "成功",
              message: "操作成功！",
            });
            this.$emit("success");
          })
          .finally(() => {
            this.isLoading = false;
          });
          return
      }
      this.isLoading = true;
      let monthList = this.dataList.map((item) => {
        return item.accountingMonth;
      });
      let params = { isAll: "0", ids: this.info.idList, monthList };
      if (this.selectall) {
        //全选
        params = {
          isAll: 1,
          monthList,
          ...this.filterParam,
        };
      }
      this.$api.information.costCompensation
        .batchBusinessAccounting(params)
        .then(({ data }) => {
          this.$notify.success({
            title: "成功",
            message: data,
          });
          this.$emit("success");
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    addOneMonth(yearMonth) {
      var date = new Date(yearMonth);
      date.setMonth(date.getMonth() + 1);
      var newYearMonth = date.toISOString().substring(0, 7);
      return newYearMonth;
    },
    //分期
    stages() {
      if (this.dataList.length > 5) {
        this.$message.error("最多只能分6期");
        return;
      }
      let arr = JSON.parse(JSON.stringify(this.dataList));
      let lastIndex = arr.length - 1;
      this.dataList.push({
        accountingMonth: this.addOneMonth(
          this.dataList[lastIndex].accountingMonth
        ),
      });
    },
    //删除
    handleDelete(item, index) {
      this.dataList.splice(index, 1);
    },
  },
};
</script>

<style lang="stylus" scoped>
.el-date-editor.el-input{
  width: 130px
}
>>>.el-form-item{
  margin-bottom: 5px !important;
}
>>>.el-input__inner {
  height 32px
  line-height 32px
}
.el-input{
  width 50%
}
</style>
