<template>
  <!-- 熟手补贴参数配置 -->
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          label-position="right"
          size="mini"
        >
          <el-form-item label="核算工厂:">
            <el-select
              v-model="searchForm.factoryId"
              filterable
              clearable
              placeholder="请选择核算工厂"
              @change="changeFactoryId"
            >
              <el-option
                v-for="item in tabList"
                :key="item.value"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="核算班组(工序):" >
            <el-select
              v-model="searchForm.groupIds"
              filterable
              clearable
              multiple
              collapse-tags
              placeholder="请选择核算班组"
              @change="onSearch"
              style="width:250px;"

            >
              <el-option
                v-for="item in processesList"
                :key="item.value"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button
            size="small"
            type="primary"

            @click="onSearch"
          >
            查询</el-button
          >
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置</el-button
          >
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-right>
        <el-button size="small" type="primary" @click="handleAdd(1, null)">
          新增</el-button
        >
        <el-button size="small" type="primary" @click="handleExport">
          导出</el-button
        >
        <el-button size="small" type="primary" @click="batchDelete">
          批量删除</el-button
        >
      </template>

      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :height="maxTableHeight"
        :data="tableData"
        @selection-change="handleSelectionChange"
      >
        <el-table-column width="40" type="selection"></el-table-column>
        <el-table-column
          prop="factoryName"
          label="核算工厂"
          align="left"
          min-width
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="groupNames"
          label="核算班组(工序)"
          align="left"
          min-width
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="grade"
          label="熟手程度"
          align="left"
          min-width
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          align="center"
          label="补贴标准(元)"
          class-name="customOne"
        >
          <el-table-column
            prop="firstAmount"
            label="出勤满30天"
            min-width
            align="center"
            show-overflow-tooltip
            class-name="customOne"
          >
          </el-table-column>
          <el-table-column
            prop="secondAmount"
            class-name="customOne"
            label="出勤满90天"
            min-width
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="thirdAmount"
            class-name="customOne"
            label="出勤满180天"
            min-width
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
        </el-table-column>
        <el-table-column
          prop="totalAmount"
          label="补贴合计(元)"
          align="left"
          min-width
          show-overflow-tooltip
        >
        <template slot="header">
            <span>补贴合计(元)</span>
            <el-tooltip
              content="补贴合计=出勤满30天+出勤满90天+出勒满180天"
              placement="top"
            >
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <template>
          <el-table-column
            prop="remarks"
            label="备注"
            align="left"
            min-width
            show-overflow-tooltip
          >
          </el-table-column>
        </template>
        <el-table-column label="操作" align="center" width="180px">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="handleAdd(3, scope.row)">
              复制</el-button
            >
            <el-button size="mini" type="text" @click="handleAdd(2, scope.row)">
              编辑</el-button
            >

            <el-button
              slot="reference"
              type="text"
              size="mini"
              @click="handleDelete(scope.row)"
            >
              删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <el-pagination
          :current-page="pageNum"
          :page-size="pageSize"
          :total="total"
          :page-sizes="[50, 100, 200]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </template>
    </table-panel>
    <!-- 新增/编辑/复制-->
    <crud-edit
      v-if="addVisible"
      :visible="addVisible"
      :modifyData="modifyData"
      :modifyNum="modifyNum"
      @cancel="handleCancel"
    >
    </crud-edit>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import { moneyFormat, moneyDelete, calculateTableWidth } from "@/utils";
import crudEdit from "./components/crud-edit";

export default {
  name: "standard",
  mixins: [tableMixin,pagePathMixin],
  components: {
    crudEdit,
  },
  data() {
    return {
      tabList: [],
      searchForm: {
        factoryId: "",
        groupIds: "",
      },
      filterParam: {},
      loading: false,
      tableData: [], //表格数据
      tableInfo: [],
      pageSize: 50,
      pageNum: 1,
      total: 1,
      visible: false,
      debitInfo: {},
      ImportVisible: false,
      info: {},
      modifyData: {},
      selection: [],
      info: "",
      addVisible: false,
      processesList: [],
      selection: [],
      modifyNum: null,
    };
  },
  async created() {
    await this.$api.plateTypeSystemManage.getBasicPermission
      .getQuFactory({ moduleId: 3 })
      .then((res) => {
        this.tabList = res.data || [];
        this.getList();
      });
  },

  methods: {
    changeFactoryId(){
      this.searchForm.groupIds = []
      this.processesList = this.tabList.find(
        (v) => v.id == this.searchForm.factoryId
      ).process;
    },

    //获取分页
    getList() {
      this.loading = true;
      this.$api.plateTypeSystemConfig
        .oldhandList({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: {
            ...this.filterParam,
            taskType: "SKILLED_SUBSIDY",
          },
        })
        .then((res) => {
          this.tableData = res.data.list;
          this.total = res.data.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },

    //计算表格列宽度
    flexWidth(...args) {
      return calculateTableWidth(...args);
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.searchForm = {};
      this.filterParam = {};
      this.processesList = []
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0)
          this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
    },

    handleSelectionChange(selection) {
      this.selection = selection;
    },
    //批量删除
    batchDelete() {
      if (!this.selection.length) {
        this.$message({
          message: "请先勾选需要批量删除的数据",
          type: "warning",
        });
        return;
      }
      this.$confirm(`确认要删除选中数据吗？确认后，物理删除当前数据。`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$api.plateTypeSystemConfig
            .oldhandbatchDelete(this.selection.map((item) => item.id))
            .then((res) => {
              this.$notify.success({
                title: "成功",
                message: "批量删除成功",
              });
              this.getList();
            })
            .finally(() => {
              this.loading = false;
            });
        })
        .catch(() => {});
    },
    //新增
    handleAdd(num, row) {
      // num:1新增 2编辑 3复制
      if (row) {
        let obj = JSON.parse(JSON.stringify(row))
        obj.groupIds =  JSON.parse(obj.groupIds);
        this.modifyData = obj || null;
      }else{
        this.modifyData = null
      }
      this.modifyNum = num;
      this.addVisible = true;
    },
    //删除
    handleDelete(row) {
      this.$confirm("此操作将永久删除该条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$api.plateTypeSystemConfig
            .oldhanddelete(row.id)
            .then((res) => {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getList();
            });
        })
        .catch(() => {});
    },
    //导出
    handleExport() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.info.accountingMonth,
        taskType: "SKILLED_SUBSIDY",
        ...this.filterParam,
      };
      this.$api.common
        .doExport("plankSkilledSubsidyParamConfig", params)
        .then((res) => {
          if (res.code == 200) {
            this.$message.success("导出操作成功，请前往导出记录查看详情");
          }
        });
    },

    handleCancel(type) {
      this.addVisible = false;
      if (type == "cancel") return;
      this.getList();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.pageNum = val;
      this.getList();
    },
    cancel(value) {
      this.ImportVisible = value;
    },
    confirm(value) {
      this.ImportVisible = value;
    },
  },
};
</script>

<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}
#item {
  margin: 0;
  padding: 5px;
}

.el-form-item__content {
  display: flex;
}
.el-table .cell{
  padding left 1px
}
>>>.el-button--text {
  padding:5px
}
>>>.search-box .search-left .el-form-item .el-input{
  width 250px
}
</style>
