<template>
  <qDialog innerScroll :visible="visible"  :innerScroll="false"   :title="isTitle" width="500px"
    @cancel="editCancel" @confirm="handleConfirm" :isLoading="isLoading" :before-close="editCancel">
      <div> 
        <el-form :model="addForm" :rules="rules" ref="addForm" size="small">
          <el-row>
            <el-col :span="24">
              <div class="info">提示：请务必确认要转出的计件工资，确保转入和转出工厂计件工资处理未核算状态，否则不能成功操作。</div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="22">
              <el-form-item label-width="100px" label="转出工厂：" prop="changFactoryId">
                <el-select style="width: 100%;"   collapse-tags v-model="addForm.changFactoryId" placeholder="请选择工厂" clearable >
                  <el-option v-for="item in factoryList" :key="item.id" :label="item.name" :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>

            </el-col>
          </el-row> 
        </el-form>  
      </div>
  </qDialog>
</template>

<script>
export default {
  name: "outFactoryDialog",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    height: {
      type: Number,
      default: 205,
    },
    isTitle: {
      type: String,
      default: "转出",
    },
    editForm: {
      type: Object,
      default: () => ({ })
    },
    factoryList: {
      type: Array,
      default: () => []
    }
  }, 
  async created() {
     this.addForm = {...this.addForm,...this.editForm};
  },
  data() {
    return { 
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      }, 
      isLoading: false,
      addForm: { 
        changFactoryId:"", 
        isGroup:false,
        ids:""
      },
      rules: {
        changFactoryId: [
          { required: true, message: '请选择转出工厂', trigger: 'change' },
        ], 
      },
    };
  },
  methods: {  
    handleConfirm() { 
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
           this.isLoading = true; 
           let { changFactoryId,ids}=this.addForm
           console.log(ids,'ids')
           let fetchApi= this.addForm.isGroup ? "changeMeGroupFactory":"changeMesPersonalFactory"
          this.$api.plateTypePieceWageSystem[fetchApi]({
            ids, 
            factoryId:changFactoryId
          }).then((res) => {
             this.$notify.success({
              title: "成功",
              message: "转出成功",
            });
              this.$emit("confirm", {
                visible: false
              });
          })
          .finally(() => {
            this.isLoading = false;
          });
            
        }
      });
    },

    editCancel() {
      this.$emit("cancel", {
        type: "cancel",
        visible: false,
      });
    }
  },
};
</script>

<style lang="stylus" scoped>
 .info{
  color:red;
  margin:10px 0 10px 10px;
  line-height:24px
 }
</style>