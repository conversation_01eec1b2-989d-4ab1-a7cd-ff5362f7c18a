import router from './router'
import store from './store'
import { Message } from 'element-ui'
import { hasToken } from '@/utils/auth' // get token from cookie

// 不需要鉴权的页面，如登录页本身
const whiteList = ['/login']

router.beforeEach(async(to, from, next) => {
  document.title = to.meta.title

  // 用户是否登录
  let hasLogined = hasToken();
  if (hasLogined) {
    // 登录后查询菜单
    await store.dispatch('user/setBaseData')

    if (to.path === '/login') {
      // 登录后，打开登录页面时，直接跳转到首页
      next({ path: '/' })
    } else {
      next();

      // 以下为根据用户角色控制可访问路由权限
      /* let hasRoles = store.getters.roles && store.getters.roles.length > 0;
      if (hasRoles) {
        next()
      } else {
        try {
          // 根据用户权限，动态增加路由
          await store.dispatch('user/getRoles')
          const accessRoutes = await store.dispatch('permission/generateRoutes', roles)
          router.addRoutes(accessRoutes)
          next()
        } catch (error) {
          // 清空登录信息
          await store.dispatch('user/resetToken')
          Message.error(error || '查询角色出错')
          next(`/login?redirect=${to.path}`)
        }
      } */
    }
  } else {
    // 未登录，如果是白名单中的路由，直接进入，如登录；
    if (whiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      // 其他跳转到登录页面
      next(`/login?redirect=${to.path}`)
    }
  }
})

