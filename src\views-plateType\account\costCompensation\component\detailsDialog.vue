<template>
  <qDialog
    :visible="visible"
    :innerScroll="true"
    :innerHeight="500"
    title="查看详情"
    width="900px"
    :showFooter="false"
    :before-close="handleCancel"
  >
    <el-form :model="stagesForm" label-width="115px" size="small">
      <el-row>
        <el-col :span="12">
          <el-form-item label="流程编号:">
            {{ stagesForm.processCode }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="processCode" label="责任分厂:">
            {{ stagesForm.jobFactoryName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="厂牌编号:">
            {{ stagesForm.staffCode }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="责任人:">
            {{ stagesForm.staffName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="执行年月:">
            {{ stagesForm.executeMonth }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="扣款类型:">
            {{ stagesForm.deductType }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="实际扣款金额:">
            {{ stagesForm.actualDeductAmount | moneyFormat }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="核算分厂:">
            {{ stagesForm.factoryName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="流程扣款金额:">
            {{ stagesForm.deductAmount | moneyFormat }}
          </el-form-item>
        </el-col>

      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="扣款详情:">
            <ul class="deduction">
              <li v-for="item in detailsList" :key="item.id">
                <span>{{ item.factoryName }}</span>
                <span>{{ item.repaymentTime }}</span>
                <span>{{ item.periodAmount | moneyFormat }}</span>
                <span>{{ item.isPay }}</span>
              </li>
            </ul>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="办理人员:">
            {{ stagesForm.handleName }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="processCode" label="办理时间:">
            {{ stagesForm.handleTime | shortDate }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注说明:" prop="remark">
            <span>{{ stagesForm.remark }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="上传凭证:" prop="file">
            <div v-for="item in proofs" :key="item.proofUrl">
              <el-button
                size="small"
                type="text"
                @click="download(item.proofUrl, item.proofName)"
              >
                {{ item.proofName }}
              </el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </qDialog>
</template>

<script>
export default {
  name: "stageDialog",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    formData: Object,
  },
  data() {
    return {
      stagesForm: {
        code: "",
        factoryId: "",
        borrowStaffName: "",
        borrowStaffCode: "",
        type: "",
        processAmount: "",
        realAmount: "",
        actualAmount: "",
        payedAmount: "",
        payingAmount: "",
        comments: "",
      },
      detailsList: [],
      proofs: [],
    };
  },
  async created() {
    await this.getDetailed();
  },
  methods: {
    //获取成本详情
    getDetailed() {
      return this.$api.plateTypeInformation.costCompensation.detailDeduct({ id: this.formData.id })
        .then(({ data }) => {
          this.stagesForm = { ...data };
          this.detailsList = data.repaymentList || [];
          this.proofs = data.proofs || [];
        });
    },
    handleCancel() {
      this.$emit("cancel");
    },
  },
};
</script>

<style lang="stylus" scoped>
.deduction {
  width: 100%;
  margin: 0;
  padding: 0;

  li {
    span {
      padding-right: 10px;
    }
  }
}
</style>
