<template>
  <q-dialog
    :visible="isVisible"
    :title="title"
    width="600px"
    :innerScroll="false"
    @close="onCancel"
    @cancel="onCancel"
    @confirm="onConfirm"
  >
    <el-form
      ref="addForm"
      :model="addForm"
      :rules="rules"
      label-width="70px">
      <el-form-item label="排程单号" prop="scheduleNumber">
        <el-input v-model="addForm.scheduleNumber" size="small"></el-input>
      </el-form-item>
      <el-form-item label="订单号" prop="orderNo">
        <el-input v-model="addForm.orderNo" size="small"></el-input>
      </el-form-item>
      <el-form-item label="成品编码" prop="productCode">
        <el-input v-model="addForm.productCode" size="small"></el-input>
      </el-form-item>
      <el-form-item label="成品描述" prop="productName">
        <el-input v-model="addForm.productName" size="small"></el-input>
      </el-form-item>
      <el-form-item label="物料编码" prop="materialCode">
        <el-input v-model="addForm.materialCode" size="small"></el-input>
      </el-form-item>
      <el-form-item label="物料描述" prop="materialName">
        <el-input v-model="addForm.materialName" size="small"></el-input>
      </el-form-item>
    </el-form>
  </q-dialog>
</template>

<script>
import {assignValue} from '@/utils/'
export default {
  name: 'CRUDEdit',
  props: {
    // 重新对外暴露visible，控制显示隐藏
    visible: {
      type: Boolean,
      default: false,
    },
    modifyData: Object
  },
  computed: {
    // 通过计算属性，对.sync进行转换，外部也可以直接使用visible.sync
    isVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
    title() {
      return this.modifyData ? '修改' : '新增'
    },
  },
  data() {
    return {
      addForm: {
        id: '',
        scheduleNumber: '',
        orderNo: '',
        productCode: '',
        productName: '',
        materialCode: '',
        materialName: '',
      },
      rules: {}
    }
  },
  methods: {
    init() {
      if (this.modifyData) {
        this.initModifyData();
      }
    },
    initModifyData() {
      assignValue(this.addForm, this.modifyData);
    },
    onCancel() {
      this.isVisible = false;
    },
    onConfirm() {
      this.$refs.addForm.validate((valid) => {
        if (!valid) return;

        this.isVisible = false;
        this.$emit('success');
        return;

        let params = {
          ...this.addForm
        };

        // 新建运单
        if (!this.waybillNo) {
          params = {
            ...params,
            outFactory: this.taskList[0].outFactory,
            inAreaCode: this.taskList[0].areaCode,
            details: this.taskList.map(item => ({
              transferTaskId: item.id,
              transferCount: item.transferAmount
            }))
          }
        }

        this.$api.waybillManagement
          .add(params)
          .then(({ data }) => {
            const message = this.waybillNo ? '修改运单成功': '创建运单成功';
            this.$notify.success({
              title: '成功',
              message,
            })
            this.isVisible = false
            this.$emit('finish')
          })
      })
    },
  },
  created() {
    this.init()
  },
}
</script>

<style lang="stylus" scoped>
.form-input
  width 200px
.input-number
  >>> .el-input__inner
    text-align left
</style>