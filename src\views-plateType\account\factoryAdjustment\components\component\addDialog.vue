<template>
  <!-- 编辑新增 -->
  <qDialog :visible="isVisible" :title="title" :innerHeight="350" width="900px" :isLoading="isLoading"
    @cancel="handleCancel" @confirm="handleConfirm" :before-close="handleCancel">
    <el-form :model="addForm" :rules="rules" ref="addForm" size="small">
      <el-row>
        <el-col :span="12">
          <el-form-item label="调整类型:" class="staffName" label-width="100px">
            <el-radio-group :disabled="title == '编辑'" v-model="isAdjustmentType" @input="onAdjustmentTypeChange">
              <el-radio label="集体">集体</el-radio>
              <el-radio label="个人">个人</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 基本信息 -->
      <el-row>
        <el-col :span="12">
          <el-form-item class="staffName" label-width="100px" label="核算工厂:">{{ addForm.factoryName }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="staffName" label-width="100px" label="核算月份:">{{ addForm.accountingMonth }}</el-form-item>
        </el-col>
      </el-row>


      <!-- 个人信息 -->
      <el-row v-if="isPersonalMethod">
        <el-col :span="12">
          <el-form-item label="厂牌编号:" class="staffName" label-width="100px" prop="staffCode">
            <el-input class="staffCode" v-model.trim="addForm.staffCode" clearable style="width: 330px;"
              placeholder="请输入厂牌编号">
              <template slot="append">
                <el-button type="primary" @click="onSearch">查询</el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="staffName" label-width="100px" label="员工姓名:">{{ addForm.staffName }}</el-form-item>
        </el-col>
      </el-row>

      <!-- 班组信息 -->
      <el-row>
        <el-col :span="12" v-if="isGroupMethod">
          <el-form-item label="核算班组:" label-width="100px" prop="groupId">
            <el-select @change="onChangeGroup" style="width: 330px;" v-model="addForm.groupId" filterable clearable
              placeholder="请选择核算班组">
              <el-option v-for="item in groupList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="staffName" label-width="180px" :label="beforeAmountLabel">{{ addForm.beforeAmount
          }}</el-form-item>
        </el-col>
      </el-row>

      <!-- 调整金额 -->
      <el-row>
        <el-col :span="12">
          <el-form-item label="   " class="afterAmount" prop="amount">
            <div style="display: flex; align-items: center;">
              <el-radio-group v-model="addForm.isOutAmount" @input="onAmountTypeChange">
                <el-radio :label="AMOUNT_TYPE.OUT">本月划出</el-radio>
                <el-radio :label="AMOUNT_TYPE.IN">本月划入</el-radio>
              </el-radio-group>
            </div>
            <el-input @input="onAmountInput" v-model="addForm.amount" style="width: 200px; margin-left: 5px;" clearable
              placeholder="请输入" @blur="onAmountBlur">
              <template slot="append">元</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="labelAfter" label-width="165px" :label="afterAmountLabel">{{ addForm.afterAmount
          }}</el-form-item>
        </el-col>
      </el-row>

      <!-- 调整事由 -->
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item class="staffName" label-width="100px" label="调整事由:">
            <el-input v-model="addForm.reason" type="textarea" resize="none" maxlength="100" show-word-limit
              rows="3"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </qDialog>
</template>

<script>
import { moneyFormat, moneyDelete } from "@/utils";
import NP from "number-precision";

// 常量定义
const ALLOT_METHOD = {
  PERSONAL: '个人',
  GROUP: '集体'
};

const AMOUNT_TYPE = {
  OUT: '1',
  IN: '2'
};

const API_ALLOT_METHOD = {
  PERSONAL: 'person',
  GROUP: 'group'
};

export default {
  name: 'AddDialog',
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    allotMethod: {
      type: String,
      required: true,
    },
    groupList: {
      type: Array,
      default: () => []
    },
    editForm: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      AMOUNT_TYPE,
      addForm: this.getInitialForm(),
      isLoading: false,
      rules: {},
      isAdjustmentType: '集体'
    };
  },
  computed: {
    // 是否为个人方法
    isPersonalMethod() {
      return this.isAdjustmentType === ALLOT_METHOD.PERSONAL;
    },
    // 是否为集体方法
    isGroupMethod() {
      return this.isAdjustmentType === ALLOT_METHOD.GROUP;
    },
    // 调整前金额标签
    beforeAmountLabel() {
      return this.isGroupMethod ? '调整前计件金额:' : '调整前个人计件';
    },
    // 调整后金额标签
    afterAmountLabel() {
      return this.isGroupMethod ? '调整后计件金额:' : '调整后个人计件';
    },
    // API调用的分配方法
    apiAllotMethod() {
      return this.isGroupMethod ? API_ALLOT_METHOD.GROUP : API_ALLOT_METHOD.PERSONAL;
    }
  },
  watch: {
    isVisible: {
      handler(newVal) {
        if (newVal) {
          this.initializeForm();
          this.setupValidationRules();
        }
      },
      immediate: true,
    },
    allotMethod: {
      handler(newVal) {
        if (newVal) {
          if (this.title == '新增') {
            this.isAdjustmentType = '集体';
            return;
          }
          this.isAdjustmentType = newVal;
        }
      },
      immediate: true,
    },
  },
  methods: {
    onAdjustmentTypeChange(val) {
    },
    // 获取初始表单数据
    getInitialForm() {
      return {
        staffName: "",
        staffCode: "",
        afterAmount: "",
        isOutAmount: AMOUNT_TYPE.OUT,
        amount: "",
        groupId: "",
        reason: "",
        beforeAmount: "",
        factoryName: "",
        accountingMonth: "",
        factoryId: "",
        id: ""
      };
    },

    // 初始化表单
    initializeForm() {
      this.addForm = {
        ...this.getInitialForm(),
        ...this.editForm
      };

      // 设置金额类型和金额
      if (this.addForm.inAmount > 0) {
        this.addForm.isOutAmount = AMOUNT_TYPE.IN;
        this.addForm.amount = this.addForm.inAmount;
      } else {
        this.addForm.isOutAmount = AMOUNT_TYPE.OUT;
        this.addForm.amount = this.addForm.outAmount;
      }

      this.addForm.afterAmount = this.addForm.afterAmount || "";
    },

    // 设置验证规则
    setupValidationRules() {
      const amountRules = [
        { required: true, message: "本月划出/划入不能为空", trigger: "blur" },
        {
          validator: this.validateAmount,
          trigger: "blur",
        },
      ];

      if (this.isGroupMethod) {
        this.rules = {
          groupId: [{ required: true, message: "请选择核算班组", trigger: "blur" }],
          amount: amountRules,
        };
      } else {
        this.rules = {
          staffCode: [{ required: true, message: "请输入厂牌编号", trigger: "blur" }],
          amount: amountRules,
        };
      }
    },

    // 金额验证器
    validateAmount(rule, value, callback) {
      const cleanValue = moneyDelete(value || "");
      if (!/^-?\d{1,10}(\.\d{1,4})?$/.test(cleanValue)) {
        return callback(new Error("小数点前面仅支持10位数，小数点后面仅支持4位数"));
      }
      callback();
    },

    // 计算调整后金额
    calculateAfterAmount() {
      const beforeAmount = Number(this.addForm.beforeAmount || 0);
      const amount = Number(this.addForm.amount || 0);

      let afterAmount = 0;
      if (this.addForm.isOutAmount === AMOUNT_TYPE.OUT) {
        afterAmount = NP.minus(beforeAmount, amount);
      } else {
        afterAmount = NP.plus(beforeAmount, amount);
      }

     

      this.addForm.afterAmount = afterAmount 
    },

    // 获取调整前金额
    async getBeforeAmount() {

      const params = {
        ...this.addForm,
        allotMethod: this.apiAllotMethod,
      };
      const { data } = await this.$api.plateTypeWorkbench.factoryAdjustment.getBeforeAmount(params);
      this.addForm.beforeAmount = data || "";
      this.calculateAfterAmount();

    },

    // 查询员工信息
    async onSearch() {
      this.$refs.addForm.validateField("staffCode", async (valid) => {
        if (valid) return;

        const { data } = await this.$api.information.employee.employeeDetails({
          staffCode: this.addForm.staffCode
        });

        this.addForm.staffName = data.staffName || "";
        await this.getBeforeAmount();

      });
    },
 
    // 金额输入失焦处理
    onAmountBlur(e) {
      const beforeAmount = Number(this.addForm.beforeAmount || 0);

      if (this.checkAmount(this.addForm.amount)) return;
      if (beforeAmount <= 0) {
        this.$message.error("没有计件工资可划分");
        this.addForm.amount = "";
        this.addForm.afterAmount = "";
        return;
      }

      const cleanAmount = moneyDelete(this.addForm.amount || "0.00");
      const amount = Number(cleanAmount);

      

      this.addForm.amount = amount 
      this.calculateAfterAmount();

      
    },
    //校验金额
    checkAmount(value) {
      let flag = false;
      let amount = moneyDelete(value);
      if (!amount) {
        this.$message({
          message: "计件工资不能为空",
          type: "warning",
        });
        flag = true;
      }

      if (!flag) {
        if (!/^-?\d{1,10}(\.\d{1,4})?$/.test(amount)) {
          this.$message({
            message: "划分金额仅支持小数点前面仅支持10位数,小数点后面仅支持4位数",
            type: "warning",
          });
          flag = true;
        }
      }
      return flag;
    },
    // 取消操作
    handleCancel() {
      this.isAdjustmentType = this.allotMethod;
      this.$emit("cancel", "cancel");
    },

    // 核算班组变更
    onChangeGroup() {
      this.getBeforeAmount();
    },

    // 金额类型变更
    onAmountTypeChange() {
      this.calculateAfterAmount();
    },

    // 金额输入处理
    onAmountInput(value) {
      // 限制小数位数
      if (value.indexOf('.') > 0) {
        this.addForm.amount = value.slice(0, value.indexOf('.') + 5);
      }
      this.calculateAfterAmount();

      
    },

    // 构建提交参数
    buildSubmitParams() {
      const params = {
        accountingMonth: this.addForm.accountingMonth,
        allotMethod: this.apiAllotMethod,
        factoryId: this.addForm.factoryId,
        groupId: this.addForm.groupId,
        reason: this.addForm.reason,
      };

      // 设置金额
      if (this.addForm.isOutAmount === AMOUNT_TYPE.OUT) {
        params.outAmount = this.addForm.amount;
        params.inAmount = 0;
      } else {
        params.inAmount = this.addForm.amount;
        params.outAmount = 0;
      }

      // 个人方法需要厂牌编号
      if (this.isPersonalMethod) {
        params.staffCode = this.addForm.staffCode;
      }

      // 编辑时需要ID
      if (this.title !== "新增") {
        params.id = this.addForm.id;
      }

      return params;
    },

    // 提交表单
    async handleConfirm() {
      console.log(this.addForm.beforeAmount * 1, 'this.addForm.beforeAmount');
      if (this.addForm.beforeAmount * 1 == 0) {
        this.$message.error("计件工资不能为空");
        return;
      }
      this.$refs.addForm.validate(async (valid) => {
        if (!valid) return;

        this.isLoading = true;

        try {
          const params = this.buildSubmitParams();

          if (this.title === "新增") {
            await this.$api.plateTypeWorkbench.factoryAdjustment.factoryAdjustmentAdd(params);
            this.$notify.success({
              title: "成功",
              message: "新增成功",
            });
          } else {
            const { success } = await this.$api.plateTypeWorkbench.factoryAdjustment.factoryAdjustmentEdit(params);
            if (success) {
              this.$notify.success({
                title: "成功",
                message: "编辑成功",
              });
            }
          }

          this.$emit("cancel", "confirm");
        } finally {
          this.isLoading = false;
        }
      });
    },
  },
};
</script>

<style lang="stylus" scoped>
.staffCode {
  >>> .el-input-group__append {
    background: #24c69a;
    color: #fff;

    .el-button {
      padding: 5px 20px;
    }
  }
}

.el-form-item {
  display: flex;

  >>> .el-form-item__label {
    text-align: left;
  }

  >>> .el-form-item__content {
    width: 100%;
    margin-left: 0 !important;
  }
}

.staffName {
  >>> .el-form-item__label {
    &::before {
      content: '*';
      color: #F23D20;
      margin-right: 4px;
      visibility: hidden;
    }
  }
}

>>> .el-textarea__inner {
  padding-right: 50px;
}

.el-textarea {
  >>> .el-input__count {
    right: 20px !important;
  }
}

.afterAmount {
  >>> .el-form-item__content {
    display: flex;
  }
}

.labelAfter {
  >>> .el-form-item__label {
    margin-left: 10px;
  }
}
</style>