<template>
  <qDialog
    :visible="visible"
    :innerScroll="false"
    :innerHeight="200"
    :title="title"
    width="800px"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <el-form :model="addForm" ref="addForm" :rules="rules" label-width="110px">
      <el-row>
        <el-col :span="11">
          <el-form-item label="员工姓名:">{{ addForm.staffName }}</el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item v-if="title == '新增'" label="厂牌编号:" prop="staffCode">
            <el-input
              class="staffCode"
              v-model.trim="addForm.staffCode"
              clearable
              placeholder="请输入厂牌编号"
            >
              <template slot="append">
                <el-button type="primary" @click="searchEmployee"> 查询 </el-button>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item v-else class="staffName" label="厂牌编号:">{{
            addForm.staffCode
          }}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item label="核算工厂:">
            {{ addForm.factoryName }}
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item
            v-if="title == '新增'"
            label="核算班组:"
            prop="accountingProcess"
          >
            <el-select
              v-model="addForm.accountingProcess"
              clearable
              placeholder="请选择核算班组"
            >
              <el-option
                v-for="item in tabList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-else class="staffName" label="核算班组:">{{
            addForm.accountingProcess
          }}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item label="自定义扣款:">
            <el-switch
              v-model="addForm.isCustom"
              :active-text="addForm.isCustom == 'Y' ? '开启' : '关闭'"
              active-value="Y"
              inactive-value="N"
              @change="onSwitch"
            >
            </el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item class="staffName" label="核算月份:">
            {{ addForm.accountingMonth }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item label="未打卡次数:" prop="unClockTimes">
            <el-input
              oninput="value=value.replace(/[^\d]/g, '')"
              v-model="addForm.unClockTimes"
              clearable
              @blur="unclockInBlur"
              @clear="addForm.unClockAmount = ''"
              placeholder="请输入未打卡次数"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item
            v-show="addForm.isCustom === 'Y'"
            label="未打卡扣款:"
            prop="unClockAmount"
          >
            <el-input clearable v-model.trim="addForm.unClockAmount">
              <template slot="append"> 元 </template>
            </el-input>
          </el-form-item>
          <el-form-item
            v-show="addForm.isCustom === 'N'"
            class="staffName"
            label="未打卡扣款:"
            >{{ addForm.unClockAmount }}</el-form-item
          >
        </el-col>
      </el-row>
    </el-form>
  </qDialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    editForm: [Object],
  },
  data() {
    return {
      addForm: {
        staffName: "",
        staffCode: "",
        factoryName: "",
        accountingProcess: "",
        accountingMonth: "",
        isCustom: "N",
        unClockTimes: "",
        unClockAmount: "",
      },
      tabList: [],
      staffCode: "",
      rules: {
        staffCode: [{ required: true, message: "厂牌编号不能为空", trigger: "blur" }],
        accountingProcess: [
          { required: true, message: "核算班组不能为空", trigger: "change" },
        ],
        unClockTimes: [
          {
            pattern: /^\+?[0-9]\d*$/,
            message: "请输入正整数",
            trigger: "blur",
          },
        ],
        unClockAmount: [
          { required: true, message: "未打卡扣款不能为空", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (!/^-?\d{1,3}(\.\d{1,2})?$/.test(value)) {
                return callback(new Error("小数点前面仅支持3位数,小数点后面仅支持2位数"));
              }
              if (!(value >= -300 && value <= 300)) {
                return callback(new Error("自定义金额应为-300-300元"));
              }
              callback();
            },
            trigger: "blur",
          },
        ],
      },
      validateFieldList: [],
    };
  },
  created() {
    const { factoryName, accountingMonth } = JSON.parse(this.$Base64.decode(this.$route.query.data));
    this.addForm = {
      ...this.addForm,
      factoryName,
      accountingMonth,
    };
    if (this.title == "编辑") {
      this.addForm = {
        ...this.addForm,
        ...this.editForm,
      };
    }
  },
  methods: {
    //获取核算班组
    async getListGroups(staffCode) {
      const { factoryId, accountingMonth } = JSON.parse(this.$Base64.decode(this.$route.query.data));
      await this.$api.logisticsDataUpload.noPunchDeduction
        .getListGroups({
          factoryId,
          accountingMonth,
          staffCode,
        })
        .then((res) => {
          if (res.code === 200) {
            this.tabList = res.data || [];
          }
        });
    },
    //查询员工信息
    searchEmployee() {
      if (!this.addForm.staffCode) {
        this.$message.warning("请输入厂牌编号");
        return;
      }
      this.$api.information.employee
        .employeeDetails({ staffCode: this.addForm.staffCode })
        .then(({ data }) => {
          this.addForm.staffName = data.staffName || "";
          this.staffCode = data.staffCode || "";
          this.getListGroups(this.staffCode);
        });
    },
    onSwitch(value) {
      this.editForm.isCustom = value;
      if (value == "N") this.unclockInBlur();
    },
    //未打卡次数失去焦点
    unclockInBlur() {
      if (
        this.addForm.isCustom == "N" &&
        /^\+?[0-9]\d*$/.test(this.addForm.unClockTimes)
      ) {
        this.addForm = {
          ...this.addForm,
          unClockAmount:
            Number(this.addForm.unClockTimes) <= 10
              ? Number(this.addForm.unClockTimes) * 10
              : (Number(this.addForm.unClockTimes) - 10) * 20 + 10 * 10,
        };
        if (this.addForm.unClockAmount > 300) {
          this.addForm.unClockAmount = 300;
        }
      }
    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      if (this.title == "新增") {
        if (!this.addForm.staffName) {
          this.$message.warning("员工姓名为空,请先查询员工姓名");
          return;
        }
        const { factoryId, accountingMonth } = JSON.parse(this.$Base64.decode(this.$route.query.data));
        let params = {
          accountingMonth,
          factoryId,
          isCustom: this.addForm.isCustom,
          processId: this.addForm.accountingProcess,
          staffCode: this.staffCode,
          staffName: this.addForm.staffName,
          unClockDeduct: this.addForm.unClockAmount,
          unClockTimes: this.addForm.unClockTimes,
        };
        if (this.addForm.isCustom == "Y") {
          this.$refs.addForm.validate((valid) => {
            if (!valid) return;
            this.$api.logisticsDataUpload.noPunchDeduction.addUnClock(params).then(() => {
              this.$notify({
                title: "提示",
                message: "新增成功",
              });
              this.$emit("cancel", "confirm");
            });
          });
        } else {
          this.$refs.addForm.validateField(
            ["staffCode", "accountingProcess"],
            (valid) => {
              this.validateFieldList.push(valid);
              if (
                this.validateFieldList.length != 2 ||
                this.validateFieldList.every((item) => item != "")
              )
                return;
              if (!this.addForm.unClockAmount) {
                this.$message.warning("请输入未打卡次数");
                return;
              }
              this.$api.logisticsDataUpload.noPunchDeduction.addUnClock(params).then(() => {
                this.$notify({
                  title: "提示",
                  message: "新增成功",
                });
                this.$emit("cancel", "confirm");
              });
            }
          );
        }
      } else {
        let params = {
          id: this.editForm.id,
          isCustom: this.addForm.isCustom,
          unClockAmount: String(this.addForm.unClockAmount),
          unClockTimes: String(this.addForm.unClockTimes),
        };
        if (this.addForm.isCustom == "Y") {
          this.$refs.addForm.validateField("unClockAmount", (valid) => {
            if (valid) return;
            this.$api.logisticsDataUpload.noPunchDeduction
              .editNoPunchDeduction(params)
              .then(({ success }) => {
                if (success) {
                  this.$notify({
                    title: "成功",
                    message: "修改成功",
                    type: "success",
                  });
                }
                this.$emit("cancel", "confirm");
              });
          });
          return;
        }
        if (!this.addForm.unClockAmount) {
          this.$message.warning("请输入未打卡次数");
          return;
        }
        this.$api.logisticsDataUpload.noPunchDeduction
          .editNoPunchDeduction(params)
          .then(({ success }) => {
            if (success) {
              this.$notify({
                title: "成功",
                message: "修改成功",
                type: "success",
              });
            }
            this.$emit("cancel", "confirm");
          });
      }
    },
  },
};
</script>

<style lang="stylus" scoped>
.staffCode {
  >>>.el-input-group__append {
    background: #24c69a;
    color: #fff;

    .el-button {
      padding: 5px 20px;
    }
  }
}

.el-row {
  &::before, &::after {
    display: none;
  }

  display: flex;
  justify-content: space-between;
}

>>>.el-select {
  width: 100%;
}

>>>.el-form-item__label {
  text-align: left;
}

.staffName {
  >>>.el-form-item__label {
    &::before {
      content: '*';
      color: #F23D20;
      margin-right: 4px;
      visibility: hidden;
    }
  }
}
</style>
