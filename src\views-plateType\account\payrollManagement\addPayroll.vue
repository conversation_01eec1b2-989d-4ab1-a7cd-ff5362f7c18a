<template>
  <qDialog
    :visible="visible"
    :innerScroll="true"
    :innerHeight="650"
    :title="title"
    width="1000px"
    :modal-append-to-body="false"
    append-to-body
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <common title="员工信息">
      <el-form
        :model="staffInfo"
        label-width="106px"
        ref="staffInfo"
        :rules="staffRules"
        size="small"
      >
        <el-row>
          <el-col :span="8">
            <el-form-item class="processCode" label="员工姓名:" prop="staffName">
              <el-input v-model="staffInfo.staffName" disabled placeholder=""> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="厂牌编号:" prop="staffCode">
              <el-input
                v-model.trim="staffInfo.staffCode"
                clearable
                placeholder="请输入厂牌编号"
              >
                <template slot="append">
                  <el-button type="primary" @click="searchEmployee"> 查询 </el-button>
                </template>factoryId
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item class="processCode" label="身份证号:" prop="idCard">
              <template>
                <span>{{ staffInfo.idCard }}</span>
                <i
                  v-if="staffInfo.idCard"
                  :class="['iconfont', showType ? 'icon-guanbi' : ' icon-yincangmima']"
                  @click="toggle"
                ></i>
              </template>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="工厂名称:" prop="factoryId">
              <el-select
                v-model="staffInfo.factoryId"
                filterable
                clearable
                placeholder="请选择工厂名称"
              >
                <el-option
                  v-for="item in factoryNameOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="工序名称:" prop="processId">
              <el-select
                v-model="staffInfo.processId"
                filterable
                clearable
                placeholder="请选择工序名称"
              >
                <el-option
                  v-for="item in processesOption"
                  :key="item.id"
                  :label="item.name"
                  :value="item.name"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </common>
    <common title="表单信息">
      <el-form
        :model="formData"
        label-width="106px"
        ref="formData"
        :rules="rules"
        size="small"
      >
        <el-row>
          <el-col :span="8">
            <el-form-item class="processCode" label="单据编号:" prop="code">
              {{ formData.no }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item class="processCode" label="制表人员:" prop="idCard">
              {{ formData.tableName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item class="processCode" label="办理日期:" prop="staffName">
              {{ formData.handleTime }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="工资表类型:" prop="type">
              <el-select
                v-model="formData.type"
                filterable
                clearable
                placeholder="请选择工资表类型"
              >
                <el-option
                  v-for="item in payrollOptions"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item class="processCode" label="审核人员:">
              {{ formData.auditName }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </common>
    <div>
      <div class="header">
        <span>工资单详情</span>
        <div class="btn">
          <el-button size="small" type="primary" @click="changeDuction"> 扣款明细 </el-button>
          <el-button size="small" type="primary" @click="seleteMonth">
            选择月份
          </el-button>
          <el-button size="small" type="primary" @click="handleEdit"> 编辑 </el-button>
          <el-button size="small" type="primary" @click="modifyRecord">
            修改记录
          </el-button>
          <el-button size="small" type="primary" @click="handleDelete"> 删除 </el-button>
        </div>
      </div>
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tablePayroll"
        :height="200"
        highlight-current-row
        :data="tableList"
        :row-class-name="tableRowClassName"
        @select-all="handleSelectionAll"
        @selection-change="handleSelectionChange"
      >
        <el-table-column width="40" type="selection"> </el-table-column>
        <el-table-column
          prop="accountingMonth"
          label="结算月份"
          width="75"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column label="出勤天数" width="75" align="left" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span
              :style="{
                color:
                  row.attendances.sysAmount == row.attendances.settlementAmount
                    ? '#0BB78E'
                    : 'red',
              }"
            >
              {{
                row.attendances.settlementAmount
                  ? row.attendances.settlementAmount
                  : "0.00"
              }}</span
            >
          </template>
        </el-table-column>
        <el-table-column label="计件工资" width="75" align="left" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span
              :style="{
                color:
                  row.totalPiece&&row.totalPiece.sysAmount ==  row.totalPiece&&row.totalPiece.settlementAmount
                    ? '#0BB78E'
                    : 'red',
              }"
            >
              {{
                 row.totalPiece&&row.totalPiece.settlementAmount
                  ?  row.totalPiece&&row.totalPiece.settlementAmount
                  : "0.00"
              }}</span
            >
          </template>
        </el-table-column>
        <el-table-column label="工资总额" width="85" align="left" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span
              :style="{
                color:
                  filterData(row.salary.sysAmount) ==
                  filterData(row.salary.settlementAmount)
                    ? '#0BB78E'
                    : 'red',
              }"
            >
              {{ row.salary.settlementAmount | moneyFormat }}</span
            >
          </template>
        </el-table-column>
        <el-table-column label="其他扣款" width="85" align="left" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span
              :style="{
                color:
                  filterData(row.otherDeduct.sysAmount) ==
                  filterData(row.otherDeduct.settlementAmount)
                    ? '#0BB78E'
                    : 'red',
              }"
            >
              {{ row.otherDeduct.settlementAmount | moneyFormat }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="厂服扣款" width="85" align="left" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span
              :style="{
                color:
                  filterData(row.uniformDeduct.sysAmount) ==
                  filterData(row.uniformDeduct.settlementAmount)
                    ? '#0BB78E'
                    : 'red',
              }"
            >
              {{ row.uniformDeduct.settlementAmount | moneyFormat }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="生活费" width="85" align="left" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span
              :style="{
                color:
                  filterData(row.living.sysAmount) ==
                  filterData(row.living.settlementAmount)
                    ? '#0BB78E'
                    : 'red',
              }"
            >
              {{ row.living.settlementAmount | moneyFormat }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="保险" width="85" align="left" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span
              :style="{
                color:
                  filterData(row.insurance.sysAmount) ==
                  filterData(row.insurance.settlementAmount)
                    ? '#0BB78E'
                    : 'red',
              }"
            >
              {{ row.insurance.settlementAmount | moneyFormat }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="工会" width="85" align="left" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span
              :style="{
                color:
                  filterData(row.labour.sysAmount) ==
                  filterData(row.labour.settlementAmount)
                    ? '#0BB78E'
                    : 'red',
              }"
            >
              {{ row.labour.settlementAmount | moneyFormat }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="借支" width="85" align="left" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span
              :style="{
                color:
                  filterData(row.loan.sysAmount) == filterData(row.loan.settlementAmount)
                    ? '#0BB78E'
                    : 'red',
              }"
            >
              {{ row.loan.settlementAmount | moneyFormat }}
            </span>
          </template>
        </el-table-column>


        <el-table-column
          label="自离扣款"
          width="85"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <span
              :style="{
                color:
                  filterData(row.leaveDeduct.sysAmount) ==
                  filterData(row.leaveDeduct.settlementAmount)
                    ? '#0BB78E'
                    : 'red',
              }"
            >
             {{ row.leaveDeduct.settlementAmount | moneyFormat }}
            </span>
          </template>
        </el-table-column>



        <el-table-column label="扣款合计" width="85" align="left" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span
              :style="{
                color:
                  filterData(row.totalDeduct.sysAmount) ==
                  filterData(row.totalDeduct.settlementAmount)
                    ? '#0BB78E'
                    : 'red',
              }"
            >
              {{ row.totalDeduct.settlementAmount | moneyFormat }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="实发工资" align="left" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span
              :style="{
                color:
                  filterData(row.actualSalary.sysAmount) ==
                  filterData(row.actualSalary.settlementAmount)
                    ? '#0BB78E'
                    : 'red',
              }"
            >
              {{ row.actualSalary.settlementAmount | moneyFormat }}
            </span>
          </template>
        </el-table-column>
      </el-table>
      <div class="table-footer">
        <div id="table_footer">
          <div class="footer_left">
            <span>合计人民币（小写）:</span>
            <span>{{ smallNum }}</span>
          </div>
          <div class="footer_left">
            <span>合计人民币（大写）:</span>
            <span>{{ bigNum }}</span>
          </div>
        </div>
        <div class="form_content">
          <span style="display: block; padding: 10px 0"
            >备注说明:1.工资总额包含（计件工资、生活补贴、奖励、返工工资、住房补贴等）
            2.其他扣款包含（处罚、成本赔偿、低耗扣款、返工扣款等）</span
          >
          <el-input
            v-model="remarks"
            type="textarea"
            resize="none"
            rows="3"
            show-word-limit
            maxlength="100"
          >
          </el-input>
        </div>
      </div>
    </div>
    <deduction-details
      v-if="deductionVisible"
      :editVisible="deductionVisible"
      :editTitle="editTitle"
      @cancel="deductionCancel"
      :modifyData="modifyData"
    >
    </deduction-details>
  </qDialog>
</template>
<script>
import common from "./component/common";
import salaryDetails from "./component/salaryDetails";
import deductionDetails from "./component/deductionDetails";
import { getSum, dealBigMoney } from "./common";
import { moneyFormat } from "@/utils";
export default {
  name: "addPayroll",
  mixins: [salaryDetails],
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    editForm: [Object],
  },
  components: { common, deductionDetails },
  data() {
    return {
      deductionVisible: false,
      modifyData: {},
      staffRules: {
        staffCode: [{ required: true, message: "厂牌编号不能为空", trigger: "blur" }],
        factoryId: [{ required: true, message: "工厂名称不能为空", trigger: "change" }],
        processId: [{ required: true, message: "工序名称不能为空", trigger: "change" }],
      },
      rules: {
        type: [{ required: true, message: "工资表类型不能为空", trigger: "change" }],
      },
      //员工信息
      staffInfo: {
        staffName: "",
        staffCode: "",
        idCard: "",
        idCardOrigin: "",
        idCardDecoded: "",
        factoryId: "",
        processId: "",
      },
      //表单信息
      formData: {
        no: "",
        tableName: "",
        handleTime: "",
        type: "",
        auditName: "",
      },
      //工资条类型
      payrollOptions: Object.freeze([
        {
          name: "辞职工资",
          value: "1",
        },
        {
          name: "自离工资",
          value: "2",
        },
        {
          name: "延发工资",
          value: "3",
        },
        {
          name: "其他工资",
          value: "4",
        },
      ]),
      remarks: "",
      idCardRsa: "",
      tabList: [],
      commonTitle: "",
      commonVisible: false,
      showType: false,
      isClearMonth: false,
      isExist: false,
      isLoading:false,
      listDetails:{}
    };
  },
  async created() {
    if (this.title == "编辑") {
      this.getDetail();
    }
    await this.$api.plateTypeSystemManage.getBasicPermission
      .getQuFactory({moduleId:3})
      .then((res) => {
          this.tabList = res.data || [];
      });
  },
  computed: {
    factoryNameOptions() {
      return (
        this.tabList.length > 0 &&
        this.tabList.map((item) => ({ id: item.id, name: item.name }))
      );
    },
    processesOption() {
      if (
        /^(?:[\u3400-\u4DB5\u4E00-\u9FEA\uFA0E\uFA0F\uFA11\uFA13\uFA14\uFA1F\uFA21\uFA23\uFA24\uFA27-\uFA29]|[\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0])+$/.test(
          this.staffInfo.factoryId
        )
      ) {
        if (this.tabList.length > 0) {
          this.staffInfo.factoryId = this.tabList.find(
            (item) => item.name == this.staffInfo.factoryId
          ).id;
        }
      }
      return (
        this.tabList.length > 0 &&
        this.staffInfo.factoryId &&
        this.tabList.find((item) => item.id === this.staffInfo.factoryId).process
      );
    },
  },
  watch: {
    "staffInfo.staffCode"(newValue, oldValue) {
      this.isClearMonth = newValue && oldValue;
      if (this.isClearMonth) {
        this.tableList = [];
      }
    },
    "staffInfo.factoryId"(newValue, oldValue) {
      let factoryId;
      if (
        /^(?:[\u3400-\u4DB5\u4E00-\u9FEA\uFA0E\uFA0F\uFA11\uFA13\uFA14\uFA1F\uFA21\uFA23\uFA24\uFA27-\uFA29]|[\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0])+$/.test(
          oldValue
        )
      ) {
        factoryId = "";
      } else {
        factoryId = oldValue;
      }
      this.isClearMonth = newValue && factoryId;
      if (this.isClearMonth) {
        this.staffInfo.processId = "";
        this.tableList = [];
      }
    },
  },
  methods: {
    //特殊工资单详情
    async getDetail() {
      const { data } = await this.$api.plateTypeInformation.payrollManagement.salaryDetail({
        id: this.editForm.id,
      });
      this.listDetails = data;
      this.idCardRsa = data.idCardRsa;
      this.remarks = data.remark;
      this.staffInfo = {
        staffName: data.staffName || "",
        staffCode: data.staffCode || "",
        idCard: data.idCard,
        idCardOrigin: data.idCard || "",
        idCardDecoded: "",
        factoryId: data.factoryName || "",
        processId: data.processName || "",
      };
      this.formData = {
        no: data.no || "",
        tableName: data.tableName || "",
        handleTime: data.handleTime || "",
        type:String(data.type),
      };
      data.list.forEach((v)=>{ //没有自离扣款手动添加
        if(!v.leaveDeduct){
          v.leaveDeduct = {
            settlementAmount:"0.00",
            sysAmount:"0.00"
          }
        }
      })

      this.tableList = data.list && data.list.sort((a, b) => new Date(a.accountingMonth).getTime() - new Date(b.accountingMonth).getTime())  || [];;
      console.log( this.tableList,' this.tableList')
      this.smallNum = moneyFormat(getSum(this.tableList))
      this.bigNum = dealBigMoney(this.smallNum);
      this.isExist = data.staffName ? true : false;
    },
    //查询员工信息
    searchEmployee() {
      this.$refs.staffInfo.validateField(["staffCode"], (valid) => {
        if (valid) return;
        this.$api.information.employee
          .employeeDetails({ staffCode: this.staffInfo.staffCode })
          .then(({ data }) => {
            this.staffInfo.staffName = data && data.staffName;
            this.staffInfo.idCard = data && data.idCard;
            this.staffInfo.idCardOrigin = data && data.idCard;
            this.idCardRsa = data && data.idCardRsa;
            this.isExist = data ? true : false;
          });
      });
    },
    //身份证解码
    toggle() {
      if (this.showType) {
        this.staffInfo.idCard = this.staffInfo.idCardOrigin;
        this.showType = false;
      } else {
        if (this.staffInfo.idCardDecoded) {
          this.staffInfo.idCard = this.staffInfo.idCardDecoded;
          this.showType = true;
          return;
        }
        this.$api.information.employee.decrypt(this.idCardRsa).then((res) => {
          this.staffInfo.idCard = res.data;
          this.staffInfo.idCardDecoded = res.data;
          this.showType = true;
        });
      }
    },
    handleCancel() {
      this.$emit("cancel", {
        type: "cancel",
        isVisible: false,
      });
    },
    handleConfirm() {
      this.$refs.staffInfo.validate((valid) => {
        this.$refs.formData.validate((_valid) => {
          if (this.tableList.length == 0) {
            this.$message.warning("工资单详情至少包含一条数据");
            return;
          }
          if (!this.staffInfo.staffName) {
            this.$message.warning("请先输入厂牌编号查询员工姓名");
            return;
          }
          if (_valid && valid) {
            if (
              /^(?:[\u3400-\u4DB5\u4E00-\u9FEA\uFA0E\uFA0F\uFA11\uFA13\uFA14\uFA1F\uFA21\uFA23\uFA24\uFA27-\uFA29]|[\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0])+$/.test(
                this.staffInfo.factoryId
              )
            ) {
              this.staffInfo.factoryId = this.tabList.find(
                (item) => item.name === this.staffInfo.factoryId
              ).id;
            }
            let process = this.tabList.find(
              (item) => item.id === this.staffInfo.factoryId
            ).process;
            let processId =
              process && process.find((item) => item.name == this.staffInfo.processId).id;
            if (
              /^(?:[\u3400-\u4DB5\u4E00-\u9FEA\uFA0E\uFA0F\uFA11\uFA13\uFA14\uFA1F\uFA21\uFA23\uFA24\uFA27-\uFA29]|[\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0])+$/.test(
                this.formData.type
              )
            ) {
              this.formData.type = this.payrollOptions.find(
                (item) => item.name === this.formData.type
              ).value;
            }
            this.isLoading = true;
            const { factoryId } = this.staffInfo;
            const { type } = this.formData;
            let params = {
              staffCode: this.staffInfo.staffCode,
              factoryId,
              processId,
              type,
              details: this.tableList,
              remark: this.remarks,
            };
            if (this.title == "新增") {
              this.$api.plateTypeInformation.payrollManagement
                .addSpecialSalary(params)
                .then(({ data }) => {
                  if (data.length > 0) {
                    let errorMsg = `新增失败!  原因:${data
                      .map((item, index) => `${index + 1}、${item}`)
                      .join(",")}`;
                    this.$notify.error({
                      title: "通知",
                      message: errorMsg,
                      duration: 0,
                    });
                  } else {
                    this.$notify.success({
                      title: "成功",
                      message: "新增成功",
                    });
                    this.$emit("cancel", {
                      type: "confirm",
                      isVisible: false,
                    });
                  }
                }).finally(()=>{
                  this.isLoading = false;
                });
            } else {
              this.$api.plateTypeInformation.payrollManagement
                .editSpecialSalary({ id: this.editForm.id, ...params })
                .then(({ data }) => {
                  if (data.length > 0) {
                    let errorMsg = `编辑失败!  原因:${data
                      .map((item, index) => `${index + 1}、${item}`)
                      .join(",")}`;
                    this.$notify.error({
                      title: "通知",
                      message: errorMsg,
                      duration: 0,
                    });
                  } else {
                    this.$notify.success({
                      title: "成功",
                      message: "编辑成功",
                    });
                    this.$emit("cancel", {
                      type: "confirm",
                      isVisible: false,
                    });
                  }
                }).finally(()=>{
                  this.isLoading = false;
               });
            }
          }
        });
      });
    },
    // 扣款明细
    changeDuction(){
      if (!this.idList.length) {
        this.$message.warning("请先勾选工资单详情数据");
        return;
      }
      // if (this.idList.length > 1) {
      //   this.$message.warning("请勿选中多行数据");
      //   return;
      // }
     const {factoryId,staffCode} = this.staffInfo;
     const {type} =this.formData;
      this.modifyData = {
        factoryId,
        type,
        staffCode,
        months: this.idList.map((item)=>{return item.accountingMonth}),
      };

      this.editTitle = "扣款明细";
      this.deductionVisible = true;
    },
    deductionCancel() {
      this.deductionVisible = false;
    }
  },
};
</script>

<style lang="stylus" scoped>
>>>.el-form-item__label {
  text-align: left;
}

>>>.el-input-group__append {
  background: #24c69a;
  color: #fff;

  .el-button {
    padding: 5px 10px;
  }
}

>>>.el-select {
  width: 100%;
}

.el-col {
  padding-right: 10px;
}

>>>.el-table__header .el-table-column--selection {
  .el-checkbox {
    visibility: hidden;
    z-index: 99999;
  }
}

.processCode {
  >>>.el-form-item__label {
    &::before {
      content: '*';
      color: #F23D20;
      margin-right: 4px;
      visibility: hidden;
    }
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;

  >span {
    color: #24c69a;
    font-weight: bold;
    font-size: 14px;
  }
}

.table-footer {
  margin-top: 10px;

  #table_footer {
    display: flex;
    justify-content: space-between;
  }
}

>>>.el-table__body {
  .table_row {
    .el-table__cell {
      background: pink !important;
    }
  }
}

>>>.el-textarea__inner {
  padding-right: 50px;
}
</style>
