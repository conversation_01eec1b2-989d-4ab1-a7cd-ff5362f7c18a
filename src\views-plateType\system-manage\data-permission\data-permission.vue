<template>
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          label-position="right"
          size="mini"
        >
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenstaffNames"
                  titleName="员工姓名"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input
              v-model.trim="searchForm.staffCode"
              size="mini"
              clearable
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenstaffCodes"
                  titleName="厂牌编号"
                />
              </template>
            </el-input>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary" @click="onSearch">查询</el-button>
          <el-button size="small" type="warning"  @click="resetSearchForm">
            重置</el-button
          >
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :data="tableData"
        :height="maxTableHeight"
        style="width: 100%"
      >
        <el-table-column type="index" width="60" align="left"> </el-table-column>
        <el-table-column prop="staffName" label="姓名" width="100" align="left">
        </el-table-column>
        <el-table-column prop="staffCode" label="厂牌编号" width="140" align="left">
        </el-table-column>
        <el-table-column prop="adAccount" label="账号" width="140" align="left">
        </el-table-column>
        <el-table-column
          prop="department"
          label="部门"
          width="140"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column prop="workStation" label="岗位" width="140" align="left">
        </el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="roleName"
          label="系统角色"
          width="140"
          align="left"
        >
        </el-table-column>
        <el-table-column prop="disable" label="账号状态" width="75" align="left">
          <template slot-scope="scope">{{
            scope.row.disable == 1 ? "启用" : "禁用"
          }}</template>
        </el-table-column>
        <el-table-column
          prop="linkProcessName"
          label="数据权限"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column label="操作" align="left" width="140">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="handleEdit(scope.row)">
              编辑</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div class="footer">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pageNum"
            :page-size="pageSize"
            :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </template>
    </table-panel>
    <edit-dialog
      v-if="visible"
      :visible="visible"
      :editForm="editForm"
      @cancel="handleCancel"
    ></edit-dialog>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import editDialog from "./editDialog.vue";
export default {
  name: "PlateTypeDataPermission",
  mixins: [tableMixin,pagePathMixin],
  components: { editDialog },
  data() {
    return {
      // 筛选数据
      searchForm: {
        content: "",
        staffCode: "",
        staffName: "",
      },
      editForm: {},
      tableData: [],
      loading: false,
      pageSize: 50, //每页条数
      pageNum: 1, //
      total: 0,
      visible: false, //控制弹窗显示隐藏
      filterParam: {},
      params: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      let params = {
        pageSize: this.pageSize,
        pageNum: this.pageNum,
        filterData: {
          ...this.filterParam,
          ...this.params,
        },
      };
      this.$api.plateTypeSystemManage.dataPermission
        .getUserPermission(params)
        .then((res) => {
          res.data.list.forEach((v) => {
            if (v.permissions) {
              v.linkProcessName = v.permissions
                .map(function (obj) {
                  if (obj) {
                    return obj.factoryName;
                  }
                })
                .join(",");
              v.factoryId = v.permissions
                .map(function (obj) {
                  if (obj) {
                    return obj.factoryId;
                  }
                })
                .join(",");
            }
          });
          this.tableData = res.data.list || [];
          this.total = res.data.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenstaffNames.resetFilter();
      this.$refs.childrenstaffCodes.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    // 点编辑
    handleEdit(row) {
      const { staffCode, staffName, permissions } = row;
      this.visible = true;
      this.editForm = {
        staffId: row.staffId,
        staffCode,
        staffName,
        permissions,
      };
    },
    //点取消
    handleCancel(type) {
      this.visible = false;
      if (type == "cancel") return;
      this.getList();
    },
    //分页   页数条数改变函数
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    handleCurrentChange(val) {
      this.pageNum = val;
      this.getList();
    },
  },
};
</script>

<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}

>>> .el-pagination__editor.el-input {
  width: 50px !important;
}
</style>
