<template>
  <div class="content-panel">
    <div class="header-right" v-if="$slots.headerRight">
      <slot name="headerRight"></slot>
    </div>
    <div class="search-area" v-if="$slots.search">
      <slot name="search"></slot>
    </div>
    <div class="sub-area" v-if="$slots.sub">
      <slot name="sub"></slot>
    </div>
    <div class="main-area">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ContentPanel'
};
</script>

<style lang="stylus" scoped>
.content-panel
  margin 4px
  .main-area, .sub-area
    color #606266
    background-color #ffffff
    border-radius 5px
  .main-area
    margin-top 4px
    padding 8px
  .sub-area
    padding 8px
</style>