<template>
  <qDialog :visible="isVisible" title="调职记录" :innerHeight="150" width="600px" @cancel="handleCancel"
    @confirm="handleConfirm" :before-close="handleCancel">
    <el-table stripe border v-loading="loading" ref="tableRef" height="150" highlight-current-row :data="tableData">
      <el-table-column width="40" type="index" align="left" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="transferDate" label="调职日期" align="left" width="120" show-overflow-tooltip> 
        <template slot-scope="{row}">
          {{ row.transferDate | dateFormats }}
        </template>
      </el-table-column>
      <el-table-column label="员工姓名" prop="staffName" align="left" width="120" show-overflow-tooltip> 
      </el-table-column>
      <el-table-column label="员工编号" prop="staffCode" align="left" width="150" show-overflow-tooltip> 
      </el-table-column>
      <el-table-column prop="oldOrgFullPath" label="原组织全路径" align="left" width="200" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="oldPost" label="原职位" align="left" width="150" show-overflow-tooltip>
      </el-table-column> 
           <el-table-column prop="oldCompileChild" label="原子工序" align="left" width="150" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="oldStaffType" label="原员工类别" align="left" width="150" show-overflow-tooltip>
      </el-table-column> 
    </el-table>
  </qDialog>
</template>

<script>
export default {
  name: "historyDialog",
  props: {
    isVisible: {
      type: Boolean,
      required: true
    },
    editForm: Object
  },
  data() {
    return {
      tableData: [],
      loading: false
    };
  },
  created() {
    this.getDetails();
  },
  methods: {
    //获取员工详情
    getDetails() {
      this.loading = true;
      this.$api.plateTypeInformation.employee.searchTransferRecord({ staffCode: this.editForm.staffCode }).then(({ data }) => {
        this.tableData = data || [];
      }).finally(() => {
        this.loading = false;
      });
    }, 
    handleCancel() {
      this.$emit('historyCancel', 'cancel');
    },
    handleConfirm() {
      this.$emit('historyCancel', 'cancel');
    }
  }
};
</script>

<style lang="stylus" scoped></style>