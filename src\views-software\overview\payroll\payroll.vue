<template>
  <content-panel>
    <table-panel ref="tablePanel">
      <template v-slot:header-left>
        <el-tabs v-model="activeName" ref="tabs">
          <el-tab-pane
            v-for="item in tabList"
            :key="item.value"
            :label="item.name"
            :name="item.value"
          >
          </el-tab-pane>
        </el-tabs>
      </template>
      <template v-slot:header-right>
        <div ref="btnRight">
          <el-button
            v-if="
              isShowCustomBtn &&
              JSON.parse($route.query.data).completeState == 0 &&
              (detailInfo.nodeName == '资料收集' ||
                detailInfo.nodeName == '财务标记')
            "
            v-permission="
              'was-customized$workBench$software$workOverview$batchRemark'
            "
            :loading="isLoading"
            size="small"
            type="primary"
            @click="handleSubmit"
          >
            提交
          </el-button>
          <el-button
            v-if="
              isShowCustomBtn &&
              JSON.parse($route.query.data).completeState == 0
            "
            v-permission="
              'was-customized$workBench$software$workOverview$batchRemark'
            "
            size="small"
            type="primary"
            @click="batchRemark"
          >
            批量备注
          </el-button>
          <el-button
            v-if="isShowCustomBtn"
            v-permission="
              'was-customized$workBench$software$workOverview$payrollCustomColumns'
            "
            size="small"
            type="primary"
            @click="handleColumns"
          >
            自定义列
          </el-button>
          <el-button
            v-if="isShowEditBtn"
            size="small"
            type="primary"
            @click="handleEdit"
          >
            编辑
          </el-button>
          <el-button size="small" type="primary" @click="handleExport">
            导出
          </el-button>
          <el-button
            v-if="activeName=='Detailed'"
            v-permission="
              `was-customized$workBench$software$workOverview$printBtn`
            "
            size="small"
            type="primary"
            @click="printSettings"
          >
            打印
          </el-button>
        </div>
      </template>
      <component
        :is="componentName"
        :key="activeName"
        :ref="activeName"
        @getStatus="getStatus"
      >
      </component>
    </table-panel>
  </content-panel>
</template>
<script>
import registerComponent from "./registerComponent";
export default {
  name:'SoftwarePayroll',
  mixins: [registerComponent],
  data() {
    return {
      activeName: "Detailed",
      componentName: "",
      tabList: [
        {
          name: "工资表",
          value: "Detailed",
          isShow: true,
        },
        {
          name: "汇总表",
          value: "Summary",
          isShow: true,
        },
        {
          name: "分配表",
          value: "Allocation",
          isShow: true,
        },
        {
          name: "汇总财务表",
          value: "Financial",
          isShow: true,
        },
        {
          name: "上卡现金表",
          value: "Cash",
          isShow: true,
        },
      ],
      isShowCustomBtn: false,
      isShowEditBtn: false,
      info: {},
      detailInfo: {},
      isLoading: false,
      isFilterEmpty: 1,
    };
  },
  created() {
    this.getTab();
  },
  watch: {
    activeName: {
      handler(value) {
        const { completeState } = this.parseRouteQueryData();
        this.isShowCustomBtn = value == "Detailed" ? true : false;
        this.isShowEditBtn =
          (value == "Financial" || value == "Summary") &&
          completeState == "0";
        this.componentName = this.filterComponent(value);
        if (value == "Detailed") this.getTaskDetail();
      },
      immediate: true,
    },
  },
  methods: {
    parseRouteQueryData(data = this.$route.query.data) {
      try {
        // 验证data是否为有效JSON字符串
        if (typeof data !== "string" || !data.trim()) {
          console.warn("Route query data is empty or invalid:", data);
          return {};
        }
        return JSON.parse(data);
      } catch (error) {
        console.error("Failed to parse route query data:", error);
        return {};
      }
    },
    getStatus(val) {
      this.isFilterEmpty = val ? 1 : 0;
    },
    filterComponent(val) {
      let values = this.tabList.map((item) => ({
        name: item.value,
        component: item.value,
      }));
      return values.find((item) => item.name == val).component;
    },
    //是否展示分配表及上卡现金表标签栏
    getTab() {
      const { accountingMonth, factoryId } = this.parseRouteQueryData();
      this.$api.softwareWorkbench
        .showTabs({ factoryId, accountingMonth })
        .then(({ data }) => {
          let { bankCashFlag, allotFlag } = data;
          this.tabList = this.tabList
            .map((item) => {
              if (item.value == "Cash") {
                item.isShow = bankCashFlag;
              }
              if (item.value == "Allocation") {
                item.isShow = allotFlag;
              }
              return item;
            })
            .filter((item) => item.isShow);
        });
    },
    //获取待办任务详情
    getTaskDetail() {
      const { id } = this.parseRouteQueryData();
      this.$api.softwareWorkbench
        .taskDetail({ taskId: id })
        .then(({ data }) => {
          this.detailInfo = data || {};
        });
    },
    //提交
    handleSubmit() {
      const { id } = this.parseRouteQueryData();
      this.isLoading = true;
      this.$api.softwareWorkbench
        .nextNode({ taskId: id })
        .then(() => {
          this.$notify.success({
            title: "成功",
            message: "提交成功",
          });
          this.getTaskDetail();
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    //批量备注
    batchRemark() {
      this.$bus.$emit("softwareBatchRemarks");
    },
    //自定义列
    handleColumns() {
      this.$bus.$emit("softwareCustomColumns");
    },
    //打印
    printSettings() {
      this.$bus.$emit("softwarePrintSettings");
    },
    //编辑
    handleEdit() {
      if (this.activeName == "Financial") {
        this.$bus.$emit("softwareEditFinancial");
        return;
      }
      this.$bus.$emit("softwareEditSummary");
    },
    //导出
    handleExport() {
      const { factoryId, accountingMonth } = this.parseRouteQueryData();
      if (!factoryId || !accountingMonth) {
        console.warn("Missing required parameters in route query:", this.$route.query);
        return;
      }
      if (this.activeName == "Allocation") {
        this.$bus.$emit("softwareAllocationExport");
      } else {
        this.$api.softwareWorkbench
          .exportComon({
            factoryId,
            accountingMonth,
            ...this.$refs[this.activeName].filterParam,
            ...this.$refs[this.activeName].params,
            isFilterEmpty: this.isFilterEmpty,
          })
          .then((res) => {
            if (res.code == 200) {
              this.$message.success("导出操作成功，请前往导出记录查看详情");
            }
          });
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
