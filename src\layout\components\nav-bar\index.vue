<template>
  <div class="navbar">
    <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />
    <breadcrumb id="breadcrumb-container" class="breadcrumb-container" />

    <div class="right-menu">
      <el-button class="nav-btn" icon="el-icon-download" @click="handleExport">导出列表</el-button>
      <el-dropdown class="user-info-container" trigger="click" @command="handleCommand">
        <span>
          <span class="user">
            <i class="el-icon-user-solid"></i>
            {{name}}
          </span>
          <i class="el-icon-caret-bottom" />
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="logout">
            <font-icon icon="icon-tuichu"></font-icon>退出登录
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger'
import Cookies from 'js-cookie'
export default {
  components: {
    Breadcrumb,
    Hamburger
  },
  data() {
    return {
      role: '',
      roleOptions: []
    }
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'name'
    ])
  },
  mounted() {
    this.$store.dispatch('user/getUserInfo')
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    handleCommand(commond) {
      if (commond === 'logout') {
        this.logout();
      }
    },
    logout() {
      this.$confirm('确认要注销登录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('user/logout').then(() => {
          console.log('logout...');
          this.$router.replace({ path: '/login' })
        });
      });
    },
    handleExport() { 
      this.$router.push('/customized/file/export')
    }
  }
}
</script>
