<template>
  <qDialog
    :visible="visible"
    :innerScroll="true"
    :isAuto="true"
    title="处理"
    width="900px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <el-form :model="stagesForm" label-width="82px" size="small">
      <el-row>
        <el-col :span="24">
          <el-form-item label="是否核算:" prop="isAttendance">
            <el-radio v-model="stagesForm.isAttendance" label="1">是</el-radio>
            <el-radio v-model="stagesForm.isAttendance" label="2">否</el-radio>
          </el-form-item>
        </el-col>
      </el-row>
      <template v-if="stagesForm.isAttendance == 2">
        <el-row>
          <el-col :span="24">
            <el-form-item label="选择原因:">
              <el-select
                v-model="stagesForm.resaonType"
                filterable
                clearable
                placeholder="请选择原因"
              >
                <el-option
                  v-for="item in reasonList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="stagesForm.resaonType == 3">
          <el-col :span="24">
            <el-form-item label="其他说明:">
              <el-input
                type="textarea"
                placeholder="请输入内容"
                v-model="stagesForm.remark"
                maxlength="45"
                show-word-limit
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <template v-else>
        <el-row>
          <el-col :span="24">
            <el-form-item label="流程编号:">
              {{ stagesForm.code }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="员工姓名:">
              {{ stagesForm.borrowStaffName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="厂牌编号:">
              {{ stagesForm.borrowStaffCode }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="借支类型:">
              {{ stagesForm.type }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请金额:">
              {{ stagesForm.processAmount | moneyFormat }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="审定金额:">
              {{ stagesForm.realAmount | moneyFormat }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实付金额:">
              <span v-if="!isShowRealAmount">{{
                stagesForm.actualAmount | moneyFormat
              }}</span>
              <el-input
                v-else
                class="periodAmount"
                v-model.trim="stagesForm.actualAmount"
                oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
                placeholder="请输入实付金额"
                clearable
                @blur="onRealAmount"
              >
              </el-input>
              <el-button type="text" @click="handleEdit">
                {{ isShowRealAmount ? "保存" : "修改" }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="已还款:">
              {{ stagesForm.payedAmount | moneyFormat }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="待还款:">
              {{ stagesForm.payingAmount | moneyFormat }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item v-if="repaidList.length > 0" label="已扣款:">
              <ul class="deduction">
                <li v-for="item in repaidList" :key="item.id">
                  <span>{{ item.repaymentTime }}</span>
                  <span class="item">{{ item.factoryName }}</span>
                  <span class="item">{{
                    item.periodAmount | moneyFormat
                  }}</span>
                  <span class="item">{{ item.isPay }}</span>
                </li>
              </ul>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="扣款详情:">
              <span style="color: #afafaf; line-height: 40px">
                <span style="padding-right: 133px">实际核算工厂</span>
                <span style="padding-right: 130px">实际执行年月</span>
                <span>实际还款金额</span>
              </span>
              <div
                class="details"
                v-for="(item, index) in detailList"
                :key="item.date"
              >
                <div class="details_content">
                  <el-select
                    v-model="item.factoryId"
                    placeholder="请选择工厂名称"
                    filterable
                  >
                    <el-option
                      v-for="item in tabList"
                      :key="item.name"
                      :label="item.label"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                  <el-select
                    style="margin: 0 15px"
                    v-model="item.startDate"
                    placeholder="请选择月份"
                    filterable
                    @change="seleteDate(item.startDate)"
                    @visible-change="visibleChange"
                  >
                    <el-option
                      v-for="it in dateList"
                      :key="it.id"
                      :label="it.date"
                      :value="it.date"
                      :disabled="it.disabled"
                    >
                    </el-option>
                  </el-select>
                  <span v-if="index == 0">{{ item.periodAmount }}</span>
                  <el-input
                    v-else
                    class="periodAmount"
                    v-model.trim="item.periodAmount"
                    clearable
                    placeholder="请输入金额"
                    oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
                    @blur="onBlur(index, item)"
                  >
                  </el-input>
                </div>
                <div class="details_btn">
                  <el-button
                    type="text"
                    @click="stages(index)"
                    v-if="stagesForm.actualAmount"
                  >
                    分期
                  </el-button>
                  <el-button
                    v-show="detailList.length > 1 && index != 0"
                    type="text"
                    @click="handleDelete(item, index)"
                  >
                    删除
                  </el-button>
                </div>
              </div>
              <!-- 未还款 -->
              <div v-if="unpaidDetails.length > 0">
                <div
                  v-for="(item, index) in unpaidDetails"
                  :key="item.startDate"
                  class="details"
                >
                  <div class="details_content">
                    <el-select
                      v-model="item.factoryId"
                      placeholder="请选择工厂名称"
                      filterable
                    >
                      <el-option
                        v-for="item in tabList"
                        :key="item.name"
                        :label="item.label"
                        :value="item.id"
                      >
                      </el-option>
                    </el-select>
                    <el-select
                      style="margin: 0 15px"
                      v-model="item.startDate"
                      placeholder="请选择月份"
                      filterable
                      @visible-change="visibleChange"
                      @change="changSelect(index, item.startDate)"
                    >
                      <el-option
                        v-for="it in dateList"
                        :key="it.id"
                        :label="it.date"
                        :value="it.date"
                        :disabled="it.disabled"
                      >
                      </el-option>
                    </el-select>
                    <el-input
                      v-model.trim="item.periodAmount"
                      clearable
                      placeholder="请输入金额"
                      oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
                      @blur="onUnpaidBlur(index, item)"
                    >
                    </el-input>
                  </div>
                  <div class="details_btn">
                    <span style="padding-left: 15px">未还款</span>
                  </div>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注说明:">
              <el-input
                v-model.trim="stagesForm.comments"
                type="textarea"
                maxlength="300"
                show-word-limit
                resize="none"
                :rows="3"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
    </el-form>
  </qDialog>
</template>

<script>
import { moneyFormat, moneyDelete } from "@/utils";
import moment from "moment";
import NP from "number-precision";
NP.enableBoundaryChecking(false);
export default {
  name: "stageDialog",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    formData: Object,
  },
  data() {
    return {
      stagesForm: {
        code: "",
        factoryId: "",
        borrowStaffName: "",
        borrowStaffCode: "",
        type: "",
        processAmount: "",
        realAmount: "",
        actualAmount: "",
        payedAmount: "",
        payingAmount: "",
        comments: "",
        isAttendance: "",
        remark: "",
      },
      reasonList: [
        { label: "无需核算", value: "无需核算" },
        // { label: "人力资源部", value: "人力资源部" },
        { label: "其他原因 ", value: "3" },
      ],
      isShowRealAmount: false,
      isBlur: false,
      isConfirm: false,
      num: 1,
      actualAmount: "",
      dateList: [],
      detailsList: [],
      unpaidDetails: [],
      tabList: [],
      detailList: [],
      isLoading: false,
    };
  },
  async created() {
    this.dateInit();
    await this.getFactory();
    await this.getDetailed();
    this.unpaidDetails = JSON.parse(JSON.stringify(this.unpaidList));
    this.init();
  },
  computed: {
    //已还款
    repaidList() {
      return this.detailsList.filter((item) => item.isPay == "已还款");
    },
    //未还款
    unpaidList() {
      return this.detailsList
        .filter((item) => item.isPay == "未还款")
        .map((item) => ({
          startDate: item.repaymentTime,
          periodAmount: moneyFormat(item.periodAmount),
          factoryId: item.factoryId,
        }));
    },
  },
  methods: {
    //获取工厂
    getFactory() {
      return this.$api.plateTypeSystemManage.getBasicPermission
        .getQuFactory({ moduleId: 3 })
        .then((res) => {
          this.tabList = res.data.map((item) => ({
            label: item.name,
            name: item.name,
            id: item.id,
          }));
        });
    },
    //获取借支详情
    getDetailed() {
      return this.$api.plateTypeInformation.debitAccount
        .debitAccountDetails({ id: this.formData.id })
        .then(({ data }) => {
          data.isAttendance = data.isAttendance ? data.isAttendance : "1"; //默认是
          this.stagesForm = { ...data };
          this.actualAmount = data.actualAmount || "";
          this.detailsList = data.repayments || [];
        });
    },
    //选择月份
    seleteDate(value) {
      this.dateList.forEach((ele) => {
        if (ele.date == value) {
          ele.disabled = true;
        }
      });
    },
    //初始月份
    dateInit() {
      const startDate = moment(moment().subtract(1, "month"));
      const endDate = moment(startDate).add(6, "month");
      const allYearMonth = []; // 接收所有年份和月份的数组
      while (
        endDate > startDate ||
        startDate.format("M") === endDate.format("M")
      ) {
        allYearMonth.push(startDate.format("YYYY-MM"));
        startDate.add(1, "month");
      }
      this.dateList = allYearMonth.map((item, index) => ({
        id: "key_" + (index + 1),
        date: item,
        disabled: false,
      }));
    },
    //数据初始化
    init() {
      let value = "";
      this.payingAmount = this.stagesForm.payingAmount;
      if (this.unpaidDetails.length > 0) {
        value = this.unpaidDetails.reduce(
          (pre, cur) => NP.plus(pre, Number(moneyDelete(cur.periodAmount))),
          0
        );
        this.payingAmount = NP.minus(
          (Number(moneyDelete(this.stagesForm.payingAmount)), value)
        );
        let date = this.unpaidDetails.map((item) => item.date);
        this.dateList = this.dateList.map((ele) => {
          if (date.includes(ele.date)) {
            ele.disabled = true;
          }
          return ele;
        });
      }
      this.initValue = this.filterMonth();
      this.dateList = this.dateList.filter(
        (item) =>
          moment(item.date).format("x") >= moment(this.initValue).format("x")
      );
      this.dateList = this.dateList.map((ele) => {
        if (ele.date == this.initValue) {
          ele.disabled = true;
        }
        return ele;
      });
      this.detailList = new Array(1).fill().map(() => {
        return {
          startDate: this.initValue,
          periodAmount: moneyFormat(this.payingAmount),
          factoryId: this.stagesForm.factoryId,
        };
      });
    },
    filterMonth() {
      let list = this.dateList
        .filter((item) => !item.disabled)
        .map((item) => item.date);
      let month = "";
      let lastMonth = moment()
        .month(moment().month() - 1)
        .startOf("month")
        .format("YYYY-MM");
      if (!list.includes(lastMonth)) {
        let nextMonth = moment().format("YYYY-MM");
        month = this._filterMonth(list, nextMonth);
      } else {
        month = lastMonth;
      }
      return month;
    },
    _filterMonth(list, date) {
      if (!list.includes(date)) {
        this.nextMonth = moment()
          .month(moment(date).month() + 1)
          .startOf("month")
          .format("YYYY-MM");
        this._filterMonth(list, this.nextMonth);
      } else {
        this.nextMonth = date;
      }
      return this.nextMonth;
    },
    //月份初始化
    initDate() {
      this.dateList.forEach((ele) => {
        ele.disabled = false;
      });
    },
    visibleChange(value) {
      if (value) {
        this.filterDate();
      }
    },
    filterDate() {
      let date = this.unpaidDetails
        .map((item) => item.startDate)
        .concat(this.detailList.map((item) => item.startDate));
      let list = this.dateList.map((ele) => {
        if (date.includes(ele.date)) {
          ele.disabled = true;
        } else {
          ele.disabled = false;
        }
        return ele;
      });
      return list;
    },
    //校验金额
    checkAmount(value, name = "") {
      let flag = false;
      let amount = moneyDelete(value);
      if (!amount) {
        this.$message({
          message: "金额不能为空",
          type: "warning",
        });
        flag = true;
      }
      if (!flag) {
        if (!/^-?\d{1,5}(\.\d{1,2})?$/.test(amount)) {
          this.$message({
            message: "小数点前面仅支持5位数,小数点后面仅支持2位数",
            type: "warning",
          });
          flag = true;
          if (name) this.stagesForm[name] = moneyFormat(this.actualAmount);
        }
      }
      return flag;
    },
    onRealAmount() {
      if (this.checkAmount(this.stagesForm.actualAmount, "actualAmount"))
        return;
      this.stagesForm.actualAmount = moneyFormat(
        moneyDelete(this.stagesForm.actualAmount)
      );
    },
    //编辑实付金额
    handleEdit() {
      this.isShowRealAmount = !this.isShowRealAmount;
      this.stagesForm.actualAmount = moneyDelete(this.stagesForm.actualAmount);
      if (this.isShowRealAmount) {
        this.stagesForm.actualAmount = moneyFormat(
          this.stagesForm.actualAmount
        );
        return;
      }
      if (this.stagesForm.actualAmount < Number(this.stagesForm.payedAmount)) {
        this.$notify.error({
          title: "错误",
          message: "实发金额小于已还款金额",
        });
        this.stagesForm.actualAmount = this.actualAmount;
        return;
      }
      this.$api.plateTypeInformation.debitAccount
        .updateActualAmount({
          id: this.formData.id,
          processCode: this.stagesForm.code,
          actualAmount: this.stagesForm.actualAmount,
        })
        .then(async ({ success }) => {
          if (success) {
            this.$notify({
              title: "成功",
              message: "实发金额修改成功",
              type: "success",
            });
            await this.getDetailed({ id: this.formData.id });
            this.initDate();
            this.init();
            this.num = 1;
          }
        });
    },
    //分期
    stages(value) {
      if (this.detailList.length > 5) {
        this.$message.error("最多只能分6期");
        return;
      }
      this.num++;
      const nextMonthItem = this.dateList.find((item) => !item.disabled);
      this.detailList.push({
        startDate: nextMonthItem.date,
        periodAmount: this.toFixedTwo(this.payingAmount / this.num),
        factoryId: this.stagesForm.factoryId,
      });
      nextMonthItem.disabled = true;
      let detailList = this.detailList.map((item, index) => {
        return {
          ...item,
          periodAmount: this.toFixedTwo(this.payingAmount / this.num),
        };
      });
      this.detailList = detailList.map((item, index) => {
        if (Number(moneyDelete(this.payingAmount)) % this.num != 0) {
          if (index === 0) {
            let count = 0;
            for (let i = 1; i < detailList.length; i++) {
              count += Number(detailList[i].periodAmount);
            }
            item.periodAmount = this.returnFloat(
              NP.minus(this.payingAmount, count)
            );
          }
        }
        return item;
      });

      this.isBlur = false;
    },
    toFixedTwo(num) {
      if (typeof num !== "number" || Number.isNaN(num)) return num;
      let resultNum = Math.floor(num * 100) / 100;
      return this.returnFloat(resultNum);
    },
    returnFloat(num) {
      num = num.toString(); // 转成字符串类型  如能确定类型 这步可省去
      if (num.indexOf(".") !== -1) {
        let [integerPart, decimalPart] = num.split(".");

        if (decimalPart.length > 2) {
          decimalPart = decimalPart.substring(0, 2);
        } else if (decimalPart.length === 1) {
          decimalPart += "0";
        }

        num = `${integerPart}.${decimalPart}`;
      } else {
        num += ".00";
      }
      return num;
    },
    //统计
    total(arr) {
      let total = arr.reduce(function (pre, cur, index, arr) {
        if (index === 0) {
          return pre + 0;
        }
        return NP.plus(pre, Number(moneyDelete(cur.periodAmount)));
      }, 0);
      return total;
    },
    //删除
    handleDelete(v, value) {
      this.num--;
      this.dateList = this.dateList.map((ele) => {
        if (v.date === ele.date) {
          ele.disabled = false;
        }
        return ele;
      });
      this.detailList.splice(value, 1);
      if (this.detailList.length > 0) {
        let detailList = this.detailList.map((item, index) => {
          return {
            ...item,
            periodAmount: this.toFixedTwo(this.payingAmount / this.num),
          };
        });
        this.detailList = detailList.map((item, index) => {
          if (Number(moneyDelete(this.payingAmount)) % this.num != 0) {
            if (index === 0) {
              let count = 0;
              for (let i = 1; i < detailList.length; i++) {
                count += Number(detailList[i].periodAmount);
              }
              item.periodAmount = this.returnFloat(
                NP.minus(this.payingAmount, count)
              );
            }
          }
          return item;
        });
      }
      if (this.detailList.length == 1) {
        this.num = 1;
      }
      let startDate = this.detailList.map((item) => item.startDate);
      this.dateList = this.dateList.map((ele) => {
        if (startDate.includes(ele.date)) {
          ele.disabled = true;
        } else {
          ele.disabled = false;
        }
        return ele;
      });
    },
    onBlur(num, it) {
      this.isBlur = true;
      this.isConfirm = false;
      if (this.checkAmount(it.periodAmount)) return;
      let detailList = this.detailList.map((item, index) => {
        item.periodAmount = moneyDelete(item.periodAmount);
        if (num === index) {
          item.periodAmount = moneyFormat(item.periodAmount);
        }
        return item;
      });
      let total = detailList.reduce(function (pre, cur, index, arr) {
        if (index === 0) {
          return pre + 0;
        }
        return NP.plus(pre, Number(moneyDelete(cur.periodAmount)));
      }, 0);
      this.detailList = this.detailList.map((item, index) => {
        if (index == 0) {
          let periodAmount = (item.periodAmount = moneyFormat(
            NP.minus(moneyDelete(this.stagesForm.payingAmount), total)
          ));
          if (Number(moneyDelete(periodAmount)) < 0) {
            this.$message.error("操作错误,扣款金额配置异常,请核查后重试!");
            this.isConfirm = false;
          } else {
            this.isConfirm = true;
          }
        }
        return {
          ...item,
          periodAmount: moneyFormat(moneyDelete(item.periodAmount)),
        };
      });
    },
    onUnpaidBlur(num, it) {
      this.checkAmount(it.periodAmount);
      this.unpaidDetails = this.unpaidDetails.map((item, index) => {
        if (num === index) {
          item.periodAmount = moneyFormat(moneyDelete(item.periodAmount));
        }
        return item;
      });
      if (this.detailList.length == 1) {
        this.initDate();
        this.init();
      } else {
        let value = this.unpaidDetails.reduce(
          (pre, cur) => NP.plus(pre, Number(moneyDelete(cur.periodAmount))),
          0
        );
        this.payingAmount = NP.minus(
          Number(moneyDelete(this.stagesForm.payingAmount)),
          value
        );
        let detailList = this.detailList.map((item, index) => {
          return {
            ...item,
            periodAmount: moneyFormat(
              NP.divide(
                Number(moneyDelete(this.payingAmount)),
                this.num
              ).toFixed(2)
            ),
          };
        });
        this.detailList = detailList.map((item, index) => {
          if (Number(moneyDelete(this.payingAmount)) % this.num != 0) {
            if (index === 0) {
              item.periodAmount = moneyFormat(
                NP.minus(
                  Number(moneyDelete(this.payingAmount)),
                  this.total(detailList)
                )
              );
            }
          }
          return item;
        });
      }
    },
    handleCancel(type) {
      this.$emit("cancel", "confirm");
    },
    handleConfirm() {
      if (this.stagesForm.isAttendance == 2) {//无需分期
        const { resaonType, remark } = this.stagesForm;
        if(!resaonType){
          this.$message.error("请选择原因");
          return;
        }
        if(resaonType == 3 && !remark){
          this.$message.error("请填写其他原因");
          return;
        }
        let params = {
          id: this.formData.id,
          remark: resaonType == 3 ? '其他原因:' + remark : resaonType,
        };
        this.isLoading = true;
        this.$api.plateTypeInformation.debitAccount
          .noAccounting(params)
          .then(() => {
            this.$notify.success({
              title: "成功",
              message: "操作成功！",
            });
            this.$emit("cancel", "confirm");
          })
          .finally(() => {
            this.isLoading = false;
          });
        return;
      }
      if (!this.isConfirm && this.isBlur) {
        this.$message.error("操作错误,扣款金额配置异常,请核查后重试!");
        return;
      }
      if (moneyDelete(this.stagesForm.payingAmount) < 0) {
        this.$message.error("操作错误,代还金额为负,请核查后重试!");
        return;
      }
      let values = [];
      let unpaidDetails = this.unpaidDetails.map((item) => ({
        startDate: item.startDate,
        factoryId: item.factoryId,
        periodAmount: moneyDelete(item.periodAmount),
      }));
      let obj = {
        id: this.formData.id,
        periodRepayments: this.detailList
          .map((item) => ({
            startDate: item.startDate,
            factoryId: item.factoryId,
            periodAmount: moneyDelete(item.periodAmount),
          }))
          .concat(unpaidDetails ? unpaidDetails : []),
        periods: this.num,
        comments: this.stagesForm.comments,
      };
      obj.periodRepayments.forEach((item) => {
        for (const key in item) {
          values.push(item[key]);
        }
      });
      if (!Object.values(values).every((item) => item)) {
        this.$message.error("扣款月份或工厂或扣款金额不能为空");
        return;
      }
      if (
        obj.periodRepayments.some(
          (item) => item.periodAmount && item.periodAmount.includes("-")
        )
      ) {
        this.$message.error("操作错误,扣款金额配置异常,请核查后重试!");
        return;
      }
      this.isLoading = true;
      this.$api.plateTypeInformation.debitAccount
        .amortizationLoan(JSON.stringify(obj))
        .then(() => {
          this.$notify.success({
            title: "成功",
            message: "分期成功！",
          });
          this.$emit("cancel", "confirm");
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
  },
};
</script>

<style lang="stylus" scoped>
.periodAmount {
  width: 60%;

  .el-icon-circle-close::before {
    margin-left: 10px !important;
  }
}

ul {
  display: flex;
  margin: 0;
  padding: 0;
}

.deduction {
  li {
    display: flex;

    .item {
      margin-left: 10px;
    }
  }
}

.details {
  display: flex;
  padding-bottom: 18px;

  &_content {
    flex: 1;
    display: flex;
    justify-content: space-between;

    >.el-select, >.el-input, >span {
      flex: 1 0 30%;
    }
  }

  &_btn {
    width: 20%;

    >.el-button {
      mar;
    }
  }
}

>>>.el-textarea__inner {
  padding-right: 55px;
}

.el-textarea {
  >>>.el-input__count {
    right: 20px !important;
  }
}
</style>
