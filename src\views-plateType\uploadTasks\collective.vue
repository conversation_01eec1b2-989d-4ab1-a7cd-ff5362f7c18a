<template>
  <content-panel>
    <template v-slot:search>
      <search-box
        class="search-box">
        <template
          v-slot:right>
          <el-button
            size="small"
            type="primary"

            @click="onSearch">
            查询</el-button>
          <el-button
            size="small"
            type="warning"
            @click="resetSearchForm">
            重置</el-button>
        </template>
      </search-box>
    </template>
    <table-panel
      ref="tablePanel">
      <el-table stripe border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :height="maxTableHeight"
        :data="tableData">
        <el-table-column
          width="50"
          align="left"
          type="index">
        </el-table-column>
        <el-table-column
          prop="factoryName"
          label="核算工厂"
          width="150"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="accountingMonth"
          label="核算月份"
          width="150"
          align="left">
        </el-table-column>
        <el-table-column
          prop="amount"
          label="集体账户"
          width="200"
          align="left">
          <template
            slot-scope="scope">
            {{ scope.row.amount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          prop="comments"
          label="备注说明"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="updateUser"
          label="修改人员"
          width="150"
          align="left">
        </el-table-column>
        <el-table-column
          prop="updateTime"
          label="修改时间"
          width="200"
          align="left">
          <template
            slot-scope="scope">
            {{ scope.row.updateTime | dateFormat }}
          </template>
        </el-table-column>
        <el-table-column
          align="left"
          width="200"
          v-if="permission">
          <template
            slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="handleAccount(scope.$index, scope.row)">
              调账</el-button>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div
          style="text-align: right">
          <el-pagination
            @size-change="onSizeChange"
            @current-change="onNumChange"
            :current-page="pageNum"
            :page-size="pageSize"
            :total="total"
            :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>
        </div>
      </template>
    </table-panel>
    <qDialog
      :visible="visible"
      :innerScroll="false"
      title="调账" width="30%"
      @cancel="handleCancel"
      @confirm="handleConfirm"
      :before-close="handleCancel">
      <template>
        <el-form size="mini"
          :model="accountForm"
          class="account"
          :rules="rules">
          <el-form-item
            label="核算工厂:">{{
            accountForm.factoryName
          }}</el-form-item>
          <el-form-item
            label="核算月份:">{{
            accountForm.accountingMonth
          }}</el-form-item>
          <el-form-item
            label="集体账户"
            prop="amount">
            <el-input
              v-model.trim="accountForm.amount"
              clearable>
            </el-input>
          </el-form-item>
          <el-form-item
            label="备注说明">
            <el-input
              oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
              v-model.trim="accountForm.comments"
              type="textarea"
              resize="none"
              rows="3">
            </el-input>
          </el-form-item>
        </el-form>
        <div></div>
      </template>
    </qDialog>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
export default {
  name: "PlateTypeCollective",
  mixins: [tableMixin,pagePathMixin],
  data() {
    let amount = (rule, value, callback) => {
      if (!/^(0|[0-9]{1,5})((\.\d{0,2})*)$/.test(value)) {
        return callback(
          new Error("小数点前面仅支持5位数,小数点后面仅支持2位数")
        );
      }
      callback();
    };
    return {
      rules: {
        amount: [
          {
            validator: amount,
            trigger: "blur",
          },
        ],
      },
      searchForm: {
        accountingMonth: "",
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      accountForm: {
        factoryName: "",
        accountingMonth: "",
        amount: "",
        comments: "",
      },
      tableData: [],
      filterParam: {},
      loading: true,
      factoryId: "",
      resizeOffset: 55,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      visible: false,
      collectiveAccountId: "",
      permission: "",
      info:{}
    };
  },
  created() { },
  mounted() { },
  computed: {
    label() {
      return this.tabList.find((item) => item.name == this.activeName).label;
    },
  },

  watch: {
    $route: {
      handler(value) {
        if (value.path.includes("collective")&&value.path.includes("plateType")) {
          this.info=JSON.parse(this.$Base64.decode(value.query.data))
          this.factoryId = this.info.factoryId;
          this.getList();
          this.getPermission();
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    //获取集体账户列表
    getList() {
      this.loading = true;
      this.$api.plateTypeDataUpload.collectiveAccount
        .getCollectiveAccountList({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: {
            factoryId: this.factoryId,
            ...this.filterParam,
            accountingMonth: this.info.accountingMonth,
          },
        })
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.list;
            this.total = res.data.total;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //是否具有待办任务操作权限
    getPermission() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.info.accountingMonth,
        type: "3",
      };
      this.$api.plateTypeWorkbench.agencyPermission(params).then((res) => {
        this.permission = res.data;
      });
    },
    //获取下标总记录
    // getStatistic() {
    //   let info = JSON.parse(sessionStorage.getItem('info'))
    //   this.$api.plateTypeDataUpload.collectiveAccount.getCollectiveAccount({
    //     factoryId: this.factoryId,
    //     month: this.searchForm.accountingMonth || info.accountingMonth
    //   }).then(res => {
    //     this.wageStatisticInfo = res.data
    //   })
    // },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.filterParam = {};
      this.onSearch();
    },
    //搜索
    onSearch() {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      this.pageNum = 1;
      this.getList();
      this.getPermission();
    },
    handleCancel() {
      this.visible = false;
    },
    handleConfirm() {
      this.visible = false;
      this.$api.plateTypeDataUpload.collectiveAccount
        .accountAdjustment({
          id: this.collectiveAccountId,
          adjustAmount: this.accountForm.amount,
          remark: this.accountForm.comments,
        })
        .then((res) => {
          this.$message({
            message: "调账成功",
            type: "success",
          });
          this.getList();
        });
    },
    //调账
    handleAccount(index, row) {
      this.visible = true;
      this.collectiveAccountId = row.id;
      this.accountForm = { ...row };
    },
    onSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
  },
};
</script>

<style lang="stylus" scoped>
>>>.el-tabs__nav-wrap::after {
  height: 0;
}

ul {
  list-style: none;
  display: flex;
  padding: 0;
}

i {
  font-style: normal;
}

.account {
  .el-form-item {
    display: flex;

    >>>.el-form-item__content {
      margin-left: 0 !important;
      flex: 1;
    }
  }
}
</style>

