<template>
  <!-- 周待办任务  查询任务-->
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form size="mini" :inline="true" :model="searchForm" ref="searchForm" label-position="right">
          <el-form-item label="待办任务:" prop="content">
            <el-input v-model.trim="searchForm.content" size="mini" clearable placeholder="请输入待办任务"
              @keyup.enter.native="onSearch">
            </el-input>
          </el-form-item>
          <el-form-item label="任务状态:" prop="status">
            <el-select @change="onSearch" v-model="searchForm.status" filterable clearable placeholder="请选择状态">
              <el-option v-for="item in statusOptions" :key="item.id" :label="item.label" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <!-- 采集类型 -->
          <el-form-item label="采集类型:" prop="collectType">
            <el-select @change="onSearch" v-model="searchForm.collectType" filterable clearable placeholder="请选择采集类型">
              <el-option v-for="item in collectTypeList" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary" @click="onSearch">
            查询</el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置</el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <el-table stripe border v-loading="loading" ref="tableRef" highlight-current-row :height="maxTableHeight"
        style="width: 100%" :data="tableData">
        <el-table-column v-for="item in columnList" :key="item.name" :label="item.label" :width="item.width"
          align="left" show-overflow-tooltip>
          <template slot-scope="scope">
            <template v-if="item.type">
              <template v-if="activeTab == 0">
                <el-button v-permission="'was-customized$pieceworkWage$plateType$weekTodoTasks$return'
                  " type="text" size="small" v-show="scope.row.status == 3 && scope.row.type == 1
                    " @click="handleBack(scope.row)" style="margin: 0">
                  退回</el-button>
                <el-button v-permission="'was-customized$pieceworkWage$plateType$weekTodoTasks$submit'" type="text"
                  size="small" v-show="scope.row.status == 1 && scope.row.type == 1
                    " @click="handleSubmit(scope.row)" style="margin: 0">
                  提交</el-button>
              </template>
              <el-button type="text" size="small" v-show="scope.row.visible == 'Y' &&
                (scope.row.status == 2 || filterType(scope.row.type))
                " @click="handleDetails(scope.row)" style="margin: 0">
                查看详情
              </el-button>
            </template>
            <span v-else-if="item.name == 'accountingWeek'">{{
              '第' + scope.row[item.name] + '周'
            }}</span>
            <span v-else-if="item.name == 'status'">{{
              scope.row[item.name] === 1 ? "待提交" : scope.row[item.name] === 3 ? "已完成" : '未知状态'
            }}</span>
            <span v-else-if="!item.type && item.name == 'collectType'">{{
              scope.row[item.name] == 0 ? "手动录入" : "自动获取"
            }}</span>
            <span v-else-if="!item.type && item.name != 'updateTime'">{{
              scope.row[item.name]
            }}</span>
            <span v-else>{{ scope.row.updateTime | dateFormat }}</span>
          </template>
        </el-table-column>
      </el-table>
    </table-panel>
    <qDialog :visible="visible" :innerScroll="false" :title="title" :isLoading="isLoading" width="30%"
      @confirm="handleConfirm" @cancel="handleCancel" :before-close="handleCancel">
      <template>
        <p v-show="title == '确认提交'">
          员工考勤提交后若需修改,需退回后才可修改，是否提交？
        </p>
        <p v-show="title == '确认退回'">
          员工考勤退回，将清空该任务下计件工资中全部数据，并清除工会费中该分厂当月数据，是否提交？
        </p>
      </template>
    </qDialog>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";

export default {
  name: "PlateTypeWeeklyTaskList",
  mixins: [tableMixin, pagePathMixin],
  data() {
    return {
      searchForm: {
        status: "",
        roleId: "",
        content: "",
        collectType: "",
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      //角色列表
      roleOptions: [],
      //状态列表
      statusOptions: [
        // 0 未上传 1 待提交 2 待审核 3 已完成
        // { label: "未上传", id: "0" },
        // （0 未上传 1 待提交 2 已提交 ）
        { label: "待提交", id: "1" },
        { label: "已完成", id: "3" },
        // { label: "已完成", id: "3" },
      ],
      //采集类型
      collectTypeList: [
        { label: "全部", value: "" },
        { label: "手动录入", value: "0" },
        { label: "自动获取", value: "1" },
      ],
      //表格头部信息
      columnList: Object.freeze([
        {
          label: "核算工厂",
          width: "150",
          name: "factoryName",
        },
        {
          label: "核算月份",
          width: "150",
          name: "accountingMonth",
        },
        {
          label: "核算周期",
          width: "150",
          name: "accountingWeek",
        },
        {
          label: "采集类型",
          width: "150",
          name: "collectType",
        },
        {
          label: "待办任务",
          name: "name",
        },
        {
          label: "任务状态",
          width: "150",
          name: "status",
        },
        {
          label: "操作",
          width: "200",
          name: "",
          type: "operation",
        },
      ]),
      info: {},
      tableData: [],
      factoryId: "",
      resizeOffset: 12,
      loading: false,
      visible: false,
      title: "",
      activeTab: '',
      filterParam: {},
      isLoading: false,
      routeInfo: Object.freeze([
        {
          type: "1",
          path: "/plateType/pieceworkWage/weeklyAttendance",
        }
      ]),
    };
  },
  created() {
    this.$api.roleInfo.getRoleInfoAll({ moduleId: 3 }).then((res) => {
      this.roleOptions = res.data || [];
    });
  },
  watch: {
    $route: {
      handler(value) {
        if (
          value.path.includes("weeklyTaskList") &&
          value.path.includes("plateType")
        ) {
          this.factoryId = JSON.parse(this.$Base64.decode(value.query.data) || '{}').factoryId;
          this.activeTab = JSON.parse(this.$Base64.decode(value.query.data) || '{}').activeTab;
          this.getList();
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    //获取待办任务列表
    getList() {
      this.loading = true;
      this.$api.plateTypePieceWageSystem.weekTodoTasks
        .getWeekTodoTasksDetailList({
          factoryId: this.factoryId,
          ...this.filterParam,
          accountingMonth: JSON.parse(this.$Base64.decode(this.$route.query.data)).accountingMonth,
          accountingWeek: JSON.parse(this.$Base64.decode(this.$route.query.data)).accountingWeek
        })
        .then(({ data }) => {
          this.tableData =
            (data &&
              data.map((item) => ({
                ...item,
                disabled: false,
              }))) ||
            [];
        })
        .finally(() => {
          this.loading = false;
        });
    },
    filterStatus(val) {
      return (
        (val && this.statusOptions.find((item) => item.id == val).label) || ""
      );
    },
    filterPath(type) {
      return (
        (type && this.routeInfo.find((item) => item.type == type).path) || ""
      );
    },
    filterType(type) {
      return (
        (type &&
          this.routeInfo.map((item) => item.type).includes(String(type))) ||
        ""
      );
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.filterParam = {};
      this.onSearch();
    },
    //搜索
    onSearch() {
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      this.pageNum = 1;
      this.getList();
    },
    //提交
    handleSubmit(row) {
      if (row.type == 1) {
        this.isLoading = false;
        this.title = "确认提交";
        this.visible = true;
        this.info = {
          ...row,
        };
      } else {
        row.disabled = true;
        this.$api.plateTypePieceWageSystem.weekTodoTasks
          .submitWeekTodoTask({ id: row.id })
          .then((res) => {
            this.$message({
              message: "提交成功",
              type: "success",
            });
            this.getList();
          })
          .finally(() => {
            row.disabled = false;
          });
      }
    },
    handleCancel() {
      this.visible = false;
    },
    //退回
    handleBack(row) {
      if (row.type == 1) {
        this.isLoading = false;
        this.title = "确认退回";
        this.visible = true;
        this.info = {
          ...row,
        };
      } else {
        row.disabled = true;
        this.$api.plateTypePieceWageSystem.weekTodoTasks
          .returnWeekTodoTask({ id: row.id, status: "1" })
          .then((res) => {
            this.$message({
              message: "退回成功",
              type: "success",
            });
            this.getList();
          })
          .finally(() => {
            row.disabled = false;
          });
      }
    },
    //查看详情
    handleDetails(row) {
      const { id, factoryId, factoryName, accountingWeek, accountingMonth, roleName, status } = row;
      let params = {};
      let obj = {
        id,
        factoryId,
        factoryName,
        accountingMonth,
        roleName,
        status,
        accountingWeek
      };
      params =
        row.type == "6" || row.type == "7"
          ? { ...obj, otherId: row.otherId, planName: row.name }
          : obj;
      this.openSubPage({
        path: this.filterPath(row.type),
        query: { data: this.$Base64.encode(JSON.stringify(params)) },
      });
    },
    handleConfirm() {
      switch (this.title) {
        case "确认提交":
          this.isLoading = true;
          this.$api.plateTypePieceWageSystem.weekTodoTasks
            .submitWeekTodoTask({ id: this.info.id })
            .then((res) => {
              this.$message({
                message: "提交成功",
                type: "success",
              });
              this.getList();
            })
            .finally(() => {
              this.visible = false;
              this.isLoading = false;
            });
          break;
        case "确认退回":
          this.isLoading = true;
          this.$api.plateTypePieceWageSystem.weekTodoTasks
            .returnWeekTodoTask({ id: this.info.id })
            .then((res) => {
              this.$message({
                message: "退回成功",
                type: "success",
              });
              this.getList();
            })
            .finally(() => {
              this.visible = false;
              this.isLoading = false;
            });
      }
    },
  },
};
</script>

<style lang="stylus" scoped>
>>>.el-tabs__nav-wrap::after {
  display: block;
  height: 0;
}
</style>
