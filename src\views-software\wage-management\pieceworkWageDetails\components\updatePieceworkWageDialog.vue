<template>
  <qDialog innerScroll :visible="visible" :innerScroll="true" :innerHeight="height" :title="title" width="500px"
    @cancel="editCancel" @confirm="handleConfirm" :isLoading="isLoading" :before-close="editCancel">
    <div class="wage-details">
      <!-- 计件工资详情 -->
      <div class="info-section">
        <div class="title">选择要刷新的工厂和月份</div>
        <el-form :model="addForm" :rules="rules" ref="addForm" size="small">
          <el-row>
            <el-col :span="20">
              <el-form-item label-width="170px" label="工厂" prop="factoryIdList">
                <el-select multiple collapse-tags v-model="addForm.factoryIdList" placeholder="请选择工厂" clearable
                  @change="handleFactoryChange">
                  <el-option v-for="item in factoryList" :key="item.id" :label="item.name" :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>

            </el-col>
          </el-row>
          <el-row>
            <el-col :span="20">
              <el-form-item label-width="170px" label="核算月份:" prop="accountingMonth">
                <el-date-picker :picker-options="pickerOptions" value-format="yyyy-MM" v-model="addForm.accountingMonth"
                  type="months" placeholder="请选择日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>



        <!-- 计算公式说明 -->
        <div class="formula-section">
          <p>说明：刷新计件的待办任务员工考勤在已完成状态（待办任务已提交一审），则不允许操作，需要将流程退回后再操作。</p>

        </div>
      </div>
    </div>
  </qDialog>
</template>

<script>
export default {
  name: "detailsPopup",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    height: {
      type: Number,
      default: 205,
    },
    factoryList: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    filteredGroups() {
      return (this.details.groups || []).filter(item => item.mesPiece !== 0);
    }
  },
  data() {
    return {
      title: '刷新',
      value4: '',
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      isLoading: false,
      addForm: {
        factoryId: [],
        factoryIdList: [],
        accountingMonth: '',
      },
      rules: {
        factoryIdList: [
          { required: true, message: '请选择工厂', trigger: 'change' },
        ],
        accountingMonth: [
          { required: true, message: '请选择月份', trigger: 'change' },
        ],
      },
    };
  },
  methods: {
    handleFactoryChange(selectedIds) {
      this.addForm.factoryId = selectedIds.map(id => {
        const factory = this.factoryList.find(item => item.id === id);
        return {
          id: id,
          name: factory ? factory.name : ''
        };
      });
    },

    handleConfirm() {
      this.isLoading = true;
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          this.$emit("confirm", {
            factoryIds: this.addForm.factoryId,
            months: this.addForm.accountingMonth
          });
        }
      });
    },

    editCancel() {
      this.$emit("cancel", {
        type: "cancel",
        visible: false,
      });
    }
  },
};
</script>

<style lang="stylus" scoped>
.wage-details {

  .info-section {

    .title {
      margin-bottom: 15px;
      font-size: 14px ;
      padding-bottom:10px;
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 15px;
    }

    .info-item {
      .label {
        color: #000;
        margin-right: 8px;
      }

      .value {
        color: #000;
        font-weight: 500;
      }
    }
  }

  .detail-section {
    margin-bottom: 10px;

    .title {
      margin-bottom: 15px;
      font-size: 14px;
      color:#000;
      border-bottom: 1px solid #ccc;
      padding-bottom:10px;
    }
  }
  .el-form-item {
  display: flex;



  >>>.el-form-item__content {
    width: 100%;
    margin-left: 0 !important;

    .el-select {
      width: 100%;
    }
    .el-input {
      width: 100%;
    }
  }
}
  .formula-section {
    margin-top: 20px;
    color: #8c8f92;
    font-size: 14px;

    p{
      padding-bottom:0
    }

    ol {
      padding-left: 20px;

      li {
        margin-bottom: 5px;
      }
    }
  }
}
</style>