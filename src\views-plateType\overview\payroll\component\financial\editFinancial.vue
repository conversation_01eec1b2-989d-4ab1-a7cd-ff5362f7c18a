<template>
  <qDialog
    :visible="visible"
    :innerScroll="false"
    :innerHeight="300"
    title="编辑"
    width="500px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <el-form :model="editForm" label-width="88px" ref="editForm" size="small">
      <el-form-item label="上卡:" prop="bankCardAmount">
        <el-input
          oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
          v-model="editForm.bankCardAmount"
          clearable
          @blur="onBlur('bankCardAmount')"
          placeholder="请输入上卡金额"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="现金:" prop="cashAmount">
        <el-input
          oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
          v-model="editForm.cashAmount"
          clearable
          @blur="onBlur('cashAmount')"
          placeholder="请输入现金金额"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="延发:" prop="delayedAmount">
        <el-input
          oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
          v-model="editForm.delayedAmount"
          clearable
          @blur="onBlur('delayedAmount')"
          placeholder="请输入延发金额"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="自离:" prop="leaveSelfAmount">
        <el-input
          oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
          v-model="editForm.leaveSelfAmount"
          clearable
          @blur="onBlur('leaveSelfAmount')"
          placeholder="请输入自离金额"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="辞职已结:" prop="resignationAmount">
        <el-input
          oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
          v-model="editForm.resignationAmount"
          clearable
          @blur="onBlur('resignationAmount')"
          placeholder="请输入辞职已结金额"
        >
        </el-input>
      </el-form-item>
    </el-form>
  </qDialog>
</template>

<script>
import { moneyFormat, moneyDelete } from "@/utils";
export default {
  name: "editFinancial",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    editInfo: [Object],
  },
  data() {
    return {
      editForm: {
        bankCardAmount: "",
        cashAmount: "",
        delayedAmount: "",
        leaveSelfAmount: "",
        resignationAmount: "",
      },
      filterParam: {},
      isLoading:false
    };
  },
  created() {
    let obj = {};
    for (const [key, val] of Object.entries(this.editInfo)) {
      if (typeof val !== "undefined" && val !== null && val !== "") {
        obj[key] = moneyFormat(val);
      }
    }
    this.editForm = {
      ...this.editForm,
      ...obj,
    };
  },
  methods: {
    onBlur(value) {
      if (value) {
        this.editForm[value] = moneyFormat(moneyDelete(this.editForm[value]));
      }
    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      for (const [key, val] of Object.entries(this.editForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = moneyDelete(val);
        }
      }
      this.isLoading = true;
      let params = {
        factoryId: JSON.parse(this.$route.query.data).factoryId,
        accountingMonth: JSON.parse(this.$route.query.data).accountingMonth,
        ...this.filterParam,
      };
      this.$api.plateTypeWorkbench.editSummaryFinance(params).then(() => {
        this.$notify.success({
          title: "成功",
          message: "编辑成功",
        });
        this.$emit("cancel", "confirm");
      }).finally(()=>{
        this.isLoading = false;
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
