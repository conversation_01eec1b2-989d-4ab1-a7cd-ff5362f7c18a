<template>
  <qDialog :visible="visible"
    title="编辑"
    :innerScroll="false"
    width="400px"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel">
    <el-form ref="editForm"
      inline :model="editForm"
      :rules="rules">
      <el-form-item
        label="技术补贴"
        prop="amount">
        <el-input
          oninput="value=value.replace(/[^\d.]/g, '');if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
          v-model="editForm.amount"
          placeholder="请输入技术补贴"
          clearable
          @blur="changAmount"></el-input>
      </el-form-item>
    </el-form>
  </qDialog>
</template>

<script>
import { moneyFormat, moneyDelete } from '@/utils'
export default {
  name: "editDialog",
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    formData: Object
  },
  data() {
    return {
      editForm: {
        amount: ""
      },
      rules: {
        amount: [
          { required: true, message: '请输入技术补贴', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (!/^\d{1,5}(\.\d{1,2})?$/.test(moneyDelete(value))) {
                callback(new Error('小数点前面仅支持5位数,小数点后面仅支持2位数'))
              }
              callback()
            }, trigger: 'blur'
          },
        ]
      }
    }
  },
  created() {
    this.editForm = {
      ...this.editForm,
      ...this.formData,
      amount: moneyFormat(this.formData.amount)
    }
  },
  methods: {
    changAmount() {
      this.$refs.editForm.validate(valid => {
        if (!valid) return
        this.editForm.amount = moneyFormat(moneyDelete(this.editForm.amount))
      })
    },
    handleCancel() {
      this.$emit('cancel', 'cancel')
    },
    handleConfirm() {
      this.$refs.editForm.validate(valid => {
        if (!valid) return
        this.$api.logisticsDataUpload.mentorship.editTech({
          ...this.editForm,
          amount: moneyDelete(this.editForm.amount)
        }).then(() => {
          this.$notify.success({
            title: '成功',
            message: '编辑成功',
          });
          this.$emit('cancel', 'confirm')
        })
      })
    },
  }
}
</script>

<style lang="scss" scoped>
</style>