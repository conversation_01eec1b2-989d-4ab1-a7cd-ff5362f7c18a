import {getPermitList} from '@/store/modules/permission'

export default {
  inserted(el, binding) {
    const { value } = binding
    const permits = getPermitList() || [];
    const hasPermission = permits.some(permit => {
      return permit.indexOf(value) > -1;
    })

    if (!hasPermission) {
      el.parentNode && el.parentNode.removeChild(el)
    }
  }
}

export function checkHasPermission(permission) {
  const permits = getPermitList() || [];
  return permits.includes(permission);
}