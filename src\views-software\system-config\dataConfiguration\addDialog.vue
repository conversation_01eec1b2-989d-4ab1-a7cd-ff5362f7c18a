<template>
  <q-dialog :visible="visible"
    :innerScroll="false"
    :title="title"
    :innerHeight="330"
    width="500px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel">
    <el-form :inline="false"
      :model="addForm"
      ref="searchForm"
      label-position="left"
      label-width="90px"
      size="small">
      <el-form-item
        label="数据名称:">
        <el-input
          v-model.trim="addForm.dataItemName"
          clearable>
        </el-input>
      </el-form-item>
      <!-- <el-form-item label="启用状态:">
          <el-switch
            active-color="#13ce66"
            inactive-color="#ff4949"
            v-model="addForm.status"
            :active-text="addForm.status ? '启用' : '禁用'"
          >
          </el-switch>
        </el-form-item> -->
      <el-form-item
        label="备注说明:"
        label-position="top">
        <el-input
          v-model.trim="addForm.comments"
          type="textarea"
          resize="none"
          rows="10"
          maxlength="300"
          show-word-limit>
        </el-input>
      </el-form-item>
    </el-form>
  </q-dialog>
</template>

<script>
export default {
  name: "addDialog",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    editForm: Object,
  },
  data() {
    return {
      addForm: {
        dataItemName: "",
        comments: "",
      },
      isLoading:false
    }
  },
  created(){
    this.addForm = {...this.addForm,...this.editForm}
  },
  methods: {
    handleCancel() {
      this.$emit('cancel', 'cancel')
    },
    handleConfirm() {
      let params = {
        ...this.addForm
      }
      this.isLoading = true
      if (this.title == "新增") {
        this.$api.softwareDataConfiguration.dataConfigSave(params).then((res) => {
          this.$notify.success({
            title: "成功",
            message: "新增成功",
          });
          this.$emit('cancel', 'confirm')
        }).finally(()=>{
            this.isLoading = false;
        });
      } else if (this.title == "编辑") {
        this.$api.softwareDataConfiguration.dataConfigUpdate({ id: this.editForm.id, ...params }).then((res) => {
          this.$notify.success({
            title: "成功",
            message: "编辑成功",
          });
          this.$emit('cancel', 'confirm')
        }).finally(()=>{
            this.isLoading = false;
        });
      }
    },
  }
}
</script>

<style lang="scss" scoped>
</style>