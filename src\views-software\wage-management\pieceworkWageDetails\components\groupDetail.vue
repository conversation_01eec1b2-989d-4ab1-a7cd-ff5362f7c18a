<template>
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form size="mini" :inline="true" :model="searchForm" label-width="90px" ref="searchForm" class="rangeTime"
          label-position="right">
          <el-form-item label="核算工厂:" prop="factoryId">
            <el-select v-model="searchForm.factoryId" filterable clearable placeholder="请选择工厂名称" @change="onSearch">
              <el-option v-for="item in tabList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="大工序编码:" prop="bigProcessCode">
            <el-input v-model="searchForm.bigProcessCode" placeholder="请输入大工序名称" clearable></el-input>
          </el-form-item>
          <el-form-item label="小工序名称:" prop="smallProcessName">
            <el-input v-model="searchForm.smallProcessName" placeholder="请输入大工序名称" clearable></el-input>
          </el-form-item>
          <el-form-item class="range" label="报工日期:" prop="accountingMonth">
            <el-date-picker :clearable="false" v-model="searchForm.startTime" value-format="yyyy-MM-dd" type="date"
              placeholder="开始日期" clearable>
            </el-date-picker>
            <span class="separator">至</span>
            <el-date-picker :clearable="false" v-model="searchForm.endTime" value-format="yyyy-MM-dd" type="date"
              placeholder="结束日期" clearable>
            </el-date-picker>
          </el-form-item>
          <el-form-item label="考勤状态:" prop="attendStatus">
            <el-select v-model="searchForm.attendStatus" filterable clearable placeholder="请选择考勤状态"
              @change="onChangeAccounted">
              <el-option v-for="item in accountedList" :key="item.value" :label="item.name" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
            <!-- 转厂核算功能 -->
          <el-form-item label="转出工厂:" prop="factoryId">
            <el-select v-model="searchForm.factoryId" filterable clearable placeholder="请选择工厂名称" @change="onSearch">
              <el-option v-for="item in tabList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary" @click="onSearch">
            查询
          </el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <el-table stripe border v-loading="loading" ref="tableRef" highlight-current-row :height="maxTableHeight"
        :data="tableData" key="tableKey" @selection-change="handleSelectionChange">
        <el-table-column width="40" type="selection"></el-table-column>
        <el-table-column prop="factoryName" label="工厂名称" width="100" fixed align="left">
        </el-table-column>
        <el-table-column prop="factoryCode" label="工厂编码" width="100" fixed align="left">
        </el-table-column>
        <el-table-column prop="pieceWorkSystem" label="计件方式" align="left">
        </el-table-column>
        <el-table-column prop="bigProcessCode" label="大工序编码" align="left">
        </el-table-column>
        <el-table-column prop="smallProcessCode" label="小工序编码" align="left">
        </el-table-column>
        <el-table-column prop="smallProcessName" label="小工序名称" align="left">
        </el-table-column>
        <el-table-column prop="amount" label="金额" align="left">
          <template slot-scope="{ row }">
            {{ row.amount | filterData }}
          </template>
        </el-table-column>
        <el-table-column prop="accountDay" label="报工日期" align="left">
        </el-table-column>
        <el-table-column prop="attendStatus" label="考勤状态" align="left">
          <template slot-scope="{ row }">
           <span :style="{
                color:
                  row.attendStatus =='正常'
                    ? '#0BB78E'
                    : 'red',
              }"> {{ row.attendStatus  }}</span>
          </template>
        </el-table-column>
           <!-- 转出工厂核算功能 -->
         <el-table-column prop="sourceFactoryName" label="转出工厂" align="left">
        </el-table-column>
        <el-table-column label="操作" align="left">
          <template slot-scope="scope">
            <div>
              <el-button type="text" size="mini" @click="handleOut(scope.$index, scope.row)">
                转出</el-button> 
            </div>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div class="table_footer">
          <ul>
            <li>
              <span>金额:</span><span>{{ groupStatistic | filterDataPiece }}</span>
            </li>
          </ul>
        </div>
        <div style="text-align: right">
          <el-pagination @size-change="onSizeChange" @current-change="onNumChange" :current-page="pageNum"
            :page-size="pageSize" :total="total" :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>
        </div>
      </template>
    </table-panel>
    <!-- 转出工厂核算功能 -->
    <OutFactoryDialog v-if="outVisible" :isTitle="isTitle" :visible="outVisible"  :factoryList="tabList" :editForm="editForm"
      @cancel="handleCancel" @confirm="handleConfirm"></OutFactoryDialog>
  </content-panel>
</template>

<script>
import moment from "moment";
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import { calculateTableWidth } from "@/utils";
import { moneyFormatZh } from "@/utils";
import OutFactoryDialog from './outFactoryDialog.vue'
export default {
  name: "softwareGroupDetail",
  mixins: [tableMixin, pagePathMixin],
  components:{OutFactoryDialog},
  data() {
    return {
      searchForm: {
        factoryId: "",
        attendStatus:"",
        accountingMonth: "",
        startTime: moment().subtract(1, 'month').startOf('month').format("YYYY-MM-DD"),
        endTime: moment().subtract(1, 'month').endOf('month').format("YYYY-MM-DD"),
        bigProcessCode: "",
        smallProcessName: ""
      },
      tabList: [],
      editForm:{},
      isTitle:"转出",
      selection:[],
      outVisible:false,
      columnList: [],
       accountedList:[
         {
          name:'全部',
          value:""
        },
        {
          name:'正常',
          value:'normal'
        },
         {
          name:'异常',
          value:'abnormal'
        }
      ],
      filterParam: {},
      params: {},
      tableData: [],
      groupStatistic: "",
      info: {},
      loading: false,
      resizeOffset: 55,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      tableKey: "",
    };
  },
  created() {
    this.$api.softwareSystemManage.getBasicPermission.getBasicPermissionAll().then((res) => {
      this.tabList = res.data.map((item) => ({
        label: item.name,
        name: item.name,
        id: item.id,
      }));
    });
    this.onSearch();
  },
  filters: {
    filterData(value) {
      return !value ? "-" : moneyFormatZh(value);
    },
    filterDataPiece(value) {
      return !value ? "0" : moneyFormatZh(value);
    }
  },
  methods: {
    // 批量转出
    batchOut() {  
      if (this.selection.length === 0) {
        this.$message({
          message: "请先勾选需要批量转出的数据",
          type: "warning",
        });
        return;
      } 
       this.editForm={
        ids:this.selection.map(item=>item.id),
        isGroup:true,
        changFactoryId:""
       }
       this.outVisible=true 
    },
    handleSelectionChange(selection) {
      this.selection = selection;
    },
    handleConfirm(data){
      this.outVisible=false  
       this.onSearch()
    },
    handleCancel(){
      this.outVisible=false 
    },
      //转出功能
    handleOut(index, row) {
       this.isTitle="转出"
      this.editForm={
        ids:[row.id],
        isGroup:true,
        changFactoryId:""
       }
       this.outVisible=true 
    },
     onChangeAccounted(){
      this.onSearch();
    },
    //统计
    getAccount() {
      this.$api.softwarePieceWageSystem.getMesPieceWagetGroupStatistic({
        ...this.filterParam,
        ...this.params,
      }).then((res) => {
        this.groupStatistic = res.data;
      });
    },
    //获取列表
    getList() {
      this.loading = true;
      this.$api.softwarePieceWageSystem
        .getMesGroupDetailList({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: {
            ...this.filterParam,
            ...this.params,
          },
        })
        .then(({ data: { list, total } }) => {
          this.tableData = list.map(item => ({ ...item, isShow: false })) || [];
          this.total = total;
          this.tableKey = Math.random();
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.filterParam = {};
      this.searchForm.startTime = moment().subtract(1, 'month').startOf('month').format("YYYY-MM-DD");
      this.searchForm.endTime = moment().subtract(1, 'month').endOf('month').format("YYYY-MM-DD");
      this.params = {};
      this.onSearch();

    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (key == 'startTime' && val) {
          this.filterParam.startTime = moment(val).format('YYYY-MM-DD') || '';
        } else if (key == 'endTime' && val) {
          this.filterParam.endTime = moment(val).format('YYYY-MM-DD') || '';
        } else if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getAccount();
      this.getList();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    //计算表格列宽度
    flexWidth(...args) {
      return calculateTableWidth(...args);
    },
    onSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(val) {
      this.pageNum = val;
      this.getList();
    },
  },
};
</script>

<style lang="stylus" scoped>
</style>
