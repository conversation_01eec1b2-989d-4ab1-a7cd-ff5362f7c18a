import printJs from "print-js";
/*
 * 格式化数字
 * number:要格式化的数字
 * decimals:保留几位小数
 * dec_point:小数点符号
 * thousands_sep:千分位符号
 * */
export function formatNumber(number, decimals, dec_point, thousands_sep) {
  number = (number + "").replace(/[^0-9+-Ee.]/g, "");
  let n = !isFinite(+number) ? 0 : +number,
    prec = !isFinite(+decimals) ? 0 : Math.abs(decimals),
    sep = typeof thousands_sep === "undefined" ? "," : thousands_sep,
    dec = typeof dec_point === "undefined" ? "." : dec_point,
    s = "";

  const toFixedFix = function (n, prec) {
    const k = Math.pow(10, prec);
    return "" + Math.floor(n * k) / k;
  };

  s = (prec ? toFixedFix(n, prec) : "" + Math.floor(n)).split(".");
  const re = /(-?\d+)(\d{3})/;

  while (re.test(s[0])) {
    s[0] = s[0].replace(re, "$1" + sep + "$2");
  }

  if ((s[1] || "").length < prec) {
    s[1] = s[1] || "";
    s[1] += new Array(prec - s[1].length + 1).join("0");
  }
  return s.join(dec);
}

function isPromise(obj) {
  return "function" == typeof obj.then;
}

export function trimObjectProperties(obj, deep = false) {
  if (typeof obj !== 'object' || obj === null) {
    return obj;
  }

  const result = Array.isArray(obj) ? [] : {};

  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      let value = obj[key];

      if (typeof value === 'string') {
        value = value.replace(/\s+/g, '');
      }
      else if (deep && typeof value === 'object' && value !== null) {
        value = trimObjectProperties(value, deep);
      }

      result[key] = value;
    }
  }

  return result;
}
// 保证fn返回resolve状态
function toResolvedPromise(fn) {
  return new Promise((resolve, reject) => {
    let result = fn();

    // 非promise结果直接返回
    if (!isPromise(result)) {
      console.log("not promise");
      return resolve(result);
    }

    result
      .then((data) => {
        resolve(data);
      })
      .catch((err) => {
        console.log("error:", err);
        resolve(null);
      });
  });
}

// 顺序执行promise
export function runPromiseInSequence(promiseArr) {
  const arr = [];
  async function run() {
    for (let p of promiseArr) {
      const val = await toResolvedPromise(p);
      arr.push(val);
    }
    return arr;
  }
  return run();
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function isString(str) {
  if (typeof str === "string" || str instanceof String) {
    return true;
  }
  return false;
}

/**
 * @param {Array} arg
 * @returns {Boolean}
 */
export function isArray(arg) {
  if (typeof Array.isArray === "undefined") {
    return Object.prototype.toString.call(arg) === "[object Array]";
  }
  return Array.isArray(arg);
}

export function isBlob(data) {
  return Object.prototype.toString.call(data) === "[object Blob]";
}

export function downloadExcel(fileData, fileName) {
  const url = window.URL.createObjectURL(
    new Blob([fileData], { type: "application/vnd.ms-excel" })
  );
  const link = document.createElement("a");
  link.style.display = "none";
  link.href = url;
  link.setAttribute("download", fileName);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  setTimeout(() => {
    URL.revokeObjectURL(url);
  });
}

export function downloadFile(response) {
  // 提取文件名
  const fileName =
    response.headers["content-disposition"].match(/filename=(.*)/)[1];

  // 将二进制流转为blob
  const blob = new Blob([response.data], { type: "application/octet-stream" });
  if (typeof window.navigator.msSaveBlob !== "undefined") {
    // 兼容IE，window.navigator.msSaveBlob:以本地方式保存文件
    window.navigator.msSaveBlob(blob, decodeURIComponent(fileName));
  } else {
    // 创建新的URL并指向File对象或者Blob对象的地址
    const blobURL = window.URL.createObjectURL(blob);
    // 创建a标签，用于跳转至下载链接
    const tempLink = document.createElement("a");
    tempLink.style.display = "none";
    tempLink.href = blobURL;
    tempLink.setAttribute("download", decodeURIComponent(fileName));
    // 兼容:某些浏览器不支持HTML5的download属性
    if (typeof tempLink.download === "undefined") {
      tempLink.setAttribute("target", "_blank");
    }
    // 挂载a标签
    document.body.appendChild(tempLink);
    tempLink.click();
    document.body.removeChild(tempLink);
    // 释放blob URL地址
    window.URL.revokeObjectURL(blobURL);
  }
}

// 防抖
export function debounce(func, ms = 1000) {
  let timer;
  return function (...args) {
    const context = this;
    clearTimeout(timer);

    timer = setTimeout(() => {
      func.apply(context, args);
    }, ms);
  };
}

// 节流
export function throttle(func, ms = 1000) {
  let timer;
  return function (...args) {
    if (timer) {
      return;
    }
    const context = this;
    timer = setTimeout(() => {
      func.apply(context, args);
      timer = null;
    }, ms);
  };
}

// 将target对应属性用source的对应属性填充
export function assignValue(target = {}, source = {}, defaultVal) {
  Object.keys(target).forEach((key) => {
    if (source.hasOwnProperty(key)) {
      target[key] = source[key] || defaultVal;
    }
  });
  return target;
}
//千分位格式化不处理两位小数
export function moneyFormatZh(num) {
  if (num === '0.00%') return num;

  if (!isNaN(parseFloat(num))) {
    // 直接使用本地化格式（自动处理千分位，保留原始小数位）
    return Number(num).toLocaleString('zh', { minimumFractionDigits: 2, maximumFractionDigits: 10 });
  }
  return "";
}
//千分位格式化数字
export function moneyFormat(num) {
  // 判断传进来的数字是否为非空数字
  if (num === null || num === undefined) {
    return "";
  }

  // 确保 num 是字符串类型
  const numStr = String(num);

  // 处理百分比
  if (numStr === '0.00%' || numStr.indexOf("%") !== -1) {
    return numStr;
  }

  // 处理数字
  if (!isNaN(parseFloat(numStr))) {
    var newNum = Number(Number(numStr).toFixed(2)).toLocaleString("zh", {
      minimumFractionDigits: 2,
    });
    return newNum;
  } else {
    return "";
  }
}
//清除数字千分位
export function moneyDelete(num) {
  if (num && num != undefined && num != null) {
    let _num = num;
    _num = _num.toString();
    _num = _num.replace(/,/gi, "");
    return _num;
  } else {
    return num;
  }
}

/**
 * 加法运算，避免数据相加小数点后产生多位数和计算精度损失。
 *
 * @param num1加数1 | num2加数2
 */
export function numAdd(num1, num2) {
  var baseNum, baseNum1, baseNum2;
  try {
    baseNum1 = num1.toString().split(".")[1].length;
  } catch (e) {
    baseNum1 = 0;
  }
  try {
    baseNum2 = num2.toString().split(".")[1].length;
  } catch (e) {
    baseNum2 = 0;
  }
  baseNum = Math.pow(10, Math.max(baseNum1, baseNum2));
  return (num1 * baseNum + num2 * baseNum) / baseNum;
}
/**
 * 减法运算，避免数据相减小数点后产生多位数和计算精度损失。
 *
 * @param num1被减数 | num2减数
 */
export function numSub(num1, num2) {
  var baseNum, baseNum1, baseNum2;
  var precision; // 精度
  try {
    baseNum1 = num1.toString().split(".")[1].length;
  } catch (e) {
    baseNum1 = 0;
  }
  try {
    baseNum2 = num2.toString().split(".")[1].length;
  } catch (e) {
    baseNum2 = 0;
  }
  baseNum = Math.pow(10, Math.max(baseNum1, baseNum2));
  precision = baseNum1 >= baseNum2 ? baseNum1 : baseNum2;
  return ((num1 * baseNum - num2 * baseNum) / baseNum).toFixed(precision);
}
/**
 * 计算表格列宽度。
 *
 * @param tableData 表格数据
 * @param prop 列字段
 * @param title 列表头
 *
 */
export function calculateTableWidth(prop, tableData, title, num = 0) {
  if (tableData.length === 0) return "150";
  let flexWidth = 0; //初始化表格列宽
  let columnContent = ""; //占位最宽的内容
  let canvas = document.createElement("canvas");
  let context = canvas.getContext("2d");
  context.font = "14px Microsoft YaHei";
  if (prop === "" && title) {
    //标题长内容少的，取标题的值,
    columnContent = title;
  } else {
    // 获取该列中占位最宽的内容
    let index = 0;
    for (let i = 0; i < tableData.length; i++) {
      const now_temp = tableData[i][prop] + "";
      const max_temp = tableData[index][prop] + "";
      const now_temp_w = context.measureText(now_temp).width;
      const max_temp_w = context.measureText(max_temp).width;
      if (now_temp_w > max_temp_w) {
        index = i;
      }
    }
    columnContent = tableData[index][prop];
    //比较占位最宽的值跟标题、标题为空的留出四个位置
    const column_w = context.measureText(columnContent).width;
    const title_w = context.measureText(title).width;
    if (column_w < title_w) {
      columnContent = title || "留四个字";
    }
  }
  // 计算最宽内容的列宽
  let width = context.measureText(columnContent);
  flexWidth = width.width + 40 + num;
  return flexWidth < 100 ? 100 : flexWidth > 200 ? 200 : Math.floor(flexWidth);
}
/**
 * 工资表打印预览html拼接
 *
 * @param data 表格数据
 *
 */
export function sallryPrintPreview(data, multipleTableData, title, date) {
  let list = data.map((n, index) => {
    let _temp = {};
    multipleTableData.forEach((m) => {
      _temp[m.fieldName] = n[m.fieldName] || "";
    });
    return _temp;
  });
  const printDom = document.createElement("div");
  printDom.setAttribute("id", "printDom");
  document.body.appendChild(printDom);
  let temp = `
  <h1 style="text-align:center;font-size:50px;border:1px solid #ccc;padding:3px 0;margin:0">${title}</h1>
    <p style="text-align:right;border:1px solid #000;padding:3px 0;margin:0">${date}</p >
    <div style="border:1px solid #000;padding:0px;height:100%">
   <table style="box-sizing:content-box;font-size:25px;margin:0px;padding-right:10px;border-right:none" cellspacing="0" border="1px">
   <thead class="thead_items">
   </thead>
  <tbody class="tbody_items">
   </tbody>
    </table>
  </div>
      `;
  printDom.innerHTML = temp;
  const theadTr = document.createElement("tr");
  for (let index = 0; index < multipleTableData.length; index++) {
    const th = document.createElement("th");
    const span = document.createElement("span");
    span.setAttribute(
      "style",
      "width:65px;display:inline-block;word-break:break-all"
    );
    span.classList.add(`thead_${multipleTableData[index].fieldName}`);
    span.innerText = multipleTableData[index].columnName;
    th.appendChild(span);
    theadTr.appendChild(th);
  }
  const thead = document.querySelector(".thead_items");
  thead.appendChild(theadTr);
  for (let i = 0; i < list.length; i++) {
    const tr = document.createElement("tr");
    for (const key in list[i]) {
      const td = document.createElement("td");
      td.style.textAlign = "center"; // 给父元素添加居中
      td.style.minHeight = "100px"; // 给父元素添加居中
      const span = document.createElement("span");
      span.setAttribute(
        "style",
        "  display:inline-block;word-break:break-all;margin: auto 0;"
      );

      span.classList.add(`tbody_${key}`);
      span.innerText = list[i][key];
      console.log("span", span);
      td.appendChild(span);
      tr.appendChild(td);
    }
    const tbody = document.querySelector(".tbody_items");
    document.querySelectorAll(".thead_staffName").forEach((item) => {
      item.style.width = "80px";
    });
    document.querySelectorAll(".tbody_staffName").forEach((item) => {
      item.style.width = "110px";

    });
    document.querySelectorAll(".thead_staffCode").forEach((item) => {
      item.style.width = "110px";
    });
    document.querySelectorAll(".thead_accountingProcess").forEach((item) => {
      item.style.width = "80px";
    });
    document.querySelectorAll(".thead_idCard").forEach((item) => {
      item.style.width = "170px";
    });
    document.querySelectorAll(".thead_entryTime").forEach((item) => {
      item.style.width = "90px";
    });

    const classList = [".thead_abcNumber", ".thead_cebNumber"]; //宽度都为180的字段
    const fourFiled = [".thead_serialNumber", ".thead_socialInsuranceDeduct"]; //宽度都为40的字段

    for (let index = 0; index < classList.length; index++) {
      document.querySelectorAll(`${classList[index]}`).forEach((item) => {
        item.style.width = "180px";
      });
    }
    for (let index = 0; index < fourFiled.length; index++) {
      document.querySelectorAll(`${fourFiled[index]}`).forEach((item) => {
        item.style.width = "40px";
      });
    }
    tbody.appendChild(tr);
  }
  document.querySelectorAll(".tbody_staffName").forEach((item) => {
    item.style.fontWeight = "bold";
    item.style.textAlign = "center";
    item.style.fontSize = "22px";
  });
  document.querySelectorAll(".tbody_actualSalary").forEach((item) => {
    item.style.fontWeight = "bold";
    item.style.fontSize = "28px";
  });
  document.querySelectorAll(".tbody_signature").forEach((item) => {
    item.style.width = "150px";
  });
  document.querySelectorAll(".tbody_subProcess").forEach((item) => {
    item.style.width = "150px";
  });
  document.querySelectorAll(".tbody_productionCategory").forEach((item) => {
    item.style.width = "150px";
  });
  document.querySelectorAll(".tbody_actualSalary").forEach((item) => {
    item.style.width = "150px";
  });
  document.querySelectorAll(".tbody_staffCode").forEach((item) => {
    item.style.width = "165px";
  });
  printJs({
    printable: "printDom",
    type: "html",
    style: "@media print { @page {margin: 10px; size: landscape;transform: scale(0.5);    body {margin: 1cm;  transform: scale(0.5); transform-origin: 0 0; }  .no-print { display: none; } }",
    maxWidth: 1100,
    scanStyles: false,
  });

  document.body.removeChild(printDom);
}
