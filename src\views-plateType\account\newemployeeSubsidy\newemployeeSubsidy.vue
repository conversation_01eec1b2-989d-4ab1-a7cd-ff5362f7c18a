<template>
    <!-- 环境补贴 -->
    <content-panel>
      <template v-slot:search>
        <search-box class="search-box">
          <el-form
            :inline="true"
            :model="searchForm"
            ref="searchForm"
            label-position="right"
            size="mini"
          >
            <el-form-item label="员工姓名:" prop="staffName">
              <el-input
                clearable
                v-model.trim="searchForm.staffName"
                size="mini"
                @keyup.enter.native="onSearch"
              >
                <template slot="append">
                  <search-batch
                    @seachFilter="onSearch('staffNames', $event)"
                    @focusEvent="focusEvent('staffNames', $event)"
                    ref="childrenStaffNames"
                    titleName="员工姓名"
                  />
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="厂牌编号:" prop="staffCode">
              <el-input
                v-model.trim="searchForm.staffCode"
                size="mini"
                clearable
                @keyup.enter.native="onSearch"
              >
                <template slot="append">
                  <search-batch
                    @seachFilter="onSearch('staffCodes', $event)"
                    @focusEvent="focusEvent('staffCodes', $event)"
                    ref="childrenStaffCodes"
                    titleName="厂牌编号"
                  />
                </template>
              </el-input>
            </el-form-item>
          </el-form>
          <template v-slot:right>
            <el-button
              size="small"
              type="primary"

              @click="onSearch"
            >
              查询</el-button
            >
            <el-button size="small" type="warning" @click="resetSearchForm">
              重置</el-button
            >
          </template>
        </search-box>
      </template>
      <table-panel ref="tablePanel">
        <template v-slot:header-left>
          <ul>
            <li>
              <span>核算工厂:</span><span>{{ info.factoryName }}</span>
            </li>
            <li>
              <span>核算月份:</span><span>{{ info.accountingMonth }}</span>
            </li>
            <li>
              <span>人数:</span>
              <span>{{ debitInfo.people }}</span>
            </li>
            <li>
              <span>熟手补贴20%扣款:</span>
              <span>{{ debitInfo.subsidyAmount }}</span>
            </li>
            <li>
              <span>额外补贴20%扣款:</span>
              <span>{{ debitInfo.subsidyAmount }}</span>
            </li>
            <li>
              <span>新员工补贴20%扣款:</span>
              <span>{{ debitInfo.subsidyAmount }}</span>
            </li>
          </ul>
        </template>
        <template
          v-slot:header-right
          v-if="info.status != '待提交' || info.roleName != '分厂文员'"
        >
          <!-- <template v-if="info.status != '已完成'"> -->
            <!-- v-show="permission" -->
             <!-- <template  v-if="permission"> -->
            <el-button size="small" type="primary" @click="handleUpdate"
              >生成数据</el-button
            >
            <el-button size="small" type="primary" @click="handleAdd">
              新增</el-button
            >
            <el-button size="small" type="primary" @click="handleImport">
              导入</el-button
            >
            <el-button size="small" type="primary" @click="batchDelete">
              批量删除</el-button
            >
          <!-- </template> -->
          <el-button size="small" type="primary" @click="handleExport">
            导出</el-button
          >
        </template>

        <el-table
          stripe
          border
          v-loading="loading"
          ref="tableRef"
          highlight-current-row
          :data="tableData"
          :height="maxTableHeight"
          @selection-change="handleSelectionChange"
        >
          <!-- :reserve-selection="true" -->
          <el-table-column width="40" type="selection"></el-table-column>
          <el-table-column
            prop="staffName"
            label="员工姓名"
            min-width
            align="left"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="staffCode"
            label="厂牌编号"
            min-width
            align="left"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="accountingMonth"
            label="核算月份"
            min-width
            align="left"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="groupName"
            label="核算班组"
            min-width
            align="left"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="groupName"
            label="熟手补贴20%扣款"
            min-width
            align="left"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="groupName"
            label="额外补贴20%扣款"
            min-width
            align="left"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="groupName"
            label="新员工补贴20%扣款"
            min-width
            align="left"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="createTime"
            label="创建时间"
            min-width
            align="left"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="updateTime"
            label="修改时间"
            min-width
            align="left"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="remarks"
            label="备注"
            width="130"
            align="left"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            label="操作"
            width="150"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleEdit(scope.$index, scope.row)"
              >
                编辑</el-button
              >

              <el-button
                slot="reference"
                type="text"
                size="mini"
                @click="handleDelete(scope.row)"
              >
                删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <template v-slot:footer>
          <el-pagination
            :current-page="pageNum"
            :page-size="pageSize"
            :total="total"
            :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          >
          </el-pagination>
        </template>
      </table-panel>
      <add-dialog
        v-if="visible"
        :visible="visible"
        :title="title"
        :editForm="editForm"
        @cancel="handleCancel"
        :info="info"
      ></add-dialog>
      <!-- <Import
        v-if="ImportVisible"
        :visible="ImportVisible"
        @cancel="cancel"
        @confirm="confirm"
        :importInfo="importInfo"
      /> -->
      <importDialog
        :showdownload="true"
        v-if="ImportVisible"
        :importInfo="importInfo"
        :visible.sync="ImportVisible"
        @after="onSearch"
      />
      <common-dialog
        v-if="commonVisible"
        :isVisible="commonVisible"
        :title="title"
        :info="info"
        @cancel="commonCancel"
        :selectall="selectall"
        :filterParam="filterParam"
      ></common-dialog>
    </content-panel>
  </template>

  <script>
  import tableMixin from "@/utils/tableMixin";
  import pagePathMixin from "@/utils/page-path-mixin";
  import { moneyFormat, moneyDelete, calculateTableWidth } from "@/utils";
  import addDialog from "./addDialog";
  import importDialog from "./ImportOffline";
  export default {
    name: "PlateTypeNewemployeeSubsidy",
    mixins: [tableMixin,pagePathMixin],
    components: {
      addDialog,
      importDialog,
    },
    data() {
      return {
        searchForm: {
          accountingMonth: "",
          name: "",
          staffCode: "",
          staffName: "",
        },
        filterParam: {},
        loading: false,
        tableData: [], //表格数据
        resizeOffset: 55,
        tableInfo: [],
        pageSize: 50,
        pageNum: 1,
        total: 1,
        visible: false,
        permission: "",
        debitInfo: {},
        ImportVisible: false,
        info: {},
        editForm: {},
        commonVisible: false,
        selection: [],
        info: "",
      };
    },
    watch: {
      $route: {
        handler(value) {
          if (
            value.path.includes("environmental") &&
            value.path.includes("customized")
          ) {
            this.info = JSON.parse(value.query.data);
            this.factoryId = this.info.factoryId;
            this.importInfo = {
              reportName: "environmentSubsidyImport",
              paramMap: {
                columnValue: "定制-环境补贴",
                factoryId: this.factoryId,
                accountingMonth: this.info.accountingMonth,
                id: this.info.id,
              },
            };
            this.getList();
            this.getStatistic();
            this.getPermission();
          }
        },
        immediate: true,
        deep: true,
      },
    },
    methods: {
      handleSelectionChange(selection) {
        this.selection = selection;
      },
      //获取分页
      getList() {
        this.loading = true;
        this.$api.dataUpload.environmentSubsidy
          .environmentSubsidylist({
            pageNum: this.pageNum,
            pageSize: this.pageSize,
            filterData: {
              accountingMonth: this.info.accountingMonth,
              factoryId: this.factoryId,
              ...this.filterParam,
              ...this.params,
            },
          })
          .then((res) => {
            this.tableData = res.data.list;
            this.total = res.data.total;
          })
          .finally(() => {
            this.loading = false;
          });
      },
      getStatistic() {
        let params = {
          accountingMonth: this.info.accountingMonth,
          factoryId: this.factoryId,
          ...this.filterParam,
          ...this.params,
        };
        this.$api.dataUpload.environmentSubsidy.statistics(params).then((res) => {
          this.debitInfo = res.data || {};
          // this.debitInfo = res.data ? res.data : {
          //   people:0,
          //   subsidyAmount:0
          // }
        });
      },
      //是否具有待办任务操作权限
      getPermission() {
        let params = {
          factoryId: this.factoryId,
          accountingMonth: this.info.accountingMonth,
          type: "26",
        };
        this.$api.workbench.agencyPermission(params).then((res) => {
          this.permission = res.data;
        });
      },
      //计算表格列宽度
      flexWidth(...args) {
        return calculateTableWidth(...args);
      },
      //重置
      resetSearchForm() {
        this.$refs.searchForm.resetFields();
        this.$refs.childrenStaffNames.resetFilter();
        this.$refs.childrenStaffCodes.resetFilter();
        this.filterParam = {};
        this.params = {};
        this.onSearch();
      },
      //搜索
      onSearch(name, data) {
        // 每次搜索都初始化搜索条件，重新添加合法的条件
        this.filterParam = {};
        for (const [key, val] of Object.entries(this.searchForm)) {
          if (typeof val !== "undefined" && val !== null && val !== "") {
            this.filterParam[key] = val;
          }
        }
        if (name && data) {
          if (Array.isArray(data) && data.length > 0)
            this.filterParam[name] = data;
        }
        this.pageNum = 1;
        this.getList();
        this.getStatistic();
      },
      focusEvent(name, data) {
        if (Array.isArray(data) && !data.length) {
          this.params[name] = [];
        }
        if (name && data) {
          if (Array.isArray(data) && data.length > 0) this.params[name] = data;
        }
      },
      //导入
      handleImport() {
        sessionStorage.setItem("tableInfo", JSON.stringify(this.tableInfo));
        this.title = "导入";
        this.ImportVisible = true;
      },
      //新增
      handleAdd() {
        this.title = "新增";
        this.visible = true;
        this.editForm = {};
      },
      //编辑
      handleEdit(index, row) {
        let list = [];
        Object.keys(row).forEach((key) => {
          if (key.includes("field")) {
            list.push({ [key]: row[key] });
          }
        });
        this.title = "编辑";
        this.visible = true;
        this.editForm = {
          staffCode: row.staffCode || "",
          staffName: row.staffName || "",
          accountingMonth: row.accountingMonth || "",
          groupId: row.groupId || "",
          accountingMonth: row.accountingMonth || "",
          subsidyAmount: row.subsidyAmount || "",
          id: row.id,
          remarks: row.remarks || "",
          groupName: row.groupName,
          actionType:row.actionType
        };
      },
      //删除
      handleDelete(row) {
        let params = {
          factoryId: row.factoryId,
          accountingMonth: row.accountingMonth,
          staffCode: row.staffCode,
          id: row.id,
        };
        this.$confirm("此操作将永久删除该条数据, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.$api.dataUpload.environmentSubsidy
              .environmentSubsidydelete(params)
              .then((res) => {
                this.$message({
                  type: "success",
                  message: "删除成功!",
                });
                this.getList();
                this.getStatistic();
              });
          })
          .catch(() => {});
      },
      //导出
      handleExport() {
        let params = {
          factoryId: this.factoryId,
          accountingMonth: this.info.accountingMonth,
          ...this.filterParam,
        };
        this.$api.common
          .doExport("exportEnvironmentSubsidy", params)
          .then((res) => {
            if (res.code == 200) {
              this.$message.success("导出操作成功，请前往导出记录查看详情");
            }
          });
      },
      //批量删除
      batchDelete() {
        // if (this.idList.length === 0 && !this.selectall) {
        //   this.$message({
        //     message: "请先勾选需要批量确认的内容",
        //     type: "warning",
        //   });
        //   return;
        // }
        // this.info = { idList: this.idList, type: "删除" };
        // this.title = "批量删除";
        // this.commonVisible = true;
        // ("");
        if (!this.selection.length) {
          this.$message({
            message: "请先勾选需要批量确认的内容",
            type: "warning",
          });
          return;
        }
        let params = {
          ids: this.selection.map((item) => item.id),
          factoryId: this.factoryId,
          accountingMonth: this.info.accountingMonth,
        };
        this.$confirm(`确认要删除选中数据吗?`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.$api.dataUpload.environmentSubsidy
              .environmentSubsidydelete(params)
              .then((res) => {
                this.$notify.success({
                  title: "成功",
                  message: "批量删除成功",
                });
                this.getList();
                this.getStatistic();
              })
              .finally(() => {
                this.loading = false;
              });
          })
          .catch(() => {});
      },
      //刷新
      handleUpdate() {
        this.$confirm("刷新后，修改记录不保存,是否继续?", "刷新", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          let params = {
            factoryId: this.factoryId,
            accountingMonth: this.info.accountingMonth,
          };
          this.$api.dataUpload.environmentSubsidy
            .generate(params)
            .then((res) => {
              if (res.code == 200) {
                this.$message.success("刷新成功");
                this.getList();
                this.getStatistic();
              }
            })
            .catch((res) => {
              this.$message.error("刷新失败,请稍后重新点击更新按钮!");
            });
        });
      },
      handleCancel(type) {
        this.visible = false;
        if (type == "cancel") return;
        this.getList();
        this.getStatistic();
      },
      handleSizeChange(val) {
        this.pageSize = val;
        this.getList();
      },
      handleCurrentChange(val) {
        this.pageNum = val;
        this.getList();
      },
      cancel(value) {
        this.ImportVisible = value;
      },
      confirm(value) {
        this.ImportVisible = value;
      },
    },
  };
  </script>

  <style lang="stylus" scoped>
  >>> .el-form--inline .el-form-item {
    margin-right: 40px;
  }
  #item {
    margin: 0;
    padding: 5px;
  }

  ul {
    height: 34px;
    list-style: none;
    display: flex;
    align-items: center;
    padding: 0;
    margin: 0
    li{
      margin-right: 10px;
    }
  }

  i {
    font-style: normal;
  }

  .el-form-item__content {
    display: flex;
  }
  </style>
