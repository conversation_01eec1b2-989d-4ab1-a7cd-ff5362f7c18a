<template>
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form class="rangeTime" :inline="true" :model="logForm" ref="searchForm" label-position="left"  size="mini">
          <el-form-item label="内容检索:" prop="searchValue">
            <el-input v-model.trim="logForm.searchValue" size="mini" clearable @keyup.enter.native="onSearch" placeholder="按姓名检索"></el-input>
          </el-form-item>
          <el-form-item class="range" label="时间检索:">
            <el-date-picker :clearable="false" v-model="logForm.startTime" value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
            </el-date-picker>
            <span class="separator">至</span>
            <el-date-picker :clearable="false" v-model="logForm.endTime" value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
            </el-date-picker>
            <!-- <el-date-picker
              class="input-date"
              v-model="logForm.date"
              :default-time="['00:00:00', '23:59:59']"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker> -->
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary" @click="onSearch">查询</el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">重置</el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <el-table stripe border v-loading="loading" ref="tableRef" highlight-current-row :data="tableData" :height="maxTableHeight" style="width: 100%">
        <el-table-column type="index" width="50" align="left">
        </el-table-column>
       <el-table-column
          prop="operateTime"
          label="时间"
          width="150"
          align="left">
        </el-table-column>
        <el-table-column
          prop="staffName"
          label="姓名"
          width="150"
          align="left"
          show-overflow-tooltip
          >
        </el-table-column>
        <el-table-column
          prop="staffCode"
          label="厂牌"
          width="120"
          align="left"
          show-overflow-tooltip
          >
        </el-table-column>
        <el-table-column
          prop="businessName"
          label="页面"
          width="150"
          align="left"
          show-overflow-tooltip
          >
        </el-table-column>
        <el-table-column
          prop="operateType"
          label="类型"
          width="150"
          align="left"
          show-overflow-tooltip
          >
        </el-table-column>
        <el-table-column prop="operateContent" label="操作内容" align="left" show-overflow-tooltip>
        </el-table-column>
      </el-table>
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="pageNum" :page-size="pageSize" :total="total" :page-sizes="[50,100,200]" layout="total, sizes, prev, pager, next, jumper">
      </el-pagination>
    </table-panel>
  </content-panel>
</template>

  <script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";

import moment from "moment";
export default {
  name: "SystemLog",
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      logForm: {
        endTime: moment().add(1, "days").format("YYYY-MM-DD"),
        startTime: moment().subtract("day").format("YYYY-MM-DD"),
        searchValue: "",
      },
      options: [],
      tableData: [],
      loading: false,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      filterParam: {},
    };
  },
  created() {
    this.getList();
  },
  mounted() { },

  methods: {
    //搜索
    onSearch() {
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.logForm)) {
        if (key === "date" && val) {
          if (val.length) {
            this.logForm.startTime = val[0] || "";
            this.logForm.endTime = val[1] || "";
          }
        } else if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      this.pageNum = 1;
      this.getList();
    },
    getList() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        filterData: {
          ...this.logForm,
          moduleId:1
        },
      };
      this.$api.agencyLog
        .logList(params)
        .then((res) => {
          this.tableData = res.data.list;
          this.total = res.data.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //重置
    resetSearchForm() {
      this.logForm = {
        endTime: '',
        startTime: '',
        searchValue: "",
      };
      this.onSearch();
    },
    //分页   页数条数改变函数
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    handleCurrentChange(val) {
      this.pageNum = val;
      this.getList();
    },
    handleEdit() { },
    handleDelete() { },
  },
};
</script>

  <style lang="stylus" scoped>
  .el-form--label-left {
    .el-form-item {
      margin-right: 16px;
    }

    >>>.el-form-item__label {
      text-align: right !important;
    }
  }
</style>

