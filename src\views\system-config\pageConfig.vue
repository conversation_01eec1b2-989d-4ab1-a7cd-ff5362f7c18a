<template>
  <content-panel>
    <table-panel ref="tablePanel">
      <template v-slot:header-right>
        <el-button
          size="small"
          type="primary"
          :class="[!isSave ? '' : 'edit-active']"
          @click="handleSave"
        >
          {{ isSave ? "保存" : "编辑" }}
        </el-button>
      </template>



      <el-table
        borde
        stripe
        v-loading="loading"
        ref="tableRef"
        :height="maxTableHeight"
        :data="tableData"
        :row-class-name="handleDragAllow"
        row-key="id"
      >
        <el-table-column prop="dataItemName" label="数据名称" width="200" align="left">
        </el-table-column>
        <el-table-column prop="dataType" label="数据类型" width="100" align="left">
          <template slot-scope="scope">
            {{ scope.row.dataType == 0 ? "固定配置" : "灵活配置" }}
          </template>
        </el-table-column>
        <el-table-column prop="isActive" label="启用状态" width="100" align="left">
          <template slot-scope="scope">
            {{ scope.row.isActive == 1 ? "启用" : "禁用" }}
          </template>
        </el-table-column>
        <el-table-column
          prop="comments"
          label="备注说明"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
      </el-table>

        <!-- <div :style="{'height':maxTableHeight+'px','overflow':'auto'}">
          <div>
            <el-button class="editBtn" type="primary" size="small" @click="edit">{{ !editInfo ? "编辑" : "保存" }}</el-button>
            <el-tree :data="data" default-expand-all :expand-on-click-node="false" :default-expanded-keys="defaultExpand" ref="menutree">
              <span class="custom-tree-node" slot-scope="{ node, data }">
                <span>{{ data.label }}</span>
                <el-switch v-model="data.visible" active-value="Y" inactive-value="N" active-color="#ec808d" inactive-color="#0ab78e" node-key="id" :active-text="data.visible=='N' ? '空数据隐藏' : '固定展示'" v-if="data.editable=='Y'&&editInfo==true" @change="switchChange(data)">
                </el-switch>
                <div v-if="data.editable=='Y'&&editInfo==false " class="nullData">
                  <i class="el-icon-success" v-if="data.visible=='N'"></i>
                  <i class="el-icon-error" v-else></i>
                  {{data.visible=='N' ? '空数据隐藏' : '固定展示'}}
                </div>
                <div v-if="data.editable=='Y'&& editInfo==true" class="topMove" @click="topMove(node, data)">
                  <i class="icon el-icon-top"></i>
                  <span>上移</span>
                </div>
                <div v-if="data.editable=='Y'&& editInfo==true" class="bottomMove" @click="bottomMove(node, data)">
                  <i class="icon el-icon-bottom"></i>
                  <span>下移</span>
                </div>
              </span>
            </el-tree>
          </div>
        </div> -->
    </table-panel>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";

import Sortable from "sortablejs";
export default {
  name: "PageConfig",
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      data: [],
      tableData: [],
      loading: false,
      defaultProps: {
        children: "children",
        label: "label",
      },
      resulet: null,
      defaultExpand: [],
      resizeOffset: 13, //设置table间距
      editInfo: false,
      list: [],
      rowIndex: [],
      isSave: false,
    };
  },
  mounted() {
    this.rowDrop();
  },
  methods: {
    handleDragAllow(row) {
      if (!this.isSave) return;
      if (row.row.editable === "Y") {
        return "edit_active";
      }
      return "";
    },
    // 行拖拽
    rowDrop() {
      console.log('aaa')
      // 要侦听拖拽响应的DOM对象
      const tbody = document.querySelector(" .el-table__body-wrapper tbody");
      const that = this;
      Sortable.create(tbody, {
        animation: 1000,
        // 结束拖拽后的回调函数
        onEnd({ newIndex, oldIndex }) {
          that.list = [];
          that.rowIndex = [];
          if (that.tableData[oldIndex].editable == "N") return;
          if (newIndex == oldIndex) return;
          that.list.push(that.tableData[oldIndex].deductionOrSubsidy);
          const currRow = that.tableData.splice(oldIndex, 1)[0];
          that.tableData.splice(newIndex, 0, currRow);
        },
        onMove(customEvent) {
          // console.log(customEvent.dragged.rowIndex, customEvent.related.rowIndex);
          let isShow = true;
          that.rowIndex.push(customEvent.related.rowIndex);
          that.tableData.forEach((item, index) => {
            if (customEvent.dragged.rowIndex == index) {
              item.editable == "N" ? (isShow = false) : (isShow = true);
            }
          });
          isShow = that.rowIndex.every((item) => that.tableData[item].editable == "Y");
          if (that.tableData[customEvent.dragged.rowIndex].editable == "N") {
            isShow = false;
          }
          return isShow && that.isSave;
        },
      });
    },
    filterData() {
      let list = [];
      if (this.list.includes(6) && !this.list.includes(7)) {
        list = this.tableData.filter(
          (item) => item.editable == "Y" && item.deductionOrSubsidy == "6"
        );
      }
      if (this.list.includes(7) && !this.list.includes(6)) {
        list = this.tableData.filter(
          (item) => item.editable == "Y" && item.deductionOrSubsidy == "7"
        );
      }
      if (this.list.includes(6) && this.list.includes(7)) {
        list = this.tableData.filter((item) => item.editable == "Y");
      }
      return list;
    },
    edit() {
      this.data.forEach((item, index) => {
        if (item.label == "其他补贴" || item.label == "其他扣款") {
          // this.visible=!this.visible
          item.children.forEach((v, i) => {
            v.value = !v.value;
          });
        }
        this.editInfo = !this.editInfo;
      });
    },
    //保存
    handleSave() {
      this.isSave = !this.isSave;
      if (this.isSave) return;
      let list = this.filterData();
      this.$api.systemConfig.pageConfigdDrag({ list }).then(({ success }) => {
        if (success)
          this.$notify({
            title: "成功",
            message: "修改成功",
            type: "success",
          });
        this.getList();
      });
    },
    //switch状态改变的函数
    // switchChange(val) {
    //   this.$api.systemConfig
    //     .pageConfigEmptyData({
    //       id: val.id,
    //       visible: val.visible == "N" ? "N" : "Y",
    //     })
    //     .then((res) => {
    //       console.log(res);
    // this.getList();
    //     });
    // },
    //获取页面配置
    getList() {
      this.loading = true;
      this.$api.systemConfig
        .pageConfig()
        .then(({ data }) => {
          this.tableData = data.map((item, index) => ({
            ...item,
            id: item.id || "key_" + (index + 1),
          }));
          // this.sortTable(this.tableData);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //表格排序
    sortTable(data) {
      if (!data.length) return;
      for (let i = 0; i < data.length; i++) {
        for (let j = 0; j < data.length - i - 1; j++) {
          if (data[j].colSort > data[j + 1].colSort) {
            var temp = data[j];
            data[j] = data[j + 1];
            data[j + 1] = temp;
          }
        }
      }
      return data;
    },
  },
  created() {
    this.getList();
  },
};
</script>

<style lang="stylus" scoped>
.custom-tree-node {
  display: flex;
  height: 29px;
  line-height: 29px;

  span {
    width: 100px;
  }

  .el-switch {
    margin-left: 30px;
    height: 29px;
  }

  >>>.el-switch__label.is-active {
    color: #606266 !important;
  }

  .icon {
    border-radius: 50%;
    color: #fff;
    font-weight: bold;
    text-align: center;
    line-height: 14px;
  }

  .topMove {
    margin: 0 20px;

    .icon {
      background: #0bb78e;
      border: 1px solid #0bb78e;
    }
  }

  .bottomMove {
    .icon {
      background: #ec808d;
      border: 1px solid #ec808d;
    }
  }

  .nullData {
    width: 120px;
    margin-left: 52px;
    font-size: 14px;
    height: 29px;
    display: flex;
    align-items: center;

    .el-icon-error {
      color: #ec808d;
    }

    i {
      font-size: 20px;
      margin-right: 10px;
      color: #0BB78E;
    }
  }
}

.el-switch {
  width: 120px;
}

>>> .el-switch__label {
  color: #606266;
}

.el-scrollbar {
  >>>.el-scrollbar__wrap {
    overflow-x: auto !important;
  }
}

.editBtn {
  position: absolute;
  right: 50px;
  z-index: 99;
}

>>>.edit_active {
  background: rgba(11, 183, 142, 0.161) !important;
}

.edit-active {
  background: #ff6b31;
  border-color: #ff6b31;
}
</style>
