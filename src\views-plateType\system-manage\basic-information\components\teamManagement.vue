<template>
  <q-dialog :visible="visible"
    :innerScroll="false"
    title="班组管理" width="1200px"
    :showFooter="false"
    @cancel="handleCancel"
    :before-close="handleCancel">
    <div class="header">
      <span>{{formData.factoryName}}</span>
     <div>
      <el-checkbox style="color: #0bb78e;margin-right: 10px;" v-model="enable"
        @change="changeHiddenTeam">隐藏已禁用核算班组</el-checkbox>
       <el-button
        type="primary"
        @click="handleAdd">新增</el-button>
     </div>
    </div>
    <el-table stripe border
      v-loading="loading"
      ref="tableRef"
      highlight-current-row
      :height="350"
      :data="tableData">
      <el-table-column
        type="index"
        align="left">
      </el-table-column>
      <el-table-column
        prop="name"
        label="核算班组"
        align="left"
        show-overflow-tooltip>
      </el-table-column>
      <el-table-column
        prop="parentName"
        label="核算大工序"
        align="left"
        show-overflow-tooltip>
      </el-table-column>
      <el-table-column
        prop="code"
        label="大工序编码"
        align="left"
        show-overflow-tooltip>
      </el-table-column>
      <!-- 增加小工序管理处理 -->
      <el-table-column
        prop="mesProcessName"
        label="小工序名称"
        align="left"
        show-overflow-tooltip>
      </el-table-column>
      <el-table-column
        prop="enable"
        label="启用状态"
        width="120"
        align="left">
        <template
          slot-scope="{row}">
          <div>
            <el-switch
              @change="onSwitch(row,'班组管理')"
              inactive-color="#ff4949"
              v-model="row.enable"
              :active-text="row.enable == 1 ? '启用' : '禁用'"
              :active-value="1"
              :inactive-value="0">
            </el-switch>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="updateTime"
        label="修改时间"
        align="left"
        width="150"
        show-overflow-tooltip>
      </el-table-column>
      <el-table-column
        label="备注说明"
        prop="remark"
        align="left"
        show-overflow-tooltip>
      </el-table-column>
      <el-table-column
        label="操作"
        width="80"
        align="left">
        <template
          slot-scope="scope">
          <el-button
            type="text"
            size="mini"
            @click="handleEdit(scope.row)">
            编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </q-dialog>
</template>

<script>
export default {
  name: "teamManagement",
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    formData: Object
  },
  data() {
    return {
      tableData: [],
      loading: false,
      enable:true
    }
  },
  created() {
    this.getTeamList()
  },
  mounted() {
    this.$bus.$off('plateTypeAddCancel')
    this.$bus.$on('plateTypeAddCancel', (val) => {
      if (val == 'cancel') return
      this.getTeamList()
    })
  },
  methods: {
    changeHiddenTeam(val) {
        this.getTeamList()
    },
    //获取所有班组列表
    getTeamList() {
      this.loading = true
      this.$api.plateTypeSystemManage.getBasicPermission.availableGroup({
        factoryId: this.formData.factoryId,
        enable:this.enable?1:""
      }).then(({ data }) => {
        this.tableData = data || []
      }).finally(() => {
        this.loading = false;
      });
    },
    //切换启用状态
    onSwitch({ id, enable }, value) {
      this.$api.plateTypeSystemManage.getBasicPermission.groupEnable({ id, enable }).then(({ success }) => {
        if (enable == 1) {
          this.$notify.success({
            title: "成功",
            message: "启用成功",
          });
        } else if (enable == 0) {
          this.$notify.success({
            title: "成功",
            message: "禁用成功",
          });
        }
        this.getTeamList()
      }).catch(() => {
        this.getTeamList()
      })
    },
    //新增班组信息
    handleAdd() {
      this.$emit('add', {
        title: '新增',
        formData: { factoryId: this.formData.factoryId}
      })
    },
    //编辑班组信息
    handleEdit(row) {
      console.log(row,'编辑班组信息')
      const { name, id, enable, } = row
      this.$emit('add', {
        title: '编辑',
        formData: {
          id,
          groupName: name || '',
          parentId: row.parentId || '',
          enable,
          factoryId: row.factoryId ||'',
          remark: row.remark || ""
        }
      })
    },
    handleCancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style lang="stylus" scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
}
</style>