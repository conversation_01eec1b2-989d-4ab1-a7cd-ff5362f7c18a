<template>
  <qDialog
    :visible="visible"
    :title="title"
    :innerScroll="true"
    width="650px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <div>员工信息</div>
    <el-form
      class="employee_info"
      ref="addForm"
      :model="addForm"
      label-width="140px"
      :rules="rules"
    >
      <el-form-item label="厂牌编号:" prop="staffCode">
        <el-input :disabled="title == '编辑'" v-model="addForm.staffCode">
          <template slot="append">
            <el-button type="primary" @click="searchEmployee"> 查询</el-button>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="员工姓名:">
        {{ addForm.staffName }}
      </el-form-item>
      <el-form-item label="核算月份:">
        {{ addForm.accountingMonth }}
      </el-form-item>
    </el-form>
    <div>
      <div>其他补贴</div>
      <div class="employee_info" v-if="subsidyItems.length > 0">
        <div class="items" v-for="item in subsidyItems" :key="item.columnName">
          <span class="column_title">{{ item.columnName + ":" }}</span>
          <el-input
            oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
            v-model="item.value"
            clearable
            @blur="onBlur(item)"
          ></el-input>
        </div>
      </div>
    </div>
  </qDialog>
</template>

<script>
import { moneyFormat, moneyDelete } from "@/utils";
export default {
  name: "addDialog",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    editForm: Object,
  },
  data() {
    return {
      addForm: {
        staffCode: "",
        staffName: "",
        accountingMonth: JSON.parse(this.$Base64.decode(this.$route.query.data)).accountingMonth,
      },
      rules: {
        staffCode: [{ required: true, message: "请输入厂牌编号", trigger: "blur" }],
      },
      subsidyItems: [],
      isLoading:false
    };
  },
  created() {
    this.getSubsidy();
    this.addForm = {
      ...this.addForm,
      ...this.editForm,
    };
  },
  methods: {
    //查询员工信息
    searchEmployee() {
      this.$refs.addForm.validate((valid) => {
        if (!valid) return;
        let params = {
          staffCode: this.addForm.staffCode,
        };
        this.$api.information.employee.employeeDetails(params).then(({ data }) => {
          this.addForm = {
            ...this.addForm,
            staffName: data.staffName,
          };
        });
      });
    },
    //获取补贴项
    async getSubsidy() {
      const { otherId, accountingMonth, factoryId } = JSON.parse(this.$Base64.decode(this.$route.query.data));
      const res = await this.$api.plateTypeSystemConfig.dynamicheader({
        planId: otherId,
        accountingMonth,
        factoryId,
      });
      let subsidyItems = res.data || [];
      subsidyItems.forEach((item) => {
        this.subsidyItems.push({
          columnName: item.columnName,
          fieldName: item.fieldName,
          value: "",
        });
      });
      if (this.title == "新增") return;
      this.subsidyItems.forEach((item) => {
        this.editForm.list.forEach((it) => {
          for (const key in it) {
            if (item.fieldName == key) item.value = moneyFormat(moneyDelete(it[key]));
          }
        });
      });
    },
    onBlur(item) {
      if (!/^-?\d{1,5}(\.\d{1,2})?$/.test(moneyDelete(item.value))) {
        this.$message({
          message: "小数点前面仅支持5位数,小数点后面仅支持2位数",
          type: "warning",
        });
        item.value = "";
        return
      }
       item.value = moneyFormat(moneyDelete(item.value));
    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      let subsidyItems = this.subsidyItems.map((v) => ({
        displayName: v.columnName,
        fieldName: v.fieldName,
        fieldValue: moneyDelete(v.value),
      }));
      if (subsidyItems.every((item) => !item.fieldValue)) {
        this.$message({
          message: "补贴项金额不能为空",
          type: "warning",
        });
        return;
      }
      this.isLoading = true;
      const { factoryId, accountingMonth, id, otherId } = JSON.parse(
        this.$Base64.decode(this.$route.query.data)
      );
      let params = {
        staffCode: this.addForm.staffCode,
        factoryId,
        accountingMonth,
        //代办任务id
        backlogId: id,
        otherDetailId: otherId,
        subsidyItems,
      };
      if (this.title == "新增") {
        this.$api.plateTypeDataUpload.otherSubsidies.getOtherSubsidySave(params).then((res) => {
          this.$notify.success({
            title: "成功",
            message: "新增成功",
          });
          this.$emit("cancel", "confirm");
        }).finally(()=>{
            this.isLoading = false;
          });
      } else {
        this.$api.plateTypeDataUpload.otherSubsidies.getOtherSubsidyUpdate(params).then((res) => {
          this.$notify.success({
            title: "成功",
            message: "编辑成功",
          });
          this.$emit("cancel", "confirm");
        }).finally(()=>{
            this.isLoading = false;
        });
      }
    },
  },
};
</script>

<style lang="stylus" scoped>
.employee_info {
  border: 1px solid #ccc;
  padding: 15px;
  box-sizing: border-box;
  margin: 15px 0;
}

>>>.el-input-group__append {
  background: #24c69a;
  color: #fff;

  .el-button {
    padding: 5px 25px;
  }
}
.items{
  display: flex
  align-items: center
  margin-bottom: 22px
  .el-input{
    flex:1
  }
}
.column_title{
  width: 140px;
  text-align: right;
  padding-right: 12px;
  box-sizing: border-box
}
>>>.el-form-item__label:before{
    display: none
}
</style>
