<template>
  <!--任务配置 -->
  <content-panel>
    <table-panel
      ref="tablePanel">
      <template
        v-slot:header-right>
        <el-button
          size="small"
          type="primary"
          @click="handleAdd">
          新增 </el-button>
      </template>
      <el-table stripe border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :data="tableData"
        :height="maxTableHeight"
        style="width: 100%">
        <el-table-column
          prop="otherPlanName"
          label="任务名称"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="roleName"
          label="所属角色"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="任务类型"
          width="220"
          align="left">
          <template
            slot-scope="scope">
            {{ scope.row.dataType == "1" ? "灵活配置" : "固定任务" }}
          </template>
        </el-table-column>
        <el-table-column
          label="启用状态"
          width="220"
          align="left">
          <template
            slot-scope="scope">
            <div>
              <el-switch
                @change="onSwitch(scope.row)"
                inactive-color="#ff4949"
                v-model="scope.row.isActive"
                :active-text="scope.row.isActive == 1 ? '启用' : '禁用'"
                :active-value="1"
                :inactive-value="0">
              </el-switch>
            </div>
            <!-- {{ scope.row.isActive == "1" ? "启用" : "禁用" }} -->
          </template>
        </el-table-column>
        <el-table-column
          label="查看模板"
          width="220"
          align="left">
          <template
            slot-scope="scope">
            <el-button
              el-button
              type="text"
              @click="downLoad(scope.$index, scope.row)">
              模板下载
            </el-button>
          </template>
        </el-table-column>
        <el-table-column
          label="备注说明"
          prop="comments"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label=""
          align="left"
          fixed="right"
          width="160">
          <template
            slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              @click="handleEdit(scope.row)">
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="pageNum"
        :page-size="pageSize"
        :total="total"
        :page-sizes="[50, 100, 200]"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        layout="total, sizes, prev, pager, next, jumper">
      </el-pagination>
    </table-panel>
    <add-dialog v-if="visible"
      :visible="visible"
      :title="title"
      :editForm="editForm"
      @cancel="handleCancel"></add-dialog>
    <add-data v-if="isVisible"
      :visible="isVisible"
      :editForm="editForm"
      @cancel="addDataCancel"></add-data>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import addDialog from "./components/addDialog";
import addData from "./components/addData";
export default {
  name:'SoftwareTaskConfig',
  components: { addDialog, addData },
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      editForm: {},
      filterParam: {},
      tableData: [],
      loading: false,
      pageSize: 50, //每页显示个数
      pageNum: 1, //当前页数
      total: 0, //总条数
      visible: false,
      isVisible: false,
      title: "",
      resizeOffset: 45, //设置table间距
    };
  },
  created() {
    this.getList();
    this.$bus.$off("softwareAdd");
    this.$bus.$on("softwareAdd", () => {
      this.isVisible = true;
    });
  },
  methods: {
    //获取任务配置列表
    getList() {
      this.loading = true;
      this.$api.softwareSystemConfig
        .taskConfigPage({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: {
            ...this.filterParam,
          },
        })
        .then(({ data: { list, total } }) => {
          this.tableData = list || [];
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    onSwitch(row) {
      this.$api.softwareSystemConfig.taskConfigtToggle(row.id).then((res) => {
        if (row.isActive == 1) {
          this.$notify.success({
            title: "成功",
            message: "启用成功",
          });
        } else if (row.isActive == 0) {
          this.$notify.success({
            title: "成功",
            message: "禁用成功",
          });
        }
        this.getList();
      });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.filterParam = {};
      this.onSearch();
    },
    //搜索
    onSearch() {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      this.pageNum = 1;
      this.getList();
    },
    //新增
    handleAdd() {
      this.title = "新增";
      this.visible = true;
      this.editForm = {};
    },
    //编辑
    handleEdit(row) {
      this.title = "编辑";
      this.visible = true;
      this.editForm = {
        id: row.id || "",
        otherPlanName: row.otherPlanName || "",
        roleId: row.roleId || "",
        comments: row.comments || "",
        type:String(row.type) || '1',
        wageItems: row.wageItems&&row.wageItems.filter((item) => item) || [],
      };
    },
    downloadExcel(fileData, fileName) {
      const name = fileName + ".xlsx";
      const url = window.URL.createObjectURL(
        new Blob([fileData], { type: "application/vnd.ms-excel" })
      );
      const link = document.createElement("a");
      link.style.display = "none";
      link.href = url;
      link.setAttribute("download", name);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      setTimeout(() => {
        URL.revokeObjectURL(url);
      });
    },

    // 模板下载
    downLoad(v, val) {
      this.$api.softwareSystemConfig
        .taskConfigDownload({
          id: val.id,
        })
        .then((res) => {
          this.downloadExcel(res.data, val.otherPlanName);
        });
    },
    handleCancel(type) {
      this.visible = false;
      if (type == "cancel") return;
      this.getList();
    },
    addDataCancel() {
      this.isVisible = false;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.pageNum = val;
      this.getList();
    },
  },
};
</script>
<style>
.el-tooltip__popper {
  max-width: 30% !important;
}
</style>
<style lang="stylus" scoped>
>>> .el-checkbox {
  display: block;
}

.dialog-title {
  margin: 0px 0 20px 0;
  // height: 26px;
  // background-color: #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;

  // text-indent: 20px;
  .left {
    width: 49%;
    display: flex;
    align-items: center;
  }

  .right {
    width: 49%;
  }
}

.content {
  display: flex;

  .left-content, .right-content {
    width: 45%;
    border: 1px solid #eee;
    padding: 14px;
    height: 300px;
    border-radius: 8px;
    overflow: hidden;
    overflow-y: scroll;
  }

  .right-content {
    margin-left: 20px;
    font-size: 14px;

    .haveInfo {
      line-height: 20px;
    }
  }
}

.employee_info {
  padding: 5px;
  border: 1px solid #ccc;
  margin: 10px 0;
}

// .el-form--label-left {
// .el-form-item {
// margin-right: 16px;
// }

// >>>.el-form-item__label {
// text-align: right !important;
// }
// }
.title {
  font-weight: bold;

  // text-indent: 12px;
  // margin-bottom: 10px;
  .tips {
    color: #00b891;
    font-weight: normal;
    display: inline-block;
    margin-left: 10px;
  }
}

.infoLabel {
  width: 70px;
}

.infoContent {
  width: 200px;
  height: 26px;
  line-height: 26px;
}

// >>>.el-input__inner {
// height: 26px;
// }
.save {
  color: #f59a23;
  cursor: pointer;
  margin-left: 15px;
}

.add {
  height: 40px;
  line-height: 40px;
  text-align: right;
  margin-right: 45px;
  color: #00b891;
  cursor: pointer;
}

>>>.employee_info {
  .el-input {
    width: 200px;
  }
}

.allocated {
  >>>.el-input-group__append {
    background: #24c69a;

    .el-button {
      color: #fff;
    }
  }
}

.edit-active {
  background: #ff6b31;
  border-color: #ff6b31;
}
</style>
