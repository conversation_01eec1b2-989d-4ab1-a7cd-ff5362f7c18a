<template>
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form  :inline="true" :model="searchForm" ref="searchForm" label-width="110px" size="mini">
          <el-form-item label="排程日期:" prop="planDate">
            <el-date-picker
              class="date-input1"
              v-model="searchForm.planDate"
              :default-time="['00:00:00', '23:59:59']"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="生产订单号:" prop="orderNo" >
            <el-input v-model="searchForm.orderNo" clearable></el-input>
          </el-form-item>
          <el-form-item label="工厂:" prop="factoryCode">
            <el-select v-model="searchForm.factoryCode" clearable @change="onSearch">
              <el-option v-for="item in factoryOptions"
                :key="item.factoryCode"
                :label="item.factoryCode + item.storeShortName"
                :value="item.factoryCode">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="工序:" prop="processCode">
            <el-select v-model="searchForm.processCode" clearable @change="onSearch">
              <el-option v-for="item in procedureOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="物料编码描述:" prop="materialCode">
            <el-input class="form-input" v-model="searchForm.materialCode" clearable></el-input>
          </el-form-item>
          <el-form-item label="成品编码描述:" prop="productCode">
            <el-input class="form-input" v-model="searchForm.productCode" clearable></el-input>
          </el-form-item>
          <el-form-item label="复选框条件:" prop="checkbox1">
            <el-checkbox-group class="search-checkbox" v-model="searchForm.checkbox1">
              <el-checkbox v-for="item in checkbox1Options" :label="item.value" :key="item.value">{{item.label}}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="单选条件:" prop="radio1">
            <el-radio-group class="search-radio" v-model="searchForm.radio1">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <div>
            <el-form-item label="复选框条件:" prop="checkbox2">
              <el-checkbox-group v-model="searchForm.checkbox2">
                <el-checkbox v-for="item in checkbox2Options" :label="item.value" :key="item.value">{{item.label}}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="国籍:" prop="radio2">
              <el-radio-group v-model="searchForm.radio2">
                <el-radio v-for="item in checkbox2Options" :label="item.value" :key="item.value">{{item.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary" @click="onSearch">查询</el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">重置</el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-right>
        <el-button type="primary" @click="handleAdd()">新增</el-button>
        <el-button type="warning" size="small" @click="handleDelete()" :disabled="!selection.length">删除</el-button>
        <el-button type="primary" size="small" @click="handleExport">导出</el-button>
      </template>
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :data="tableData"
        :height="maxTableHeight"
        style="width: 100%"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="40" fixed="left"></el-table-column>
        <el-table-column prop="planDate" label="排程日期" width="100" fixed="left" align="center">
          <template slot-scope="scope">
            {{scope.row.planDate | shortDate}}
          </template>
        </el-table-column>
        <el-table-column prop="scheduleNumber" label="排程单号" width="120" ></el-table-column>
        <el-table-column prop="factoryCode" label="工厂" width="55" align="center"></el-table-column>
        <el-table-column prop="processCode" label="工序" width="50" align="center"></el-table-column>
        <el-table-column prop="orderNo" label="生产订单号" width="120" ></el-table-column>
        <el-table-column prop="scheduleNumber" label="排程单号" width="120" ></el-table-column>
        <el-table-column prop="factoryCode" label="工厂" width="55"></el-table-column>
        <el-table-column prop="processCode" label="工序" width="50"></el-table-column>
        <el-table-column prop="orderNo" label="生产订单号" width="120" ></el-table-column>
        <el-table-column prop="scheduleNumber" label="排程单号" width="120" ></el-table-column>
        <el-table-column prop="factoryCode" label="工厂" width="55"></el-table-column>
        <el-table-column prop="processCode" label="工序" width="50"></el-table-column>
        <el-table-column prop="orderNo" label="生产订单号" width="120" ></el-table-column>
        <el-table-column prop="productCode" label="成品编码" width="100" show-overflow-tooltip></el-table-column>
        <el-table-column prop="productName" label="成品描述" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="materialCode" label="物料编码" show-overflow-tooltip></el-table-column>
        <el-table-column prop="materialName" label="物料描述" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="originalMaterialCount" label="原定额量" width="100" align="center"></el-table-column>
        <el-table-column prop="materialCount" label="发板量" width="100" align="center"></el-table-column>
        <el-table-column prop="status" label="操作" width="110" class-name="action-column" fixed="right">
          <template v-slot="scope">
            <el-button slot="reference" type="text" size="small" @click="handleAdd(scope.row)">修改</el-button>
            <el-button slot="reference" type="text" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div style="text-align: right;">
          <el-pagination
            @size-change="onSizeChange"
            @current-change="onNumChange"
            :current-page="pageNum"
            :page-size="pageSize"
            :total="total"
            :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>
        </div>
      </template>
    </table-panel>

    <crud-edit
      v-if="isShowAdd"
      :visible.sync="isShowAdd"
      :modifyData="modifyData"
      @success="getList"
    />
  </content-panel>
</template>

<script>
import crudEdit from './crud-edit.vue'
import tableMixin from '@/utils/tableMixin';
import pagePathMixin from "@/utils/page-path-mixin";

export default {
  name: 'crudList',
  mixins: [tableMixin,pagePathMixin],
  components: {
    crudEdit
  },
  data() {
    return {
      searchForm: {
        productCode: '',
        materialCode: '',
        factoryCode: '',
        orderNo: '',
        scheduleNumber: '',
        processCode: '',
        planDate: [],
        checkbox1: [],
        checkbox2: [],
        radio1: '',
        radio2: ''
      },

      // 表格相关
      tableData: [],
      selection: [],
      pageSize: 50,
      pageNum: 1,
      total: 0,
      filterParam: {},
      loading: false,
      schedulePlanExtraId: '',
      factoryOptions: [],
      procedureOptions: [
        {value: 10, label: '10'},
        {value: 11, label: '11'},
        {value: 15, label: '15'}
      ],
      isShowAdd: false,
      modifyData: null,
      checkbox1Options: [
        {label: '选项1', value: 1},
        {label: '选项2', value: 2},
        {label: '选项3', value: 3},
      ],
      checkbox2Options: [
        {label: '中国', value: 1},
        {label: '俄罗斯', value: 2},
        {label: '巴勒斯坦', value: 3},
        {label: '英国', value: 4},
        {label: '阿尔及利亚', value: 5},
      ]
    }
  },
  methods: {
    init() {
      // this.getFactoryList();
      this.getList();
    },
    getFactoryList() {
      return this.$store.dispatch('user/getUserFactoryList')
        .then(factoryList => {
          this.factoryOptions = factoryList;
        });
    },
    getList() {
      const tableData = [];
      for (let i = 0; i < 50; i++) {
        tableData.push({
          id: i,
          "scheduleNumber" : "202107026180",
          "factoryCode" : "6180",
          "processCode" : "11",
          "planDate" : "2021-07-02T08:04:24.000+0000",
          "planDateStr" : "2021-07-02",
          "orderNo" : "109325476",
          "productCode" : "66162100203",
          "productName" : "61621二门书柜左233#哑4-3侧板",
          "originalMaterialCode" : "101000003",
          "originalMaterialName" : "5mm中纤板2440*1220mm",
          "originalMaterialCount" : 8.156,
          "materialCode" : "101000003",
          "materialName" : "5mm中纤板2440*1220mm",
          "materialCount" : 8.156,
          "materialCount3" : 8.156,
          "replaceFlg" : null
        });
      }
      this.tableData = tableData;
      return;


      this.loading = true;
      const params = {
        pageNo: this.pageNum,
        pageSize: this.pageSize,
        data: {
          ...this.filterParam
        }
      }
      this.$api.replaceMgt.getReplacedPanelList(params)
        .then(({data: {records, total}}) => {
          this.tableData = records || [];
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleCurrentChange(row) {
      this.selection = row;
    },
    onSizeChange(pageSize) {
      this.pageSize = pageSize;
      // 切换每页条数后从第一页查询
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    handleSelectionChange(selection) {
      this.selection = selection;
    },
    handleExport() {
      let params = {
        ...this.filterParam
      };
      this.$api.common.doExport('issueComparisonDataExport', params).then(() => {
        this.$message.success('导出操作成功，请前往导出记录查看详情');
      });
    },
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.filterParam = {};
      this.onSearch();
    },
    onSearch() {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (key === 'planDate' && val) {
          this.filterParam.planDateStart = val[0] || '';
          this.filterParam.planDateEnd = val[1] || '';
        } else if (typeof val !== 'undefined' && val !== null && val !== '') {
          this.filterParam[key] = val;
        }
      }
      this.pageNum = 1;
      this.getList();
    },
    handleAdd(row) {
      this.modifyData = row || null;
      this.isShowAdd = true;
    },
    handleDelete(row) {
      const params = row ? [row.id] : this.selection.map(item => item.id);
      if (!params.length) {
        return;
      }

      this.$confirm('将删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.$api.boardRule
            .deleteRule(params)
            .then(({data}) => {
              this.$notify.success({
                title: '成功',
                message: '删除成功',
              });
              this.getList();
            })
            .finally(() => {
              this.loading = false;
            });
        })
        .catch(() => {});
    },
  },
  created() {
    this.init();
  }
}
</script>

<style lang="stylus" scoped>
.search-checkbox
  .el-checkbox
    margin-right 10px
.search-radio
  width 200px
</style>