import Vue from 'vue'
import moment from 'moment'
import 'moment/locale/zh-cn'
moment.locale('zh-cn')
import {formatNumber,moneyFormat} from './index'

Vue.filter('NumberFormat', function (value) {
    if (!value) {
        return '0'
    }
    const intPartFormat = value.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,') // 将整数部分逢三一断
    return intPartFormat
})

Vue.filter('dateFormat', function (dataStr, pattern = 'YYYY-MM-DD HH:mm:ss') {
    if(!dataStr){
      return '';
    }
    return moment(dataStr).format(pattern)
})
Vue.filter('dateFormats', function (dataStr, pattern = 'YYYY-MM-DD') {
    if(!dataStr){
      return '';
    }
    return moment(dataStr).format(pattern)
})

Vue.filter('shortDate', function (dataStr) {
  if(!dataStr){
    return '';
  }
  return moment(dataStr).format('YYYY-MM-DD')
})

Vue.filter("decode", function(data) {
  if (!data) {
    return '';
  }
  return decodeURIComponent(data)
})

Vue.filter('checkVailidNum', function(val) {
  if (+val === NaN || val === null || val === '') {
    return 0;
  }
  return val;
})

Vue.filter('currency', function(val) {
  if (!val) {
    return '0';
  }
  return formatNumber(val);
})
Vue.filter('moneyFormat', function(val) {
  if (!val) {
    return '0';
  }
  return moneyFormat(val);
})
