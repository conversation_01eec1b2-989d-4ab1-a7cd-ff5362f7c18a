<template>
  <!-- 新/熟手补贴名单管理 -->
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          size="mini"
          :inline="true"
          :model="searchForm"
          label-width="103px"
          ref="searchForm"
          label-position="right"
        >
          <el-form-item label="应聘人姓名:" prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="应聘人姓名"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="应聘岗位:" prop="post">
            <el-input
              v-model.trim="searchForm.post"
               clearable
               placeholder="请输入应聘岗位"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="补贴类型:" prop="subsidyType">
            <el-select
              v-model="searchForm.subsidyType"
              filterable
              clearable
              placeholder="请选择补贴类型"
              @change="onSearch"
            >
              <el-option
                v-for="item in subsidy"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="熟练程度:"
            prop="masteryLevel"
            v-if="searchForm.subsidyType != '无补贴'"
          >
            <el-select
              v-model="searchForm.masteryLevel"
              filterable
              clearable
              placeholder="请选择熟练程度"
              @change="onSearch"
            >
              <el-option
                v-for="item in skilledList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="组织路径:" prop="orgPath">
            <el-input v-model="searchForm.orgPath" placeholder="请输入组织路径">
            </el-input>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button
            size="small"
            type="primary"

            @click="onSearch"
          >
            查询
          </el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-left>
        <el-tabs v-model="processState">
          <el-tab-pane
            v-for="item in items"
            :key="item.name"
            :name="item.value"
          >
            <span slot="label">{{
              item.number != null ? `${item.name}(${item.number})` : item.name
            }}</span>
          </el-tab-pane>
        </el-tabs>
      </template>
      <template v-slot:header-right>
        <div ref="btnRight">
          <el-button
            size="small"
            type="primary"
            @click="handleAdd"
            v-show="processState == '0'"
          >
            新增
          </el-button>
          <el-button
            size="small"
            type="primary"
            @click="handleExport"
            v-show="processState == '0'"
          >
            导出
          </el-button>
          <el-button
            size="small"
            type="primary"
            @click="handleImport"
            v-show="processState == '0'"
          >
            导入
          </el-button>

        </div>
      </template>
      <!-- :key="tableKey" -->
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :height="maxTableHeight"
        :data="tableData"
        :row-key="getRowKeys"
      >
        <el-table-column
          prop="startDate"
          label="报名日期"
          width="100"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="staffName"
          label="应聘人姓名"
          width="100"
          align="left"
        >
        </el-table-column>
        <el-table-column
          prop="staffCode"
          label="厂牌编号"
          width="110"
          align="left"
        >
        </el-table-column>
        <el-table-column
          prop="idCard"
          label="应聘人身份证号"
          width="135"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="post"
          label="应聘岗位"
          width="80"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="orgAbbr"
          label="应聘组织"
          width="100"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="orgPath"
          label="应聘组织全路径"
          width="120"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="subsidyType"
          label="补贴类型"
          width="90"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="masteryLevel"
          label="熟手程度"
          width="80"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="curOrgPath"
          label="现部门全路径"
          width="120"
          align="left"
          show-overflow-tooltip
        >
         <template slot-scope="{ row }">
            {{ row.curOrgPath | filterData }}
          </template>
        </el-table-column>
        <el-table-column
          prop="curProcess"
          label="现工序"
          width="110"
          align="left"
          show-overflow-tooltip
        >
            <template slot-scope="{ row }">
            {{ row.curProcess | filterData }}
          </template>
        </el-table-column>
        <el-table-column
          prop="curSubProcess"
          label="现子工序"
          width="110"
          align="left"
          show-overflow-tooltip
        >
         <template slot-scope="{ row }">
            {{ row.curSubProcess | filterData }}
          </template>
        </el-table-column>
        <el-table-column
          prop="curShiftGroup"
          label="现班组"
          width="110"
          align="left"
          show-overflow-tooltip
        >
         <template slot-scope="{ row }">
            {{ row.curShiftGroup | filterData }}
          </template>
        </el-table-column>
        <el-table-column
          prop="startMonth"
          label="开始月份"
          width="80"
          align="left"
        >
        </el-table-column>
        <el-table-column
          prop="endMonth"
          label="截止月份"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          v-if="processState == '1'"
          prop="actualEndMonth"
          label="结束月份"
          width="150"
          align="left"
        >
        </el-table-column>
        <el-table-column
          prop="acceptTime"
          label="接收时间"
          width="150"
          align="left"
        >
        </el-table-column>
        <el-table-column width="130" align="left" fixed="right" label="操作">
          <template slot-scope="scope">
            <el-button
              v-show="processState == '0'"
              style="margin-left: 0"
              size="small"
              type="text"
              @click="handleEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              v-show="processState == '1'"
              style="margin-left: 0"
              size="small"
              type="text"
              @click="handleDetails(scope.row)"
            >
              查看详情
            </el-button>
            <el-button
              v-show="processState == '0' && scope.row.actionType == 'manual'"
              style="margin-left: 0"
              size="small"
              type="text"
              @click="handleDelete(scope.$index, scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div style="text-align: right">
          <el-pagination
            @size-change="onSizeChange"
            @current-change="onNumChange"
            :current-page="pageNum"
            :page-size="pageSize"
            :total="total"
            :page-sizes="[10, 30, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
          >
          </el-pagination>
        </div>
      </template>
      <!-- 新增-->
      <add-cost
        v-if="visible"
        :isVisible="visible"
        @cancel="handleCancel"
        :title="title"
      ></add-cost>
      <!-- 查看详情 -->
      <details-popup
        v-if="isVisibleDteails"
        :visible="isVisibleDteails"
        :formData="editData"
        @cancel="detailsCancel"
      ></details-popup>
      <!-- 编辑 -->
      <edit-popup
        v-if="isvisible"
        :visible="isvisible"
        :formData="formData"
        :title="title"
        @cancel="editCancel"
      ></edit-popup>
      <Import
        v-if="importVisible"
        :visible="importVisible"
        @cancel="cancel"
        @confirm="confirm"
        :importInfo="importInfo"
      />
    </table-panel>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import moment from "moment";
import addCost from "./component/addCost";
import detailsPopup from "./component/detailsPopup";
import editPopup from "./component/editPopup";
export default {
  name: "SoftwareNewEmployeeSubsidies",
  mixins: [tableMixin,pagePathMixin],
  components: { addCost, detailsPopup, editPopup },
  data() {
    return {
      searchForm: {
        post: "",
        subsidyType: "",
        masteryLevel: "",
        orgPath: "",
        factoryId: "",
        staffCode: "",
        staffName: "",
        amountRangeList: "",
      },
      editData: {},
      formData: {},
      isVisibleDteails: false,
      selection: [],
      items: [
        {
          name: "已处理",
          value: "0",
          type: "processedTotal",
          number: 0,
        },
        {
          name: "已完成",
          value: "1",
          type: "finishedTotal",
          number: 0,
        },
      ],
      processState: "0",
      filterParam: {},
      params: {},
      importInfo: {
        reportName: "softwareNewStaffSubsidyListImport",
        paramMap: {
          columnValue: "定制-新/熟手补贴名单",
        },
      },
      //表格数据
      tableData: [],
      loading: false,
      visible: false,
      isvisible: false,
      importVisible: false,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      title: "",
      subsidy: Object.freeze([
        {
          name: "新手补贴",
          value: "新手补贴",
        },
        {
          name: "新员工补贴",
          value: "新员工补贴",
        },
        {
          name: "熟手补贴",
          value: "熟手补贴",
        },
        {
          name: "无补贴",
          value: "无补贴",
        },
      ]),
    };
  },
  filters: {
    filterData(value) {
       const chineseRegex = /[\u4e00-\u9fa5]/;
        
        // 如果传入的不是字符串或字符串为空，直接返回空字符串
        if (typeof value !== 'string' || value.length === 0) {
            return '';
        }
        
        // 如果包含中文字符则返回原值，否则返回空字符串
        return chineseRegex.test(value) ? value : '';
    },
  },
  computed: {
    skilledList() {
      this.searchForm.masteryLevel = "";
      let name = this.searchForm.subsidyType;
      if (name == "新手补贴" || name == "新员工补贴") {
        return [{ name: "生手", value: "生手" }];
      }
      if (name == "熟手补贴") {
        return [
          { name: "熟手A", value: "熟手A" },
          { name: "熟手B", value: "熟手B" },
          { name: "标准", value: "标准" },
        ];
      }
    },
  },

  watch: {
    $route: {
      handler(value) {
        if (
          value.path.includes("newEmployeeSubsidies") &&
          value.path.includes("software")
        ) {
          this.onSearch();
        }
      },
      deep: true,
      immediate: true,
    },
    processState: {
      handler() {
        this.idList = [];
        this.getList();
        this.getStatistics();
      },
    },
  },
  methods: {
    getRowKeys(row) {
      return row.id;
    },
    //新手补贴列表
    getList() {
      this.loading = true;
      this.tableData = [];
      this.$api.softwareInformation.newEmployeeSubsidies
        .newstaffList({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: {
            processState: this.processState,
            ...this.filterParam,
            ...this.params,
          },
        })
        .then(({ data: { list, total } }) => {
          this.tableData = list || [];
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //获取统计信息
    async getStatistics() {
      let params = {
        processState: this.processState,
        ...this.filterParam,
        ...this.params,
      };
      const list =
        await this.$api.softwareInformation.newEmployeeSubsidies.newstafftotal(params);
      const data = list.data || {};
      if (Object.keys(data).length != 0) {
        this.items = this.items.map((item) => {
          for (const key in data) {
            if (item.type == key) {
              item.number = this.getTotal(list.data[item.type]);
            }
          }
          return item;
        });
      } else {
        this.items = [
          {
            name: "已处理",
            value: "0",
            type: "processedTotal",
            number: 0,
          },
          {
            name: "已完成",
            value: "1",
            type: "finishedTotal",
            number: 0,
          },
        ];
      }
    },
    getTotal(num) {
      if (isNaN(Number(num))) return 0;
      return Number(num) > 9999 ? "9999+" : num;
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0)
          this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
      this.getStatistics();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) {
          this.params[name] = data;
        }
      }
    },
    //新增
    handleAdd() {
      this.title = "新增";
      this.visible = true;
    },
    //编辑
    handleEdit(row) {
      this.formData = {
        startDate: row.startDate,
        staffName: row.staffName,
        staffCode: row.staffCode,
        idCard: row.idCard,
        post: row.post,
        curProcess: row.curProcess,
        curSubProcess: row.curSubProcess,
        curShiftGroup: row.curShiftGroup,
        orgPath: row.orgPath,
        curOrgPath: row.curOrgPath,
        subsidyType: row.subsidyType,
        masteryLevel: row.masteryLevel,
        id: row.id,
        orgAbbr: row.orgAbbr || "",
      };
      this.title = "编辑";
      this.isvisible = true;
    },
    //查看详情
    handleDetails(row) {
      this.editData = {
        startDate: row.startDate,
        staffName: row.staffName,
        staffCode: row.staffCode,
        idCard: row.idCard,
        post: row.post,
        curProcess: row.curProcess,
        curSubProcess: row.curSubProcess,
        curShiftGroup: row.curShiftGroup,
        orgPath: row.orgPath,
        curOrgPath: row.curOrgPath,
        subsidyType: row.subsidyType,
        masteryLevel: row.masteryLevel,
        id: row.id,
        orgAbbr: row.orgAbbr || "",
      };
      this.isVisibleDteails = true;
    },
    detailsCancel() {
      this.isVisibleDteails = false;
    },
    editCancel(obj) {
      const { type, isVisible } = obj;
      this.isvisible = isVisible;
      this.cancellation(type);
    },
    //导入
    handleImport() {
      this.importVisible = true;
    },
    //导出
    handleExport() {

      let params = {
        ...this.filterParam,
        ...this.params,
        processState: this.processState
      };
      this.$api.common.doExport("softwareNewStaffSubsidyListExport", params).then(() => {
        this.$message.success("导出操作成功，请前往导出记录查看详情");
      });
    },
    //删除
    handleDelete(index, row) {
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$api.softwareInformation.newEmployeeSubsidies
          .newstaffdelete({
            id: row.id,
          })
          .then(() => {
            this.$notify.success({
              title: "成功",
              message: "删除成功",
            });
            this.getList();
            this.getStatistics();
          });
      });
    },
    cancellation(type) {
      if (type && type == "cancel") return;
      this.getList();
      this.getStatistics();
    },
    handleCancel(obj) {
      const { type, isVisible } = obj;
      this.visible = isVisible;
      this.cancellation(type);
    },
    calculateCancel(obj) {
      const { type, isVisible } = obj;
      this.calculateVisible = isVisible;
      this.cancellation(type);
    },
    backCancel(obj) {
      this.$refs.tableRef.clearSelection();
      const { type, isVisible } = obj;
      this.backVisible = isVisible;
      this.cancellation(type);
    },
    cancel(value) {
      this.importVisible = value;
    },
    confirm(value) {
      this.importVisible = value;
      this.getList();
    },
    onSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(val) {
      this.pageNum = val;
      this.getList();
    },
  },
};
</script>

<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}
.select{
  background:#0bb78e
  color:#fff
}
>>>.el-checkbox.is-bordered.el-checkbox--mini{
    height:25px !important
}
>>>.el-checkbox__input.is-checked+.el-checkbox__label{
    color:white !important
}
>>>.el-checkbox__input.is-checked .el-checkbox__inner{
   border-color:white !important
}
</style>
