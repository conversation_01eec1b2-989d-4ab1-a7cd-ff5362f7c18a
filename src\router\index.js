import Vue from "vue";
import VueRouter from "vue-router";
import Layout from "@/layout";
import softwareRouter from "./software-router";
import plateTypeRouter from "./plateType-router";
import logisticsRouter from "./logistics-router";
Vue.use(VueRouter);

/**
 * Note: sub-menu only appear when route children.length >= 1
 *
 * hidden: true                  菜单栏是否显示，默认false
 * alwaysShow: true              是否总是显示一级节点，默认false，子节点大于2时才显示，只有一个子节点时，子节点占位
 * redirect: noRedirect          配置为noRedirect ，面包屑中不能跳转
 * name:'router-name'            the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    breadcrumb: false            配置为false，不会出现在面包屑中，默认true
    activeMenu: '/example/list'  默认选中的菜单
  }
 */

/**
 * 导航要出现在标签栏，需要配置:
 * 1.路由配置name属性，且对应组件增加name属性，二者必须一致，通常首字母大写
 * 2.路由meta中配置title属性，affix表示是否固定在标签栏
 */

// 解决编程式路由往同一地址跳转时会报错的情况
const originalPush = VueRouter.prototype.push;
const originalReplace = VueRouter.prototype.replace;

// push
VueRouter.prototype.push = function push(location, onResolve, onReject) {
  if (onResolve || onReject)
    return originalPush.call(this, location, onResolve, onReject);
  return originalPush.call(this, location).catch((err) => err);
};

//replace
VueRouter.prototype.replace = function push(location, onResolve, onReject) {
  if (onResolve || onReject)
    return originalReplace.call(this, location, onResolve, onReject);
  return originalReplace.call(this, location).catch((err) => err);
};

export const constantRoutes = [
  {
    path: "/redirect",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "/redirect/:path*",
        component: () => import("@/views/redirect/index"),
      },
    ],
  },
  {
    path: "/login",
    component: () => import("@/views/login/index"),
    hidden: true,
    meta: {
      title: "登录",
    },
  },
];

// 需要通过权限控制菜单时，过滤asyncRoutes，动态加载，当前未使用权限
export const asyncRoutes = [
  {
    path: "/",
    component: Layout,
    redirect: "/home",
    children: [
      {
        name: "Home",
        path: "home",
        component: () => import("@/views/home/<USER>"),
        meta: {
          title: "首页",
        },
      },
    ],
  },
  {
    path: "/customized/workbench",
    component: Layout,
    redirect: "/customized/workbench/overview",
    meta: {
      title: "工作台",
    },
    children: [
      {
        name: "AgencyTask",
        path: "overview",
        component: () => import("@/views/overview/agencyTask"),
        meta: {
          title: "待办任务",
        },
      },
      {
        name: "TaskDetails",
        path: "taskDetails",
        component: () => import("@/views/overview/taskDetails"),
        meta: {
          title: "任务明细",
        },
      },
      {
        name: "Adjustment",
        path: "adjustment",
        component: () => import("@/views/overview/adjustment/adjustment"),
        meta: {
          title: "分厂调整",
        },
      },
      {
        name: "Upload",
        path: "upload",
        component: () => import("@/views/overview/upload/upload"),
        meta: {
          title: "个税扣款",
        },
      },
      {
        name: "CustomizedPayroll",
        path: "payroll",
        component: () => import("@/views/overview/payroll/payroll"),
        meta: {
          title: "工资表",
        },
      },
      // {
      //   name: "PayrollDetails",
      //   path: "payrollDetails",
      //   component: () => import("@/views/overview/summary/detailed"),
      //   meta: {
      //     title: "工资表明细",
      //   },
      // },
    ],
  },
  {
    path: "/customized/account",
    component: Layout,
    redirect: "/customized/account/remaining",
    meta: {
      title: "信息台账",
    },
    children: [
      {
        name: "Remaining",
        path: "remaining",
        component: () => import("../views/account/remaining/ocale/ocale"),
        meta: {
          title: "余留管理",
        },
      },
      {
        name: "RemainingDetailed",
        path: "remainingDetailed",
        component: () => import("../views/account/remaining/remaining"),
        meta: {
          title: "余留调整明细",
        },
      },
      {
        name: "Debit",
        path: "debit",
        component: () => import("@/views/account/debit/debit"),
        meta: {
          title: "借支台账",
        },
      },
      {
        name: "DebitList",
        path: "debitList",
        component: () => import("@/views/account/debitList"),
        meta: {
          title: "借支清单",
        },
      },
      {
        name: "Reward",
        path: "reward",
        component: () => import("@/views/account/reward/reward"),
        meta: {
          title: "奖惩台账",
        },
      },
      {
        name: "RewardList",
        path: "rewardList",
        component: () => import("@/views/account/rewardList"),
        meta: {
          title: "奖惩清单",
        },
      },
      {
        name: "Payroll",
        path: "specialPayrollMgent",
        component: () =>
          import("@/views/account/payrollManagement/payrollManagement"),
        meta: {
          title: "特殊工资单管理",
        },
      },
      {
        name: "SpecialPayroll",
        path: "specialPayroll",
        component: () => import("@/views/account/specialPayroll"),
        meta: {
          title: "特殊工资单查看",
        },
      },
      {
        name: "Compensation",
        path: "compensation",
        component: () =>
          import("@/views/account/costCompensation/costCompensation"),
        meta: {
          title: "成本赔偿扣款台账",
        },
      },
      {
        name: "UnpaidLedger",
        path: "unpaidLedger",
        component: () => import("@/views/account/unpaidLedger/unpaidLedger"),
        meta: {
          title: "未付款台账",
        },
      },
      {
        name: "SalarySearch",
        path: "salarySearch",
        component: () => import("@/views/account/salarySearch"),
        meta: {
          title: "工资查询",
        },
      },

      //返工扣款明细
      {
        name: "reworkdeDuction",
        path: "reworkdeDuction",
        component: () => import("@/views/account/reworkdeDuction"),
        meta: {
          title: "返工扣款明细",
        },
      },
      //新员工补贴20%扣款
      {
        name: "newemployeeSubsidyAccount",
        path: "newemployeeSubsidy",
        component: () =>
          import("@/views/account/newemployeeSubsidy/newemployeeSubsidy"),
        meta: {
          title: "新员工补贴20%扣款",
        },
      },
      //新/熟手补贴名单管理
      {
        name: "newEmployeeSubsidies",
        path: "newEmployeeSubsidies",
        component: () =>
          import("@/views/account/newEmployeeSubsidies/newEmployeeSubsidies"),
        meta: {
          title: "新/熟手补贴名单管理",
        },
      },
    ],
  },
  {
    path: "/customized/upload",
    component: Layout,
    redirect: "/customized/upload/attendance",
    meta: {
      title: "数据上传",
    },
    children: [
      {
        name: "Attendance",
        path: "attendance",
        component: () => import("@/views/upload/attendance/attendance"),
        meta: {
          title: "员工考勤",
        },
      },
      {
        name: "Wage",
        path: "wage",
        component: () => import("@/views/upload/wage/wage"),
        meta: {
          title: "计件工资",
        },
      },
      {
        name: "Collective",
        path: "collective",
        component: () => import("@/views/upload/collective"),
        meta: {
          title: "集体账户",
        },
      },
      {
        name: "Subsidy",
        path: "subsidy",
        component: () => import("@/views/upload/subsidy/subsidy"),
        meta: {
          title: "其他补贴",
        },
      },
      {
        name: "Deduction",
        path: "deduction",
        component: () => import("@/views/upload/deduction/deduction"),
        meta: {
          title: "其他扣款",
        },
      },
      {
        name: "Security",
        path: "security",
        component: () => import("@/views/upload/security/security"),
        meta: {
          title: "社保扣款",
        },
      },
      {
        name: "Puncandeduct",
        path: "puncandeduct",
        component: () => import("@/views/upload/puncandeduct/puncandeduct"),
        meta: {
          title: "未打卡扣款",
        },
      },
      {
        name: "Unionfee",
        path: "unionfee",
        component: () => import("@/views/upload/unionfee/unionfee"),
        meta: {
          title: "工会费",
        },
      },
      {
        name: "Companysubsidies",
        path: "companysubsidies",
        component: () => import("@/views/upload/companysubsidies"),
        meta: {
          title: "公司补贴",
        },
      },
      {
        name: "Housingsubsidies",
        path: "housingsubsidies",
        component: () => import("@/views/upload/housingsubsidies"),
        meta: {
          title: "住房补贴",
        },
      },
      {
        name: "Factoryservicededuction",
        path: "factoryservicededuction",
        component: () => import("@/views/upload/factoryservicededuction"),
        meta: {
          title: "厂服扣款",
        },
      },
      {
        name: "Factorycarddeduction",
        path: "factorycarddeduction",
        component: () => import("@/views/upload/factorycarddeduction"),
        meta: {
          title: "厂牌扣款",
        },
      },
      {
        name: "Lifefei",
        path: "lifefei",
        component: () => import("@/views/upload/lifefei"),
        meta: {
          title: "生活费",
        },
      },
      {
        name: "Physicalexamination",
        path: "physicalexamination",
        component: () =>
          import("@/views/upload/physicalexamination/physicalexamination"),
        meta: {
          title: "体检费",
        },
      },
      {
        name: "CostcompensationList",
        path: "costcompensationList",
        component: () => import("@/views/upload/costcompensationList"),
        meta: {
          title: "成本赔偿清单",
        },
      },
      {
        name: "Costcompensation",
        path: "costcompensation",
        component: () => import("@/views/upload/costcompensation"),
        meta: {
          title: "成本赔偿",
        },
      },
      {
        name: "Lowconsumptiongoods",
        path: "lowconsumptiongoods",
        component: () => import("@/views/upload/lowconsumptiongoods"),
        meta: {
          title: "低耗品",
        },
      },
      {
        name: "Reworkdeduction",
        path: "reworkdeduction",
        component: () =>
          import("@/views/upload/reworkdeduction/reworkdeduction"),
        meta: {
          title: "返工扣款",
        },
      },
      {
        name: "Miscellaneous",
        path: "miscellaneous",
        component: () => import("@/views/upload/miscellaneous/miscellaneous"),
        meta: {
          title: "杂工考勤",
        },
      },
      {
        name: "environmental",
        path: "environmental",
        component: () => import("@/views/upload/environmental/environmental"),
        meta: {
          title: "环境补贴",
        },
      },
      {
        name: "ldhandsubsidy",
        path: "ldhandsubsidy",
        component: () => import("@/views/upload/ldhandsubsidy/ldhandsubsidy"),
        meta: {
          title: "熟手补贴",
        },
      },
      {
        name: "newemployeeSubsidy",
        path: "newemployeeSubsidy",
        component: () => import("@/views/upload/newemployeeSubsidy/newemployeeSubsidy"),
        meta: {
          title: "新员工补贴20%扣款",
        },
      },
    ],
  },
  {
    path: "/customized/system",
    component: Layout,
    redirect: "/customized/system/pageconfig",
    meta: {
      title: "系统配置",
    },
    children: [
      {
        name: "PageConfig",
        path: "pageconfig",
        component: () => import("@/views/system-config/pageConfig.vue"),
        meta: {
          title: "表头管理",
        },
      },
      {
        name: "TaskConfig",
        path: "taskconfig",
        component: () => import("@/views/system-config/taskConfig/taskConfig"),
        meta: {
          title: "任务配置",
        },
      },
      {
        name: "Configuration",
        path: "dataconfiguration",
        component: () => import("@/views/system-config/dataConfiguration.vue"),
        meta: {
          title: "数据配置",
        },
      },
      {
        name: "config-uration",
        path: "config-uration",
        component: () =>
          import("@/views/system-config/config-uration/config-uration"),
        meta: {
          title: "环境补贴配置",
        },
      },
      {
        name: "config-oldhand",
        path: "config-oldhand",
        component: () =>
          import("@/views/system-config/config-oldhand/config-oldhand"),
        meta: {
          title: "熟手补贴配置",
        },
      },
      //新手补贴配置
      {
        name: "config-newemployee",
        path: "config-newemployee",
        component: () =>
          import("@/views/system-config/config-newemployee/newemployee"),
        meta: {
          title: "新员工补贴20%扣款",
        },
      },
    ],
  },
  {
    path: "/customized/manage",
    component: Layout,
    redirect: "/customized/manage/employee",
    meta: {
      title: "系统管理",
    },
    children: [
      {
        name: "Employee",
        path: "employee",
        component: () => import("@/views/system-manage/employee/employee"),
        meta: {
          title: "员工管理",
        },
      },
      {
        name: "Largeprocess",
        path: "largeprocess",
        component: () => import("@/views/system-manage/largeprocess/largeprocess"),
        meta: {
          title: "大工序管理",
        },
      },
      {
        name: "BasicInformation",
        path: "basic",
        component: () =>
          import("@/views/system-manage/basic-information/basic-information"),
        meta: {
          title: "班组管理",
        },
      },
      {
        name: "DataPermission",
        path: "data",
        component: () =>
          import("@/views/system-manage/data-permission/data-permission"),
        meta: {
          title: "数据权限",
        },
      },
      {
        name: "SystemLog",
        path: "systemlog",
        component: () => import("@/views/system-manage/system-log.vue"),
        meta: {
          title: "系统日志",
        },
      },
    ],
  },
  {
    path: "/customized/reportManager",
    component: Layout,
    redirect: "/customized/reportManager/summary",
    children: [
      {
        name: "customizedSummary",
        path: "summary",
        component: () => import("@/views/reportManager/summary"),
        meta: {
          title: "财务汇总表",
        },
      },
      {
        name: "customizedSalaryAnalysis",
        path: "salaryAnalysis",
        component: () => import("@/views/reportManager/salaryAnalysis"),
        meta: {
          title: "工资分析表",
        },
      },
      {
        name: "customizedMonthlyDifferentialAttendance",
        path: "monthlyDifferentialAttendance",
        component: () => import("@/views/reportManager/monthlyDifferentialAttendance"),
        meta: {
          title: "月度考勤差异分析报表",
        },
      },
      {
        name: "CustomizedSalaryDifferenceWarningForm",
        path: "salaryDifferenceWarningForm",
        component: () => import("@/views/reportManager/salaryDifferenceWarningForm/salaryDifferenceWarningForm"),
        meta: {
          title: "工资差异预警表",
        },
      },
      {
        name: "CustomizedWageTransferAnalyze",
        path: "wageTransfer-analyze",
        component: () => import("@/views/reportManager/WageTransferAnalyze"),
        meta: {
          title: "工资划拨分析表",
        },

      }, {
        name: "CustomizedManualEfficiencyProcess",
        path: "manualEfficiencyProcess",
        component: () => import("@/views/reportManager/manualEfficiencyProcess/manualEfficiencyProcess"),
        meta: {
          title: "人工效率工序管理",
        },

      }, {
        name: "customizedLaborEfficiency",
        path: "laborEfficiency",
        component: () => import("@/views/reportManager/laborEfficiency"),
        meta: {
          title: "人工效率分析表",
        },

      }
    ],
  },
  {
    path: "/customized/file",
    component: Layout,
    redirect: "/customized/file/import",
    children: [
      {
        name: "ImportFile",
        path: "import",
        component: () => import("@/views/import-export/import-list"),
        meta: {
          title: "导入列表",
        },
      },
      {
        name: "ExportFile",
        path: "export",
        component: () => import("@/views/import-export/export-list"),
        meta: {
          title: "导出列表",
        },
      },
    ],
  },
  ...softwareRouter,
  ...plateTypeRouter,
  ...logisticsRouter,
  {
    path: "*",
    redirect: "/home",
  },
];

const createRouter = () =>
  new VueRouter({
    // mode: 'history', // require service support
    scrollBehavior: () => ({ y: 0 }),
    // routes: constantRoutes
    routes: [...constantRoutes, ...asyncRoutes],
  });

const router = createRouter();

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

export default router;
