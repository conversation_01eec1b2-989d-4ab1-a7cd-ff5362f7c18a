import { local } from './storage'

const pageCache = {};

export const setPageMap = pageMap => local.set('pageMap', pageMap)
export const removePageMap = () => local.remove('pageMap')

export const setPageName = (key, value) => {
  const pageObj = local.get('pageMap') || {};
  
  if (!pageObj.hasOwnProperty(key)) {
    local.set('pageMap', {
      ...pageObj,
      [key]: value
    });
  }
};

export const getPageName = key => {
  const pageObj = local.get('pageMap') || {};
  return pageObj[key];
}

export const getPageNameEncoded = key => {
  let pageName = pageCache[key];

  if (!pageName) {
    pageName = encodeURIComponent(getPageName(key) || '');
    pageCache[key] = pageName;
  }
  
  return pageName;
}

export const removePageName = key => {
  const pageObj = local.get('pageMap') || {};
  delete pageObj[key];
  local.set('pageMap', pageObj);
}