<template>
  <div class="k-v-panel">
    <div class="k-v-panel-key">
      {{label}}:
    </div>
    <div class="k-v-panel-value">
      <div class="k-v-panel-body">
        <slot>{{value}}</slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'KVPanel',
  props: {
    label: String,
    value: {},
    // 无value时隐藏
    hideWhenNoValue: {
      type: Boolean,
      default: true
    }
  }
}
</script>

<style lang="stylus" scoped>
  .k-v-panel
    position relative
    display flex
    box-sizing border-box
    padding 10px 16px
    overflow hidden
    width 100%
    font-size 14px
    color #606266
    &-key
      flex none
      box-sizing border-box
      margin-right 12px
      width 5em
      text-align left
    &-value
      flex 1
      position relative
      overflow hidden
      vertical-align middle
    &-body
      display flex
      align-items center
</style>