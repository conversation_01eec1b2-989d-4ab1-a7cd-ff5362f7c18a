<template>
  <content-panel class="panel-tabs">
    <table-panel>
      <div class="boxWrapper" :style="{ height: boxHeight }">
        <div class="boxTop">
          <el-button size="small" type="primary" @click="isModifyType = true" v-if="!isModifyType">编辑</el-button>
          <template v-else>
            <el-button size="small" type="primary" @click="changeSave" :loading="saveLoading">保存</el-button>
            <el-button size="small" type="warning" @click="changeCancel">取消</el-button>
          </template>
        </div>
        <div class="blockHeader">
          <span>职务补贴</span>
        </div>

        <el-row style="display:flex;align-items: center;">
          <div :span="2"> 补贴标准:</div>
          <el-col :span="4" v-for="(item, index) in tableData.dutySubsidysList" :key="index">
            <div class="item">
              <div class="itemLabel">
                {{item.optionName}}
              </div>
              <div class="itemVal">
                <el-input :disabled="!isModifyType" type="number" v-model="item.optionValue" placeholder="请输入">
                  <template slot="append" >元/天</template>
                </el-input>
              </div>
            </div>
          </el-col>
        </el-row>

        <div class="blockHeader">
          <span>基本工资</span>
        </div>
        <el-row style="display:flex;align-items: center;margin:10px 0px;">
          <div :span="2"> 基本工资:</div>
          <el-col :span="4" v-for="(item, index) in tableData.staffBaseWageList" :key="index">
            <div class="item">
              <div class="itemVal">
                <el-input :disabled="!isModifyType" type="number" v-model="item.optionValue" placeholder="请输入">
                  <template slot="append">元/天</template>
                </el-input>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row style="display:flex;align-items: center">
          <div :span="2">等级系数:</div>
          <el-col :span="3" v-for="(item, index) in tableData.staffBaseLevelList" :key="index">
            <div class="item">
              <div class="itemLabel" style="width:50px">
                {{ item.optionName }}级
              </div>
              <div class="itemVal">
                <el-input :disabled="!isModifyType" type="number" v-model="item.optionValue" placeholder="请输入">
                </el-input>
              </div>
            </div>
          </el-col>
        </el-row>

        <div class="blockHeader">
          <span>保底工资</span>
        </div>
        <el-row>
          <el-col :span="24" v-for="(item, index) in tableData.minimumWageList" :key="index">
            <div class="item" style="margin-top:10px;">
              <div class="" style="width:100px;">
                {{ item.optionName }}
              </div>
              <div class="itemVal">
                <el-input :disabled="!isModifyType" type="number" v-model="item.optionValue" placeholder="请输入">
                  <template slot="append">元</template>
                </el-input>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" v-for="(item, index) in tableData.minimumWageDaysList" :key="index">
            <div class="item" style="margin-top:10px;">
              <div class="" style="width:100px;">
                {{ item.optionName }}
              </div>
              <div class="itemVal">
                <el-input :disabled="!isModifyType" type="number" v-model="item.optionValue" placeholder="请输入">
                  <template slot="append">元</template>
                </el-input>
              </div>
            </div>
          </el-col>
        </el-row>

        <div class="blockHeader">
          <span>技术补贴</span>
        </div>
        <el-row style="display:flex;align-items: center;margin-top:10px;">
          <div :span="2">补贴标准:</div>
          <el-col :span="4" v-for="(item, index) in tableData.techTitleList" :key="index">
            <div class="item">
              <div class="itemLabel">
                {{ item.optionName }}
              </div>
              <div class="itemVal">
                <el-input   :disabled="!isModifyType" type="number" v-model="item.optionValue" placeholder="请输入">
                  <template slot="append">元/天</template>
                </el-input>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </table-panel>
  </content-panel>
</template>
<script>
export default {
  name:'Logisticsterconfiguration',
  data() {
    return {
      isModifyType: false,
      saveLoading: false,
      info: {},
      boxHeight: "auto",
      tableData: {},
    };
  },
  mounted() {
    let dom = document.querySelector(".app-main");
    this.boxHeight = dom.getBoundingClientRect().height - 90 + "px";
  },
  methods: {
    changeCancel() {
      this.getList();
      this.isModifyType = false;
    },
    changeSave() {
      let arr = [];
      for (let key in this.tableData) {
        if (this.tableData.hasOwnProperty(key)) {
          this.tableData[key].forEach((item) => {
            arr.push({ id: item.id, optionValue: item.optionValue,sortCode:item.sortCode });
          });
        }
      }

      this.saveLoading = true;
      this.$api.logisticsDataConfiguration
        .dataUpdate(arr)
        .then(() => {
          this.$message.success("保存成功");
          this.getList();
        })
        .finally(() => {
          this.saveLoading = false;
          this.isModifyType = false;
          this.getList()
        });

    },
    //获取页面配置
    getList() {
      this.loading = true;
      this.$api.logisticsDataConfiguration
        .getConfiglistAll()
        .then(({ data }) => {
          this.tableData = data || {};
          console.log("DATA", data);
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
  created() {
    this.getList();
  },
};
</script>

<style  lang="stylus" scoped>
.boxTop {
  text-align: right;
  /* display: flex;
  justify-content: space-between; */
}
.boxWrapper {
  overflow-y: auto;
}

.blockHeader {
  /* color: #0bb78e; */
  padding: 10px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #ccc;
  font-weight: bold;
}

.item {
  box-sizing: border-box;
  /* border: 1px solid #ddd; */
  display: flex;
  align-items: center;
}

.itemLabel {
  width: 120px;
  /* background: #f1f4f7; */
  /* color: #999; */
  padding: 10px;
  text-align: right;
}

.borderBttom {
  border-bottom: white;
}

.borderLeft {
  border-left: white;
}

.btnBox {
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.itemVal {
  margin-left: 5px;
  width: 100%;
  padding: 0 5px;
}
>>> .el-input input {
  height: 24px;
  line-height: 24px;
  width: 95px;
  padding: 0px 10px;
}
>>>.el-input-group__append{
  background: #aaa;
  color: white;
  padding:0 7px
}
>>>.el-input{
  width:0px
}
</style>
