<template>
  <el-checkbox 
    :ref="'condition-item-' + source.value"
    class="condition-item" 
    v-model="checked" 
    @change="onCheckChange">
    {{source.label}}
  </el-checkbox>
</template>

<script>
import bus from '@/utils/bus'
export default {
  name: 'ConditionItem',
  props: {
    source: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  watch: {
    'source.checked': {
      handler(val) {
        this.checked = val
      },
      immediate: true
    }
  },
  data() {
    return {
      checked: false
    }
  },
  methods: {
    onCheckChange(checked) {
      bus.$emit('checkBoxValueChange', this.source.value, checked);
    }
  }
}
</script>

<style lang="stylus" scoped>
.condition-item
    display block
</style>