<template>
  <el-popover :ref="'filter-pop-' + prop"
    popper-class="filter-pop"
    placement="bottom"
    :width="260"
    trigger="click"
    @show="onPopShow">
    <div class="header-label" slot="reference">
      <slot>
        <span>{{label}}</span>
      </slot>
      <i class="el-icon-search" :class="{'is-filtered': isFiltered}"></i>
    </div>
    <div class="filter-panel">
      <div class="filter-panel-sort-wrap" v-if="useSort">
        <el-button
          type="primary"
          plain
          size="mini"
          :class="{active: sortType == 'up'}"
          icon="el-icon-sort-up"
          @click="doSort('up')">升序</el-button>
        <el-button
          type="primary"
          plain
          size="mini"
          :class="{active: sortType == 'down'}"
          icon="el-icon-sort-down"
          @click="doSort('down')">降序</el-button>
      </div>
      <div class="filter-panel-number-wrap" v-if="isNumberFilter">
        <el-select
          class="number-select"
          v-model="numCondition"
          size="mini"
          placeholder="">
          <el-option v-for="item in numberConditions"
            :key="item.value"
            :label="item.label"
            :value="item.value"/>
        </el-select>
        <el-input-number
          class="number-input"
          v-model="numVal1"
          :controls="false"
          step-strictly
          size="mini"/>
        <el-input-number
          v-if="isRange"
          class="number-input"
          v-model="numVal2"
          :controls="false"
          step-strictly
          size="mini"/>
      </div>
      <div class="filter-panel-select-wrap" v-if="isSelectFilter">
        <el-select
          class="select-input"
          v-model="selectVal"
          size="mini"
          multiple
          filterable
          collapse-tags>
          <el-option v-for="item in sortedOptions"
            :key="item"
            :label="item"
            :value="item"/>
        </el-select>
        <el-button class="select-btn" type="primary" icon="el-icon-search" size="small" @click="confirmFilter"></el-button>
      </div>
      <div class="filter-panel-input-wrap" v-if="isInputFilter">
        <el-input
          class="filter-input"
          placeholder="请输入内容"
          size="mini"
          v-model="inputVal">
          <i slot="append" class="el-icon-search"  @click="confirmFilter"></i>
        </el-input>
      </div>
      <div class="filter-panel-date-wrap" v-if="isDateFilter">
        <el-date-picker
          class="date-input"
          v-model="dateVal1"
          type="date"
          size="mini"
          placeholder="开始日期">
        </el-date-picker>
        <span>-</span>
        <el-date-picker
          class="date-input"
          v-model="dateVal2"
          type="date"
          size="mini"
          placeholder="结束日期">
        </el-date-picker>
      </div>

      <div class="filter-panel-btn-wrap">
        <el-button size="mini" @click="resetFilter">重置</el-button>
        <el-button size="mini" @click="closePop">取消</el-button>
        <el-button size="mini" type="primary" @click="confirmFilter" v-if="!isSelectFilter">确定</el-button>
      </div>
    </div>
  </el-popover>
</template>

<script>
import {numberCompareEnum} from '@/utils/constant'
export default {
  name: 'QTableHeaderFilterFront',
  props: {
    label: String,
    prop: String,
    // 过滤类型:text|number|select
    filterType: String,
    useSort: {
      type: Boolean,
      default: false
    },
    // 当前列是否是数字列
    isNumberSelect: {
      type: Boolean,
      default: false
    },
    selectOptions: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      sortType: '',
      inputVal: '',
      selectVal: [],
      dateVal1: null,
      dateVal2: null,
      numCondition: undefined,
      numVal1: undefined,
      numVal2: undefined,
      lastFilter: {
        sort: '',
        textValue: '',
        numberValues: [],
        selectValues: [],
        dateValues: []
      },
      numberConditions: [
        {label: '大于', value: numberCompareEnum.greater},
        {label: '小于', value: numberCompareEnum.less},
        {label: '等于', value: numberCompareEnum.equal},
        {label: '大于等于', value: numberCompareEnum.greaterOrEqual},
        {label: '小于等于', value: numberCompareEnum.lessOrEqual},
        {label: '不等于', value: numberCompareEnum.notEqual},
        {label: '介于', value: numberCompareEnum.range},
      ],
    }
  },
  computed: {
    isFiltered() {
      return !!this.lastFilter.textValue
        || this.lastFilter.selectValues.length > 0
        || this.lastFilter.dateValues.filter(item => !!item).length > 0
        || this.lastFilter.numberValues.filter(item => item !== undefined).length > 0
        || !!this.lastFilter.sort;
    },
    isInputFilter() {
      return this.filterType === 'text';
    },
    isNumberFilter() {
      return this.filterType === 'number';
    },
    isSelectFilter() {
      return this.filterType === 'select';
    },
    isDateFilter() {
      return this.filterType === 'date';
    },
    isRange() {
      return this.numCondition == numberCompareEnum.range;
    },
    sortedOptions() {
      if (!this.isNumberSelect) {
        return ['空', ...this.selectOptions];
      }
      const options = [...this.selectOptions].sort((a, b) => Number(a) - Number(b));
      return ['空', ...options];
    }
  },
  methods: {
    resetModel() {
      this.inputVal = '';
      this.selectVal = [];
      this.dateVal1 = null;
      this.dateVal2 = null;
      this.sortType = '';
      this.numCondition = undefined;
      this.numVal1 = undefined;
      this.numVal2 = undefined;
      this.lastFilter = {
        sort: '',
        textValue: '',
        numberValues: [],
        selectValues: [],
        dateValues: []
      }
    },
    resetFilter() {
      this.resetModel();
      this.closePop();
      this.confirmFilter();
    },
    confirmSort() {
      this.lastFilter.sort = this.sortType;
      this.$emit('confirmSort', {
        prop: this.prop,
        sort: this.sortType,
        type: this.filterType,
      });
      this.closePop();
    },
    confirmFilter() {
      this.sortType = '';
      const emitVal = this.lastFilter = {
        prop: this.prop,
        type: this.filterType,
        textValue: this.inputVal,
        numberValues: [this.numCondition, this.numVal1, this.numVal2],
        selectValues: this.selectVal,
        dateValues: [this.dateVal1, this.dateVal2],
        sort: ''
      };
      this.$emit('confirmFilter', emitVal);
      this.closePop();
    },
    closePop() {
      const popRef = this.$refs['filter-pop-' + this.prop];
      if (popRef) {
        popRef.doClose();
      }
    },
    doSort(type) {
      if (!this.sortType || this.sortType != type) {
        this.sortType = type;
        this.confirmSort();
      } else {
        this.closePop();
      }
    },
    onPopShow() {
      if (this.isNumberFilter) {
        const [numCondition, numVal1, numVal2] = this.lastFilter.numberValues;
        this.numCondition = numCondition;
        this.numVal1 = numVal1;
        this.numVal2 = numVal2;
      }

      if (this.isInputFilter) {
        this.inputVal = this.lastFilter.textValue;
      }

      if (this.isSelectFilter) {
        this.selectVal = this.lastFilter.selectValues;
      }

      if (this.isDateFilter) {
        const [dateVal1, dateVal2] = this.lastFilter.dateValues;
        this.dateVal1 = dateVal1 || null;
        this.dateVal2 = dateVal2 || null;
      }

      this.sortType = this.lastFilter.sort;
    }
  }
}
</script>

<style lang="stylus" scoped>
.header-label
  display inline-flex
  align-items center
  line-height 23px
  cursor pointer
  padding 0
  .is-filtered
    color #0BB78E
    font-weight bold
.filter-pop
  padding 10px 5px
.filter-panel
  .filter-input
    >>> .el-input-group__append
      padding 0 10px
  &-number-wrap
    display flex
    align-items center
    margin-bottom 10px
    >span
      flex none
      width 65px
    .number-select
      min-width 120px
      flex 1
    .number-input
      flex none
      margin-left 5px
      width 60px
      >>> .el-input__inner
        padding 0 5px
  &-select-wrap
    display flex
    align-items center
    .select-input
      flex 1
    .select-btn
      flex none
      padding 7px
      margin-left 5px
  &-date-wrap
    display flex
    align-items center
    .date-input
      flex 1
    >span
      flex none
      margin 0 5px
  &-sort-wrap
    display flex
    align-items center
    justify-content space-around
    margin-bottom 10px
  &-btn-wrap
    margin-top 10px
    text-align right
.active
  color #fff
  background-color #0BB78E
  border-color #0BB78E
</style>