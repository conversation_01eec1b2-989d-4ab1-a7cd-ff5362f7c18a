<template>
  <qDialog
    :visible="visible"
    title="数据权限"
    :innerScroll="false"
    width="800px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <div class="dialog-info">
      {{ title }}
    </div>
    <div class="dialog-title">
      <div>所有权限</div>
      <div>已有权限</div>
    </div>
    <div class="content">
      <div class="left-content">
        <el-checkbox
          :indeterminate="indeterminate"
          v-model="checkAll"
          @change="checkAllChange"
        >
          全选</el-checkbox
        >
        <div style="margin: 0" class="checkList">
          <el-checkbox-group v-model="list" @change="permissionsChange">
            <el-checkbox v-for="item in allPermission" :label="item" :key="item.id">
              {{ item.name }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
      <div class="right-content">
        <div v-for="item in selectedList" :key="item.id" class="haveInfo">
          {{ item.name }}
        </div>
      </div>
    </div>
  </qDialog>
</template>

<script>
export default {
  name: "editDialog",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    editForm: Object,
  },
  data() {
    return {
      indeterminate: false,
      checkAll: false,
      allPermission: [],
      checkPermission: [],
      list: [],
      selectedList: [],
      isLoading:false
    };
  },
  computed: {
    title() {
      return `${this.editForm.staffName || ""}  ${this.editForm.staffCode || ""}`;
    },
  },
  async created() {
    await this.getBasic();
  },
  methods: {
    getBasic() {
      return this.$api.plateTypeSystemManage.getBasicPermission
        .getQuFactory({moduleId:3})
        .then(({ data }) => {
          this.list = data.filter((v) =>
            this.editForm.permissions
              .filter((item) => item)
              .map((it) => it.factoryName)
              .includes(v.name)
          );
          this.allPermission = data || [];
          this.permissionsChange(this.list);
        });
    },
    //全选
    checkAllChange(val) {
      this.selectedList = this.list = val ? this.allPermission : [];
      this.indeterminate = false;
    },
    permissionsChange(val) {
      this.selectedList = val;
      this.checkAll = this.list.length == this.allPermission.length;
      this.indeterminate = val.length > 0 && val.length < this.allPermission.length;
    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      let params = {
        staffId: this.editForm.staffId,
        factoryIds: this.selectedList.map((item) => item.id).join(","),
      };
      this.isLoading = true;
      this.$api.plateTypeSystemManage.dataPermission.editPermission(params).then((res) => {
        this.$notify.success({
          title: "成功",
          message: "添加成功",
        });
        this.$emit("cancel", "confirm");
      }).finally(()=>{
            this.isLoading = false;
      });
    },
  },
};
</script>

<style lang="stylus" scoped>
.dialog-info {
  font-size: 14px;
  color: #000;
  font-weight: bold;
  height: 20px;
  border-bottom: 1px solid #eee;
}

.dialog-title {
  margin: 30px 0 20px 0;
  height: 26px;
  background-color: #eee;
  display: flex;
  align-items: center;
  font-size: 12px;
  text-indent: 20px;

  >div {
    flex: 1;
  }
}

.content {
  display: flex;

  .left-content, .right-content {
    width: 45%;
    border: 1px solid #eee;
    padding: 14px;
    height: 160px;
    border-radius: 8px;
    overflow: auto;
  }

  .right-content {
    margin-left: 20px;
    font-size: 14px;

    .haveInfo {
      line-height: 20px;
    }
  }
}

>>>.el-checkbox__label {
  font-size: 14px;
  line-height: 20px;
}

.el-checkbox-group {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
}
</style>
