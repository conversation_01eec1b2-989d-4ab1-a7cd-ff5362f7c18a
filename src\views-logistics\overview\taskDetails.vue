<template>
  <!-- 任务明细-->
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form size="mini" :inline="true" :model="searchForm" ref="searchForm" label-position="right">
          <el-form-item label="待办任务:" prop="content">
            <el-input v-model.trim="searchForm.content" size="mini" clearable placeholder="请输入待办任务"
              @keyup.enter.native="onSearch">
            </el-input>
          </el-form-item>
          <el-form-item label="所属角色:" prop="roleId">
            <el-select @change="onSearch" v-model="searchForm.roleId" filterable clearable placeholder="请选择角色">
              <el-option v-for="item in roleOptions" :key="item.id" :label="item.roleName" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="任务状态:" prop="status">
            <el-select @change="onSearch" v-model="searchForm.status" filterable clearable placeholder="请选择状态">
              <el-option v-for="item in statusOptions" :key="item.id" :label="item.label" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <!-- 采集类型 -->
          <!-- <el-form-item label="采集类型:" prop="collectType">
            <el-select
              @change="onSearch"
              v-model="searchForm.collectType"
              filterable
              clearable
              placeholder="请选择采集类型"
            >
              <el-option
                v-for="item in collectTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item> -->
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary" @click="onSearch">
            查询</el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置</el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <el-table stripe border v-loading="loading" ref="tableRef" highlight-current-row :height="maxTableHeight"
        style="width: 100%" :data="tableData">
        <el-table-column v-for="item in columnList" :key="item.name" :label="item.label" :width="item.width"
          align="left" show-overflow-tooltip>
          <template slot-scope="scope">
            <template v-if="item.type">
              <!-- 考勤退回-->
              <el-button type="text" size="small" v-permission="'was-customized$workBench$logistics$workOverview$attendanceBack'
                " v-show="scope.row.status == '待审核' &&
                  scope.row.status != '已完成' &&
                  scope.row.type == 10
                  " @click="handleBack(scope.row)" style="margin: 0">
                退回</el-button>
              <!-- 考勤提交-->
              <el-button type="text" size="small" v-permission="'was-customized$workBench$logistics$workOverview$attendanceSubmission'
                " v-show="scope.row.status != '已完成' &&
                  scope.row.status != '待审核' &&
                  scope.row.type == 10
                  " @click="handleSubmit(scope.row)" style="margin: 0">
                提交</el-button>

              <el-button type="text" size="small" v-permission="'was-customized$workBench$logistics$workOverview$taskBackBtn'
                " v-show="scope.row.status == '待审核' &&
                  scope.row.status != '已完成' &&
                  scope.row.type != 10 &&
                  scope.row.visible == 'Y' &&
                  (staffStatus == '待审核' || filterType(scope.row.type))
                  " @click="handleBack(scope.row)" style="margin: 0">退回</el-button>
              <el-button type="text" size="small" v-permission="'was-customized$workBench$logistics$workOverview$submitBtn'
                " v-show="scope.row.status != '已完成' &&
                  scope.row.status != '待审核' &&
                  scope.row.type != 10 &&
                  scope.row.visible == 'Y' &&
                  (staffStatus == '待审核' || filterType(scope.row.type))
                  " @click="handleSubmit(scope.row)" style="margin: 0">提交</el-button>
              <el-button type="text" size="small" v-show="scope.row.visible == 'Y' &&
                (staffStatus == '待审核' || filterType(scope.row.type))
                " @click="handleDetails(scope.row)" style="margin: 0">
                查看详情
              </el-button>
              <!-- <el-button
                type="text"
                size="small"

                @click="handleDetails(scope.row)"
                style="margin: 0">
                查看详情
              </el-button> -->
            </template>
            <span v-else-if="!item.type && item.name == 'collectType'">{{
              scope.row[item.name] == 0 ? "手动录入" : "自动获取"
            }}</span>
            <span v-else-if="!item.type && item.name != 'updateTime'">{{
              scope.row[item.name]
            }}</span>
            <span v-else>{{ scope.row.updateTime | dateFormat }}</span>
          </template>
        </el-table-column>
      </el-table>
    </table-panel>
    <qDialog :visible="visible" :innerScroll="false" :title="title" :isLoading="isLoading" width="30%"
      @confirm="handleConfirm" @cancel="handleCancel" :before-close="handleCancel">
      <template>
        <p v-show="title == '确认提交'">
          员工考勤提交后若需修改,需退回后才可修改，是否提交？
        </p>
        <p v-show="title == '确认退回'">
          考勤退回清空保底工资数据、更新基本工资、职务补贴任务中的总出勤天数、更新技术补贴中的总出勤天数、总加班天数，是否提交？
        </p>
      </template>
    </qDialog>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
export default {
  name: "LogisticsTaskDetails",
  mixins: [tableMixin, pagePathMixin],
  data() {
    return {
      searchForm: {
        status: "",
        roleId: "",
        content: "",
        collectType: "",
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      //角色列表
      roleOptions: [],
      //状态列表
      statusOptions: [
        // {label:"未上传",id:"0"},
        { label: "待提交", id: "1" },
        { label: "待审核", id: "2" },
        { label: "已完成", id: "3" },
      ],
      //采集类型
      collectTypeList: [
        { label: "全部", value: "" },
        { label: "手动录入", value: "0" },
        { label: "自动获取", value: "1" },
      ],
      //表格头部信息
      columnList: Object.freeze([
        {
          label: "核算组织",
          width: "150",
          name: "factoryName",
        },
        {
          label: "核算月份",
          width: "150",
          name: "accountingMonth",
        },
        {
          label: "所属角色",
          width: "150",
          name: "roleName",
        },
        {
          label: "待办任务",
          name: "name",
        },
        {
          label: "任务状态",
          width: "150",
          name: "status",
        },
        {
          label: "操作",
          width: "200",
          name: "",
          type: "operation",
        },
      ]),
      info: {},
      tableData: [],
      factoryId: "",
      resizeOffset: 12,
      loading: false,
      isShow: false,
      visible: false,
      title: "",
      filterParam: {},
      staffStatus: "",
      isLoading: false,
      routeInfo: Object.freeze([
        {
          type: "10",
          path: "/logistics/uploadTasks/attendance",
        },
        {
          type: "20",
          path: "/logistics/uploadTasks/wage",
        },
        {
          type: "30",
          path: "/logistics/uploadTasks/minimumWage",
        },
        {
          type: "40",
          path: "/logistics/uploadTasks/unionfee",
        },
        {
          type: "50",
          path: "/logistics/uploadTasks/security",
        },
        {
          type: "60",
          path: "/logistics/uploadTasks/duties",
        },
        {
          type: "70",
          path: "/logistics/uploadTasks/technical",
        },
        {
          type: "80",
          path: "/logistics/uploadTasks/basicSalary",
        },
        {
          type: "90",
          path: "/logistics/account/debitList",
        },
        {
          type: "100",
          path: "/logistics/account/rewardList",
        },
        {
          type: "110",
          path: "/logistics/uploadTasks/subsidy",
        },
        {
          type: "130",
          path: "/logistics/uploadTasks/deduction",
        },

        {
          type: "120",
          path: "/logistics/uploadTasks/housingsubsidies",
        },
        {
          type: "140",
          path: "/logistics/uploadTasks/factoryservicededuction",
        },
        {
          type: "150",
          path: "/logistics/uploadTasks/factorycarddeduction",
        },
        {
          type: "160",
          path: "/logistics/uploadTasks/lifefei",
        },
        {
          type: "170",
          path: "/logistics/uploadTasks/physicalexamination",
        },
        {
          type: "180",
          path: "/logistics/uploadTasks/costcompensationList",
        },
        {
          type: "190",
          path: "/logistics/uploadTasks/miscellaneous",
        },
        {
          type: "200",
          path: "/logistics/uploadTasks/mentorship",
        },
        {
          type: "210",
          path: "/logistics/uploadTasks/pallet",
        },
        {
          type: "220",
          path: "/logistics/uploadTasks/waterproof",
        },
        {
          type: "230",
          path: "/logistics/uploadTasks/handyman",
        },
        {
          type: "240",
          path: "/logistics/uploadTasks/expatriate",
        },
        {
          type: "250",
          path: "/logistics/uploadTasks/diningLife",
        },
        {
          type: "260",
          path: "/logistics/uploadTasks/flood",
        },
        {
          type: "270",
          path: "/logistics/uploadTasks/internalPurchase",
        },
        {
          type: "280",
          path: "/logistics/uploadTasks/addGoods",
        },
        {
          type: "290",
          path: "/logistics/uploadTasks/forklift",
        },
        {
          type: "300",
          path: "/logistics/uploadTasks/temperature",
        },
        {
          type: "310",
          path: "/logistics/uploadTasks/risk",
        },
        {
          type: "320",
          path: "/logistics/uploadTasks/workOvertime",
        },
        {
          type: "330",
          path: "/logistics/uploadTasks/newEmployee",
        },
        {
          type: "340",
          path: "/logistics/uploadTasks/noPiece",
        },
        {
          type: "350",
          path: "/logistics/uploadTasks/accidentRisk",
        },
        {
          type: "360",
          path: "/logistics/uploadTasks/thresholdFee",
        },
      ]),
    };
  },
  created() {
    this.$api.roleInfo.getRoleInfoAll({ moduleId: 2 }).then((res) => {
      this.roleOptions = res.data || [];
    });
  },
  watch: {
    $route: {
      handler(value) {
        if (
          value.path.includes("taskDetails") &&
          value.path.includes("logistics")
        ) {
          this.factoryId = JSON.parse(this.$Base64.decode(value.query.data)).factoryId;
          this.getList();
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    //获取待办任务列表
    getList() {
      this.loading = true;
      this.$api.logisticsWorkbench
        .getAgencyTasks({
          factoryId: this.factoryId,
          ...this.filterParam,
          accountingMonth: JSON.parse(this.$Base64.decode(this.$route.query.data)).accountingMonth,
        })
        .then(({ data }) => {
          this.tableData =
            (data &&
              data.map((item) => ({
                ...item,
                factoryName: item.factoryId == "TEST" ? "测试专用" : "物流本部",
                status: this.filterStatus(item.status),
              }))) ||
            [];
          this.tableData.forEach((v) => {
            if (v.planName == "员工考勤") {
              this.staffStatus = v.status;
            }
            this.isShow = v.visible;
          });
        })
        .finally(() => {
          this.loading = false;
        });
    },
    filterStatus(val) {
      return (
        (val && this.statusOptions.find((item) => item.id == val).label) || ""
      );
    },
    filterPath(type) {
      return (
        (type && this.routeInfo.find((item) => item.type == type).path) || ""
      );
    },
    filterType(type) {
      return (
        (type &&
          this.routeInfo.map((item) => item.type).includes(String(type))) ||
        ""
      );
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.filterParam = {};
      this.onSearch();
    },
    //搜索
    onSearch() {
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      this.pageNum = 1;
      this.getList();
    },
    //提交
    handleSubmit(row) {
      if (row.type == "10") {
        this.isLoading = false;
        this.title = "确认提交";
        this.visible = true;
        this.info = {
          ...row,
        };
      } else {
        this.$api.logisticsWorkbench.submit({ id: row.id }).then((res) => {
          this.$message({
            message: "提交成功",
            type: "success",
          });
          this.getList();
        });
      }
    },
    handleCancel() {
      this.visible = false;
    },
    //退回
    handleBack(row) {
      if (row.type == "10") {
        this.isLoading = false;
        this.title = "确认退回";
        this.visible = true;
        this.info = {
          ...row,
        };
      } else {
        this.$api.logisticsWorkbench
          .back({ id: row.id, status: "1" })
          .then((res) => {
            this.$message({
              message: "退回成功",
              type: "success",
            });
            this.getList();
          });
      }
    },
    //查看详情
    handleDetails(row) {
      const { id, factoryId, factoryName, accountingMonth, roleName, status } =
        row;
      let params = {};
      let obj = {
        id,
        factoryId,
        factoryName,
        accountingMonth,
        roleName,
        status,
      };
      params =
        row.type == "110" || row.type == "130"
          ? { ...obj, otherId: row.otherId, planName: row.name }
          : obj;
      this.openSubPage({
        path: this.filterPath(row.type),
        query: { data: this.$Base64.encode(JSON.stringify(params)) },
      });
    },
    handleConfirm() {
      switch (this.title) {
        case "确认提交":
          this.isLoading = true;
          this.$api.logisticsWorkbench
            .submit({ id: this.info.id, status: "2" })
            .then((res) => {
              this.$message({
                message: "提交成功",
                type: "success",
              });
              this.getList();
            })
            .finally(() => {
              this.visible = false;
              this.isLoading = false;
            });
          break;
        case "确认退回":
          this.isLoading = true;
          this.$api.logisticsWorkbench
            .back({ id: this.info.id, status: "1", type: 10 })
            .then((res) => {
              this.$message({
                message: "退回成功",
                type: "success",
              });
              this.getList();
            })
            .finally(() => {
              this.visible = false;
              this.isLoading = false;
            });
      }
    },
  },
};
</script>

<style lang="stylus" scoped>
>>>.el-tabs__nav-wrap::after {
  display: block;
  height: 0;
}
</style>
