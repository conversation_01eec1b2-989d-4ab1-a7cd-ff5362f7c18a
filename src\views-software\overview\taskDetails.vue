<template>
  <!-- 任务明细-->
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          size="mini"
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          label-position="right"
        >
          <el-form-item label="待办任务:" prop="content">
            <el-input
              v-model.trim="searchForm.content"
              size="mini"
              clearable
              placeholder="请输入待办任务"
              @keyup.enter.native="onSearch"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="所属角色:" prop="roleId">
            <el-select
              @change="onSearch"
              v-model="searchForm.roleId"
              filterable
              clearable
              placeholder="请选择角色"
            >
              <el-option
                v-for="item in roleOptions"
                :key="item.id"
                :label="item.roleName"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="任务状态:" prop="status">
            <el-select
              @change="onSearch"
              v-model="searchForm.status"
              filterable
              clearable
              placeholder="请选择状态"
            >
              <el-option
                v-for="item in statusOptions"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <!-- 采集类型 -->
          <el-form-item label="采集类型:" prop="collectType">
            <el-select
              @change="onSearch"
              v-model="searchForm.collectType"
              filterable
              clearable
              placeholder="请选择采集类型"
            >
              <el-option
                v-for="item in collectTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button
            size="small"
            type="primary"

            @click="onSearch"
          >
            查询</el-button
          >
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置</el-button
          >
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :height="maxTableHeight"
        style="width: 100%"
        :data="tableData"
      >
        <el-table-column
          v-for="item in columnList"
          :key="item.name"
          :label="item.label"
          :width="item.width"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <template v-if="item.type">
              <!-- 考勤退回-->
              <el-button
                type="text"
                size="small"
                v-permission="
                  'was-customized$workBench$software$workOverview$attendanceBack'
                "
                v-show="
                  scope.row.status == '待审核' &&
                  scope.row.status != '已完成' &&
                  scope.row.type == 1
                "
                @click="handleBack(scope.row)"
                style="margin: 0"
              >
                退回</el-button
              >
              <!-- 考勤提交-->
              <el-button
                type="text"
                size="small"
                v-permission="
                  'was-customized$workBench$software$workOverview$attendanceSubmission'
                "
                v-show="
                  scope.row.status != '已完成' &&
                  scope.row.status != '待审核' &&
                  scope.row.type == 1
                "
                @click="handleSubmit(scope.row)"
                style="margin: 0"
              >
                提交</el-button
              >
              <el-button
                :disabled="scope.row.disabled"
                type="text"
                size="small"
                v-permission="
                  'was-customized$workBench$software$workOverview$taskBackBtn'
                "
                v-show="
                  scope.row.status == '待审核' &&
                  scope.row.status != '已完成' &&
                  scope.row.type != 1 &&
                  scope.row.visible == 'Y' &&
                  (staffStatus == '待审核' || filterType(scope.row.type))
                "
                @click="handleBack(scope.row)"
                style="margin: 0"
              >
                退回</el-button
              >
              <el-button
                :disabled="scope.row.disabled"
                type="text"
                size="small"
                v-permission="
                  'was-customized$workBench$software$workOverview$submitBtn'
                "
                v-show="
                  scope.row.status != '已完成' &&
                  scope.row.status != '待审核' &&
                  scope.row.type != 1 &&
                  scope.row.visible == 'Y' &&
                  (staffStatus == '待审核' || filterType(scope.row.type))
                "
                @click="handleSubmit(scope.row)"
                style="margin: 0"
              >
                提交</el-button
              >
              <el-button
                type="text"
                size="small"
                v-show="
                  scope.row.visible == 'Y' &&
                  (staffStatus == '待审核' || filterType(scope.row.type))
                "
                @click="handleDetails(scope.row)"
                style="margin: 0"
              >
                查看详情
              </el-button>
            </template>
            <span v-else-if="!item.type && item.name == 'collectType'">{{
              scope.row[item.name] == 0 ? "手动录入" : "自动获取"
            }}</span>
            <span v-else-if="!item.type && item.name != 'updateTime'">{{
              scope.row[item.name]
            }}</span>
            <span v-else>{{ scope.row.updateTime | dateFormat }}</span>
          </template>
        </el-table-column>
      </el-table>
    </table-panel>
    <qDialog
      :visible="visible"
      :innerScroll="false"
      :title="title"
      :isLoading="isLoading"
      width="30%"
      @confirm="handleConfirm"
      @cancel="handleCancel"
      :before-close="handleCancel"
    >
      <template>
        <p v-show="title == '确认提交'">
          员工考勤提交后若需修改,需退回后才可修改，是否提交？
        </p>
        <p v-show="title == '确认退回'">
          员工考勤退回，将清空该任务下计件工资中全部数据，并清除工会费中该分厂当月数据，是否提交？
        </p>
      </template>
    </qDialog>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
export default {
  name: "SoftwareTaskDetails",
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      searchForm: {
        status: "",
        roleId: "",
        content: "",
        collectType:"",
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      //角色列表
      roleOptions: [],
      //状态列表
      statusOptions: [
        // {label:"未上传",id:"0"},
        { label: "待提交", id: "1" },
        { label: "待审核", id: "2" },
        { label: "已完成", id: "3" },
      ],
       //采集类型
       collectTypeList:[
      { label: "全部", value: "" },
      { label: "手动录入", value: "0" },
      { label: "自动获取", value: "1" },
      ],
      //表格头部信息
      columnList: Object.freeze([
        {
          label: "核算工厂",
          width: "150",
          name: "factoryName",
        },
        {
          label: "核算月份",
          width: "150",
          name: "accountingMonth",
        },
        {
          label: "所属角色",
          width: "150",
          name: "roleName",
        },
        {
          label: "采集类型",
          width: "150",
          name: "collectType",
        },
        {
          label: "待办任务",
          name: "name",
        },
        {
          label: "任务状态",
          width: "150",
          name: "status",
        },
        {
          label: "操作",
          width: "200",
          name: "",
          type: "operation",
        },
      ]),
      info: {},
      tableData: [],
      factoryId: "",
      resizeOffset: 12,
      loading: false,
      isShow: false,
      visible: false,
      title: "",
      filterParam: {},
      staffStatus: "",
      isLoading: false,
      routeInfo: Object.freeze([
        {
          type: "1",
          path: "/software/uploadTasks/attendance",
        },
        {
          type: "2",
          path: "/software/uploadTasks/wage",
        },
        {
          type: "3",
          path: "/software/uploadTasks/collective",
        },
        {
          type: "4",
          path: "/software/account/debitList",
        },
        {
          type: "5",
          path: "/software/account/rewardList",
        },
        {
          type: "6",
          path: "/software/uploadTasks/subsidy",
        },
        {
          type: "7",
          path: "/software/uploadTasks/deduction",
        },
        {
          type: "8",
          path: "/software/uploadTasks/security",
        },
        {
          type: "12",
          path: "/software/uploadTasks/puncandeduct",
        },
        {
          type: "13",
          path: "/software/uploadTasks/unionfee",
        },
        {
          type: "14",
          path: "/software/uploadTasks/companysubsidies",
        },
        {
          type: "15",
          path: "/software/uploadTasks/housingsubsidies",
        },
        {
          type: "16",
          path: "/software/uploadTasks/factoryservicededuction",
        },
        {
          type: "17",
          path: "/software/uploadTasks/factorycarddeduction",
        },
        {
          type: "18",
          path: "/software/uploadTasks/lifefei",
        },
        {
          type: "19",
          path: "/software/uploadTasks/physicalexamination",
        },
        {
          type: "20",
          path: "/software/uploadTasks/costcompensation",
        },
        {
          type: "21",
          path: "/software/uploadTasks/lowconsumptiongoods",
        },
        {
          type: "22",
          path: "/software/uploadTasks/reworkdeduction",
        },
        {
          type: "23",
          path: "/software/uploadTasks/costcompensationList",
        },
        {
          type: "25",
          path: "/software/uploadTasks/miscellaneous",
        },

        {
          type: "26",
          path: "/software/uploadTasks/environmental",
        },
        {
          type: "28",
          path: "/software/uploadTasks/hightemperature",
        },
        {
          type: "29",
          path: "/software/uploadTasks/mployeesubsidies",
        },
        {
          type: "30",
          path: "/software/uploadTasks/oldhandsubsidy",
        },
        {
          type: "31",
          path: "/software/uploadTasks/minimumwage",
        },
        {
          type: "32",
          path: "/software/uploadTasks/newemployeeSubsidy",
        },
      ]),
    };
  },
  created() {
    this.$api.roleInfo.getRoleInfoAll({ moduleId: 4 }).then((res) => {
      this.roleOptions = res.data || [];
    });
  },
  watch: {
    $route: {
      handler(value) {
        if (
          value.path.includes("taskDetails") &&
          value.path.includes("software")
        ) {
          this.factoryId = JSON.parse(this.$Base64.decode(value.query.data)).factoryId;
          this.getList();
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    //获取待办任务列表
    getList() {
      this.loading = true;
      this.$api.softwareWorkbench
        .getAgencyTasks({
          factoryId: this.factoryId,
          ...this.filterParam,
          accountingMonth: JSON.parse(this.$Base64.decode(this.$route.query.data)).accountingMonth,
        })
        .then(({ data }) => {
          this.tableData =
            (data &&
              data.map((item) => ({
                ...item,
                status: this.filterStatus(item.status),
                disabled: false,
              }))) ||
            [];
          this.tableData.forEach((v) => {
            if (v.planName == "员工考勤") {
              this.staffStatus = v.status;
            }
            this.isShow = v.visible;
          });
        })
        .finally(() => {
          this.loading = false;
        });
    },
    filterStatus(val) {
      return (
        (val && this.statusOptions.find((item) => item.id == val).label) || ""
      );
    },
    filterPath(type) {
      return (
        (type && this.routeInfo.find((item) => item.type == type).path) || ""
      );
    },
    filterType(type) {
      return (
        (type &&
          this.routeInfo.map((item) => item.type).includes(String(type))) ||
        ""
      );
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.filterParam = {};
      this.onSearch();
    },
    //搜索
    onSearch() {
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      this.pageNum = 1;
      this.getList();
    },
    //提交
    handleSubmit(row) {
      if (row.type == "1") {
        this.isLoading = false;
        this.title = "确认提交";
        this.visible = true;
        this.info = {
          ...row,
        };
      } else {
        row.disabled = true;
        this.$api.softwareWorkbench
          .submit({ id: row.id })
          .then((res) => {
            this.$message({
              message: "提交成功",
              type: "success",
            });
            this.getList();
          })
          .finally(() => {
            row.disabled = false;
          });
      }
    },
    handleCancel() {
      this.visible = false;
    },
    //退回
    handleBack(row) {
      if (row.type == "1") {
        this.isLoading = false;
        this.title = "确认退回";
        this.visible = true;
        this.info = {
          ...row,
        };
      } else {
        row.disabled = true;
        this.$api.softwareWorkbench
          .back({ id: row.id, status: "1" })
          .then((res) => {
            this.$message({
              message: "退回成功",
              type: "success",
            });
            this.getList();
          })
          .finally(() => {
            row.disabled = false;
          });
      }
    },
    //查看详情
    handleDetails(row) {
      const { id, factoryId, factoryName, accountingMonth, roleName, status } =
        row;
      let params = {};
      let obj = {
        id,
        factoryId,
        factoryName,
        accountingMonth,
        roleName,
        status,
      };
      params =
        row.type == "6" || row.type == "7"
          ? { ...obj, otherId: row.otherId, planName: row.name }
          : obj;
        this.openSubPage({
        path: this.filterPath(row.type),
        query: { data: this.$Base64.encode(JSON.stringify(params)) },
      });
    },
    handleConfirm() {
      switch (this.title) {
        case "确认提交":
          this.isLoading = true;
          this.$api.softwareWorkbench
            .submit({ id: this.info.id, status: "2" })
            .then((res) => {
              this.$message({
                message: "提交成功",
                type: "success",
              });
              this.getList();
            })
            .finally(() => {
              this.visible = false;
              this.isLoading = false;
            });
          break;
        case "确认退回":
          this.isLoading = true;
          this.$api.softwareWorkbench
            .back({ id: this.info.id, status: "1", type: 1 })
            .then((res) => {
              this.$message({
                message: "退回成功",
                type: "success",
              });
              this.getList();
            })
            .finally(() => {
              this.visible = false;
              this.isLoading = false;
            });
      }
    },
  },
};
</script>

<style lang="stylus" scoped>
>>>.el-tabs__nav-wrap::after {
  display: block;
  height: 0;
}
</style>
