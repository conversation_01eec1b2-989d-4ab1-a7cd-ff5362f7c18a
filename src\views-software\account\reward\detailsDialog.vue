<template>
  <qDialog
    :visible="visible"
    :innerScroll="true"
    :innerHeight="500"
    title="查看详情"
    width="900px"
    :showFooter="false"
    :before-close="handleCancel"
  >
    <el-form :model="calculateForm" label-width="115px" size="small">
      <el-row>
        <el-col :span="12">
          <el-form-item label="奖惩编号:">
            {{ calculateForm.code }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="processCode" label="责任分厂:">
            {{ calculateForm.factoryName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="厂牌编号:">
            {{ calculateForm.staffCode }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="员工姓名:">
            {{ calculateForm.staffName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="执行年月:">
            {{ calculateForm.accountingMonth }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="奖惩日期:">
            {{ calculateForm.rewardDate | shortDate }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="奖惩类型:">
            {{ calculateForm.type }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="奖惩金额:">
            {{ calculateForm.amount }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="实际奖惩金额:">
            {{ calculateForm.actualAmount }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="实际奖惩厂牌:">
            {{ calculateForm.actualStaffCode }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="文件编号:">
            {{ calculateForm.fileNo }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="奖惩详情:">
            <ul class="deduction">
              <li v-for="item in detailsList" :key="item.id">
                <span>{{ item.factoryName }}</span>
                <span>{{ item.repaymentTime }}</span>
                <span>{{ item.periodAmount | moneyFormat }}</span>
                <span>{{ item.isPay }}</span>
              </li>
            </ul>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item label="奖惩原因:">
            {{ calculateForm.reason }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注说明:" prop="comments">
            <span>{{ calculateForm.comments }}</span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </qDialog>
</template>

<script>
import moment from "moment";
import { moneyFormat, moneyDelete } from "@/utils";
export default {
  name: "stageDialog",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    editForm: Object,
  },
  data() {
    return {
      calculateForm: {
        code: "",
        factoryName: "",
        staffCode: "",
        staffName: "",
        accountingMonth: "",
        rewardDate: "",
        type: "",
        amount: "",
        fileNo: "",
        actualFactoryName: "",
        actualMonth: "",
        actualAmount: "",
        reason: "",
        comments: "",
        actualStaffCode:"",
      },
      detailsList: [],
      proofs: [],
    };
  },
  async created() {
    await this.deductDetail();
  },
  methods: {
    //奖惩台账详情
    async deductDetail() {
      const { data } =
        await this.$api.softwareWorkbench.getViewDetail({
          id: this.editForm.id,
        });
      for (const key in this.calculateForm) {
        if (Object.hasOwnProperty.call(this.calculateForm, key)) {
          if (key == "amount" || key == "actualAmount") {
            this.calculateForm[key] =
              (data[key] && moneyFormat(data[key])) || "";
          } else {
            this.calculateForm[key] = data[key] || "";
          }
        }
      }
      this.detailsList = data.repaymentList || [];
    },
    handleCancel() {
      this.$emit("cancel");
    },
  },
};
</script>

<style lang="stylus" scoped>
.deduction {
  width: 100%;
  margin: 0;
  padding: 0;

  li {
    span {
      padding-right: 10px;
    }
  }
}
</style>
