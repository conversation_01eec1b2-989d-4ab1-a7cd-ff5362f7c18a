<template>
  <content-panel>
    <!-- 工会费 -->
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          size="mini"
          :inline="true"
          :model="searchForm"
          ref="searchForm"
        >
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="员工姓名"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input
              v-model.trim="searchForm.staffCode"
              size="mini"
              clearable
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenStaffCodes"
                  titleName="厂牌编号"
                />
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="扣款状态:" prop="cutStatus">
            <el-select
              v-model="searchForm.cutStatus"
              filterable
              clearable
              placeholder="请选择扣款状态"
              @change="onSearch"
            >
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="多厂牌编号:" prop="repeat">
            <el-select
              v-model="searchForm.repeat"
              filterable
              clearable
              placeholder="请选择"
              @change="onSearch"
            >
              <el-option label="全部" value="0"> </el-option>
              <el-option label="否" value="1"> </el-option>
              <el-option label="是" value="2"> </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button
            size="small"
            type="primary"

            @click="onSearch"
          >
            查询
          </el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-left>
        <ul>
          <li>
            <span>核算工厂:</span><span>{{ info.factoryName }}</span>
          </li>
          <li>
            <span>核算月份:</span><span>{{ info.accountingMonth }}</span>
          </li>
          <li>
            <span>人数:</span><span>{{ unionfeeInfo.people }}</span>
          </li>
          <li>
            <span>工会费:</span
            ><span>{{ unionfeeInfo.amount | moneyFormat }}</span>
          </li>
        </ul>
      </template>
      <template v-slot:header-right>
        <el-button size="small" type="primary" @click="handleExport">
          导出
        </el-button>
        <el-button size="small" type="primary" @click="bulkeDit" v-if="permission">批量编辑</el-button>
      </template>
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :height="maxTableHeight"
        :data="tableData"
         @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50"> </el-table-column>
        <el-table-column
          prop="staffName"
          label="员工姓名"
          width="100"
          align="left"
        >
        </el-table-column>
        <el-table-column
          prop="staffCode"
          label="厂牌编号"
          width="150"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="身份证"
          width="200"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <div class="idCardBox">
              <div class="idCard">{{ row.idCard }}</div>
            </div>
            <i
              v-if="row.idCard"
              :class="[
                'iconfont',
                row.isShow ? 'icon-guanbi' : ' icon-yincangmima',
              ]"
              @click="toggleEye(row)"
            ></i>
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="工会费" width="150" align="left">
          <template slot-scope="{ row }">
            {{ row.amount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          prop="amount"
          label="本厂是否扣款"
          width="150"
          align="left"
        >
          <template slot-scope="{ row }">
            {{
              (row.isLocalDeduct && (row.isLocalDeduct == "Y" ? "是" : "否")) ||
              ""
            }}
          </template>
        </el-table-column>

        <el-table-column
          prop="cutStatus"
          label="扣款状态"
          width="100"
          align="left"
        >
          <template slot-scope="{ row }">
            <span
              :style="{
                color: row.cutStatus == 'abnormal' ? 'red' : '#0BB78E',
              }"
              >{{ row.cutStatus == "abnormal" ? "异常" : "正常" }}</span
            >
          </template>
        </el-table-column>

        <el-table-column
          prop="remark"
          label="备注说明"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column v-if="permission" label="" align="left" width="100">
          <template slot-scope="scope">
            <el-button
              size="small"
              type="text"
              @click="handlerEdit(scope.$index, scope.row)"
            >
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div style="text-align: right">
          <el-pagination
            @size-change="onSizeChange"
            @current-change="onNumChange"
            :current-page="pageNum"
            :page-size="pageSize"
            :total="total"
            :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper"
          >
          </el-pagination>
        </div>
      </template>
    </table-panel>
    <edit-dialog
      v-if="visible"
      :visible="visible"
      :formData="editForm"
      @cancel="handleCancel"
    ></edit-dialog>
    <batch-edit-dialog
      v-if="batchisVisible"
      :isVisible="batchisVisible"
      :formData="formData"
      @success="successCancel"
    ></batch-edit-dialog>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import editDialog from "./editDialog.vue";
import batchEditDialog from "./batchEditDialog";

export default {
  name: "LogisticsUnionfee",
  components: { editDialog,batchEditDialog},
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      searchForm: {
        staffCode: "",
        staffName: "",
        repeat: "0",
        cutStatus: "",
      },
      editForm: {},
      statusOptions: Object.freeze([
        { label: "正常", value: "normal" },
        { label: "异常", value: "abnormal" },
      ]),
      unionfeeInfo: {}, //工会费统计
      info: {},
      factoryId: "",
      tableData: [],
      loading: true,
      resizeOffset: 72,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      permission: "",
      filterParam: {},
      params: {},
      visible: false,
      deductionFactory: [],
      isShowSwitch: [],
      batchisVisible:false,
      idList:[],
    };
  },
  watch: {
    $route: {
      handler(value) {
        if (
          value.path.includes("unionfee") &&
          value.path.includes("logistics")
        ) {
          this.info = JSON.parse(this.$Base64.decode(value.query.data));
          this.factoryId = this.info.factoryId;
          this.getList();
          this.getStatistic();
          this.getPermission();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
     //多选
     handleSelectionChange(val) {
      this.idList = (val && val.map((item) => item.id)) || [];
    },
    //获取工会费列表
    getList() {
      this.loading = true;
      this.$api.logisticsDataUpload.unionFee
        .getUnionFee({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: {
            factoryId: this.factoryId,
            accountingMonth: this.info.accountingMonth,
            ...this.filterParam,
            ...this.params,
            repeat:
              this.searchForm.repeat == "0"
                ? ""
                : String(+this.searchForm.repeat - 1),
          },
        })
        .then(({ data: { list, total } }) => {
          this.tableData =
            list.map((item) => ({
              ...item,
              idCardOrigin: item.idCard,
              idCardDecoded: "",
              isShow: false,
            })) || [];
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //是否具有代办任务操作权限
    getPermission() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.info.accountingMonth,
        type: "40",
      };
      this.$api.logisticsWorkbench.agencyPermission(params).then((res) => {
        this.permission = res.data;
      });
    },
    //获取统计信息
    getStatistic() {
      this.$api.logisticsDataUpload.unionFee
        .statistictUnionFee({
          accountingMonth: this.info.accountingMonth,
          factoryId: this.factoryId,
          ...this.filterParam,
          ...this.params,
          repeat:
            this.searchForm.repeat == "0"
              ? ""
              : String(+this.searchForm.repeat - 1),
        })
        .then(({ success, data }) => {
          if (success) {
            this.unionfeeInfo = data || {};
          }
        });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.searchForm.repeat = "0";
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
       // 批量编辑
       bulkeDit(){
        if (this.idList.length === 0) {
        this.$message({
          message: "请先勾选需要批量编辑的内容",
          type: "warning",
        });
        return;
      }
      this.batchisVisible = true;
      this.formData = {
        idList: this.idList,
      };

    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0)
          this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
      this.getStatistic();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    toggleEye(row) {
      if (!row.isShow) {
        if (row.idCardDecoded) {
          row.idCard = row.idCardDecoded;
          row.isShow = true;
          return;
        }
        if (row.staffCode.includes("SG")) {
          this.$api.information.employee
            .idCardDecodeStaff({ idCardRsa: row.idCardRsa })
            .then((res) => {
              row.idCard = res.data;
              row.idCardDecoded = res.data;
              row.isShow = true;
            });
        } else {
          this.$api.information.employee.decrypt(row.idCardRsa).then((res) => {
            row.idCard = res.data;
            row.idCardDecoded = res.data;
            row.isShow = true;
          });
        }
      } else {
        row.idCard = row.idCardOrigin;
        row.isShow = false;
      }
    },
    //编辑
    handlerEdit(index, row) {
      this.visible = true;
      const { id } = row;
      this.editForm = { id };
    },
    //导出
    handleExport() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.info.accountingMonth,
      };
      if (Object.keys(this.filterParam).length) {
        params = {
          ...params,
          ...this.filterParam,
        };
      }
      this.$api.common
        .doExport("logisticsExportunionfees", {
          ...params,
          ...this.params,
          repeat:
            this.searchForm.repeat == "0"
              ? ""
              : String(+this.searchForm.repeat - 1),
        })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success("导出操作成功，请前往导出记录查看详情");
          }
        });
    },
    handleCancel(type) {
      this.visible = false;
      if (type == "cancel") return;
      this.getList();
      this.getStatistic();
    },
    onSizeChange(pageSize) {
      this.pageSize = pageSize;
      // 切换每页条数后从第一页查询
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    successCancel(type) {
      this.batchisVisible = false;
      if(type == 'cancel') return
      this.onSearch();
    },
  },
};
</script>

<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}

>>>.el-tabs__nav-wrap::after {
  height: 0;
}

ul {
  list-style: none;
  display: flex;
  padding: 0;
}
li{
padding-right:25px
}
i {
  font-style: normal;
}

>>>.table-panel-footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .table_footer {
    overflow: auto;

    // >ul {
    //   display: flex;
    //   list-style: none;
    //   padding: 0;
    //   margin: 0;

    //   li {
    //     min-width: 80px;
    //     padding: 0 5px;
    //   }
    // }
  }
}

>>>.el-table__row {
  td {
    &:nth-child(4) {
      .cell {
        display: flex;
        justify-content: space-between;

        .idCardBox {
          width: 80%;
          .idCard {
            display: block;
            cursor: pointer;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
        }
      }
    }
  }

}
>>>[data-v-6ddfdfae] .table-panel-footer-top{

   display: flex;
     justify-content: flex-end;

 }
</style>
