import TableFilterService from '@/utils/table-filter-service';
import { numberCompareEnum } from '@/utils/constant';

/**
 * 业务模块修改:
 * 1. data:
 *    下拉列表字段:selectFields: ['unit', 'type', ]
 *    tableData字段名:tableDataKey: 'tableData'
 * 2. created:this.initFilter();
 * 3. 每次加载表格数据后
 *    this.updateFilterOptions(this.tableData);
 *    this.clearFilter();
 *    this.tableFilterService.resetAll();
 *    this.tableFilterService.updateData(this.tableData);
 */
export default {
  data() {
    return {
      tableFilterService: null,
      headerFilterOptions: {},
    };
  },
  computed: {
    tableDataName() {
      return this.tableDataKey || 'tableData';
    }
  },
  methods: {
    initFilter() {
      this.tableFilterService = new TableFilterService([]);
    },
    updateFilterOptions(data = []) {
      const headerFilterOptions = {};
      const selectFields = this.selectFields || [];
      data.forEach(item => {
        selectFields.forEach(field => {
          if (!headerFilterOptions[field]) {
            headerFilterOptions[field] = [];
          }
          if (!headerFilterOptions[field].includes(item[field]) && item[field] !== '' && item[field] !== null) {
            headerFilterOptions[field].push(item[field]);
          }
        });
      });
      console.log(headerFilterOptions);
      this.headerFilterOptions = headerFilterOptions;
    },
    confirmFilter({ prop, type, textValue, numberValues, selectValues, dateValues }) {
      console.log('confirmFilter:', prop, type, textValue, numberValues, selectValues, dateValues);

      // 重置条件
      let isReset = false;
      if (type === 'text') {
        isReset = textValue.trim() === '';
      } else if (type === 'number') {
        isReset = numberValues.filter(item => item !== undefined).length === 0;
      } else if (type === 'select') {
        isReset = selectValues.length === 0;
      } else if (type === 'date') {
        isReset = dateValues.filter(item => item !== null).length === 0;
      }

      if (isReset) {
        this.tableData = this.tableFilterService.resetField(prop);
      } else {
        this.tableData = this.tableFilterService.filterByField(prop, {
          type, textValue, numberValues, selectValues, dateValues
        });
      }
    },
    confirmSort({ prop, sort, type }) {
      console.log('confirmSort:', prop, sort, type);
      this.tableData = this.tableFilterService.sortByField(prop, sort, type === 'number');
    },
    clearFilter() {
      Object.keys(this.$refs).forEach(key => {
        if (key.startsWith('headerFilter')) {
          const ref = this.$refs[key];
          ref && ref.resetModel();
        }
      });
    }
  }
};