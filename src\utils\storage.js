class Storage {
  constructor(storage) {
    this.storage = storage;
  }

  set(key, value) {
    if (value !== null && typeof value === 'object') {
      value = JSON.stringify(value);
    }
    
    this.storage.setItem(key, value);
  }

  get(key) {
    let val = this.storage.getItem(key);

    if (val === null || val === 'null') {
      return null;
    } else if (val === 'undefined') {
      return undefined;
    } else {
      try {
        return JSON.parse(val)
      } catch (error) {
        // 非json值直接返回
        return val;
      }
    }
  }

  remove(key) {
    this.storage.removeItem(key)
  }
};

const session = new Storage(sessionStorage);
const local = new Storage(localStorage);

export {session, local};
export default Storage;