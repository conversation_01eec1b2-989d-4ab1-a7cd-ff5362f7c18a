<template>
  <!--新员工补贴20%扣款参数配置 -->
  <content-panel>
    <div class="config-header">
      <div class="header-left"></div>
      <div class="header-right">
        <el-button
          size="small"
          type="primary"
          @click="isModifyType = true"
          v-if="!isModifyType"
          >编辑</el-button
        >
        <template v-else>
          <el-button size="small" type="primary" @click="confirmSave"
            >保存</el-button
          >
          <el-button size="small" type="warning" @click="getList"
            >取消</el-button
          >
        </template>
      </div>
    </div>
    <div class="config-box" v-for="(item,index) in configFormList " :key="index">

    <div class="config-content">
      <span class="deduction">熟手补贴扣款</span>
      <span class="switchMg">启用开关</span>
      <el-switch
        :disabled="!isModifyType"
        class="mg15"
        v-model="item.onOff"
        active-value="on"
        inactive-value="off"
      ></el-switch>
      <el-tooltip
        content="开启后配置生效,反之不生效"
        placement="right"
        style="margin: 0px 5px"
      >
        <i class="el-icon-question"></i>
      </el-tooltip>
    </div>
    <div class="blockHeader"></div>
    <div class="config-content">
      <span>入职</span>
      <el-input
        :disabled="!isModifyType"
        v-model="item.leaveMonths"
        type="text"
        maxlength="4"
        style="width: 60px; margin: 0px 5px"
        onkeyup="value=value.replace(/[^0-9]/g, '')"
      ></el-input>
      <span>个月内离职的熟手员工，扣除的熟手补贴费用=已享受熟手补贴总和的</span>
      <el-select
        style="width: 90px; margin: 0px 8px"
        :disabled="!isModifyType"
        placeholder=""
        v-model="item.deductionRatio"
      >
        <el-option
          v-for="value in percentageOptions"
          :key="value"
          :label="`${value}%`"
          :value="value"
        ></el-option>
      </el-select>
    </div>

  </div>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";

import moment from "moment";
export default {
  name: "PlateTypeConfigNewemployee",
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      isModifyType: false,
      configFormList:[{}],
      percentageOptions: Array.from({ length: 101 }, (_, i) => i),
    };
  },
  computed: {},
  methods: {
    getList() {
      this.isModifyType = false;
      this.$api.plateTypeSystemConfig
        .employeeConfigGlobal("PERCENTAGE_DEDUCTION")
        .then((res) => {
          // ? [res.data[0]] :
          this.configFormList = res.data || [{taskType:"PERCENTAGE_DEDUCTION_SKILLED_SUBSIDY",onOff:'on'}]
        });
    },
    confirmSave() {
      if (!this.configFormList[0].leaveMonths) {
        this.$message.error("请输入月份");
        return;
      }
      this.configFormList.forEach((x)=>{
        x.configJson = JSON.stringify(x)
      })
      this.$api.plateTypeSystemConfig
        .editConfigGlobal(this.configFormList)
        .then((res) => {
          this.$message.success("操作成功");
          this.getList();
          this.isModifyType = false;
        });
    },
  },
  created() {
    this.getList();
  },
};
</script>
<style lang="stylus" scoped>
>>>.main-area {
    height:100vh
}
.deduction{
    font-weight:bold;
    line-height:30px
}
.switchMg{
    margin:0px 10px 0px 20px;
    font-weight:400
   }
.el-select{
  width:80px
  height:30px
}
.config-header{
  display:flex;
  justify-content:space-between;
  align-items:center;

  .header-left{
    font-weight:bold;
   .switchMg{
    margin:0px 10px 0px 20px;
    font-weight:400
   }
  }
}
.config-content{
  display:flex;
  align-items:center;
  margin:10px 0px
}
>>>.el-input__inner{
  height:30px;
  line-height:30px
}
>>> .el-range__icon {
  line-height:23px !important
}
>>>  .el-range__close-icon {
  line-height:23px !important
}
>>> .el-range-separator{
  line-height:23px !important
}

.config-bottom{
 display:flex;
 align-items:center;

}
.mg10{
  margin:0px 10px
}
.blockHeader {
  border-bottom: 1px solid #ccc;
  font-weight: bold;
}
</style>
