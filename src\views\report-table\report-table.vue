<template>
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form  :inline="true" :model="searchForm" ref="searchForm" label-width="40px">
          <el-form-item label="排程日期" prop="planDate" label-width="70px">
            <el-date-picker
              class="date-input"
              size="small"
              v-model="searchForm.planDate"
              :default-time="['00:00:00', '23:59:59']"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="排程单号" prop="scheduleNumber" label-width="70px">
            <el-input v-model="searchForm.scheduleNumber" size="small" clearable></el-input>
          </el-form-item>
          <el-form-item label="生产订单号" prop="orderNo" label-width="90px">
            <el-input v-model="searchForm.orderNo" size="small" clearable></el-input>
          </el-form-item>
          <el-form-item label="工厂" prop="factoryCode">
            <el-select v-model="searchForm.factoryCode" size="small" clearable @change="onSearch">
              <el-option v-for="item in factoryOptions"
                :key="item.factoryCode"
                :label="item.factoryCode + item.storeShortName"
                :value="item.factoryCode">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="工序" prop="processCode">
            <el-select v-model="searchForm.processCode" size="small" clearable @change="onSearch">
              <el-option v-for="item in procedureOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="物料编码/描述" prop="materialCode" label-width="100px">
            <el-input class="form-input" v-model="searchForm.materialCode" size="small" clearable></el-input>
          </el-form-item>
          <el-form-item label="成品编码/描述" prop="productCode" label-width="100px">
            <el-input class="form-input" v-model="searchForm.productCode" size="small" clearable></el-input>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary" @click="onSearch">查询</el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">重置</el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-left>
        <el-button type="primary" size="small" @click="handleExport">导出</el-button>
      </template>
      <el-table
        stripe
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :data="tableData"
        :height="maxTableHeight"
        style="width: 100%">
        <el-table-column type="index" width="50" label="序号"></el-table-column>
        <el-table-column prop="planDate" label="排程日期" width="100">
          <template slot-scope="scope">
            {{scope.row.planDate | shortDate}}
          </template>
        </el-table-column>
        <el-table-column prop="scheduleNumber" label="排程单号" width="120" ></el-table-column>
        <el-table-column prop="factoryCode" label="工厂" width="55"></el-table-column>
        <el-table-column prop="processCode" label="工序" width="50"></el-table-column>
        <el-table-column prop="orderNo" label="生产订单号" width="120" ></el-table-column>
        <el-table-column prop="productCode" label="成品编码" show-overflow-tooltip></el-table-column>
        <el-table-column prop="productName" label="成品描述" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="materialCode" label="物料编码" show-overflow-tooltip></el-table-column>
        <el-table-column prop="materialName" label="物料描述" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="originalMaterialCount" label="原定额量" width="100" ></el-table-column>
        <el-table-column prop="materialCount" label="发板量" width="100" ></el-table-column>
      </el-table>
      <template v-slot:footer>
        <div style="text-align: right;">
          <el-pagination
            @size-change="onSizeChange"
            @current-change="onNumChange"
            :current-page="pageNum"
            :page-size="pageSize"
            :total="total"
            :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>
        </div>
      </template>
    </table-panel>
  </content-panel>
</template>

<script>
import tableMixin from '@/utils/tableMixin';
import pagePathMixin from "@/utils/page-path-mixin";

export default {
  name: 'ReportTable',
  mixins: [tableMixin,pagePathMixin],
  computed: {
  },
  data() {
    return {
      searchForm: {
        productCode: '',
        materialCode: '',
        factoryCode: '',
        orderNo: '',
        scheduleNumber: '',
        processCode: '',
        planDate: []
      },

      // 表格相关
      tableData: [],
      selection: null,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      filterParam: {},
      loading: false,
      schedulePlanExtraId: '',
      resizeOffset: 70,
      factoryOptions: [],
      procedureOptions: [
        {value: 10, label: '10'},
        {value: 11, label: '11'},
        {value: 15, label: '15'}
      ],
    }
  },
  methods: {
    init() {
      // this.getFactoryList();
      this.getList();
    },
    getFactoryList() {
      return this.$store.dispatch('user/getUserFactoryList')
        .then(factoryList => {
          this.factoryOptions = factoryList;
        });
    },
    getList() {
      this.tableData = [ {
        "scheduleNumber" : "202107026180",
        "factoryCode" : "6180",
        "processCode" : "11",
        "planDate" : "2021-07-02T08:04:24.000+0000",
        "planDateStr" : "2021-07-02",
        "orderNo" : "109325476",
        "productCode" : "66162100203",
        "productName" : "61621二门书柜左233#哑4-3侧板",
        "originalMaterialCode" : "101000003",
        "originalMaterialName" : "5mm中纤板2440*1220mm",
        "originalMaterialCount" : 8.156,
        "materialCode" : "101000003",
        "materialName" : "5mm中纤板2440*1220mm",
        "materialCount" : 8.156,
        "materialCount3" : 8.156,
        "replaceFlg" : null
      }, {
        "scheduleNumber" : "202107026180",
        "factoryCode" : "6180",
        "processCode" : "11",
        "planDate" : "2021-07-02T08:04:24.000+0000",
        "planDateStr" : "2021-07-02",
        "orderNo" : "109325476",
        "productCode" : "66162100203",
        "productName" : "61621二门书柜左233#哑4-3侧板",
        "originalMaterialCode" : "101000013",
        "originalMaterialName" : "15mm中纤板2440*1220mm",
        "originalMaterialCount" : 8.156,
        "materialCode" : "101000013",
        "materialName" : "15mm中纤板2440*1220mm",
        "materialCount" : 6.447,
        "materialCount3" : 6.447,
        "replaceFlg" : null
      }, {
        "scheduleNumber" : "202107026180",
        "factoryCode" : "6180",
        "processCode" : "11",
        "planDate" : "2021-07-02T08:04:24.000+0000",
        "planDateStr" : "2021-07-02",
        "orderNo" : "109325476",
        "productCode" : "66162100203",
        "productName" : "61621二门书柜左233#哑4-3侧板",
        "originalMaterialCode" : "101000091",
        "originalMaterialName" : "5mm单面70#三胺板2440*1220mm",
        "originalMaterialCount" : 47.85,
        "materialCode" : "101000091",
        "materialName" : "5mm单面70#三胺板2440*1220mm",
        "materialCount" : 47.85,
        "materialCount3" : 47.85,
        "replaceFlg" : null
      }, {
        "scheduleNumber" : "202107026180",
        "factoryCode" : "6180",
        "processCode" : "11",
        "planDate" : "2021-07-02T08:04:24.000+0000",
        "planDateStr" : "2021-07-02",
        "orderNo" : "109325476",
        "productCode" : "66162100203",
        "productName" : "61621二门书柜左233#哑4-3侧板",
        "originalMaterialCode" : "101000093",
        "originalMaterialName" : "5mm双面70#三胺板2440*1220mm",
        "originalMaterialCount" : 53.44,
        "materialCode" : "101000093",
        "materialName" : "5mm双面70#三胺板2440*1220mm",
        "materialCount" : 53.44,
        "materialCount3" : 53.44,
        "replaceFlg" : null
      }, {
        "scheduleNumber" : "202107026180",
        "factoryCode" : "6180",
        "processCode" : "11",
        "planDate" : "2021-07-02T08:04:24.000+0000",
        "planDateStr" : "2021-07-02",
        "orderNo" : "109325476",
        "productCode" : "66162100203",
        "productName" : "61621二门书柜左233#哑4-3侧板",
        "originalMaterialCode" : "101011270",
        "originalMaterialName" : "15mm单面233#哑光免漆板(覆膜)单面70#三胺板2440*1220mm",
        "originalMaterialCount" : 50.94,
        "materialCode" : "101011270",
        "materialName" : "15mm单面233#哑光免漆板(覆膜)单面70#三胺板2440*1220mm",
        "materialCount" : 50.94,
        "materialCount3" : 50.94,
        "replaceFlg" : null
      }];
      return;


      this.loading = true;
      const params = {
        pageNo: this.pageNum,
        pageSize: this.pageSize,
        data: {
          ...this.filterParam
        }
      }
      this.$api.replaceMgt.getReplacedPanelList(params)
        .then(({data: {records, total}}) => {
          this.tableData = records || [];
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleCurrentChange(row) {
      this.selection = row;
    },
    onSizeChange(pageSize) {
      this.pageSize = pageSize;
      // 切换每页条数后从第一页查询
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    handleExport() {
      let params = {
        ...this.filterParam
      };
      this.$api.common.doExport('issueComparisonDataExport', params).then(() => {
        this.$message.success('导出操作成功，请前往导出记录查看详情');
      });
    },
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.filterParam = {};
      this.onSearch();
    },
    onSearch() {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (key === 'planDate' && val) {
          this.filterParam.planDateStart = val[0] || '';
          this.filterParam.planDateEnd = val[1] || '';
        } else if (typeof val !== 'undefined' && val !== null && val !== '') {
          this.filterParam[key] = val;
        }
      }
      this.pageNum = 1;
      this.getList();
    },
  },
  created() {
    this.init();
  }
}
</script>

<style lang="stylus" scoped>
.date-input
  width 220px !important
</style>