<template>
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          size="mini"
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          label-position="right"
          label-width="98px"
        >
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="员工姓名"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input
              v-model.trim="searchForm.staffCode"
              size="mini"
              clearable
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenStaffCodes"
                  titleName="厂牌编号"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="核算班组:" prop="processId">
            <el-select
              @change="onSearch"
              v-model="searchForm.processId"
              filterable
              clearable
              placeholder="请选择核算班组"
            >
              <el-option
                v-for="item in procedureOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button
            size="small"
            type="primary"

            @click="onSearch"
          >
            查询
          </el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-left>
        <div style="display: flex; align-items: center">
          <div v-if="isShow">更新中</div>
          <div v-else>
            <span
              v-show="statusName != ''"
              :style="{ color: statusName == 1 ? 'red' : '#24c69a' }"
              >{{
                statusName == 0
                  ? "处理中"
                  : statusName == 1
                  ? "处理失败"
                  : "处理成功"
              }}</span
            >
          </div>
          <ul>
            <li>
              <span>核算组织:</span
              ><span>{{ wageStatisticInfo.factoryName }}</span>
            </li>
            <li>
              <span>核算月份:</span
              ><span>{{ wageStatisticInfo.accountingMonth }}</span>
            </li>
            <li>
              <span>人数:</span><span>{{ debitInfo.peopleTotal }}</span>
            </li>
            <li>
              <span>计件工资（系统）:</span
              ><span>{{ moneyFormatPiece(debitInfo.systemAmount) }}</span>
            </li>
            <li>
              <span>计件工资（调整）:</span
              ><span>{{ moneyFormatPiece(debitInfo.editAmount) }}</span>
            </li>

            <li>
              <span>新员工补贴:</span
              ><span>{{ moneyFormatPiece(debitInfo.subsidyAmount) }}</span>
            </li>
            <li>
              <span>线下工资:</span
              ><span>{{ moneyFormatPiece(debitInfo.offlineWage) }}</span>
            </li>
            <li>
              <span>补贴+计件工资:</span
              ><span>{{ moneyFormatPiece(debitInfo.totalPieceWage) }}</span>
            </li>
          </ul>
        </div>
      </template>
      <template v-slot:header-right>
        <div
          ref="btnRight"
          style="display: flex; justify-content: space-between"
        >
          <el-button
            v-show="permission"
            size="small"
            type="primary"
            @click="handleUpdate"
          >
            同步更新
          </el-button>
          <el-button
            size="small"
            type="primary"
            @click="handleImport"
            v-show="permission"
          >
            导入
          </el-button>
          <el-button size="small" type="primary" @click="handleExport">
            导出
          </el-button>
        </div>
      </template>
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :height="maxTableHeight"
        :data="tableData"
      >
        <el-table-column type="index" label="序号" width="50" align="left">
        </el-table-column>
        <el-table-column
          prop="staffName"
          label="员工姓名"
          width="100"
          align="left"
        >
        </el-table-column>
        <el-table-column
          prop="staffCode"
          label="厂牌编号"
          width="130"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="accountingGroup"
          label="核算班组"
          width="130"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="systemPieceWage"
          label="计件工资（系统）"
          width="150"
          align="left"
        >
        </el-table-column>
        <el-table-column
          prop="offlineEdit"
          label="计件工资（调整）"
          width="130"
          align="left"
        >
        </el-table-column>
        <el-table-column
          prop="subsidyAmount"
          label="新员工补贴"
          width="150"
          align="left"
        >
        </el-table-column>
        <el-table-column
          prop="offlineWage"
          label="线下工资"
          width="130"
          align="left"
        >
        </el-table-column>

        <el-table-column
          prop="totalPieceWage"
          label="补贴+计件工资"
          width="180"
          align="left"
        >
        </el-table-column>
        <el-table-column
          prop="comments"
          label="备注说明"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="updateTime"
          width="150"
          label="修改时间"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column width="100" label="操作" align="left" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="small"
              type="text"
              @click="reconciliation(scope.$index, scope.row)"
              :disabled="!pieceWageFlag"
            >
              计件调整
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div style="text-align: right">
          <el-pagination
            @size-change="onSizeChange"
            @current-change="onNumChange"
            :current-page="pageNum"
            :page-size="pageSize"
            :total="total"
            :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper"
          >
          </el-pagination>
        </div>
      </template>
    </table-panel>
    <adjustDialog
      v-if="visible"
      :visible="visible"
      :adjustForm="adjustForm"
      @cancel="handleCancel"
    >
    </adjustDialog>
    <common-dialog
      v-if="isVisible"
      :visible="isVisible"
      @cancel="commonCancel"
    ></common-dialog>
    <Import
      v-if="ImportVisible"
      :visible="ImportVisible"
      @cancel="cancel"
      @confirm="confirm"
      :importInfo="importInfo"
    />
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import { moneyFormat, moneyDelete } from "@/utils";
import pagePathMixin from "@/utils/page-path-mixin";
import adjustDialog from "./component/adjustDialog";
import commonDialog from "./component/commonDialog";
export default {
  name: "Wage",
  mixins: [tableMixin,pagePathMixin],
  components: {
    adjustDialog,
    commonDialog,
  },
  data() {
    return {
      searchForm: {
        content: "",
        processId: "",
        staffCode: "",
        staffName: "",
      },
      adjustForm: {},
      isShow: false,
      timmer: null,
      debitInfo: {},
      multiProcess: [
        { label: "是", value: "1" },
        { label: "否", value: "0" },
      ],
      tableData: [],
      procedureOptions: [],
      filterParam: {},
      params: {},
      loading: true,
      factoryId: "",
      resizeOffset: 55,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      title: "",
      wageStatisticInfo: {},
      permission: "",
      visible: false,
      isVisible: false,
      ImportVisible: false,
      importInfo: {},
      pieceWageFlag: false,
      statusName: "",
    };
  },
  beforeRouteLeave(to, form, next) {
    this.$destroy();
    next();
  },
  destroyed() {
    clearInterval(this.timmer);
  },
  watch: {
    $route: {
      handler(value) {
        if (value.path.includes("wage") && value.path.includes("customized")) {
          this.wageStatisticInfo = JSON.parse(this.$Base64.decode(value.query.data));
          this.factoryId = this.wageStatisticInfo.factoryId;
          this.$nextTick(() => {
            this.$api.dataUpload.pieceRateWage
              .getGroups({
                factoryId: this.factoryId,
                accountingMonth: this.wageStatisticInfo.accountingMonth,
              })
              .then(({ success, data }) => {
                if (success) {
                  this.procedureOptions = data || [];
                }
              });
            this.importInfo = {
              reportName: "pieceWageImport",
              paramMap: {
                columnValue: "计件工资",
                factoryId: this.factoryId,
                accountingMonth: this.wageStatisticInfo.accountingMonth,
                id: this.wageStatisticInfo.id,
              },
            };
            this.getList();
            this.getDebitList();
            this.getPermission();
            this.getPermission1();
            this.getSubscriptList();
          });
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    getSubscriptList() {
      var formData = new FormData();
      formData.append(
        "accountingMonth",
        this.wageStatisticInfo.accountingMonth
      );
      formData.append("factoryId", this.factoryId);
      return this.$api.dataUpload.pieceRateWage
        .syncTaskStatus(formData)
        .then((res) => {
          if (res.code == 200) {
            this.statusName = res.data ? res.data.processStatus : "";
          }
        });
    },
    times() {
      this.timmer = window.setInterval(async () => {
        this.isShow = true;
        await this.getSubscriptList();
        if (this.statusName != 0) {
          clearInterval(this.timmer);
          this.getList();
          this.getDebitList();
        }
        this.isShow = false;
      }, 3000);
    },
    //获取计件工资列表
    getList() {
      this.loading = true;
      this.$api.dataUpload.pieceRateWage
        .getPieceRateWageList({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: {
            factoryId: this.factoryId,
            accountingMonth: this.wageStatisticInfo.accountingMonth,
            ...this.filterParam,
            ...this.params,
          },
        })
        .then(({ data: { list, total } }) => {
          this.tableData =
            list.map((item) => ({
              ...item,
              systemPieceWage: moneyFormat(item.systemPieceWage),
              offlineEdit: moneyFormat(item.offlineEdit),
              pieceWage: moneyFormat(item.pieceWage),
              subsidyAmount: moneyFormat(item.subsidyAmount),
              totalPieceWage: moneyFormat(item.totalPieceWage),
            })) || [];
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    getDebitList() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.wageStatisticInfo.accountingMonth,
        ...this.filterParam,
        ...this.params,
      };
      this.$api.dataUpload.pieceRateWage
        .getSubscriptList(params)
        .then((res) => {
          this.debitInfo = res.data
            ? res.data
            : {
                peopleTotal: 0,
                systemAmount: 0,
                editAmount: 0,
                incomeAmount: 0,
                remainAmount: 0,
                totalAmount: 0,
              };
        });
    },
    getPermission1() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.wageStatisticInfo.accountingMonth,
        type: "2",
      };
      this.$api.workbench.agencyPermission(params).then((res) => {
        this.permission = res.data;
      });
    },
    //是否具有待办任务操作权限
    getPermission() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.wageStatisticInfo.accountingMonth,
        type: "2",
      };
      this.$api.statiStics.getPieceWagePermission(params).then((res) => {
        this.pieceWageFlag = res.data.pieceWageFlag;
      });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0)
          this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
      this.getDebitList();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    moneyFormatPiece(value) {
      return moneyFormat(value);
    },
    //导入
    handleImport() {
      this.title = "导入";
      this.ImportVisible = true;
    },
    //导出
    handleExport() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.wageStatisticInfo.accountingMonth,
      };
      if (Object.keys(this.filterParam).length) {
        params = {
          ...params,
          ...this.filterParam,
        };
      }
      this.$api.common
        .doExport("exportPieceWage", { ...params, ...this.params })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success("导出操作成功，请前往导出记录查看详情");
          }
        });
    },
    //同步更新
    handleUpdate() {
      this.isVisible = true;
    },
    //计件调整
    reconciliation(index, row) {
      this.visible = true;
      this.adjustForm = {
        id: row.id || "",
        factoryId: this.factoryId || "",
        factoryName: this.wageStatisticInfo.factoryName || "",
        accountingMonth: row.accountingMonth,
        staffCode: row.staffCode || "",
        staffName: row.staffCode || "",
      };
    },
    onSizeChange(pageSize) {
      this.pageSize = pageSize;
      // 切换每页条数后从第一页查询
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    cancel(value) {
      this.ImportVisible = value;
    },
    confirm(value) {
      this.ImportVisible = value;
      this.getDebitList();
    },
    handleCancel(type) {
      this.visible = false;
      if (type == "cancel") return;
      this.getList();
      this.getDebitList();
    },
    commonCancel(type, name = "") {
      this.isVisible = false;
      if (type == "cancel" && !name) return;
      this.isShow = true;
      if (name == "error") return;
      this.times();
    },
  },
};
</script>

<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}

>>>.el-tabs__nav-wrap::after {
  height: 0;
}

.el-icon-success {
  color: green;
}

ul {
  list-style: none;
  display: flex;
  align-items: center;
  padding: 0;
  margin: 0
  margin-left: 50px
  li{
    margin-right: 10px;
  }
}

i {
  font-style: normal;
}
</style>
