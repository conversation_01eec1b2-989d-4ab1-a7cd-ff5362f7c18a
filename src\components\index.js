import Vue from 'vue'
// import dialog from './dialog/dialog'
import FontIcon from './font-icon'
import qDialog from './qDialog/qDialog'
import remark from './remark/remark'
import textCollapse from './text-collapse/text-collapse'
import SearchBox from './search-box/search-box'
import TablePanel from './TablePanel/table-panel'
import ContentPanel from './content-panel/content-panel'
// import QTableColumn from './qTable/qTableColumn/q-table-column.vue'
// import QTableHeaderFilter from './qTable/q-table-header-filter/q-table-header-filter'
// import QTableHeaderFilterSimple from './qTable/q-table-header-filter-simple/q-table-header-filter-simple'
// import QTableHeaderFilterFront from './qTable/q-table-header-filter-front/q-table-header-filter-front'
import KVPanel from './k-v-panel/k-v-panel'
import ScrollPanel from './horizontal-scroll-panel/scroll-panel'
import TextTip from './text-with-tooltip/text-with-tooltip'
import CustomScene from './customScene'
import SearchBatch from './searchBatch/searchBatch'
import Import from './importDialog/import'
import taskVerify from './taskVerify/verifyDialog'
import payrollCustom from './payroll-custom/custom'
import SelectMultiple from './select-multiple/select-multiple'
const components = [
  FontIcon, qDialog, remark, textCollapse, SearchBox, TablePanel, 
  ContentPanel, KVPanel, ScrollPanel, TextTip,CustomScene,SearchBatch,Import,taskVerify,payrollCustom,SelectMultiple
];
components.forEach(component => {
  Vue.component(component.name, component);
});