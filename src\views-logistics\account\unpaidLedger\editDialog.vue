<template>
  <qDialog
    :visible="isVisible"
    title="编辑"
    :innerHeight="250"
    width="700px"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel">
    <el-form :model="editForm"
      label-width="130px"
      ref="editForm"
      :rules="rules"
      size="small">
      <el-row>
        <el-col :span="11">
          <el-form-item
            class="staffName"
            label="员工姓名:">{{editForm.staffName}}</el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item
            class="staffName"
            label="厂牌编号:">{{editForm.staffCode}}</el-form-item></el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item
            class="staffName"
            label="未付金额:">{{editForm.notPayAmount | moneyFormat}}</el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item
            label="其他扣款:"
            prop="otherDeduct">
            <el-input
              v-model="editForm.otherDeduct"
              clearable
              placeholder="请输入其他扣款"
              @blur="onBlur('otherDeduct')"
              @clear="onBlur('otherDeduct')"></el-input>
          </el-form-item></el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item
            label="自离扣款:"
            prop="leaveSelfDeduct">
            <el-input
              v-model="editForm.leaveSelfDeduct"
              clearable
              placeholder="请输入自离扣款"
              @blur="onBlur('leaveSelfDeduct')"
              @clear="onBlur('leaveSelfDeduct')"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item
            class="staffName"
            label="实付金额:">{{editForm.actualAmount | moneyFormat}}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            class="staffName"
            label="其他扣款备注:"
            label-width="150px">
            <el-input
              type="textarea"
              v-model.trim="editForm.remark"
              resize="none"
              rows="2"
              show-word-limit
              maxlength="50"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </qDialog>
</template>

<script>
import { numSub } from '@/utils'
import { moneyFormat, moneyDelete } from "@/utils";
export default {
  props: {
    isVisible: {
      type: Boolean,
      required: true
    },
    paymentId: String
  },
  data() {
    return {
      editForm: {
        staffName: "",
        staffCode: "",
        notPayAmount: "",
        otherDeduct: "",
        leaveSelfDeduct: "",
        actualAmount: "",
        remark: ""
      },
      rules: {
        otherDeduct: [{ required: true, message: "其他扣款不能为空", trigger: 'blur' },
        {
          validator: (rule, value, callback) => {
            if (!/^-?\d{1,5}(\.\d{1,2})?$/.test(moneyDelete(value))) {
              return callback(
                new Error("小数点前面仅支持5位数,小数点后面仅支持2位数")
              );
            }
            callback();
          },
          trigger: "blur",
        },
        ],
        leaveSelfDeduct: [{ required: true, message: "自离扣款不能为空", trigger: 'blur' },
        {
          validator: (rule, value, callback) => {
            if (!/^-?\d{1,5}(\.\d{1,2})?$/.test(moneyDelete(value))) {
              return callback(
                new Error("小数点前面仅支持5位数,小数点后面仅支持2位数")
              );
            }
            callback();
          },
          trigger: "blur",
        },
        ],
      }
    }
  },
  created() {
    this.getDetails()
  },
  methods: {
    //获取
    getDetails() {
      this.$api.logisticsInformation.unpaidLedger.unPaidDetail({ id: this.paymentId }).then(({ data }) => {
        this.editForm = {
          staffName: data.staffName || "",
          staffCode: data.staffCode || "",
          notPayAmount: data.notPayAmount || "",
          otherDeduct: moneyFormat(data.otherDeduct) || "",
          leaveSelfDeduct: moneyFormat(data.leaveSelfDeduct) || "",
          actualAmount: data.actualAmount || "",
          remark: data.remark || "",
        }
      })
    },
    onBlur(name) {
      if (!this.editForm[name]) {
        this.editForm.actualAmount = this.getTotal()
        return
      }
      this.editForm[name] = moneyFormat(moneyDelete(this.editForm[name]))
      this.$refs.editForm.validate(valid => {
        if (!valid) return
        this.editForm.actualAmount = this.getTotal()
      })
    },
    number(value) {
      return Number(moneyDelete(value))
    },
    //实付金额
    getTotal() {
      let actualAmount = numSub(numSub(this.number(this.editForm.notPayAmount), this.number(this.editForm.otherDeduct)), this.number(this.editForm.leaveSelfDeduct))
      return actualAmount
    },
    handleCancel() {
      this.$emit('handleCancel', 'cancel')
    },
    handleConfirm() {
      this.$refs.editForm.validate(valid => {
        if (!valid) return
        let params = {
          id: this.paymentId,
          otherDeduct: moneyDelete(this.editForm.otherDeduct),
          leaveSelfDeduct: moneyDelete(this.editForm.leaveSelfDeduct),
          remark: this.editForm.remark
        }
        this.$api.logisticsInformation.unpaidLedger.editUnPaidList(params).then(() => {
          this.$notify({
            title: '成功',
            message: '编辑成功',
            type: 'success'
          });
          this.$emit('handleCancel', 'confirm')
        })
      })
    }
  }
}
</script>

<style lang="stylus" scoped>
.el-row {
  &::before, &::after {
    display: none;
  }

  display: flex;
  justify-content: space-between;
}

.el-form-item {
  display: flex;

  >>>.el-form-item__label {
    text-align: left;
  }

  >>>.el-form-item__content {
    width: 100%;
    margin-left: 0 !important;
  }
}

.staffName {
  >>>.el-form-item__label {
    &::before {
      content: '*';
      color: #F23D20;
      margin-right: 4px;
      visibility: hidden;
    }
  }
}

>>>.el-textarea__inner {
  padding-right: 50px;
}

.el-textarea {
  >>>.el-input__count {
    right: 20px !important;
  }
}
</style>