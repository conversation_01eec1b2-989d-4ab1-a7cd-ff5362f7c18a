<template>
  <!-- 核算班组 -->
  <qDialog
    :visible="visible"
    title="选择核算班组"
    :innerScroll="false"
    width="800px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <div class="main">
      <el-row :gutter="20">
        <el-col :span="10" class="mian-left">
          <div class="topHeader">
            <span>选择区域 </span>
            <span>{{ flatSelectionData.length + "/" + tableData.length }}</span>
          </div>
          <el-input
            clearable
            placeholder="请输入计件工序"
            v-model="searchForm.keyLeft"
            @input="onSearch"
            @keyup.enter.native="onSearch"
          ></el-input>
          <el-table
            stripe
            border
            :data="tableData"
            v-loading="loading"
            ref="multipleTable"
            :height="420"
            @select="handleSelect"
            @select-all="handleSelectAll"
          >
            <el-table-column width="40" type="selection"> </el-table-column>
            <el-table-column prop="name" label="计件工序" align="center">
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="10" class="main-right">
          <div class="topHeader">
            <span>已选区域</span>
            <span>{{ flatSelectionData.length }}</span>
          </div>
          <el-input
            clearable
            placeholder="请输入计件工序"
            v-model="searchForm.keyRight"
            @input="onSearchRight"
            @keyup.enter.native="onSearchRight"
          ></el-input>
          <el-table stripe border :data="flatSelectionData" :height="420">
            <el-table-column
              prop="name"
              label="计件工序"
              align="center"
            ></el-table-column>
            <el-table-column label="操作" align="center" width="80">
              <template slot-scope="{ row }">
                <el-button
                  style="padding: 0"
                  type="text"
                  @click="handleDelete(row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </div>
  </qDialog>
</template>

<script>
export default {
  name: "add-data",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    factoryId: String,
    checkData: {
      typeof: Array,
      default: [],
    },
    processesList: {
      typeof: Array,
      default: [],
    },
  },
  data() {
    return {
      searchForm: {
        keyLeft: "",
        keyRight: "",
      },
      processdata: [],
      tableData: [],
      isLoading: false,
      loading: false,
      filterParam: {},
      allSelectionData: [],
      initAllData: [], //选中的右侧(不会随搜索变动)
      initData: [],
    };
  },
  computed: {
    localValue: {
      get() {
        return this.visible;
      },
      set(value) {
        this.$emit("update:visible", value);
      },
    },
    flatSelectionData() {
      let arr = this.allSelectionData;
      let arrIds = Array.from(new Set(arr.map((v) => v.id)));
      let newArr = [];
      arrIds.forEach((id) => {
        newArr.push(arr.find((v) => v.id === id));
      });
      return newArr;
    },
  },
  created() {
    this.init(true);
  },
  methods: {
    init(flag) {
      this.tableData = this.processesList;
      this.initData = this.processesList; //保留初始化左侧表格数据
      if (flag) {
        let arr = this.processesList.filter((v) =>
          this.checkData.find((k) => k === v.id)
        );
        this.allSelectionData = arr;
        this.initAllData = arr;
      }
      this.showCheckedNode();
    },
    onSearch() {
      if (this.searchForm.keyLeft) {
        this.tableData = this.initData.filter((v) =>
          v.name.match(this.searchForm.keyLeft)
        );
        this.showCheckedNode();
      } else {
        this.tableData = this.initData;
        this.showCheckedNode();
      }
    },
    onSearchRight() {
      if (this.searchForm.keyRight) {
        this.allSelectionData = this.initAllData.filter((v) =>
          v.name.match(this.searchForm.keyRight)
        );
      } else {
        this.allSelectionData = this.initAllData;
      }
    },
    handleSelect(data, row) {
      let currentData = this.allSelectionData;
      const checkStatus = !currentData.find((v) => v.id === row.id);
      if (checkStatus) {
        // 勾选
        this.allSelectionData = [...currentData, ...data];
        this.initAllData = [...currentData, ...data];
      } else {
        // 取消勾选
        currentData = currentData.filter((v) => v.id !== row.id);
        this.allSelectionData = currentData;
        this.initAllData = currentData;
      }
    },
    handleSelectAll(data) {
      let currentData = this.allSelectionData;
      if (data.length === 0) {
        let clearData = this.tableData;
        let checkedData = currentData.filter(
          (v) => !clearData.find((k) => k.id === v.id)
        );
        this.allSelectionData = checkedData;
        this.initAllData = checkedData;
      } else {
        this.allSelectionData = [...currentData, ...data];
        this.initAllData = [...currentData, ...data];
      }
    },
    handleDelete(row) {
      this.allSelectionData = this.allSelectionData.filter(
        (item) => item.id != row.id
      );
      this.initAllData = this.initAllData.filter((item) => item.id != row.id);
      this.$refs.multipleTable.toggleRowSelection(
        this.tableData.find((item) => item.id == row.id)
      );
    },

    // 回显已选中节点
    showCheckedNode() {
      this.$nextTick((_) => {
        const data = this.tableData.filter((v) =>
          this.flatSelectionData.find((k) => k.id === v.id)
        );
        data.forEach((row) => {
          this.$refs.multipleTable.toggleRowSelection(row);
        });
      });
    },
    handleCancel() {
      this.localValue = false;
    },
    handleConfirm() {
      const uniqueArr = Array.from(
        new Set(this.allSelectionData.map(JSON.stringify))
      ).map(JSON.parse);

      this.$emit("confirmGroupIds", uniqueArr); 
      this.handleCancel();
    },
  },
};
</script>

<style lang="stylus" scoped>
.main{
  height 500px
}
>>>.el-col-10{
  width 49.5%
}
>>>.el-dialog__body{
  font-size:16px
}
>>>.el-input__inner{
  height 30px
  line height 30px
  margin 5px 0

}
.topHeader{
  display:flex;
  justify-content: space-between
}
// >>>.el-table__empty-block{
//   min-height:420px
// }
</style>
