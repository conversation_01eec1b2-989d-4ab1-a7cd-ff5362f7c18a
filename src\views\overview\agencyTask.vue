<template>
  <!-- 待办任务-->
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          size="mini"
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          label-position="right"
        >
          <el-form-item label="核算工厂:" prop="factoryId">
            <el-select
              v-model="searchForm.factoryId"
              filterable
              clearable
              placeholder="请选择工厂"
              @change="onSearch"
            >
              <el-option
                v-for="item in tabList"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="核算月份:" prop="accountingMonth">
            <el-date-picker
              @change="onSearch"
              v-model="searchForm.accountingMonth"
              type="month"
              placeholder="请选择日期"
              :picker-options="pickerOptions"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="任务阶段:" prop="stage">
            <el-select
              @change="onSearch"
              v-model="searchForm.stage"
              filterable
              clearable
              placeholder="请选择任务状态"
            >
              <el-option
                v-for="item in statusOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button
            size="small"
            type="primary"

            @click="onSearch"
          >
            查询</el-button
          >
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置</el-button
          >
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-left>
        <el-tabs v-model="activeTab">
          <el-tab-pane name="0" label="当前任务"> </el-tab-pane>
          <el-tab-pane name="1" label="历史任务"> </el-tab-pane>
        </el-tabs>
      </template>
      <template v-slot:header-right>
        <el-button
          size="small"
          type="primary"
          v-permission="
            'was-customized$workBench$customized$workOverview$downloadAttendance'
          "
          @click="downloadAttendance"
          >下载考勤</el-button
        >
      </template>
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :data="tableData"
        :height="maxTableHeight"
        style="width: 100%"
      >
        <el-table-column
          prop="factoryName"
          label="核算工厂"
          width="130"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="accountingMonth"
          label="核算月份"
          width="80"
          align="left"
        >
        </el-table-column>
        <el-table-column
          prop="status"
          label="任务阶段"
          width="100"
          align="left"
        >
          <template slot-scope="{ row }">
            {{ filterType(row.status) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="任务状态" width="80" align="left">
          <template slot-scope="{ row }">
            {{ row.completeState == 0 ? "进行中" : "已完成" }}
          </template>
        </el-table-column>
        <el-table-column
          prop="salaryId"
          label="工资表"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              v-if="scope.row.salaryId"
              v-permission="
                'was-customized$workBench$customized$workOverview$payroll'
              "
              @click="handleJump(scope.row)"
              class="active"
            >
              {{ scope.row.salaryId }}
            </el-button>
            <span v-else>/</span>
          </template>
        </el-table-column>
        <el-table-column label="调整审核" align="left" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span v-if="row.status == 2 && row.subStatus == 0">未提交</span>
            <span v-if="row.status == 2 && row.subStatus == 1">已提交</span>
            <span v-if="row.status == 2 && row.subStatus == 2">已审核</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="comments"
          label="驳回原因"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="操作"
          align="left"
          :width="activeTab == '0' ? 500 : 150"
        >
          <template slot-scope="scope">
            <el-button
              v-permission="
                'was-customized$workBench$customized$workOverview$ViewTasks'
              "
              type="text"
              size="mini"
              @click="handleLook(scope.$index, scope.row)"
            >
              查看任务</el-button
            >
            <el-button
              type="text"
              size="mini"
              v-show="scope.row.status == '10' && activeTab == '0'"
              v-permission="
                'was-customized$workBench$customized$workOverview$endCollectionBtn'
              "
              @click="endCollection(scope.row)"
            >
              结束收集</el-button
            >
            <el-button
              type="text"
              size="mini"
              v-show="scope.row.status >= '20' && activeTab == '0'"
              @click="adjustment(scope.row)"
              v-permission="
                'was-customized$workBench$customized$workOverview$branchAdjustment'
              "
              >分厂调整
            </el-button>
            <el-button
              type="text"
              size="mini"
              v-show="
                scope.row.status == '20' &&
                scope.row.subStatus == 1 &&
                activeTab == '0'
              "
              @click="adjustmentReview(scope.row)"
              v-permission="
                'was-customized$workBench$customized$workOverview$adjustmentReview'
              "
              >调整审核
            </el-button>
            <el-button
              type="text"
              size="mini"
              v-show="
                scope.row.status == '20' &&
                scope.row.isFactoryAdjust == '1' &&
                scope.row.subStatus == 2 &&
                activeTab == '0'
              "
              v-permission="
                'was-customized$workBench$customized$workOverview$submitFirstInstance'
              "
              @click="submitFirstInstance(scope.row)"
            >
              提交一审</el-button
            >
            <el-button
              type="text"
              size="mini"
              v-permission="
                'was-customized$workBench$customized$workOverview$waitingManager'
              "
              v-show="
                scope.row.status == '30' &&
                isSectionChief &&
                scope.row.firstAuditStatus == '2' &&
                activeTab == '0'
              "
              >等待经理一审
            </el-button>
            <el-button
              type="text"
              size="mini"
              v-show="
                scope.row.status == '30' &&
                isManager &&
                scope.row.firstAuditStatus == '1' &&
                activeTab == '0'
              "
              >等待科长一审
            </el-button>
            <el-button
              type="text"
              size="mini"
              v-show="scope.row.status == '30' && activeTab == '0'"
              v-permission="
                'was-customized$workBench$customized$workOverview$firstInstancePassed'
              "
              @click="financialFirstReview(scope.row)"
            >
              财务一审</el-button
            >
            <el-button
              type="text"
              size="mini"
              v-permission="
                'was-customized$workBench$customized$workOverview$personalTaxUpload'
              "
              @click="update(scope.row)"
              v-show="scope.row.status >= '40' && activeTab == '0'"
            >
              个税上传</el-button
            >
            <el-button
              type="text"
              size="mini"
              v-show="
                scope.row.status == '40' &&
                scope.row.isTaxUpload == '1' &&
                activeTab == '0'
              "
              v-permission="
                'was-customized$workBench$customized$workOverview$submitSecondInstance'
              "
              @click="submitSecondInstance(scope.row)"
            >
              提交二审</el-button
            >
            <el-button
              type="text"
              size="mini"
              v-show="scope.row.status == '50' && activeTab == '0'"
              v-permission="
                'was-customized$workBench$customized$workOverview$passSecondInstance'
              "
              @click="financialSecondReview(scope.row)"
            >
              财务二审</el-button
            >
            <el-button
              size="mini"
              type="text"
              v-show="scope.row.status > 30 && activeTab == '0'"
              v-permission="
                'was-customized$workBench$customized$workOverview$forcedBack'
              "
              @click="forcedBack(scope.row)"
            >
              强制退回</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div style="text-align: right">
          <el-pagination
            @size-change="onSizeChange"
            @current-change="onNumChange"
            :current-page="pageNum"
            :page-size="pageSize"
            :total="total"
            :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper"
          >
          </el-pagination>
        </div>
      </template>
    </table-panel>
    <qDialog
      :visible="visible"
      :innerScroll="false"
      :isShowCancelBtn="showFooter"
      :title="title"
      width="500px"
      @confirm="handleConfirm"
      @cancel="handleCancel"
      :before-close="handleCancel"
    >
      <template>
        <!-- <p
          v-show="title == '一审通过'||title == '二审通过'">
          {{title == '一审通过'?'是否通过一审？':'是否通过二审？'}}
        </p> -->
        <div v-show="title == '结束收集'">
          <Verify
            :list="finishList"
            :isAllTrue="isFinishAllTrue"
            title="验证成功！是否结束收集？"
          />
        </div>
        <div v-show="title == '提交一审'">
          <Verify
            :list="firstAuditList"
            :isAllTrue="isFirstAllTrue"
            title="验证成功，是否提交一审？"
          />
        </div>
        <div v-show="title == '提交二审'">
          <Verify
            :list="secondAuditList"
            :isAllTrue="isSecondAllTrue"
            title="验证成功，是否提交二审？"
          />
        </div>
        <span v-show="title == '强制退回'"
          >强制退回将退回至【资料收集阶段】，中间数据全部重置，是否强制退回？</span
        >
        <el-form
          :model="formData"
          v-show="['财务一审', '财务二审', '调整审核'].includes(title)"
          ref="formRef"
          label-width="120px"
          class="formData"
        >
          <el-form-item label="">
            <el-radio-group v-model="formData.type">
              <el-radio label="0">通过</el-radio>
              <el-radio label="1">不通过</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            v-if="formData.type == 1"
            label="驳回原因:"
            prop="reason"
          >
            <el-input
              type="textarea"
              resize="none"
              rows="3"
              maxlength="20"
              show-word-limit
              v-model.trim="formData.reason"
            >
            </el-input>
          </el-form-item>
        </el-form>
      </template>
    </qDialog>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import moment from "moment";
import Verify from "./compontent/verifyDialog";
import pagePathMixin from "@/utils/page-path-mixin";
export default {
  name: "AgencyTask",
  mixins: [tableMixin,pagePathMixin],
  components: { Verify },
  data() {
    return {
      searchForm: {
        accountingMonth: moment().subtract(1, "month").format("YYYY-MM"),
        stage: "",
        factoryId: "",
      },
      formData: {
        type: "0",
        reason: "",
      },
      pickerOptions: {},
      activeTab: "0",
      tabList: [],
      visible: false,
      title: "",
      availableMonths: [],
      statusOptions: Object.freeze([
        {
          name: "资料收集",
          id: "10",
        },
        {
          name: "财务标记",
          id: "11",
        },
        {
          name: "分厂调整",
          id: "20",
        },
        {
          name: "财务一审",
          id: "30",
        },
        {
          name: "个税调整",
          id: "40",
        },
        {
          name: "财务二审",
          id: "50",
        },
        {
          name: "任务结束",
          id: "60",
        },
      ]),
      filterParam: {
        accountingMonth: moment().subtract(1, "month").format("YYYY-MM"),
      },
      tableData: [],
      loading: false,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      info: {},
      isManager: "",
      isSectionChief: "",
      finishList: [],
      firstAuditList: [],
      secondAuditList: [],
      startTime: 0,
      diffTime: 0,
    };
  },
  created() {
    this.$api.roleInfo.getRoleList({ moduleId: 1 }).then((res) => {
      if (res.data.includes("was00005")) {
        this.isManager = false;
        this.isSectionChief = true;
      } else {
        this.isManager = true;
        this.isSectionChief = false;
      }
    });
    this.$api.systemManage.getBasicPermission
      .getBasicPermissionAll()
      .then(({ data, success }) => {
        if (success) {
          this.tabList =
            data.map((item) => ({
              label: item.name,
              name: item.name,
              id: item.id,
            })) || [];
        }
      });
    this.getAvailableMonths();
  },
  mounted() {
    let that = this;
    this.pickerOptions = {
      disabledDate(time) {
        return !that.availableMonths.includes(moment(time).format("YYYY-MM"));
      },
    };
  },
  computed: {
    isFinishAllTrue() {
      return this.finishList.map((item) => item.icon).every((item) => item);
    },
    isFirstAllTrue() {
      return this.firstAuditList.map((item) => item.icon).every((item) => item);
    },
    isSecondAllTrue() {
      return this.secondAuditList
        .map((item) => item.icon)
        .every((item) => item);
    },
    showFooter() {
      return this.title == "结束收集"
        ? this.isFinishAllTrue
        : this.title == "提交一审"
        ? this.isFirstAllTrue
        : this.isSecondAllTrue;
    },
  },
  watch: {
    $route: {
      handler(value) {
        if (
          value.path.includes("overview") &&
          value.path.includes("customized")
        ) {
          this.getList(this.activeTab);
        }
      },
      // 深度观察监听
      deep: true,
    },
    activeTab: {
      handler(value) {
        this.getList(value);
      },
      immediate: true,
    },
  },
  methods: {
    //获取任务总览列表
    getList(complete = "0") {
      this.loading = true;
      this.$api.workbench
        .getTaskOverviewList({
          pageSize: this.pageSize,
          pageNum: this.pageNum,
          filterData: { ...this.filterParam, complete },
        })
        .then(({ data: { list, total } }) => {
          this.tableData = list || [];
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //获取核算月份
    getAvailableMonths() {
      this.$api.workbench.availableMonths().then(({ data }) => {
        this.availableMonths = data || [];
      });
    },
    //结束收集验证
    finishCollecting(data) {
      this.$api.workbench
        .checkFinishCollecting(data)
        .then(({ data, success }) => {
          if (success) this.finishList = data;
        });
    },
    //提交一审校验
    firstAuditSubmit(data) {
      this.$api.workbench
        .checkSubmitFirstAudit(data)
        .then(({ data, success }) => {
          if (success) this.firstAuditList = data;
        });
    },
    //提交二审校验
    secondAuditSubmit(data) {
      this.$api.workbench
        .checkSubmitSecondAudit(data)
        .then(({ data, success }) => {
          if (success) this.secondAuditList = data;
        });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.filterParam = {
        accountingMonth: moment().subtract(1, "month").format("YYYY-MM"),
      };
      this.pageNum = 1;
      this.getList(this.activeTab);
    },
    //搜索
    onSearch() {
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (key == "accountingMonth" && moment.isDate(val)) {
          this.filterParam.accountingMonth = moment(val).format("YYYY-MM");
        } else if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      this.pageNum = 1;
      this.getList(this.activeTab);
    },
    //工资表  分配表  上卡现金表 跳转
    handleJump(data) {
      const { id, factoryId, factoryName, accountingMonth, completeState } =
        data;
      let params = {
        id,
        factoryId,
        factoryName,
        accountingMonth,
        completeState,
      };
      this.openSubPage({
        path: "/customized/workbench/payroll",
        query: {
          data: JSON.stringify(params),
        },
      });
    },
    //财务一审
    financialFirstReview(row) {
      this.formData = {
        type: "0",
        reason: "",
      };
      this.title = "财务一审";
      this.visible = true;
      this.info = { ...row };
    },
    //财务二审
    financialSecondReview(row) {
      this.formData = {
        type: "0",
        reason: "",
      };
      this.title = "财务二审";
      this.visible = true;
      this.info = { ...row };
    },
    //调整审核
    adjustmentReview(row) {
      this.title = "调整审核";
      (this.formData = {
        type: "0",
        reason: "",
      }),
        (this.visible = true);
      this.info = { ...row };
    },
    //强制退回
    forcedBack(row) {
      this.title = "强制退回";
      this.visible = true;
      this.info = { ...row };
    },
    //提交一审
    async submitFirstInstance(row) {
      await this.firstAuditSubmit({
        accountingMonth: row.accountingMonth,
        factoryId: row.factoryId,
      });
      this.title = "提交一审";
      this.visible = true;
      this.info = { ...row };
    },
    //提交二审
    async submitSecondInstance(row) {
      await this.secondAuditSubmit({
        accountingMonth: row.accountingMonth,
        factoryId: row.factoryId,
      });
      this.title = "提交二审";
      this.visible = true;
      this.info = { ...row };
    },
    //结束收集
    async endCollection(row) {
      this.title = "结束收集";
      await this.finishCollecting({
        accountingMonth: row.accountingMonth,
        factoryId: row.factoryId,
      });
      this.visible = true;
      this.info = { ...row };
    },
    filterType(val) {
      return this.statusOptions.find((item) => item.id === String(val)).name;
    },
    //查看任务
    handleLook(index, data) {
      this.openSubPage({
        path: "/customized/workbench/taskDetails",
        query: {
          data: this.$Base64.encode(
            JSON.stringify({
              factoryId: data.factoryId,
              accountingMonth: data.accountingMonth,
            })
          ),
        },
      });
    },
    //个税上传
    update(data) {
      const { id, factoryId, factoryName, accountingMonth } = data;
      let params = { id, factoryId, factoryName, accountingMonth };
      this.openSubPage({
        path: "/customized/workbench/upload",
        query: {
          data:this.$Base64.encode(
           JSON.stringify(params)),
        },
      });
    },
    //分厂调整
    adjustment(data) {
      const { id, factoryId, factoryName, accountingMonth, subStatus } = data;
      let params = { id, factoryId, factoryName, accountingMonth, subStatus };
      this.openSubPage({
        path: "/customized/workbench/adjustment",
        query: {
          data: this.$Base64.encode(
            JSON.stringify(params),
          )

        },
      });
    },
    //下载考勤
    downloadAttendance() {
      let nowTime = moment();
      this.diffTime = nowTime.diff(this.startTime, "second");
      if (this.diffTime < 11) {
        this.$message.warning("10s内不能重复点击");
        return;
      } else {
        this.startTime = nowTime;
      }
      let params = {
        accountingMonth: this.searchForm.accountingMonth
          ? moment(this.searchForm.accountingMonth).format("YYYY-MM")
          : "",
        factoryId: this.searchForm.factoryId,
      };
      this.$api.common.doExport("exportStaffAttendance", params).then(() => {
        this.$message.success("导出操作成功，请前往导出记录查看详情");
      });
    },
    handleConfirm() {
      switch (this.title) {
        case "财务一审":
          if (this.formData.type == "0") {
            this.$api.workbench
              .firstAudit({
                taskId: this.info.id,
                factoryName: this.info.factoryName,
                type: "Y",
              })
              .then((res) => {
                if (res.code === 200) {
                  this.getList();
                  this.visible = false;
                }
              });
          } else {
            this.$api.workbench
              .firstAudit({
                taskId: this.info.id,
                reason: this.formData.reason,
                factoryName: this.info.factoryName,
                type: "N",
              })
              .then((res) => {
                if (res.code === 200) {
                  this.getList();
                  this.visible = false;
                }
              });
          }

          break;
        case "财务二审":
          if (this.formData.type == "0") {
            this.$api.workbench
              .secondAudit({
                taskId: this.info.id,
                factoryName: this.info.factoryName,
                type: "Y",
              })
              .then((res) => {
                if (res.code === 200) {
                  this.getList();
                  this.visible = false;
                }
              });
          } else {
            this.$api.workbench
              .secondAudit({
                taskId: this.info.id,
                reason: this.formData.reason,
                factoryName: this.info.factoryName,
                type: "N",
              })
              .then((res) => {
                if (res.code === 200) {
                  this.getList();
                  this.visible = false;
                }
              });
          }

          break;
        case "调整审核":
          if (this.formData.type == "0") {
            this.$api.workbench
              .adjustmentAudit({
                taskId: this.info.id,
                factoryName: this.info.factoryName,
                type: "Y",
              })
              .then((res) => {
                if (res.code === 200) {
                  this.getList();
                  this.visible = false;
                }
              });
          } else {
            this.$api.workbench
              .adjustmentAudit({
                taskId: this.info.id,
                reason: this.formData.reason,
                factoryName: this.info.factoryName,
                type: "N",
              })
              .then((res) => {
                if (res.code === 200) {
                  this.getList();
                  this.visible = false;
                }
              });
          }
          break;
        case "结束收集":
          this.visible = false;
          if (!this.showFooter) return;
          this.$api.workbench
            .finishCollecting({
              taskId: this.info.id,
              factoryName: this.info.factoryName,
            })
            .then((res) => {
              if (res.code === 200) {
                this.$message({
                  message: "结束收集成功",
                  type: "success",
                  duration: 1500,
                });
                this.getList(this.activeTab);
              }
            });
          break;
        case "提交一审":
          this.visible = false;
          if (!this.showFooter) return;
          this.$api.workbench
            .submitFirstAudit({ taskId: this.info.id })
            .then((res) => {
              if (res.code === 200) {
                this.$message({
                  message: "提交一审成功",
                  type: "success",
                  duration: 1500,
                });
                this.getList();
              }
            });
          break;
        case "提交二审":
          this.visible = false;
          if (!this.showFooter) return;
          this.$api.workbench
            .submitSecondAudit({ taskId: this.info.id })
            .then((res) => {
              if (res.code === 200) {
                this.$message({
                  message: "提交二审成功",
                  type: "success",
                  duration: 1500,
                });
                this.getList();
              }
            });
          break;
        default:
          this.visible = false;
          this.$api.workbench
            .forceBack({
              taskId: this.info.id,
            })
            .then((res) => {
              if (res.code === 200) {
                this.getList();
              }
            });
          break;
      }
    },
    handleCancel() {
      this.visible = false;
    },
    onSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList(this.activeTab);
    },
    onNumChange(val) {
      this.pageNum = val;
      this.getList(this.activeTab);
    },
  },
};
</script>

<style lang="stylus" scoped>
#item {
  margin: 0;
  padding: 5px;
}

.active {
  width: 100%;
  padding: 0;

  >>>span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    width: 100%;
    text-align: left;
  }
}

.formData {
  .el-form-item {
    display: flex;

    >>>.el-form-item__content {
      width: 100%;
      margin-left: 0 !important;
    }
  }
}
</style>
