<template>
  <!-- 订单BOM修订已处理 -->
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          size="mini"
          :inline="true"
          :model="searchForm"
          label-width="103px"
          ref="searchForm"
          label-position="right"
        >
          <el-form-item label="核算工厂:" prop="factoryId">
            <el-select
              v-model="searchForm.factoryId"
              filterable
              clearable
              placeholder="请选择核算工厂"
              @change="onSearch"
            >
              <el-option
                v-for="item in tabList"
                :key="item.value"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="核算工序:" prop="processId">
             <el-select
              v-model="searchForm.processId"
              filterable
              clearable
              placeholder="请选择核算工序"
              @change="onSearch"
            >
              <el-option
                v-for="item in processList"
                :key="item.value"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="类型:" prop="type">
            <el-select
              v-model="searchForm.type"
              filterable
              clearable
              placeholder="请选择类型"
              @change="onSearch"
            >
              <el-option
                v-for="item in typeList"
                :key="item.value"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="员工姓名"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input
              v-model.trim="searchForm.staffCode"
              size="mini"
              clearable
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenStaffCodes"
                  titleName="厂牌编号"
                />
              </template>
            </el-input>
          </el-form-item>
               <el-form-item label="流程编号:" prop="processCode">
            <el-input
              v-model="searchForm.processCode"
              placeholder="请输入流程编号"
              maxlength="50"
              @keyup.enter.native="onSearch"
            >
            </el-input>
          </el-form-item> 
          <el-form-item label="分配方式:" prop="allotMethod">
            <el-select
              v-model="searchForm.allotMethod"
              filterable
              clearable
              placeholder="请选择分配方式"
              @change="onSearch"
            >
              <el-option
                v-for="item in allotMethodList"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="执行年月:" prop="accountingMonth">
            <el-date-picker
              v-model="searchForm.accountingMonth"
              type="month"
              value-format="yyyy-MM"
              placeholder="选择日期"
              @change="onSearch"
            >
            </el-date-picker>
          </el-form-item> 
      
        </el-form>
        <template v-slot:right>
          <el-button
            size="small"
            type="primary"

            @click="onSearch"
          >
            查询
          </el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">  
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :height="maxTableHeight"
        :data="tableData"
        @selection-change="handleSelectionChange"
        :key="tableKey"
        :row-key="getRowKey"
      >
        <el-table-column 
          width="40"
          type="selection"
          :reserve-selection="true"
        >
        </el-table-column>
        <el-table-column
          prop="processCode"
          label="流程编号"
          width="160"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column> 
        <el-table-column
          prop="staffName"
          label="员工姓名"
          width="100"
          align="left"
        >
        </el-table-column>
        <el-table-column
          prop="staffCode"
          label="厂牌编号"
          width="130"
          align="left"
        >
        </el-table-column>
        <el-table-column 
          prop="actualStaffCode"
          label="实际核算厂牌"
          width="110"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="factoryName"
          label="工厂名称"
          width="110"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
         <el-table-column
          prop="actualFactoryName"
          label="实际核算工厂"
          width="110"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>

        <el-table-column
          prop="accountingMonth"
          label="执行年月"
          width="100"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column> 
        <el-table-column
          prop="actualAccountingMonth"
          label="实际执行年月"
          width="120"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column> 

        <el-table-column
          prop="processName"
          label="核算工序"
          width="100"
          align="left"
        >
        </el-table-column>
       <el-table-column
          prop="actualProcessName"
          label="实际核算工序"
          width="120"
          align="left"
        >
        </el-table-column>
        <el-table-column
          prop="allotMethod"
          label="分配方式"
          width="100"
          align="left"
        >
        </el-table-column>
        <el-table-column
          prop="type"
          label="类型"
          width="100"
          align="left"
        >
        </el-table-column>
        <el-table-column
          prop="amount"
          label="金额（元）"
          width="100"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <span>
              {{ row.amount | moneyFormat }}</span
            >
          </template>
        </el-table-column> 
        <el-table-column
          prop="actualAmount"
          label="实际增扣金额（元）"
          width="150"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <span 
            >
              {{ row.actualAmount | moneyFormat }}</span
            >
          </template>
        </el-table-column> 
         
        <el-table-column
          prop="applyDate"
          label="申请日期"
          width="150"
          align="left"
        >
          <template slot-scope="{ row }">
            {{ row.applyDate | dateFormats }}
          </template>
        </el-table-column>
        <el-table-column
          prop="remarks"
          label="备注说明"
           width="120"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="updateUser"
          label="办理人员"
          width="80"
          align="left"
        >
        </el-table-column>
         <el-table-column
          prop="updateTime"
          label="办理时间"
          width="150"
          align="left"
        >
          <template slot-scope="scope">
            {{ scope.row.updateTime | dateFormat }}
          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          label="接收时间"
          width="150"
          align="left"
        >
          <template slot-scope="scope">
            {{ scope.row.createTime | dateFormat }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160" align="left" fixed="right">
          <template slot-scope="scope">  
            <el-button
              style="margin-left: 0"
              size="small"
               v-permission="'was-customized$informationAccount$plateType$orderBomRevision$viewDetails'"
              type="text" 
              @click="handleDetails(scope.row)"
            >
              查看详情
            </el-button> 
            <el-button
              style="margin-left: 0"
              size="small"
               v-permission="'was-customized$informationAccount$plateType$orderBomRevision$return'"
              type="text" 
              @click="handleBack(scope.$index, scope.row)"
            >
              退回
            </el-button> 
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div class="table_footer">
          <ul style="display: flex;align-items: center;">
            <li>
              <span>金额:</span><span>{{ statisticAmout.amount | filterDataPiece }}</span>
            </li>
            <li style="margin-left: 20px;">
              <span>实际增扣金额:</span><span>{{ statisticAmout.actualAmount | filterDataPiece }}</span>
            </li>
          </ul>
        </div>
        <div style="text-align: right">
          <el-pagination
            @size-change="onSizeChange"
            @current-change="onNumChange"
            :current-page="pageNum"
            :page-size="pageSize"
            :total="total"
            :page-sizes="[10, 30, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
          >
          </el-pagination>
        </div>
      </template>
    </table-panel>
    <!-- 新增-->
    <add-cost v-if="visible" :isVisible="visible" @cancel="handleCancel">
    </add-cost> 
    <common-dialog
      v-if="backVisible"
      :isVisible="backVisible"
      :title="title"
      :info="info"
      :selectall="selectall"
      @cancel="backCancel"
      :filterParam="filterParam"
      :auditStatus = "'1'"
    >
    </common-dialog>
    <Import
      v-if="importVisible"
      :visible="importVisible"
      @cancel="cancel"
      @confirm="confirm"
      :importInfo="importInfo"
    />
    <!-- 查看详情 -->
    <details-dialog
      v-if="isVisibleDteails"
      :visible="isVisibleDteails"
      :formData="detailsData"
      @cancel="detailsCancel"
    ></details-dialog> 
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import moment from "moment";
import addCost from "./component/addCost"; 
import commonDialog from "./component/commonDialog";
import detailsDialog from "./component/detailsDialog";
import { moneyFormatZh } from "@/utils";
import accounDialog from "./component/accounDialog";
export default {
  name: "PlateTypeUntreated",
  mixins: [tableMixin,pagePathMixin],
  components: { addCost, commonDialog, detailsDialog, accounDialog },
 data() {
    return {
      selectall: false,
      backVisible:false,
      isVisibleDteails:false,
      statisticAmout:"",
      searchForm: {
        handleStatus:1,
        processCode:"",
        processId: "",
        factoryId: "",  
        accountingMonth: moment().subtract(1, "month").format("YYYY-MM"),
        staffCode: "",
        staffName: "",
        type:"", 
        allotMethod:""
      },
      editForm: {},
      importVisible:false,
      detailsData: {}, 
      typeList: [
        {
          id: "plus",
          name: "增补",
        },
        {
          id: "minus",
          name: "扣减",
        }
      ],
      //分配方式
      allotMethodList: Object.freeze([
      {
          name: "集体",
          value: "group",
        },
        {
          name: "个人",
          value: "person",
        }
      ]),  
      tabList: [],
      filterParam: {},
      params: {},
      importInfo: {
        reportName: "plankOrderBomWageImport",
        paramMap: {
          columnValue: "板木-订单BOM修订",
        },
      },
      //表格数据
      tableData: [],
      visible:false,
      idList: [],
      info: {},
      loading: false, 
      pageSize: 50,
      pageNum: 1,
      total: 0,
      title: "", 
      calculateVisible: false, 
      diffTime: 0,
      startTime: 0,
      tableKey: "",
      accounDialogVisible: false,
    };
  },
     filters: {
    filterData(value) {
      return !value ? "-" : moneyFormatZh(value);
    },
    filterDataPiece(value) {
      return !value ? "0" : moneyFormatZh(value);
    }
  },
  created() {
    this.$api.plateTypeSystemManage.getBasicPermission
      .getBasicPermissionAll()
      .then((res) => {
        this.tabList =
          res.data.map((item) => ({
            label: item.name,
            name: item.name,
            id: item.id,
            ...item
          })) || [];
      });
       
  },
  computed:{
    processList() { 
       return  (this.searchForm.factoryId &&
            this.tabList.find((item) => item.id == this.searchForm.factoryId)
              .process) ||
          [] 
    },
  },
   mounted() {
      this.init();
  }, 
  methods: {
     //统计
    getAccount() {
      this.$api.plateTypeInformation.orderBomRevision.orderBomRevisionStatisticList({
        ...this.filterParam,
        ...this.params,
      }).then((res) => {
        this.statisticAmout = res.data;
      });
    },
    //切换actived初始化
    init(){
        this.selectall = false;
        this.$refs.tableRef.clearSelection();
        this.idList = [];
        this.onSearch(); 
    },
      getRowKey(row) {
        return row.id;
  },
    //成本扣款台账列表
    getList() {
      
      this.loading = true;
      this.tableData = [];
      this.tableKey = Math.random();
      this.$api.plateTypeInformation.orderBomRevision
        .orderBomRevisionList({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: {  
            ...this.filterParam,
            ...this.params,
          },
        })
        .then(({ data: { list, total } }) => {
          this.tableData = list || [];
          this.total = total;

          if (this.selectall) {
            this.tableData.forEach((row) => {
              this.$refs.tableRef.toggleRowSelection(row, true);
            });
          }
        })
        .finally(() => {
           this.$emit('updateNumber')
          this.loading = false;
        });
    },
    
    getTotal(num) {
      if (isNaN(Number(num))) return 0;
      return Number(num) > 9999 ? "9999+" : num;
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.searchForm.actualAccountingMonth = moment()
        .subtract(1, "month")
        .format("YYYY-MM");
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (key === "actualAccountingMonth") {
          if (val) this.filterParam[key]  = moment(val).format("YYYY-MM");
        }else if (key === "amountRangeList" && val.length) {
          this.filterParam.amountRangeList = val.map((item) => {
            return {
              maxAmount: item.max,
              minAmount: item.min,
            };
          });
        } else if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0)
          this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getAccount()
      this.getList(); 
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) {
          this.params[name] = data;
        }
      }
    },
    //新增
    handleAdd() {
      this.visible = true;
    }, 
    //批量退回
    batchBack() {
      if (this.idList.length === 0 && !this.selectall) {
        this.$message({
          message: "请先勾选需要批量确认的内容",
          type: "warning",
        });
        return;
      } 
      console.log(this.idList )
      this.info = { idList: this.idList };
      this.title = "批量退回";
      this.backVisible = true;
    }, 
    //导入
    handleImport() {
      this.importVisible = true;
    },
    //导出
    handleExport() {
      let nowTime = moment();
      this.diffTime = nowTime.diff(this.startTime, "second");
      if (this.diffTime < 11) {
        this.$message.warning("10s内不能重复点击");
        return;
      } else {
        this.startTime = nowTime;
      }
      let params = {
        ...this.filterParam,
        ...this.params,
      };
      this.$api.common.doExport("plankExportbpmdeduct", params).then(() => {
        this.$message.success("导出操作成功，请前往导出记录查看详情");
      });
    },
    detailsCancel() {
      this.isVisibleDteails = false;
    },
    //查看详情
    handleDetails(item) {
      this.isVisibleDteails = true;
      this.detailsData = item
    }, 
    //编辑
    handleEdit(index, row) {
      this.editForm = {
        id: row.id,
      };
      this.title = "编辑";
      this.visible = true;
    }, 
    //退回
    handleBack(index, row) {
      this.title = "退回";
      this.backVisible = true;
      this.info = row;
    }, 
    cancellation(type) {
      if (type && type == "cancel") return;
      this.getList(); 
    },
    handleCancel(obj) {
      const { type, isVisible } = obj;
      this.visible = isVisible;
      this.cancellation(type);
    }, 
    backCancel(obj) { 
      const { type, isVisible } = obj;
      this.backVisible = isVisible;
      this.$emit("clearSelection");
      this.$refs.tableRef.clearSelection();
      this.cancellation(type);
    },
    handleSelectionChange(val) {
      this.idList = (val && val.map((item) => item.id)) || [];
    },
    cancel(value) {
      this.importVisible = value;
    },
    confirm(value) {
      this.importVisible = value;
      this.getList();
    },
    onSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(val) {
      this.pageNum = val;
      this.getList();
    },
  },
};
</script>

<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}
.select{
  background:#0bb78e
  color:#fff
}
>>>.el-checkbox.is-bordered.el-checkbox--mini{
    height:25px !important
}
>>>.el-checkbox__input.is-checked+.el-checkbox__label{
    color:white !important
}
>>>.el-checkbox__input.is-checked .el-checkbox__inner{
   border-color:white !important
}
>>>.table-panel-footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .table_footer {
    overflow: auto;

    >ul {
      display: flex;
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        white-space: nowrap
        padding: 0 10px;
      }
    }
  }
}
</style>
