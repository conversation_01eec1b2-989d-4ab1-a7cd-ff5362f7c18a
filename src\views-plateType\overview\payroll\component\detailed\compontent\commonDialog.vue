<template>
  <qDialog
    :visible="visible"
    :innerScroll="false"
    :title="title"
    width="450px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <el-form
      v-if="title == '编辑'"
      :model="procedureForm"
      :rules="procedureRules"
      ref="procedureRef"
      label-width="93px"
    >
      <el-form-item label="核算班组:" class="procedure" prop="procedure">
        <el-select v-model="procedureForm.procedure" placeholder="请选择核算班组">
          <el-option
            v-for="item in procedureOptions"
            :key="item.id"
            :label="item.name"
            :value="item.name"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="备注二:" prop="markStatus">
        <el-select
          v-model="procedureForm.markStatus"
          placeholder="请选择"
          @change="selectMarkStatus"
          :disabled="formData.params.markStatus == '已结算'"
        >
          <el-option
            v-for="item in remarkTypeList"
            :key="item.value"
            :label="item.name"
            :value="item.name"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label=""
        prop="remark"
        v-if="['其他（暂不发放）', '其他','正常'].includes(procedureForm.markStatus)"
      >
        <el-input
          type="textarea"
          resize="none"
          rows="2"
          maxlength="50"
          show-word-limit
          v-model.trim="procedureForm.remark"
        >
        </el-input>
      </el-form-item>
    </el-form>
    <el-form v-else label-width="88px" :model="remarkForm" size="small" ref="formRef">
      <p style="padding-left: 20px">已选{{ formData.idList.length }}条数据</p>
      <el-form-item label="备注二:" prop="markStatus">
        <el-select v-model="remarkForm.markStatus" placeholder="请选择">
          <el-option
            v-for="item in remarkTypeList"
            :key="item.value"
            :label="item.name"
            :value="item.name"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label=""
        prop="remark"
        v-if="['其他（暂不发放）', '其他','正常'].includes(remarkForm.markStatus)"
      >
        <el-input
          type="textarea"
          resize="none"
          rows="2"
          maxlength="50"
          show-word-limit
          v-model.trim="remarkForm.remark"
        >
        </el-input>
      </el-form-item>
    </el-form>
  </qDialog>
</template>

<script>
export default {
  name: "commonDialog",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    formData: Object,
  },
  data() {
    return {
      procedureForm: {
        procedure: "",
        markStatus: "",
        remark: "",
      },
      remarkForm: {
        remark: "",
        markStatus: "",
      },
      procedureRules: {
        procedure: [{ message: "工序不能为空", trigger: "change", required: true }],
      },
      procedureOptions: [],
      remarkTypeList: Object.freeze([
        {
          name: "正常",
          value: "0",
        },
        {
          name: "辞职上卡",
          value: "11",
        },
        {
          name: "自离",
          value: "22",
        },
        {
          name: "延发",
          value: "3",
        },
        {
          name: "线下已结",
          value: "4",
        },
        {
          name: "其他（暂不发放）",
          value: "5",
        },
        {
          name: "其他",
          value: "6",
        },
        {
          name: "辞职领现",
          value: "12",
        },
        {
          name: "自离上卡",
          value: "21",
        },
      ]),
      isLoading:false
    };
  },
  created() {
    if (this.title == "编辑") {
      this.procedureForm = {
        markStatus: this.formData.params.markStatus,
        procedure: this.formData.procedure,
        remark: this.formData.params.remark,
      };
      this.getProcedureList();
    }
  },
  methods: {
    //获取工序列表
    getProcedureList() {
      const { factoryId, accountingMonth } = JSON.parse(this.$route.query.data);
      const { staffCode } = this.formData.params;
      this.$api.plateTypeWorkbench
        .queryGroup({
          factoryId,
          accountingMonth,
          staffCode,
        })
        .then(({ data }) => {
          this.procedureOptions = data;
        });
    },
    selectMarkStatus() {
      if (this.formData.params.markStatus == this.procedureForm.markStatus) {
        this.procedureForm.remark = this.formData.params.remark;
      } else {
        this.procedureForm.remark = "";
      }
    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      if (this.title == "编辑") {
        this.$refs.procedureRef.validate((valid) => {
          if (!valid) return;
          let processIds = [];
          processIds.push(
            this.procedureOptions.find(
              (item) => item.name == this.procedureForm.procedure
            ).id || ""
          );
          let markStatus = this.remarkTypeList.find(
            (item) => item.name == this.procedureForm.markStatus
          ).value;
          if (markStatus == "6" && !this.procedureForm.remark) {
            this.$message.warning("备注不能为空");
            return;
          }
          this.isLoading = true;
          let params = {
            id: this.formData.params.id,
            processIds,
            markStatus,
            remark: this.procedureForm.remark,
          };
          this.$api.plateTypeWorkbench.editSalaryProcedure(params).then(({ success }) => {
            if (success) {
              this.$notify.success({
                title: "成功",
                message: "修改成功",
              });
              this.$emit("cancel", "confirm");
            }
          }).finally(()=>{
            this.isLoading = false;
          });
        });
      } else {
        let markStatus = this.remarkTypeList.find(
          (item) => item.name == this.remarkForm.markStatus
        ).value;
        if (markStatus == "6" && !this.remarkForm.remark) {
          this.$message.warning("备注不能为空");
          return;
        }
        this.isLoading = true;
        this.$api.plateTypeWorkbench
          .batchRemark({
            ids: this.formData.idList,
            remark: this.remarkForm.remark,
            markStatus,
          })
          .then(() => {
            this.$notify.success({
              title: "成功",
              message: "批量备注成功",
            });
            this.$emit("cancel", "confirm");
          }).finally(()=>{
            this.isLoading = false;
          });
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
