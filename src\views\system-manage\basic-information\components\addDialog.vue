<template>
  <q-dialog
    :visible="isVisible"
    :innerScroll="false"
    :title="title"
    width="500px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <el-form
      :model="addForm"
      :rules="rules"
      ref="addForm"
      label-width="108px"
      size="small"
    >
      <el-form-item label="核算班组:" prop="groupName">
        <el-input
          v-model.trim="addForm.groupName"
          clearable
          placeholder="请输入核算班组"
          maxlength="30"
          show-word-limit
        >
        </el-input>
      </el-form-item>
      <el-form-item label="核算大工序:" prop="parentId">
        <el-select
          v-model="addForm.parentId"
          filterable
          clearable
          placeholder="请选择核算大工序"
        >
          <el-option
            v-for="item in bigGroupList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="物理产线:" prop="productLineId">
        <el-select
          v-model="addForm.productLineId"
          filterable
          clearable
          placeholder="请选择物理产线"
        >
          <el-option
            v-for="item in productList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="启用状态:" prop="enable">
        <el-switch
          inactive-color="#ff4949"
          v-model="addForm.enable"
          :active-text="addForm.enable == '1' ? '启用' : '禁用'"
          :active-value="1"
          :inactive-value="0"
        >
        </el-switch>
      </el-form-item>
      <el-form-item label="备注说明:" label-position="top">
        <el-input
          type="textarea"
          resize="none"
          rows="5"
          maxlength="300"
          show-word-limit
          v-model.trim="addForm.remark"
        >
        </el-input>
      </el-form-item>
    </el-form>
  </q-dialog>
</template>

<script>
export default {
  name: "addDialog",
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    editForm: Object,
  },
  data() {
    return {
      addForm: {
        groupName: "",
        enable: 1,
        remark: "",
        parentId:"",
        productLineId:"",
        treeLevel:3,
      },
      isLoading: false,
      bigGroupList: [],
      productList:[],
      rules: {
        groupName: [
          { required: true, message: "请输入核算班组", trigger: "blur" },
        ],
        parentId: [
          { required: true, message: "请选择核算大工序", trigger: "change" },
        ],
        // productLineId: [
        //   { required: true, message: "请选择物理产线", trigger: "change" },
        // ],
        enable: [
          { required: true, message: "请选择启用状态", trigger: "change" },
        ],
      },
    };
  },
  async created() {
    await this.getBigGroupList();
    this.addForm = {
      ...this.addForm,
      ...this.editForm,
    };
  },
  methods: {

    //获取大工序列表
    getBigGroupList() {
      return this.$api.systemManage.getBasicPermission
        .getBasicPermissionAll1()
        .then(({ data }) => {
          let obj = data && data.find((item) => item.id == this.editForm.factoryId)
          console.log('obj',obj)
          this.bigGroupList= obj && obj.bigProcess ? obj.bigProcess: [];
          this.productList=  obj && obj.productLines ? obj.productLines: [];

        });
    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      this.$refs.addForm.validate((valid) => {
        if (!valid) return;
        this.isLoading = true;
        if (this.title == "新增") {
          this.$api.systemManage.getBasicPermission
            .addGroup({ factoryId: this.addForm.factoryId, ...this.addForm })
            .then(({ success }) => {
              if (success)
                this.$notify({
                  title: "成功",
                  message: "新增成功",
                  type: "success",
                });
              this.$emit("cancel", "cancel");
              this.$bus.$emit("customizedAddCancel", "confirm");
            })
            .finally(() => {
              this.isLoading = false;
            });
        } else {
          this.$api.systemManage.getBasicPermission
            .editGroup(this.addForm)
            .then(({ success }) => {
              if (success)
                this.$notify({
                  title: "成功",
                  message: "修改成功",
                  type: "success",
                });
              this.$emit("cancel", "cancel");
              this.$bus.$emit("customizedAddCancel", "confirm");
            })
            .finally(() => {
              this.isLoading = false;
            });
        }
      });
    },
  },
};
</script>

<style lang="stylus" scoped>
>>>.el-textarea__inner {
  padding-right: 50px;
}

.el-textarea {
  >>>.el-input__count {
    right: 20px !important;
  }
}
</style>
