<template>
  <content-panel> 
  <qDialog 
    :visible="detailsVisible"
    :innerScroll="true"
    :isAuto="true"
    title="详情"
    :isShowCancelBtn="false"
    :showFooter="false"
    width="500px"
    :isLoading="isLoading" 
    :before-close="handleCancel">
     <el-form
      :model="calculateForm"
      ref="calculateForm"
      label-width="80px"
      :rules="rules" 
      size="small"
      :key="upKey"
    >  
      <template>
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="个人:"  >
              {{ calculateForm.personPiece&&calculateForm.personPiece.settlementAmount|| '0.00' }}
            </el-form-item>
          </el-col> 
        </el-row> 
           
        <el-row>
          <el-col :span="24">
            <el-form-item label="集体:"  > 
              <div  style="margin-top: 20px;" v-if="details.length"> 
              <div
                class="details" 
                v-for="(item, index) in details"
                :key="index"
              >
                <div class="details_content">
                    <span style="display: inline-block;width: 100px;">{{ item.groupName }}</span>
                    <span style="margin-left: 50px;">{{ item.amount }}</span>
                </div> 
              </div>
              </div>
              <div v-else>{{ calculateForm.groupPiece&&calculateForm.groupPiece.settlementAmount|| '0.00' }}</div>
            </el-form-item>
          </el-col>
        </el-row> 
         <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="汇总:"  >
              {{ calculateForm.totalPiece&&calculateForm.totalPiece.settlementAmount || '0.00'}}
            </el-form-item>
          </el-col> 
        </el-row> 
      </template>
    </el-form>
  </qDialog> 
  </content-panel>
</template>

<script>
import moment from "moment";
import { moneyFormat, moneyDelete } from "@/utils"; 
import NP from "number-precision";
import { Select } from "element-ui";
export default { 
  props: {
    detailsVisible: {
      type: Boolean,
      required: true,
    },
    editForm: Object,
  },
  data() {
    return {
      upKey:0, 
      isLoading:false,
      calculateForm:{

      },
      rules: { },
      groupList:[],
      totalAmount:"",
      details:[]
    };
  },
  watch: {
      detailsVisible: {
        handler(newVal) {
          
          if (newVal) {
              this.calculateForm={
                ...this.calculateForm,
                ...this.editForm
              }  
              this.details=this.calculateForm.groupPiece&&this.calculateForm.groupPiece.details ||[]   
          }
        },
        immediate: true,
      },
  },
  created() {
    
  },
  computed: {
    // 通过计算属性，对.sync进行转换，外部也可以直接使用visible.sync
    isVisible: {
      get() {
        return this.detailsVisible
      },
      set(val) {
        this.$emit('update:detailsVisible', val)
      },
    }, 
  },
  methods: {
  
    handleCancel() {
       this.isVisible = false;
    }, 
  },
};
</script>

<style lang="stylus" scoped>
>>>.el-select {
  width: 100%;
}

.edit_header {
  >>>.el-form-item {
    margin-bottom: 5px;
  }
}
.el-form-item { 
  >>>.el-form-item__label {
    text-align: right;

  }

 
}

>>>.input_box {
  margin: 3px 0;
}

>>>.el-textarea__inner {
  padding-right: 45px;
}

.el-textarea {
  >>>.el-input__count {
    right: 20px !important;
  }
}
</style>
