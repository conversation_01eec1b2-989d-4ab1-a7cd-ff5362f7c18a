<template>
  <content-panel>
    <qDialog
      :visible="visible"
      :innerScroll="false"
      :innerHeight="500"
      :title="title"
      width="500px"
      :isLoading="isLoading"
      @cancel="handleCancel"
      @confirm="handleConfirm"
      :before-close="handleCancel"
    >
      <el-form
        :model="addForm"
        :rules="rules"
        ref="addForm"
        size="small"
        label-width="173px"
      >
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="核算工厂:" prop="factoryId">
              <el-select
                v-model="addForm.factoryId"
                filterable
                clearable
                style="width: 100%"
                placeholder="请选择核算工厂"
                @change="changeFactoryId"
              >
                <el-option
                  v-for="item in factoryList"
                  :key="item.value"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item
              label="核算班组(工序):"
              prop="groupNames"
              :show-message="!addForm.groupNames"
            >
              <el-input
                disabled
                clearable
                placeholder="请选择核算班组"
                v-model="addForm.groupNames"
                type="text"
              >
                <template slot="append">
                  <el-button @click="selectlist">选择</el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="等级:" prop="grade">
              <el-select
                v-model="addForm.grade"
                filterable
                clearable
                style="width: 100%"
                placeholder="请选择等级"
              >
                <el-option
                  v-for="item in gradeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-form-item label="第一个月日均计件工资(G):">
            <el-col :span="6">
              <el-select
                v-model="addForm.firstConditionVo.operatorSymbol"
                filterable
                clearable
                style="margin-right: 15px"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in Level1"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="12">
              <el-input
                clearable
                placeholder="请输入金额"
                v-model="addForm.firstConditionVo.rightValue"
                maxlength="6"
                type="number"
                class="no-spin-buttons"
              >
                <template slot="append">元</template>
              </el-input>
            </el-col>
          </el-form-item>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="第一个月日均保底:" prop="firstAmount">
              <el-input
                clearable
                placeholder="请输入金额"
                v-model="addForm.firstAmount"
                type="number"
                class="no-spin-buttons"
                @blur="checkAmount()"
              >
                <template slot="append">元/天</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="第二个月日均计件工资(G):">
            <el-col :span="6">
              <el-select
                v-model="addForm.secondConditionVo.operatorSymbol"
                filterable
                clearable
                style="margin-right: 15px"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in Level1"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="12">
              <el-input
                clearable
                placeholder="请输入金额"
                maxlength="6"
                v-model="addForm.secondConditionVo.rightValue"
                type="number"
                class="no-spin-buttons"
              >
                <template slot="append">元</template>
              </el-input>
            </el-col>
          </el-form-item>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="第二个月日均保底:" prop="secondAmount">
              <el-input
                clearable
                placeholder="请输入金额"
                v-model="addForm.secondAmount"
                type="number"
                class="no-spin-buttons"
                @blur="checkAmount()"
              >
                <template slot="append">元/天</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="第三个月日均计件工资(G):">
            <el-col :span="6">
              <el-select
                v-model="addForm.thirdConditionVo.operatorSymbol"
                filterable
                clearable
                style="margin-right: 15px"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in Level1"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="12">
              <el-input
                clearable
                placeholder="请输入金额"
                v-model="addForm.thirdConditionVo.rightValue"
                maxlength="6"
                type="number"
                class="no-spin-buttons"
              >
                <template slot="append">元</template>
              </el-input>
            </el-col>
          </el-form-item>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="第三个月日均保底:" prop="thirdAmount">
              <el-input
                clearable
                placeholder="请输入金额"
                v-model="addForm.thirdAmount"
                type="number"
                class="no-spin-buttons"
                @blur="checkAmount()"
              >
                <template slot="append">元/天</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-form-item label="第四个月日均计件工资(G):" prop="operatorSymbol">
            <el-col :span="6">
              <el-select
                v-model="addForm.fourthConditionVo.operatorSymbol"
                filterable
                clearable
                style="margin-right: 15px"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in Level1"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="12">
              <el-input
                clearable
                placeholder="请输入金额"
                maxlength="6"
                v-model="addForm.fourthConditionVo.rightValue"
                type="number"
                class="no-spin-buttons"
              >
                <template slot="append">元</template>
              </el-input>
            </el-col>
          </el-form-item>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="第四个月日均保底:" prop="fourthAmount">
              <el-input
                clearable
                placeholder="请输入金额"
                v-model="addForm.fourthAmount"
                type="number"
                class="no-spin-buttons"
                @blur="checkAmount()"
              >
                <template slot="append">元/天</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-form-item label="第五个月日均计件工资(G):" prop="operatorSymbol">
            <el-col :span="6">
              <el-select
                v-model="addForm.fifthConditionVo.operatorSymbol"
                filterable
                clearable
                style="margin-right: 15px"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in Level1"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="12">
              <el-input
                clearable
                placeholder="请输入金额"
                maxlength="6"
                v-model="addForm.fifthConditionVo.rightValue"
                type="number"
                class="no-spin-buttons"
              >
                <template slot="append">元</template>
              </el-input>
            </el-col>
          </el-form-item>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="第五个月日均保底:" prop="fifthAmount">
              <el-input
                clearable
                placeholder="请输入金额"
                v-model="addForm.fifthAmount"
                type="number"
                class="no-spin-buttons"
                @blur="checkAmount()"
              >
                <template slot="append">元/天</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item class="assignedFactory" label="备注:" prop="remarks">
              <el-input
                type="textarea"
                v-model="addForm.remarks"
                resize="none"
                rows="3"
                maxlength="50"
                show-word-limit
                placeholder=""
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </qDialog>
    <add-Data
      v-if="isvisible"
      :visible.sync="isvisible"
      :processesList="processesList"
      :checkData="addForm.groupIds"
      @confirmGroupIds="confirmGroupIds"
    />
  </content-panel>
</template>
<script>
import { assignValue } from "@/utils";
import addData from "./add-data.vue";
export default {
  components: { addData },
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    modifyData: {
      type: Object,
      default: {},
    },
    editForm: Object,
    modifyNum: Number,
  },
  computed: {
    title() {
      return this.modifyNum == 1
        ? "新增"
        : this.modifyNum == 2
        ? "编辑"
        : "复制";
    },
    processesList() {
      if (this.factoryList.length && this.addForm.factoryId) {
        return this.factoryList.find((v) => v.id == this.addForm.factoryId)
          .process;
      }
    },
  },
  data() {
    return {
      addForm: {
        factoryId: "",
        groupIds: [],
        firstAmount: "",
        secondAmount: "",
        remarks: "",
        groupNames: "",
        id: "",
        grade: "",
        firstConditionVo: {
          leftValue: "G",
          operatorSymbol: "",
          rightValue: "",
        },
        secondConditionVo: {
          leftValue: "G",
          operatorSymbol: "",
          rightValue: "",
        },
        thirdConditionVo: {
          leftValue: "G",
          operatorSymbol: "",
          rightValue: "",
        },
        fourthConditionVo: {
          leftValue: "G",
          operatorSymbol: "",
          rightValue: "",
        },
        fifthConditionVo: {
          leftValue: "G",
          operatorSymbol: "",
          rightValue: "",
        },
        firstAmount: "",
        secondAmount: "",
        thirdAmount: "",
        fourthAmount: "",
        fifthAmount: "",
      },
      gradeList: [
        { label: "一级", value: "一级" },
        { label: "二级", value: "二级" },
      ],
      Level1: [
        { label: ">", value: ">" },
        { label: "<", value: "<" },
        { label: "=", value: "=" },
        { label: ">=", value: ">=" },
        { label: "<=", value: "<=" },
      ],

      rules: {
        factoryId: [
          { required: true, message: "核算工厂不能为空", trigger: "change" },
        ],
        groupNames: [
          { required: true, message: "核算班组不能为空", trigger: "blur" },
        ],
        grade: [{ required: true, message: "等级不能为空", trigger: "change" }],

        firstAmount: [
          {
            required: true,
            pattern: /^(0|[0-9]{1,6})((\.\d{0,2})*)$/,
            message: "小数点前面仅支持6位数,小数点后面仅支持2位数",
            trigger: "blur",
          },
        ],
        secondAmount: [
          {
            required: true,
            pattern: /^(0|[0-9]{1,6})((\.\d{0,2})*)$/,
            message: "小数点前面仅支持6位数,小数点后面仅支持2位数",
            trigger: "blur",
          },
        ],
        thirdAmount: [
          {
            required: true,
            pattern: /^(0|[0-9]{1,6})((\.\d{0,2})*)$/,
            message: "小数点前面仅支持6位数,小数点后面仅支持2位数",
            trigger: "blur",
          },
        ],
        fourthAmount: [
          {
            required: true,
            pattern: /^(0|[0-9]{1,6})((\.\d{0,2})*)$/,
            message: "小数点前面仅支持6位数,小数点后面仅支持2位数",
            trigger: "blur",
          },
        ],
        fifthAmount: [
          {
            required: true,
            pattern: /^(0|[0-9]{1,5})((\.\d{0,2})*)$/,
            message: "小数点前面仅支持5位数,小数点后面仅支持2位数",
            trigger: "blur",
          },
        ],
      },
      factoryList: [],

      isLoading: false,
      isvisible: false,
    };
  },
  created() {
    this.getFactoryList();
    this.init();
  },
  methods: {
    getFactoryList() {
      this.$api.softwareSystemManage.getBasicPermission
        .getQuFactory({ moduleId: 4 })
        .then((res) => {
          this.factoryList = res.data || [];
        });
    },
    changeFactoryId() {
      this.addForm.groupNames = "";
      this.addForm.groupIds = [];
    },
    init() {
      if (this.modifyData) {
        this.initModifyData();
      }
    },
    initModifyData() {
      if (this.modifyNum == 3) {
        //复制不需要id
        this.modifyData.id = "";
      }
      assignValue(this.addForm, this.modifyData);
    },

    //选择工序
    selectlist() {
      if (!this.addForm.factoryId) {
        this.$message.error("请先选择核算工厂");
        return;
      }
      this.isvisible = true;
    },
    confirmGroupIds(data) {
      let names = data.map((v) => {
        return v.name;
      });
      this.addForm.groupNames = names.join(",");
      this.addForm.groupIds = data.map((v) => {
        return v.id;
      });
    },
    checkAmount() {
      if (
        parseInt(this.addForm.firstConditionVo.rightValue) >
        parseInt(this.addForm.firstAmount)
      ) {
        (this.addForm.firstConditionVo.rightValue = ""),
          (this.addForm.firstAmount = "");
        this.$message.error("第一个月日均保底工资要大于第一个月日均计件工资");
        return;
      }
      if (
        parseInt(this.addForm.secondConditionVo.rightValue) >
        parseInt(this.addForm.secondAmount)
      ) {
        (this.addForm.secondConditionVo.rightValue = ""),
          (this.addForm.secondAmount = ""),
          this.$message.error("第二个月日均保底工资要大于第二个月日均计件工资");
        return;
      }
      if (
        parseInt(this.addForm.thirdConditionVo.rightValue) >
        parseInt(this.addForm.thirdAmount)
      ) {
        (this.addForm.thirdConditionVo.rightValue = ""),
          (this.addForm.thirdAmount = "");
        this.$message.error("第三个月日均保底工资要大于第三个月日均计件工资");
        return;
      }
      if (
        parseInt(this.addForm.fourthConditionVo.rightValue) >
        parseInt(this.addForm.fourthAmount)
      ) {
        (this.addForm.fourthConditionVo.rightValue = ""),
          (this.addForm.fourthAmount = "");
        this.$message.error("第四个月日均保底工资要大于第四个月日均计件工资");
        return;
      }
      if (
        parseInt(this.addForm.fifthConditionVo.rightValue) >
        parseInt(this.addForm.fifthAmount)
      ) {
        (this.addForm.fifthConditionVo.rightValue = ""),
          (this.addForm.fifthAmount = "");
        this.$message.error("第五个月日均保底工资要大于第五个月日均计件工资");
        return;
      }
    },
    handleConfirm() {
      this.$refs.addForm.validate((valid) => {
        this.checkAmount();
        if (!valid) return;
        if (!this.addForm.firstConditionVo.operatorSymbol) {
          this.$message.error("请选择第一个月日均计件等级");
          return;
        }
        if (!this.addForm.firstConditionVo.rightValue) {
          this.$message.error("请输入第一个月日均计件金额");
          return;
        }
        if (!this.addForm.secondConditionVo.operatorSymbol) {
          this.$message.error("请选择第二个月日均计件等级");
          return;
        }
        if (!this.addForm.secondConditionVo.rightValue) {
          this.$message.error("请输入第二个月日均计件金额");
          return;
        }
        if (!this.addForm.thirdConditionVo.operatorSymbol) {
          this.$message.error("请选择第三个月日均计件等级");
          return;
        }
        if (!this.addForm.thirdConditionVo.rightValue) {
          this.$message.error("请输入第三个月日均计件金额");
          return;
        }
        if (!this.addForm.fourthConditionVo.operatorSymbol) {
          this.$message.error("请选择第四个月日均计件等级");
          return;
        }
        if (!this.addForm.fourthConditionVo.rightValue) {
          this.$message.error("请输入第四个月日均计件金额");
          return;
        }
        if (!this.addForm.fifthConditionVo.operatorSymbol) {
          this.$message.error("请选择第五个月日均计件等级");
          return;
        }
        if (!this.addForm.fifthConditionVo.rightValue) {
          this.$message.error("请输入第五个月日均计件金额");
          return;
        }
        this.isLoading = true;
        const {
          factoryId,
          groupIds,
          firstAmount,
          secondAmount,
          remarks,
          groupNames,
          id,
          grade,
          thirdAmount,
          fourthAmount,
          fifthAmount,
        } = this.addForm;
        let params = {
          firstCondition: JSON.stringify({
            leftValue: "G",
            operatorSymbol: this.addForm.firstConditionVo.operatorSymbol,
            rightValue: this.addForm.firstConditionVo.rightValue,
          }),
          secondCondition: JSON.stringify({
            leftValue: "G",
            operatorSymbol: this.addForm.secondConditionVo.operatorSymbol,
            rightValue: this.addForm.secondConditionVo.rightValue,
          }),
          thirdCondition: JSON.stringify({
            leftValue: "G",
            operatorSymbol: this.addForm.thirdConditionVo.operatorSymbol,
            rightValue: this.addForm.thirdConditionVo.rightValue,
          }),
          fourthCondition: JSON.stringify({
            leftValue: "G",
            operatorSymbol: this.addForm.fourthConditionVo.operatorSymbol,
            rightValue: this.addForm.fourthConditionVo.rightValue,
          }),
          fifthCondition: JSON.stringify({
            leftValue: "G",
            operatorSymbol: this.addForm.fifthConditionVo.operatorSymbol,
            rightValue: this.addForm.fifthConditionVo.rightValue,
          }),
          factoryId,
          groupIds,
          firstAmount,
          secondAmount,
          remarks,
          groupNames,
          id,
          grade,
          thirdAmount,
          fourthAmount,
          fifthAmount,
          taskType: "MINIMUM_WAGE",
        };
        // modifyNum:1新增 2编辑 3复制
        let fullApi =
          this.modifyNum == 1 || this.modifyNum == 3
            ? "oldhandAdd"
            : "oldhandedit";
        this.$api.softwareSystemManage.getBasicPermission[fullApi](params)
          .then(() => {
            this.$notify({
              title: "成功",
              message: this.title + "成功",
              type: "success",
            });
            this.$emit("cancel", "confirm");
          })
          .finally(() => {
            this.isLoading = false;
          });
      });
    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
  },
};
</script>

<style lang="stylus" scoped>
.staffCode {
  >>>.el-input-group__append {
    background: #24c69a;
    color: #fff;

    .el-button {
      padding: 5px 20px;
    }
  }
}

.el-row {
  &::before, &::after {
    display: none;
  }

  display: flex;
  justify-content: space-between;
}

.el-form-item {
  display: flex;

  >>>.el-form-item__label {
    text-align: right;

  }

  >>>.el-form-item__content {
    width: 100%;
    margin-left: 0 !important;
  }
}

.assignedFactory {
  >>>.el-form-item__label {
    &::before {
      content: '*';
      color: #F23D20;
      margin-right: 4px;
      visibility: hidden;
    }
  }
}

>>>.el-date-editor {
  width: 100%;
}
.illustrate{
  color:#FF8900;
  padding:10px;
  margin-left:15px

}
>>>[data-v-0a79d792] .el-input-group__append button.el-button{
  color: #fff;
  background-color: #0bb78e;
  border: 4px solid #0bb78e;
}
>>>.el-form-item--small .el-form-item__label{
  line-height:18px
}
>>>.el-col-6{
  width:40%
}
>>>.el-col-12{
  width:60%
}
>>>.el-input-group__append button.el-button{
  color: #fff;
  background-color: #0bb78e;
  border: 4px solid #0bb78e;
}
>>>.el-input.is-disabled .el-input__inner{
  background-color: #fff;
}
::v-deep .no-spin-buttons input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

::v-deep .no-spin-buttons input::-webkit-outer-spin-button {
  -webkit-appearance: none !important;
}

::v-deep .no-spin-buttons input[type="number"] {
  -moz-appearance: textfield !important;
}
</style>
