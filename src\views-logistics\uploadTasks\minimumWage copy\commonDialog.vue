<template>
  <qDialog :visible="isVisible"
    title="批量删除"
    :innerScroll="false"
    width="400px"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel">
  
  </qDialog>
</template>

<script>
export default {
  name: "editDialog",
  props: {
    isVisible: {
      type: Boolean,
      required: true
    },
    editForm:Object
  },
  data() {
    return {

    }
  },
  methods: {
    handleCancel() {
      this.$emit('cancel', 'cancel')
    },
    handleConfirm() {
      this.$emit('cancel', 'confirm')
    },
  }
}
</script>

<style lang="scss" scoped>
</style>