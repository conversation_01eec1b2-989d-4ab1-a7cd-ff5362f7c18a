<template>
  <content-panel>
    <!-- 公司补贴 -->
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          label-position="left"
          size="mini"
        >
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="员工姓名"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input
              clearable
              v-model.trim="searchForm.staffCode"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenStaffCodes"
                  titleName="厂牌编号"
                />
              </template>
            </el-input>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary"  @click="onSearch">
            查询
          </el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-right>
        <el-button v-show="permission" size="small" type="primary" @click="handleImport">
          导入
        </el-button>
      </template>
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :data="tableData"
        :height="maxTableHeight"
      >
        <el-table-column type="index" fixed width="40"> </el-table-column>
        <el-table-column label="片区" prop="area" width="100" fixed show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="分厂名称"
          prop="factoryName"
          width="130"
          fixed
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="员工姓名"
          prop="staffName"
          width="100"
          fixed
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="厂牌编号"
          prop="staffCode"
          width="130"
          fixed
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="封边台数"
          prop="machineNum"
          width="100"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="考核金额"
          prop="assessAmount"
          width="100"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="核算月份"
          prop="accountingMonth"
          width="100"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="质量管理(共25分)"
          prop="qualityControl"
          width="150"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="功能使用(共20分)"
          prop="functionUsage"
          width="150"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="设备问题反馈及处理(共20分)"
          prop="questionFeedBack"
          width="200"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="零部件损坏(共10分)"
          prop="partsDamage"
          width="150"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="设备保养(共15分)"
          prop="equipmentMaintenance"
          width="150"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="出勤扣分"
          prop="attndPenalty"
          width="100"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="出勤天数(10)"
          prop="attndDays"
          width="100"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="出勤考核(50元/天)"
          width="150"
          prop="attndCheck"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column label="备注说明" prop="remark" width="150" show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="本月总扣分"
          prop="totalPenaltyScore"
          width="100"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="最终得分"
          prop="finalScore"
          width="100"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="补贴金额"
          prop="companyAllowance"
          width="100"
          show-overflow-tooltip
        >
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div class="table_footer">
          <ul>
            <li>
              <span>核算工厂:</span><span>{{ infoList.factoryName }}</span>
            </li>
            <li>
              <span>核算月份:</span><span>{{ infoList.accountingMonth }}</span>
            </li>
            <li>
              <span>人数:</span><span>{{ debitInfo.people }}</span>
            </li>
            <li>
              <span>补贴金额:</span
              ><span>{{ debitInfo.companyAllowance | moneyFormat }}</span>
            </li>
          </ul>
        </div>
        <el-pagination
          :current-page="pageNum"
          :page-size="pageSize"
          :total="total"
          :page-sizes="[50, 100, 200]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onSizeChange"
          @current-change="onNumChange"
        >
        </el-pagination>
      </template>
    </table-panel>
    <Import
      :visible="ImportVisible"
      @cancel="cancel"
      @confirm="confirm"
      :importInfo="importInfo"
    />
  </content-panel>
</template>
<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
export default {
  name: "LogisticsCompanysubsidies",
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      searchForm: {
        staffCode: "",
        staffName: "",
      },
      infoList: {},
      tableData: [],
      loading: false,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      filterParam: {},
      params: {},
      factoryId: "",
      permission: "",
      resizeOffset: 70,
      debitInfo: { sysTotal: 0, totalSocialSecurity: 0 },
      ImportVisible: false,
    };
  },
  watch: {
    $route: {
      handler(value) {
        if (value.path.includes("companysubsidies")&&value.path.includes("logistics")) {
          this.infoList = JSON.parse(this.$Base64.decode(value.query.data));
          this.factoryId = this.infoList.factoryId;
          this.importInfo = {
            reportName: "logisticsImportcoallowance",
            paramMap: {
              columnValue: "物流-公司补贴",
              factoryId: this.factoryId,
              accountingMonth: this.infoList.accountingMonth,
              id: this.infoList.id,
            },
          };
          this.getList();
          this.getDebitList();
          this.getPermission();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    //获取公司补贴列表
    getList() {
      this.loading = true;
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        filterData: {
          factoryId: this.factoryId,
          accountingMonth: this.infoList.accountingMonth,
          ...this.filterParam,
          ...this.params,
        },
      };
      this.$api.logisticsDataUpload.coSubsidy
        .getCoSubsidy(params)
        .then(({ data: { list, total } }) => {
          this.tableData = list || [];
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //是否具有待办任务操作权限
    getPermission() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.infoList.accountingMonth,
        type: "14",
      };
      this.$api.logisticsWorkbench.agencyPermission(params).then((res) => {
        this.permission = res.data;
      });
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
      this.getDebitList();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    onSizeChange(pageSize) {
      this.pageSize = pageSize;
      // 切换每页条数后从第一页查询
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    // 获取底部数据信息
    getDebitList() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.infoList.accountingMonth,
        ...this.filterParam,
        ...this.params,
      };
      this.$api.logisticsDataUpload.coSubsidy.coSubsidyStatistic(params).then((res) => {
        if (res.code == 200) {
          this.debitInfo = res.data
            ? res.data
            : {
                sysTotal: 0,
                totalSocialSecurity: 0,
              };
        }
      });
    },
    handleImport() {
      this.title = "导入";
      this.ImportVisible = true;
    },
    cancel(value) {
      this.ImportVisible = value;
    },
    confirm(value) {
      this.ImportVisible = value;
    },
  },
};
</script>
<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}
ul {
  list-style: none;
  display: flex;
  padding: 0;
}

i {
  font-style: normal;
}

>>>.table-panel-footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .table_footer {
    overflow: auto;

    >ul {
      display: flex;
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        white-space: nowrap
        padding: 0 10px;
      }
    }
  }
}
</style>
