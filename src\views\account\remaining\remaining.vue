<template>
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form size="mini" :inline="true" :model="searchForm" ref="searchForm" label-position="right" label-width="98px">
          <el-form-item label="修改人员:" prop="updateUser">
            <el-input v-model.trim="searchForm.updateUser" placeholder="请选择修改人员" @keyup.enter.native="onSearch"></el-input>
          </el-form-item>
          <el-form-item label="修改月份:" prop="accountingMonth">
            <el-date-picker v-model="searchForm.accountingMonth" align="right" type="month" placeholder="请选择修改月份" :picker-options="pickerOptions" @change="onSearch">
            </el-date-picker>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary"  @click="onSearch">查询</el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">重置</el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-right>
        <div ref="btnRight">
          <el-button size="small" type="primary" @click="handleExport">导出</el-button>
        </div>
      </template>
      <el-table stripe border v-loading="loading" ref="tableRef" highlight-current-row :height="maxTableHeight" :data="tableData">
        <el-table-column prop="factoryName" label="工厂名称" width="150" align="left">
        </el-table-column>
        <el-table-column prop="updateTime" label="修改时间" width="150" align="left">
          <template slot-scope="scope">
            {{scope.row.updateTime | dateFormat}}
          </template>
        </el-table-column>
        <el-table-column prop="accountingMonth" label="核算月份" width="150" align="left">
        </el-table-column>
        <!-- <el-table-column prop="beforeAmount" label="调整前金额" width="150" align="left" show-overflow-tooltip>
          <template slot-scope="{row}">
            {{row.beforeAmount|moneyFormat}}
          </template>
        </el-table-column> -->
        <el-table-column prop="editAmount" label="调整金额" width="150" align="left" show-overflow-tooltip>
          <template slot-scope="{row}">
            {{row.editAmount|moneyFormat}}
          </template>
        </el-table-column>
        <!-- <el-table-column prop="afterAmount" label="当月余留" width="150" align="left" show-overflow-tooltip>
          <template slot-scope="{row}">
            {{row.afterAmount|moneyFormat}}
          </template>
        </el-table-column> -->
        <el-table-column prop="updateUser" label="修改人员" width="150" align="left">
        </el-table-column>
        <el-table-column prop="comments" label="调整说明" align="left" show-overflow-tooltip>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div style="text-align: right">
          <el-pagination @size-change="onSizeChange" @current-change="onNumChange" :current-page="pageNum" :page-size="pageSize" :total="total" :page-sizes="[ 50, 100, 200]" layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>
        </div>
      </template>
    </table-panel>
  </content-panel>
</template>

<script>
import { mapGetters } from 'vuex'
import tableMixin from '@/utils/tableMixin';
import pagePathMixin from "@/utils/page-path-mixin";
export default {
  name: "RemainingDetailed",
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      searchForm: { updateUser: "", accountingMonth: "" },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      tableData: [],
      loading: false,
      filterParam: {},
      pageSize: 50,
      resizeOffset: 55,
      pageNum: 1,
      total: 0,
      factoryId: ""
    };
  },
  created() {
  },
  mounted() { },
  computed: {
    ...mapGetters([
      'name'
    ]),
  },
  watch: {
    $route: {
      handler(value) {
        if (value.path.includes('remainingDetailed')&&value.path.includes("customized"))
          this.factoryId = value.query.factoryId
        this.getList()
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 获取工厂详细余留
    getList() {
      this.loading = true
      this.$api.information.remaining.getDetailedRemaining({ pageSize: this.pageSize, pageNum: this.pageNum, filterData: { factoryId: this.factoryId, ...this.filterParam } }).then(res => {
        if (res.code === 200) {
          this.tableData = res.data.list
          this.total = res.data.total
        }
      }).finally(() => {
        this.loading = false
      });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.filterParam = {};
      this.onSearch();
    },
    //搜索
    onSearch() {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== 'undefined' && val !== null && val !== '') {
          this.filterParam[key] = val;
        }
      }
      if (this.searchForm.accountingMonth) {
        this.filterParam = {
          ...this.filterParam, accountingMonth: this.filterParam.accountingMonth.getFullYear() + "-" + (this.filterParam.accountingMonth.getMonth() + 1 < 10 ? "0" + (this.filterParam.accountingMonth.getMonth() + 1) : this.filterParam.accountingMonth.getMonth() + 1),
        };
      }
      this.pageNum = 1;
      this.getList();
    },
    //导出
    handleExport() {
      let params = {}
      if (Object.keys(this.filterParam).length) {
        params = {
          ...this.filterParam,
        }
      }
      this.$api.common.doExport('exportRemainDetal', { ...params, factoryId: this.$route.query.factoryId }).then(() => {
        this.$message.success('导出操作成功，请前往导出记录查看详情');
      });
    },
    onSizeChange(pageSize) {
      this.pageSize = pageSize;
      // 切换每页条数后从第一页查询
      this.pageNum = 1;
      this.getList()
    },
    onNumChange(pageNum) {
      this.pageNum = pageNum;
      this.getList()
    },
  },
};
</script>

<style lang="stylus" scoped></style>

