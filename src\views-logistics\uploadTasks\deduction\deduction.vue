<template>
  <!-- 其他扣款 -->
  <content-panel class="security">
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          label-position="right"
          size="mini"
        >
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="员工姓名"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input
              v-model.trim="searchForm.staffCode"
              size="mini"
              clearable
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenStaffCodes"
                  titleName="厂牌编号"
                />
              </template>
            </el-input>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary"  @click="onSearch">
            查询</el-button
          >
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置</el-button
          >
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template
        v-slot:header-right
        v-if="info.status != '待提交' || info.roleName != '分厂文员'"
      >
        <el-button size="small" type="primary" @click="handleAdd" v-show="permission">
          新增</el-button
        >
        <el-button size="small" type="primary" @click="handleImport" v-show="permission">
          导入</el-button
        >
      </template>
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :data="tableData"
        :height="maxTableHeight"
        style="width: 100%"
      >
        <el-table-column
          v-for="(item, index) in tableInfo"
          :key="index"
          :label="item.columnName"
          :prop="item.fieldValue"
          align="left"
          :width="item.width"
          :fixed="item.fixed"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row[item.fieldName] }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="130" fixed="right" v-if="permission">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              @click="handleEdit(scope.$index, scope.row)"
            >
              编辑</el-button
            >
            <el-popconfirm title="确认删除吗?" @confirm="handleDelete(scope.row)">
              <el-button slot="reference" type="text" size="mini">删除 </el-button>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <ul>
          <li>核算月份:{{ info.accountingMonth }}</li>
          <li>人数:{{ debitInfo.people }}</li>
        </ul>
        <el-pagination
          :current-page="pageNum"
          :page-size="pageSize"
          :total="total"
          :page-sizes="[50, 100, 200]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </template>
    </table-panel>
    <add-dialog
      v-if="visible"
      :visible="visible"
      :title="title"
      :editForm="editForm"
      @cancel="handleCancel"
    ></add-dialog>
    <Import
      v-if="ImportVisible"
      :visible="ImportVisible"
      @cancel="cancel"
      @confirm="confirm"
      :importInfo="importInfo"
    />
  </content-panel>
</template>
<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import { moneyFormat, moneyDelete, calculateTableWidth } from "@/utils";
import addDialog from "./addDialog";
export default {
  name: "LogisticsDeduction",
  components: { addDialog },
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      searchForm: {
        deduction: "",
        name: "",
        staffCode: "",
        staffName: "",
      },
      loading: false,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      visible: false,
      tableData: [], //表格数据
      //表格表头信息
      tableInfo: [],
      filterParam: {},
      params: {},
      info: {},
      editForm: {},
      resizeOffset: 75, //设置table间距
      title: "",
      factoryId: "",
      permission: "",
      ImportVisible: "",
      importInfo: "",
      debitInfo: {},
    };
  },
  watch: {
    $route: {
      handler(value) {
        if (value.path.includes("deduction")&&value.path.includes("logistics")) {
          this.info = JSON.parse(this.$Base64.decode(value.query.data));
          this.factoryId = this.info.factoryId;
          this.importInfo = {
            reportName: "logisticsOtherdeductionimport",
            paramMap: {
              columnValue: "物流-其他扣款",
              factoryId: this.factoryId,
              accountingMonth: this.info.accountingMonth,
              id: this.info.id,
            },
          };
          this.getList();
          this.getDebitList();
          this.getPermission();
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    getList() {
      //获取分页
      this.loading = true;
      this.$api.logisticsDataUpload.OtherDeductions.getotherDeductionList({
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        filterData: {
          accountingMonth: this.info.accountingMonth,
          factoryId: this.factoryId,
          planId: this.info.otherId,
          ...this.filterParam,
          ...this.params,
        },
      })
        .then((res) => {
          this.$nextTick(() => {
            const width = document.querySelector(".el-table").clientWidth;
            const list = res.data.columnVOs.filter(
              (item, index) => item.fieldType == "double"
            );
            this.tableData =
              res.data.pageInfo.list.map((item, index) => {
                list.forEach((it) => {
                  for (const key in item) {
                    if (key == it.fieldName) item[key] = moneyFormat(item[key]);
                  }
                });
                return item;
              }) || [];
            let items = {
              staffName: "100",
              staffCode: "110",
              accountingMonth: "80",
              createTime: "150",
            };
            this.tableInfo = res.data.columnVOs.map((item) => {
              if (
                ["姓名", "厂牌编号", "核算月份", "修改时间"].includes(item.columnName)
              ) {
                Object.keys(items).forEach((key) => {
                  if (key == item.fieldName) {
                    item.width = items[item.fieldName];
                  }
                });
              } else {
                item.width = this.flexWidth(
                  item.fieldName,
                  this.tableData,
                  item.columnName
                );
              }
              if (["姓名", "厂牌编号"].includes(item.columnName)) {
                item.fixed = "left";
              }
              return item;
            });
            let totalWidth = this.tableInfo.reduce((pre, cur) => {
              return (pre += Number(cur.width));
            }, 0);
            console.log(totalWidth,width);
            if (totalWidth <= width) {
              this.tableInfo.forEach((item) => {
                if(!["姓名", "厂牌编号", "核算月份", ].includes(item.columnName)){
                  delete item.width
                }
              });
            }
          });
          this.total = res.data.pageInfo.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    getDebitList() {
      let params = {
        accountingMonth: this.info.accountingMonth,
        factoryId: this.factoryId,
        ...this.filterParam,
        ...this.params,
      };
      this.$api.logisticsStatiStics.statisticsOtherDeduction(params).then((res) => {
        this.debitInfo = res.data
          ? res.data
          : {
              people: 0,
            };
      });
    },
    //是否具有待办任务操作权限
    getPermission() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.info.accountingMonth,
        type: "130",
      };
      this.$api.logisticsWorkbench.agencyPermission(params).then((res) => {
        this.permission = res.data;
      });
    },
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.filterParam[name] = data;
      }
      this.getList();
      this.getDebitList();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    //导入
    handleImport() {
      sessionStorage.setItem("tableInfo", JSON.stringify(this.tableInfo));
      this.title = "导入";
      this.ImportVisible = true;
    },
    //计算表格列宽度
    flexWidth(...args) {
      return calculateTableWidth(...args);
    },
    //新增
    handleAdd() {
      this.title = "新增";
      this.visible = true;
      this.editForm = {};
    },
    //编辑
    handleEdit(index, row) {
      let list = [];
      Object.keys(row).forEach((key) => {
        if (key.includes("field")) {
          list.push({ [key]: row[key] });
        }
      });
      this.title = "编辑";
      this.visible = true;
      this.editForm = {
        staffCode: row.staffCode || "",
        staffName: row.staffName || "",
        accountingMonth: row.accountingMonth || "",
        list,
      };
    },

    //删除
    handleDelete(row) {
      let params = {
        factoryId: row.factoryId,
        accountingMonth: row.accountingMonth,
        staffCode: row.staffCode,
      };
      this.$api.logisticsDataUpload.OtherDeductions.getOtherDeductionDelete(params)
        .then((res) => {
          this.$notify.success({
            title: "成功",
            message: "删除成功",
          });

          this.getList();
          this.getDebitList();
        })
        .finally(() => {
          this.loading = false;
        });
    },

    handleCancel(type) {
      this.visible = false;
      if (type == "cancel") return;
      this.getList();
      this.getDebitList()
    },
    //导入
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePreview(file) {
      console.log(file);
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.pageNum = val;
      this.getList();
    },
    cancel(value) {
      this.ImportVisible = value;
    },
    confirm(value) {
      this.ImportVisible = value;
    },
  },
};
</script>

<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}
#item {
  margin: 0;
  padding: 5px;
}

.table-panel {
  position: relative;

  >>>.btn_right {
    position: absolute;
    right: 0;
    z-index: 2;
  }
}

ul {
  list-style: none;
  display: flex;
  padding: 0;
}

i {
  font-style: normal;
}

>>>.table-panel-footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  ul {
    display: flex;

    li {
      padding: 0 5px;
    }
  }
}
.el-form-item__content {
  display: flex;
}
</style>
