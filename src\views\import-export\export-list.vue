<template>
  <content-panel class="main">
    <template v-slot:search>
      <search-box class="search-box">
        <el-form :inline="true" :model="searchForm" ref="searchForm" size="mini" label-width="90px">
          <el-form-item label="导出日期:" class="date">
           <el-date-picker
              v-model="searchForm.startDate"
              type="date"
              :clearable="false"
              placeholder="选择开始日期">
            </el-date-picker>
           <span style="padding:0 5px">至</span>
            <el-date-picker
              v-model="searchForm.endDate"
              type="date"
              :clearable="false"
              :picker-options="endTimeOptions"
              placeholder="选择结束日期">
            </el-date-picker>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary" @click="onSearch">查询</el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">重置</el-button>
        </template>
      </search-box>
    </template>
    <table-panel class="tab-content">
      <export-task ref="exportTask"></export-task>
    </table-panel>
  </content-panel>
</template>

<script>
import { SUB_APP_CODE } from '@/api/api'
import moment from 'moment'
export default {
  name: 'ExportList',
  data() {
    return {
      searchForm: {
        startDate: '',
        endDate: '',
      }
    }
  },
  computed: {
     endTimeOptions() {
        return {
          disabledDate: (time) =>  time.getTime() < new Date(this.searchForm.startDate).getTime()
        }
      },
  },
  methods: {
    onSearch() {
      const formData = this.$refs['exportTask'].setSearchForm({
       startDate: this.searchForm.startDate && moment(this.searchForm.startDate).startOf('d').format('YYYY-MM-DD HH:mm:ss') || '',
       endDate:  this.searchForm.endDate && moment(this.searchForm.endDate).endOf('d').format('YYYY-MM-DD HH:mm:ss') || '',
      });
     let data = { ...formData, appCode: SUB_APP_CODE }
      this.$refs['exportTask'].init(data);
    },
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.searchForm.startDate = '';
      this.searchForm.endDate = '';
      this.onSearch();
    }
  },
  mounted() {
    this.onSearch();
  }
}
</script>

<style lang="stylus" scoped>
.tab-content {
  >>> .table-panel-content {
    height: calc(100vh - 140px);
  }
}

.main {
  >>> .main-area {
    padding: 0;
  }
}

.input-date {
  width: 250px !important;
}
</style>
<style lang="stylus">
.table-panel-content {
  .root {
    .el-container {
      .page-header {
        display: none;
      }

      .table-main {
        padding: 0;
      }
    }

    .main-weapper {
      height: 95%;
    }

    .el-table {
      height: calc(100% - 40px) !important;
    }
  }
}
</style>
<style lang="stylus" scoped>
>>>.date {
  .el-form-item__content {
    .el-date-editor {
      width: 150px !important;
    }
  }
}
</style>