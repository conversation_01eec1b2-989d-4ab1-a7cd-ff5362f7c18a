<template>
  <div class="custom">
    <span class="custom_title">{{ title }}</span>
    <div class="custom_content" ref="customContent" style="height: 400px">
      <div class="custom_search">
        <el-form :model="searchForm" size="small">
          <el-form-item>
            <el-input
              placeholder="输入关键字搜索"
              v-model="searchForm.value"
              @input="onSearch"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div class="scorll-wrap" ref="scorllWrap" :style="{ height: maxHeight + 'px' }">
        <el-scrollbar
          class="dialog-scrollbar"
          wrap-class="dialog-scroll-wrap"
          ref="dialogScrollbar"
        >
          <!-- 内容区默认插槽 -->
          <div class="dialog-content">
            <slot name="content"></slot>
          </div>
        </el-scrollbar>
      </div>
      <div v-if="showFooter" ref="footer">
        <slot name="footer"></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "payrollCustom",
  props: {
    title: {
      type: String,
      required: true,
    },
    showFooter: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      searchForm: {
        value: "",
      },
      maxHeight: 300,
    };
  },
  created() {
    this.$nextTick(() => {
      let customContent =
        this.$refs.customContent && this.$refs.customContent.getBoundingClientRect();
      let scorllWrap =
        this.$refs.scorllWrap && this.$refs.scorllWrap.getBoundingClientRect();
      let footer = this.$refs.footer && this.$refs.footer.getBoundingClientRect();
      this.maxHeight =
        customContent.height -
        (scorllWrap.top - customContent.top) -
        ((footer && footer.height + 10) || 0);
    });
  },
  methods: {
    onSearch() {
      this.$emit("customSearch", {
        title: this.title,
        value: this.searchForm.value,
      });
    },
  },
};
</script>

<style lang="stylus" scoped>
.custom {
  .custom_title {
    font-weight: bold;
    text-align: center;
    display: block;
    margin-bottom: 10px;
  }

  .custom_content {
    border: 1px solid #ccc
    overflow :hidden
    .custom_search {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 10px 0;
      .el-form{
        display: flex
        align-items: center
        .el-form-item{
          margin: 0
        }      }
      .el-button {
        height: 31px
      }
    }
  }
}

>>>.el-checkbox__label {
  font-size: 14px;
  line-height: 20px;
}

.el-checkbox-group {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
}
>>>.dialog-scrollbar{
  height: 100%

}
>>>.dialog-scroll-wrap{
   overflow-x: hidden
}
</style>
