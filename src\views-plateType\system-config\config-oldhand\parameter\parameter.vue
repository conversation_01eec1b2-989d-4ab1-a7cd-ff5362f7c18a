<template>
  <!-- 参数配置 -->
  <content-panel>
    <div class="config-header">
      <div class="header-left">
        <span>参数设置</span>
        <span class="switchMg">启用开关</span>
        <el-switch
          :disabled="!isModifyType"
          class="mg15"
          v-model="configForm.onOff"
          active-value="on"
          inactive-value="off"
        ></el-switch>
        <el-tooltip content="开启后配置生效,反之不生效" placement="right">
          <i class="el-icon-question"></i>
        </el-tooltip>
      </div>
      <div class="header-right">
        <el-button
          size="small"
          type="primary"
          @click="isModifyType = true"
          v-if="!isModifyType"
          >编辑</el-button
        >
        <template v-else>
          <el-button size="small" type="primary" @click="confirmSave"
            >保存</el-button
          >
          <el-button size="small" type="warning" @click="getList"
            >取消</el-button
          >
        </template>
      </div>
    </div>
    <div class="config-content">
      <span class="mg16">熟手补贴的员工,从入职后第</span>
      <el-input
        :disabled="!isModifyType"
        v-model="configForm.stopMonths"
        type="text"
        maxlength="2"
        onkeyup="value=value.replace(/[^0-9]/g, '')"
        style="width: 70px; margin: 0px 2px"
      ></el-input>
      <span>个月停止补贴</span>
    </div>
    <div class="config-bottom">
      <el-input
        disabled
        v-model="configForm.year"
        type="text"
        style="width: 70px; margin: 0px 2px"
      ></el-input>
      <span class="mg10">年春节放假时间</span>
      <el-date-picker
        :disabled="!isModifyType"
        v-model="configForm.workDay"
        type="daterange"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="calculateDuration"
      >
      </el-date-picker>
      <span class="mg10">顺延员工补贴</span>
      <el-input
        disabled
        v-model="configForm.delayDays"
        type="text"
        onkeyup="value=value.replace(/[^0-9]/g, '')"
        style="width: 60px; margin: 0px 2px"
      ></el-input>
      <span>天</span>
    </div>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import moment from "moment";
export default {
  name: "parameter",
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      isModifyType: false,
      configForm: {
        year: "",
        onOff: "on",
        taskType: "SKILLED_SUBSIDY",
        workDay: [],
        id: "",
        stopMonths: "",
        delayDays: "",
        startDate: "",
        endDate: "",
      },
    };
  },
  computed: {},
  methods: {
    calculateDuration() {
      let start = new Date(moment(this.configForm.workDay[0]).format("YYYY-MM-DD"));
      let end = new Date(moment(this.configForm.workDay[1]).format("YYYY-MM-DD"));
      this.configForm.delayDays = Math.abs(end - start) / (1000 * 60 * 60 * 24) + 1;
    },
    init() {
      var date = new Date();
      this.configForm.year = date.getFullYear();
    },
    getList() {
      this.isModifyType = false;
      let formData = new FormData();
      formData.append("taskType", "SKILLED_SUBSIDY");
      this.$api.plateTypeSystemConfig
        .oldhandgetConfigGlobal(formData)
        .then((res) => {
          if(!res.data) return
          const { onOff, stopMonths, startDate, endDate, delayDays, id } =
            res.data[0];
          this.configForm.onOff = onOff ? onOff : "on",
          this.configForm.id = id;
          this.configForm.stopMonths = stopMonths;
          this.configForm.workDay = [startDate, endDate];
          this.configForm.delayDays = delayDays;
        });
    },
    confirmSave() {
      const { onOff, stopMonths, workDay, delayDays, id } = this.configForm;
      if (!stopMonths) {
        this.$message.error("请输入多少月后停止补贴");
        return;
      }
      if (!workDay) {
        this.$message.error("请选择春节放假日期");
        return;
      }
      let json = {
        delayDays,
        stopMonths,
      };
      let params = {
        id,
        configJson: JSON.stringify(json),
        onOff,
        startDate: moment(workDay[0]).format("YYYY-MM-DD"),
        endDate: moment(workDay[1]).format("YYYY-MM-DD"),
        taskType: "SKILLED_SUBSIDY",
      };
      this.$api.plateTypeSystemConfig
        .oldupdateConfigGlobal(params)
        .then((res) => {
          this.$message.success("操作成功");
          this.getList();
          this.isModifyType = false;
        });
    },
  },
  created() {
    this.init();
    this.getList();
  },
};
</script>
<style lang="stylus" scoped>
.content-panel  {
    height:100vh
}
.el-select{
  width:80px
  height:30px
}
.config-header{
  display:flex;
  justify-content:space-between;
  align-items:center;

  .header-left{
    font-weight:bold;
   .switchMg{
    margin:0px 10px 0px 20px;
    font-weight:400
   }
  }
}
.config-content{
  display:flex;
  align-items:center;
  margin:10px 0px
}
>>>.el-input__inner{
  height:30px;
  line-height:30px
}
>>> .el-range__icon {
  line-height:23px !important
}
>>>  .el-range__close-icon {
  line-height:23px !important
}
>>> .el-range-separator{
  line-height:23px !important
}

.config-bottom{
 display:flex;
 align-items:center;

}
.mg10{
  margin:0px 10px
}
</style>
