<template>
  <content-panel
    class="import-content-panel">
    <template v-slot:search>
      <search-box
        class="search-box">
        <el-form
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          size="mini">
          <el-form-item
            label="文件名:"
            prop="reportName">
            <el-input
              @keyup.enter.native="onSearch"
              v-model="searchForm.reportName"
              clearable
              placeholder="请输入文件名称">
            </el-input>
          </el-form-item>
          <el-form-item
            class="date"
            label="导入时间:">
             <el-date-picker
              v-model="searchForm.startDate"
              type="date"
              :clearable="false"
              placeholder="选择开始日期">
            </el-date-picker>
            <span style="padding:0 5px">至</span>
            <el-date-picker
              v-model="searchForm.endDate"
              type="date"
              :clearable="false"
              :picker-options="endTimeOptions"
              placeholder="选择结束日期">
            </el-date-picker>
          </el-form-item>
        </el-form>
        <template
          v-slot:right>
          <el-button
            size="small"
            type="primary"
            @click="onSearch">
            查询</el-button>
          <el-button
            size="small"
            type="warning"
            @click="resetSearchForm">
            重置</el-button>
        </template>
      </search-box>
    </template>
    <table-panel
      class="tab-content">
      <template
        v-slot:header-left>
        <el-button
          type="primary"
          size="small"
          @click="handleImport">
          批量导入</el-button>
        <el-button
          type="primary"
          size="small"
          @click="downloadTemplate">
          下载模板</el-button>
      </template>
      <import-task
        ref="importTask">
      </import-task>
    </table-panel>
    <qDialog
      :visible="visible"
      :innerScroll="false"
      title="下载模板" width="30%"
      @cancel="handleCancel"
      @confirm="handleConfirm"
      :before-close="handleCancel">
      <template>
        <el-form
          :model="tempForm">
          <el-form-item
            label="模板名称:">
            <el-select
              v-model="tempForm.name"
              filterable
              multiple
              collapse-tags
              placeholder="请选择模板名称">
              <el-option
                v-for="item in tempOptions"
                :key="item.id"
                :label="item.fileName"
                :value="item.reportName">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </template>
    </qDialog>
    <import-dialog
      v-if="isShowImport"
      :visible.sync="isShowImport"
      @after="handleAfter" />
  </content-panel>
</template>

<script>
import ImportDialog from './import-dialog'
import moment from 'moment'
import { APP_CODE, SUB_APP_CODE } from '@/api/api'
import { downloadExcel } from "@/utils";
export default {
  name: 'ImportFile',
  components: {
    ImportDialog
  },
  data() {
    return {
      taskSummaryId: {},

      loading: false,
      isShowImport: false,
      visible: false,
      searchForm: {
        moduleValue: '',
        startDate: '',
        endDate: '',
        reportName: ""
      },
      tempForm: {
        name: ""
      },
      tempOptions: [],
      tableData: []
    }
  },
  computed: {
     endTimeOptions() {
        return {
          disabledDate: (time) =>  time.getTime() < new Date(this.searchForm.startDate).getTime()
        }
      },
  },
  methods: {
    //获取Excel配置列表
    getList() {
      this.$api.importList.getExcelList({
        pageNum: 1,
        pageSize: 1000,
        filterData: {
          appCode: 'was-customized',
          reportType: "IMPORT"
        },
      }).then(res => {
          this.tempOptions = res.data.list.filter(item => !item.reportName.includes('software')&&!item.reportName.includes('plank')&&!item.reportName.includes('logistics')).filter(item => item.reportName!='importCostCompensation')
      })
    },
    handleImport() {
      this.isShowImport = true;
    },
    handleAfter() {
      this.onSearch();
    },
    onSearch() {
      let filterParams = {
        reportType: "IMPORT",
        startDate: this.searchForm.startDate && moment(this.searchForm.startDate).startOf('d').format('YYYY-MM-DD HH:mm:ss') || '',
        endDate:  this.searchForm.endDate && moment(this.searchForm.endDate).endOf('d').format('YYYY-MM-DD HH:mm:ss') || '',
        appCode: SUB_APP_CODE,
        reportNameCN: this.searchForm.reportName
      };
      this.$refs['importTask'].init(filterParams);
    },
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.searchForm.startDate = '';
      this.searchForm.endDate = '';
      this.onSearch();
    },
    //下载模板
    downloadTemplate() {
      this.getList()
      this.tempForm.name = ""
      this.visible = true
    },
    handleCancel() {
      this.visible = false
    },
    handleConfirm() {
      this.visible = false
      this.$nextTick(() => {
        this.tempForm.name.forEach(templatePath => {
           if (['otherDeductionImport', 'otherSubsidyImport'].includes(templatePath)) {
            let otherPlanName = this.tempOptions.find(item => item.reportName === templatePath).fileName || ''
            this.$api.systemConfig
              .taskConfigDownload({
                otherPlanName,
              })
              .then((res) => {
                this.downloadOtherFile(res.data, otherPlanName);
              });
          } else {
            this.$api.importList.getTemplate({ reportName: templatePath }).then(res => {
              this.downloadFile(res.data.templatePath, res.data.fileName);
            })
          }
        });

      })
      // this.$api.importList.getCustomColumnImportant({
      //   file: '',
      //   taskDTO: {
      //     appCode: SUB_APP_CODE,
      //     reportName: this.$route.type == 'deduction' ? 'otherDeductionImport' : 'otherSubsidyImport',
      //     paramMap: {

      //     },
      //     columnDTOs: [
      //       {
      //         columnName: '',
      //         fieldName: '',
      //         fieldType: '',
      //         columnFormat: '',
      //       },
      //     ]
      //   }
      // }).then(res => {
      //   console.log(res);
      // })
    },
    downloadOtherFile(fileData, fileName) {
      const name = fileName + ".xlsx";
      const url = window.URL.createObjectURL(
        new Blob([fileData], { type: "application/vnd.ms-excel" })
      );
      const link = document.createElement("a");
      link.style.display = "none";
      link.href = url;
      link.setAttribute("download", name);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      setTimeout(() => {
        URL.revokeObjectURL(url);
      });
    },
    async downloadFile(fileUrl, fileName) {
      try {
        let response = await fetch(fileUrl);
        let blob = await response.blob();
        let objectUrl = window.URL.createObjectURL(blob);
        let a = document.createElement("a");
        a.href = objectUrl;
        a.download = `xxx厂xx年xx月${fileName}表.xlsx`;
        a.click();
        a.remove();
      } catch (error) {
        console.log(error);
      }
    }
  },
  mounted() {
    this.onSearch();
  },
}
</script>

<style lang="stylus" scoped>
>>>.table-panel-header {
  padding: 10px !important;
}

.date-item {
  display: flex;
  align-items: center;

  .date-input {
    flex: 1;
  }

  >span {
    flex: none;
    margin: 0 5px;
  }
}

.tab-content {
  >>> .table-panel-content {
    height: calc(100vh - 140px);
  }
}

.import-content-panel {
  >>> .main-area {
    padding: 0;
  }
}

>>>.el-main {
  >div:nth-of-type(1) {
    display: none !important;
  }
}
</style>
<style lang="stylus">
.table-panel-content {
  .root {
    .el-container {
      .page-header {
        display: none;
      }
    }

    .main-weapper {
      height: 88%;
    }

    .el-table {
      height: calc(100% - 40px) !important;
    }
  }
}
</style>
<style lang="stylus" scoped>
>>>.date {
  .el-form-item__content {
    .el-date-editor {
      width: 150px !important;
    }
  }
}
</style>