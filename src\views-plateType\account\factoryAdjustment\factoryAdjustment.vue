<template>
  <!-- 板木分厂调整 -->
  <content-panel class="panel-tabs">
    <el-row class="tabs-row">
      <el-col :span="16">
        <el-tabs v-model="activeTab" @tab-click="handleClick" class="tabs">
          <el-tab-pane  name="collective" label="集体"> </el-tab-pane>
          <el-tab-pane  name="person" label="个人"> </el-tab-pane>
        </el-tabs>
      </el-col>
 
      <el-col :span="8" class="table-btn-area" v-show="showActionButtons">  
         <el-button size="small" type="primary"  :loading="isLoading" @click="handleSubmit"  v-show="permission && detailInfo.nodeName == '分厂调整-未提交'">提交</el-button>
        <el-button size="small" type="primary" @click="handleAdd"       v-show="permission && detailInfo.nodeName == '分厂调整-未提交'">新增</el-button>
        <el-button size="small" type="primary" @click="handleImport"   v-show="permission && detailInfo.nodeName == '分厂调整-未提交'">导入</el-button>
        <el-button size="small" type="primary" @click="handleExportList">导出列表</el-button>
        <el-button size="small" type="primary" @click="handleExportPiecework">导出计件</el-button>
        <el-button size="small" type="primary" @click="handleExportBefore" >导出调整前</el-button>
        <el-button size="small" type="primary" @click="handleExportPayroll">导出工资表</el-button>
      </el-col>
    </el-row>

    <component :permission="permission" :detailInfo="detailInfo" @handleEdit="handleEdit" @handleEditRecord="handleEditRecord" @handleDelete="handleDelete"
      :is="activeTab" ref="PlateTypeFactoryAdjustmentRef"></component>

    <!-- 新增编辑弹窗 -->
    <add-dialog v-if="isVisible" :isVisible="isVisible" :groupList="groupList" :allotMethod="allotMethod" :title="title"
      :editForm="editForm" @cancel="addCancel"></add-dialog>

    <!-- 调整记录弹窗 -->
    <adjustment-records v-if="visible" :visible="visible" :allotMethod="allotMethod" :editForm="editForm"
      @adjustmentCancel="adjustmentCancel"></adjustment-records>

    <!-- 导出弹窗 -->
    <export-dialog v-if="exportVisible" :visible="exportVisible" @cancel="exportCancel"></export-dialog>

    <!-- 导入弹窗 -->
    <Import v-if="ImportVisible" :visible="ImportVisible" @cancel="cancel" @confirm="confirm"
      :importInfo="importInfo" />
  </content-panel>
</template>

<script>
import { getPermitList } from '@/store/modules/permission';
import Collective from './components/collective.vue';
import Person from './components/person.vue';
import defaultName from "./components/defaultNmae.vue";
import addDialog from "./components/component/addDialog.vue";
import adjustmentRecords from "./components/component/adjustmentRecords";
import exportDialog from "./components/component/exportDialog";

// 常量定义
const ALLOT_METHOD = {
  COLLECTIVE: '集体',
  PERSONAL: '个人'
};

const TAB_NAMES = {
  DEFAULT: 'defaultName',
  COLLECTIVE: 'collective',
  PERSONAL: 'person'
};

const EXPORT_TYPES = {
  LIST: 'plankPieceSystemEditExport',
  PIECEWORK: 'plankPieceSummaryExport'
}; 

export default {
  name: 'FactoryAdjustment',
  components: {
    Collective,
    Person,
    defaultName,
    addDialog,
    adjustmentRecords,
    exportDialog
  },
  data() {
    return {
      activeTab: TAB_NAMES.COLLECTIVE,
      allotMethod: ALLOT_METHOD.COLLECTIVE,
      isVisible: false, // 新增编辑弹窗
      visible: false, // 调整记录弹窗
      exportVisible: false, // 导出弹窗
      ImportVisible: false, // 导入弹窗
      factoryId: "", // 工厂id
      info: {}, // 分厂调整前详情
      title: '新增', // 新增编辑title
      isLoading:false,
       detailInfo:{},
      permission: "",
      importInfo: {}, // 导入信息
      groupList: [],
      editForm: {}, // 编辑行内容
    };
  },
  computed: { 

    // 是否显示操作按钮
    showActionButtons() {
      return this.activeTab && this.activeTab !== TAB_NAMES.DEFAULT;
    },

    // 当前是否为集体方法
    isCollectiveMethod() {
      return this.activeTab === TAB_NAMES.COLLECTIVE;
    }
  },
  watch: { 

    $route: {
      handler(value) {
        if (this.isFactoryAdjustmentRoute(value)) {
          this.initializeRouteData(value);
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    // 检查是否为工厂调整路由
    isFactoryAdjustmentRoute(route) {
      return route.path.includes("factoryAdjustment") && route.path.includes("plateType");
    },

    // 初始化路由数据
    async initializeRouteData(route) {
  
        this.info = JSON.parse(this.$Base64.decode(route.query.data));
        this.factoryId = this.info.factoryId;

        await this.loadGroupList();
        this.setupImportInfo();
        this.getTaskDetail();
        this.getPermission();
    
    },

    // 加载班组列表
    async loadGroupList() {
   
        const res = await this.$api.plateTypeSystemManage.getBasicPermission.getBasicGroupList(this.factoryId);
        this.groupList = res.data.map(item => ({
          label: item.name,
          name: item.name,
          id: item.id,
        }));
      
    },
     //获取待办任务详情
    getTaskDetail() {
      this.$api.plateTypeWorkbench
        .taskDetail({ taskId: JSON.parse(this.$Base64.decode(this.$route.query.data)).id })
        .then(({ data }) => {
          this.detailInfo = data || {};
        });
    },
    //是否具有总览任务操作权限
    getPermission() {
      let params = {
        factoryId: this.info.factoryId,
        accountingMonth: this.info.accountingMonth,
        name: "FACTORY_EDIT",
      };
      this.$api.plateTypeWorkbench.overviewPermission(params).then(({ data }) => {
        this.permission = data;
      });
    },
    //提交
    handleSubmit(){
      this.isLoading = true;
      this.$api.plateTypeWorkbench
        .nextNode({ taskId: this.info.id })
        .then(({ success }) => {
          if (success) {
            this.$notify({
              title: "成功",
              message: "提交成功",
              type: "success",
            });
            this.getPermission();
            this.getTaskDetail()
          }
        }).finally(()=>{
          this.isLoading = false;
        });
    },
    //切换集体个人
    handleClick(tab, event) {
        this.importInfo = {
          reportName: this.activeTab=='collective' ? "plankPieceGroupEditImport":"plankPiecePersonEditImport",
          paramMap: {
            columnValue: "板木-计件调整",
            factoryId: this.factoryId,
            accountingMonth: this.info.accountingMonth,
            id: this.info.id,
          },
        };
      },
    // 设置导入信息
    setupImportInfo() {
       this.importInfo = {
        reportName: "plankPieceGroupEditImport",
        paramMap: {
          columnValue: "板木-计件调整",
          factoryId: this.factoryId,
          accountingMonth: this.info.accountingMonth,
          id: this.info.id,
        },
      };
    },

    // 格式化标签页标题
    formatTabLabel(tab) {
      return tab.number != null ? `${tab.label}(${tab.number})` : tab.label;
    },

    // 设置分配方法
    setAllotMethod() {
      this.allotMethod = this.isCollectiveMethod ? ALLOT_METHOD.COLLECTIVE : ALLOT_METHOD.PERSONAL;
    },

    // 构建编辑表单数据
    buildEditForm(row = {}) {
      return {
        ...this.info,
        ...row
      };
    },

    // 获取导出参数
    getExportParams() {
      return JSON.parse(JSON.stringify(this.$refs.PlateTypeFactoryAdjustmentRef.filterParam));
    },

    // 执行导出操作
    async executeExport(exportType, params) {
     
        await this.$api.common.doExport(exportType, {
          ...params,
          factoryId: this.info.factoryId,
          accountingMonth: this.info.accountingMonth,
        });
        this.$message.success("导出操作成功，请前往导出记录查看详情");
      
    },
    //导出列表
    handleExportList(){
        const exportParams = this.getExportParams();
        this.executeExport(EXPORT_TYPES.LIST, exportParams);
    },
    // 导出计件
    handleExportPiecework() {
      const exportParams = this.getExportParams();
      this.executeExport(EXPORT_TYPES.PIECEWORK, exportParams);
    },

    // 调整记录
    handleEditRecord(row) {
      this.setAllotMethod();
      this.editForm = this.buildEditForm(row);
      this.visible = true;
    },

    // 导出调整前
    handleExportBefore() {
      this.exportVisible = true;
    },

    // 导出工资表
    handleExportPayroll() {
      const exportParams = this.getExportParams();
      this.$api.plateTypeWorkbench
          .exportComon({
            factoryId: this.info.factoryId,
             accountingMonth: this.info.accountingMonth,
             isFilterEmpty: 1,
            ...exportParams
          })
          .then((res) => {
            if (res.code == 200) {
              this.$message.success("导出操作成功，请前往导出记录查看详情");
            }
          });
    },

    // 新增
    handleAdd() {
      this.setAllotMethod();
      this.editForm = this.buildEditForm();
      this.title = "新增";
      this.isVisible = true;
    },

    // 导入
    handleImport() {
      this.title = "导入"; 
      this.ImportVisible = true;
    },

    // 删除
    handleDelete(row) {
      this.$confirm("此操作将永久删除该条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
           
            await this.$api.plateTypeWorkbench.factoryAdjustment.factoryAdjustmentDel({ id: row.id });
            this.$message.success("删除成功!");
            this.refreshList();
         
        }) 
    },

    // 编辑
    handleEdit(row) {
      this.setAllotMethod();
      this.editForm = this.buildEditForm(row);
      this.title = "编辑";
      this.isVisible = true;
    },

    // 刷新列表
    refreshList() { 
      if (this.$refs.PlateTypeFactoryAdjustmentRef && this.$refs.PlateTypeFactoryAdjustmentRef.getList) { 
        this.$refs.PlateTypeFactoryAdjustmentRef.getList();
      }
    },

    // 新增编辑取消回调
    addCancel(type) {
      this.isVisible = false; 
      if (type == "cancel") return;
      this.refreshList();
    },

    // 调整记录取消回调
    adjustmentCancel() {
      this.visible = false;
    },

    // 导出取消回调
    exportCancel() {
      this.exportVisible = false;
    },

    // 导入取消回调
    cancel(value) {
      this.ImportVisible = value;
    },

    // 导入确认回调
    confirm(value) {
      this.ImportVisible = value;
      this.refreshList();
    },
  },
};
</script>

<style lang="stylus" scoped>
.select {
  background: #0bb78e;
  color: #fff !important;
}

>>> .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #fff !important;
}

.panel-tabs {
  position: relative;

  >>> .main-area {
    padding-top: 0;
  }
}

.tabs-row {
  display: flex;
  align-items: center;

  &:after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 2px;
    background-color: #E4E7ED;
    z-index: 1;
  }
}

.table-btn-area {
  display: flex;
  justify-content: flex-end;
  margin-right: 12px;

  .el-button {
    margin-left: 8px;

    &:first-child {
      margin-left: 0;
    }
  }
}

.tabs {
  >>> .el-tabs__header {
    margin-bottom: 5px;

    .el-tabs__nav-wrap::after {
      display: none;
    }
  }
}
</style>