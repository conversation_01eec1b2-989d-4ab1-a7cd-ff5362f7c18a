<template>
  <qDialog :visible="visible"
    :title="title"
    :innerScroll="true"
    width="800px"
    :innerHeight="500"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel">
    <div class="title">基本信息
    </div>
    <el-form ref="addForm"
      :model="addForm"
      label-width="85px"
      :rules="rules"
      size="small">
      <el-form-item
        class="otherPlanName"
        label="任务名称:"
        prop="otherPlanName">
        <el-input type="text"
          v-model.trim="addForm.otherPlanName"
          clearable
          placeholder="请输入文本">
        </el-input>
      </el-form-item>
      <el-form-item
        label="所属角色:"
        prop="roleId">
        <el-select
          v-model="addForm.roleId"
          clearable
          placeholder="请选择选项">
          <el-option
            v-for="item in roleList"
            :key="item.id"
            :label="item.roleName"
            :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="任务类型:"
        prop="type">
       <el-radio-group v-model="addForm.type">
         <el-radio label="1">补贴</el-radio>
        <el-radio label="2">扣款</el-radio>
       </el-radio-group>
      </el-form-item>
      <el-form-item
        label="备注说明:">
        <el-input
          type="textarea"
          maxlength="300"
          resize="none"
          rows="5"
          show-word-limit
          v-model="addForm.comments"
          placeholder="请输入文本">
        </el-input>
      </el-form-item>
    </el-form>
    <div
      style="display: flex; justify-content: space-between">
      <div class="title">
        数据配置
        <span
          class="tips">Tips:数据名称最多添加50项</span>
      </div>
      <div>
        <el-button
          size="small"
          type="primary"
          @click="handleAdd">
          新增
        </el-button>
        <el-button
          size="small"
          type="primary"
          :class="[!isSave?'':'edit_active']"
          @click="handleSave">
          {{isSave?'保存':'编辑'}}
        </el-button>
      </div>
    </div>
    <div class="items">
      <el-table stripe border
        v-loading="loading"
        ref="tableRef"
        row-key="id"
        highlight-current-row
        :data="tableData"
        :height="300">
        <el-table-column
          prop="dataItemName"
          label="数据名称"
          width="150"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="备注说明"
          prop="comments"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
      </el-table>
    </div>
  </qDialog>
</template>

<script>
import Sortable from 'sortablejs';
export default {
  name: "addDialog",
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    title: {
      type: String,
      required: true
    },
    editForm: Object
  },
  data() {
    return {
      addForm: {
        otherPlanName: "",
        roleId: "",
        comments: "",
        type:'1'
      },
      rules: {
        otherPlanName: [
          { required: true, message: "请输入任务名称", trigger: "blur" },
        ],
        type: [
          { required: true, message: "请选择任务类型", trigger: "change" },
        ],
        roleId: [{ required: true, message: "请选择角色", trigger: "change" }],
        isActive: [
          { required: true, message: "请选择所属状态", trigger: "change" },
        ],
      },
      roleList: [], //所属角色
      isSave: false,
      loading: false,
      tableData: [],
      tableKey: "",
      isLoading:false
    }
  },
 async created() {
   await this.getRoloeInfo()
    if (this.title == '编辑') {
      this.tableData = this.editForm.wageItems
      delete this.editForm.wageItems
    }
    this.addForm = {
      ...this.addForm,
      ...this.editForm
    }
    this.$bus.$off('plateTypeList')
    this.$bus.$on('plateTypeList', (data) => {
      this.tableData = [...this.tableData, ...data]
    })
  },
  watch: {
    visible: {
      handler(val) {
        if (val)
          this.$nextTick(() => {
            this.rowDrop()
          })
      },
      immediate: true
    }
  },
  methods: {
    // 行拖拽
    rowDrop() {
      // 要侦听拖拽响应的DOM对象
      const tbody = document.querySelector('.items .el-table__body-wrapper tbody');
      new Sortable(tbody, {
        animation: 1000,
        // 结束拖拽后的回调函数
        onEnd: ({ newIndex, oldIndex }) => {
          const currRow = this.tableData.splice(oldIndex, 1)[0]
          this.tableData.splice(newIndex, 0, currRow)
        },
        onMove: () => {
          return this.isSave
        }
      })
    },
    //获取角色信息
    getRoloeInfo() {
      return  this.$api.roleInfo.getRoleInfoAll({ moduleId: 3 }).then(({ data }) => {
        this.roleList = data || [];
      });
    },
    //新增
    handleAdd() {
      this.$bus.$emit('plateTypeAdd')
    },
    //编辑
    handleSave() {
      this.isSave = !this.isSave
    },
    handleCancel() {
      this.$emit('cancel', 'cancel')
    },
    handleConfirm() {
      if (this.title == "新增") {
        this.$refs.addForm.validate((valid) => {
          if (!valid) return
          if (!this.tableData.length) {
            this.$message({
              message: "请添加工资项配置",
              type: "warning",
            });
            return;
          }
          this.isLoading = true
          let params = {
            ...this.addForm,
            items: this.tableData,
          };
          this.$api.plateTypeSystemConfig.taskConfigAdd(params).then((res) => {
            this.$message({
              type: "success",
              message: "新增成功!",
            });
            this.$emit('cancel', 'confirm')
          }).finally(()=>{
            this.isLoading = false;
          })
        });
      } else {
        this.$refs.addForm.validate((valid) => {
          if (!valid) return
          if (!this.tableData.length) {
            this.$message({
              message: "请添加工资项配置",
              type: "warning",
            });
            return;
          }
          this.isLoading = true
          let params = {
            id: this.editForm.id,
            ...this.addForm,
            items: this.tableData,
          };
          this.$api.plateTypeSystemConfig.taskConfigEdit(params).then((res) => {
            this.$message({
              type: "success",
              message: "编辑成功!",
            });
            this.$emit('cancel', 'confirm')
          }).finally(()=>{
            this.isLoading = false;
          });
        });
      }
    }
  }
}
</script>

<style lang="stylus" scoped>
border_style() {
  padding: 10px;
  border: 1px solid #ccc;
  margin: 10px 0;
}

.title {
  font-weight: bold;

  .tips {
    color: #00b891;
    font-weight: normal;
    display: inline-block;
    margin-left: 10px;
  }
}

.el-form {
  border_style();

  >>> .el-select {
    width: 35%;
  }

  .otherPlanName {
    >>>.el-input {
      width: 35%;
    }
  }
}

>>>.el-textarea__inner {
  padding-right: 50px;
}

.el-textarea {
  >>>.el-input__count {
    right: 20px !important;
  }
}

.items {
  border_style();
}

.edit_active {
  background: #ff6b31;
  border-color: #ff6b31;
}
</style>