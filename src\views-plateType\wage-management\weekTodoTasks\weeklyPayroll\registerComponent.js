//将首字母转成大写
function toUpperCase(str) {
  return str.replace(
    /\b(\w)(\w*)/g,
    ($0, $1, $2) => $1.toUpperCase() + $2.toLowerCase()
  );
}
const modulesFiles = require.context("./component", true, /\.vue$/);
const modules = modulesFiles.keys().reduce((modules, modulePath) => {
  let moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, "$1");
  if (moduleName.includes("/")) {
    moduleName = moduleName.split("/")[1];
  }
  const value = modulesFiles(modulePath);
  modules[moduleName] = value.default;
  return modules;
}, {});

const upperModules = Object.keys(modules)
  .filter(key => modules[key])
  .reduce((acc, key) => ({
    ...acc,
    [key.replace(/(^\w|-\w)/g, (match) =>
      match.replace(/-/, "").toUpperCase())
    ]: modules[key]
  }), {});

export default {
  components: {
    ...upperModules,
  },
};