<template>
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form size="mini" :inline="true" :model="searchForm" ref="searchForm" label-width="95px" class="rangeTime"
          label-position="right">
          <el-form-item label="核算工厂:" prop="factoryId">
            <el-select v-model="searchForm.factoryId" filterable clearable placeholder="请选择核算工厂"
              @change="onChangeFactory">
              <el-option v-for="item in tabList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="核算班组:" prop="groupId">
            <el-select v-model="searchForm.groupId" filterable clearable placeholder="请选择核算班组"
              @change="onChangeProcess">
              <el-option v-for="item in groupList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="大工序编码:" prop="bigProcessCode">
            <el-select  @change="onChangeBigProcess" v-model="searchForm.bigProcessCode" filterable clearable placeholder="请选择大工序编码" >
              <el-option v-for="item in process.bigProcessList" :key="item.id" :label="item.code" :value="item.code">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="小工序名称:" prop="smallProcessName">
            <el-select v-model="searchForm.smallProcessName" filterable clearable placeholder="请选择大工序名称" @change="onSearch">
              <el-option v-for="item in processList" :key="item.id" :label="item.name" :value="item.name">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="range" label="核算月份:" prop="accountingMonth">
            <el-date-picker :clearable="false" v-model="searchForm.startMonth" value-format="yyyy-MM" type="month"
              placeholder="开始月份" clearable>
            </el-date-picker>
            <span class="separator">至</span>
            <el-date-picker :clearable="false" v-model="searchForm.endMonth" value-format="yyyy-MM" type="month"
              placeholder="结束月份" clearable>
            </el-date-picker>
          </el-form-item>
           <el-form-item label="考勤状态:" prop="attendStatus">
            <el-select v-model="searchForm.attendStatus" filterable clearable placeholder="请选择考勤状态"
              @change="onChangeAccounted">
              <el-option v-for="item in accountedList" :key="item.value" :label="item.name" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:right>
           
          <el-button size="small" type="primary" @click="onSearch">
            查询
          </el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel"> 
      <el-table border   stripe v-loading="loading" ref="tableRef" row-key="id" highlight-current-row
        :height="maxTableHeight" @select-all="handleSelectAll" :data="tableData"
        @selection-change="handleSelectionChange" key="tableKey"
        :tree-props="{ children: 'details', hasChildren: 'hasChildren' }">
        <el-table-column type="selection" width="40">
          <template slot-scope="scope">
            <el-checkbox v-if="!isChildRow(scope.row)" :disabled="false" v-model="scope.row.isSelected"
              @change="(val) => handleManualSelect(scope.row, val)"></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column prop="factoryName" label="核算工厂" fixed align="left">
        </el-table-column>
        <el-table-column prop="accountingMonth" label="核算月份" align="left">
        </el-table-column>
        <el-table-column prop="bigProcessCode" label="大工序编码" fixed align="left">
        </el-table-column>
        <el-table-column prop="smallProcessCode" label="小工序编码" fixed align="left">
        </el-table-column>
        <el-table-column prop="smallProcessName" label="小工序名称" fixed align="left">
        </el-table-column>
        <el-table-column prop="amount" label="金额" align="left">
          <template slot-scope="{ row }">
            {{ row.amount | filterData }}
          </template> 
        </el-table-column>
        <el-table-column prop="groupName" label="核算班组" align="left">
        </el-table-column>
        <el-table-column prop="allotAmount" label="分配金额" align="left">
          <template slot-scope="{ row }">
            {{ row.allotAmount | filterData }}
          </template> 
        </el-table-column>
        <el-table-column prop="remarks" label="备注说明" align="left"    show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ row.remarks   }}
          </template> 
        </el-table-column>
         <el-table-column prop="attendStatus" label="考勤状态" align="left">
            <template slot-scope="{ row }"> 
             <span v-show="!(row&&row.children)" :style="{
                color:
                  row.attendStatus =='正常'
                    ? '#0BB78E'
                    : 'red',
              }"> {{ row.attendStatus }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="left">
          <template slot-scope="scope">
            <div v-if="!isChildRow(scope.row)">
              <el-button type="text" size="mini" @click="handleEdit(scope.$index, scope.row)">
                编辑</el-button>
              <el-button type="text" size="mini" @click="handleDel(scope.$index, scope.row)">
                删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div class="table_footer">
        <ul>
          <li>
            <span>分配工资:</span><span>{{ pieceworkWageInfo | filterDataPiece }}</span>
          </li> 
        </ul>
        </div>
        <div style="text-align: right">
          <el-pagination @size-change="onSizeChange" @current-change="onNumChange" :current-page="pageNum"
            :page-size="pageSize" :total="total" :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>
        </div>
      </template>
    </table-panel>
    <divisionEdit v-if="visible" :isVisible="visible" :title="title" :factoryList="tabList" :editForm="editForm"
      @cancel="handleCancel"></divisionEdit>
  </content-panel>
</template>

<script>
import moment from "moment";
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import { moneyFormatZh } from "@/utils";
import divisionEdit from "./divisionEdit.vue";
export default {
  name: "plateTypePieceworkWage",
  mixins: [tableMixin, pagePathMixin],
  components: {
    divisionEdit
  },
  props:{ 
    isAll: {
      type: Boolean,
      required: false,
    },
  },
  data() {
    return {
      searchForm: {
        groupId: '',
        bigProcessCode:"",
        smallProcessName:"",
        factoryId: "",
        accountingMonth: "",
        attendStatus:"",
        startMonth: moment().subtract(1, "month").format("YYYY-MM"),
        endMonth: moment().subtract(1, "month").format("YYYY-MM")
      },
      tabList: [], 
      columnList: [],
      title: '新增',
      filterParam: {},
      groupList: [],
      visible: false,
      editForm: {},
      accountedList:[
         {
          name:'全部',
          value:""
        },
        {
          name:'正常',
          value:'normal'
        },
         {
          name:'异常',
          value:'abnormal'
        }
      ],
     
      idList: [],
      processList:[],
      pieceworkWageInfo: 0,
      factoryCode:"",
      params: {},
      details: {},
      tableData: [],
      loading: false,
      resizeOffset: 55,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      tableKey: "",
    };
  },
  created() {
    this.$api.plateTypeSystemManage.getBasicPermission.getBasicPermissionAll().then((res) => {
      this.tabList = res.data.map((item) => ({
        label: item.name,
        name: item.name,
        id: item.id,
        ...item
      }));
    });
    this.onSearch();
  },
  filters: {
    filterData(value) {
      return !value ? "-" : moneyFormatZh(value);
    },
    filterDataPiece(value) {
      return !value ? "0" : moneyFormatZh(value);
    }
  },
  computed: {
    process() {
      return {
        processList:
          (this.searchForm.factoryId &&
            this.tabList.find((item) => item.id == this.searchForm.factoryId)
              .process) ||
          [],
        bigProcessList:
          (this.searchForm.factoryId &&
            this.tabList.find((item) => item.id == this.searchForm.factoryId)
              .bigProcess) ||
          [],
      };
    },
  }, 
  methods: {
    changeIsall(val){ 
       if (val) {
          this.$refs.tableRef.toggleAllSelection();
        } else {
          this.$refs.tableRef.clearSelection();
        }
    },
      onChangeAccounted(){
      this.onSearch();
    },
      //切换大工序
    onChangeBigProcess(val){
      this.processList=[]
      this.$api.plateTypeSystemManage.getBasicPermission.getlistMesProcessByCode({
        bigProcessCode:val,
        factoryCode:this.factoryCode,
      }).then((res) => {
        this.processList = res.data || [];
      });
      this.onSearch();
    },
    handleCancel(){
      this.visible=false
      this.getList()
    },
    isChildRow(row) {
      // 遍历所有数据，检查该行是否在任何行的children中
      for (const parentRow of this.tableData) {
        if (parentRow.children && parentRow.children.some(child => child.id === row.id)) {
          return true;
        }
      }
      return false;
    },

    // 手动处理选择
    handleManualSelect(row, isSelected) {
      if (isSelected) {
        this.$refs.tableRef.toggleRowSelection(row, true);
      } else {
        this.$refs.tableRef.toggleRowSelection(row, false);
      }
    },

    // 处理全选
    handleSelectAll(selection) {
      // 清空之前的选择
      this.$refs.tableRef.clearSelection(); 
      // 只选择一级行（非子行）
      if (selection.length > 0) { 
        this.$emit('chanIsAll',true)
        this.tableData.forEach(row => {
          if (!this.isChildRow(row)) {
            this.$refs.tableRef.toggleRowSelection(row, true);
            // 更新绑定值
            this.$set(row, 'isSelected', true);
          }
        });
      } else { 
        this.$emit('chanIsAll',false)
        // 取消全选
        this.tableData.forEach(row => {
          if (!this.isChildRow(row)) {
            this.$set(row, 'isSelected', false);
          }
        });
      }
    },

    // 修改handleSelectionChange方法，确保只处理非子行
    handleSelectionChange(val) {
      
      // 过滤掉子行
      const filteredVal = val.filter(item => !this.isChildRow(item));
      this.idList = (filteredVal && filteredVal.map((item) => item)) || [];
 
      // 同步复选框状态
      this.tableData.forEach(row => {
        if (!this.isChildRow(row)) {
          this.$set(row, 'isSelected', filteredVal.some(item => item.id === row.id));
        }
      });
    },


    //统计
    getAccount() {
      this.$api.plateTypePieceWageSystem.getMesPieceWageDivisionStatistics({
        ...this.filterParam,
        ...this.params,
        operationType:"query"
      }).then((res) => {
        this.pieceworkWageInfo = res.data || {};
      });
    },
    onChangeFactory(val) {
      this.onSearch();
       let factory=  this.tabList.find((item) => item.id == val) 
      this.factoryCode=factory.factoryCode 
      this.$api.plateTypeSystemManage.getBasicPermission.getBasicGroupList(this.searchForm.factoryId).then((res) => {
        this.groupList = res.data.map((item) => ({
          label: item.name,
          name: item.name,
          id: item.id,
        }));
      });
    },
    onChangeProcess() {
      this.onSearch();
    },

    //新增
    handleAddd(index, row) {
      this.title = "新增";
      this.visible = true;
      this.editForm = {

      };
    },
    //编辑
    handleEdit(index, row) {
      this.title = "编辑";
      this.visible = true;
      this.editForm = {
        ...row
      };
    },
    // 批量删除
    batchDelete() { 
     
      if (this.idList.length === 0) {
        this.$message({
          message: "请先勾选需要批量删除的内容",
          type: "warning",
        });
        return;
      }
      const ids = this.idList.reduce((acc, item) => 
        acc.concat(item.children.map(child => child.id)), []
      );
      this.$confirm("是否确认批量删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
         this.$api.plateTypePieceWageSystem.delMesPieceWageDivision({
            ids: ids,
            isAll:this.isAll?1:0
          }) .then(({ data }) => {
              this.$notify.success({
                title: "成功",
                message: data,
              });
            this.getList(); 
          });
        }) 
    },
    //删除
    handleDel(index, data) { 
      const ids=data.details.map(item=>item.id) 
      this.$confirm("是否确认删除?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
         
          this.$api.plateTypePieceWageSystem.delMesPieceWageDivision({
            ids: ids,
            isAll:0
          }).then(() => {
            this.$notify.success({
              title: "成功",
              message: "删除成功",
            });
            this.getList(); 
          });
      });
    
    },
    //导出
    export(){ 
       this.$api.common
        .doExport('plankPieceGroupAllotExport', { 
           ...this.filterParam,
            ...this.params, 
           operationType:'export'
          })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success("导出操作成功，请前往导出记录查看详情");
          }
        }); 
    },
        
    //获取列表
    getList() {
      this.getAccount();
      this.loading = true;
      this.$api.plateTypePieceWageSystem
        .getMesPieceWageDivisiontList({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: {
            ...this.filterParam,
            ...this.params,
            operationType:"query"
          },
        })
        .then(({ data: { list, total } }) => { 
          const newArray = list.map((item,index) => ({
              ...item,
              children: item.details,
              id:index+1
          }));
           
          this.tableData = newArray 
           
          this.total = total;
          this.tableKey = Math.random();
          console.log('获取列表', this.tableData)
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields(); 
      this.filterParam = {};
      this.searchForm.startMonth = moment().subtract(1, "month").format("YYYY-MM");
      this.searchForm.endMonth = moment().subtract(1, "month").format("YYYY-MM");
      this.params = {};
      this.onSearch();

    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (key == 'startMonth' && val) {
          this.filterParam.startMonth = moment(val).format('YYYY-MM') || '';
        } else if (key == 'endMonth' && val) {
          this.filterParam.endMonth = moment(val).format('YYYY-MM') || '';
        } else if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    onSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(val) {
      this.pageNum = val;
      this.getList();
    },
  },
};
</script>

<style lang="stylus" scoped>
.select{
  background:#0bb78e
  color:#fff
}
>>>.el-checkbox.is-bordered.el-checkbox--mini{
    height:25px !important
}
>>>.el-checkbox__input.is-checked+.el-checkbox__label{
    color:white !important
}
>>>.el-checkbox__input.is-checked .el-checkbox__inner{
   border-color:white !important
}
ul {
  list-style: none;
  display: flex;
  align-items: center;
  padding: 0;
  margin: 0;
  margin-top:10px
  li{
    margin-right: 10px;
  }
}
</style>
