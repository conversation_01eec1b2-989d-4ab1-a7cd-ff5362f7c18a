<template>
  <!-- 汇总表 -->
  <content-panel>
    <table-panel ref="tablePanel">
      <template v-slot:header-left>
        <div class="header_tableName">{{ filterName.factory }}工资汇总表</div>
        <div class="header_date">日期:{{ filterName.date }}</div>
      </template>
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :data="tableData"
        :height="maxTableHeight"
        :span-method="objectSpanMethod"
        style="width: 100%"
        class="topTable"
        :default-sort="{ prop: 'pieceWage', order: 'descending' }"
      >
        <el-table-column
          v-for="item in tableHeader"
          :key="item.fieldName"
          :label="item.columnName"
          :width="item.width"
          align="center"
          :prop="item.fieldName"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <span v-if="['adjust', 'add', 'reduce'].includes(row.itemName)">{{
              filterTitle(row.itemName)
            }}</span>
            <span
              v-else-if="
                !isNaN(Number(row[item.fieldName])) && item.fieldName != 'serialNumber'
              "
              >{{  filterData(row[item.fieldName]) }}</span>
            <div v-else-if="row[item.fieldName]=='线下核算'">
              <span>{{ row[item.fieldName]}}</span>
              <el-tooltip
              content="线下核算行有汇总金额计算，列的实发金额不参与计算"
              placement="top">
              <i
                class="el-icon-question"></i>
            </el-tooltip>
            </div>
            <div v-else-if="row[item.fieldName]=='净余留工资'">
              <span>{{ row[item.fieldName]}}</span>
              <el-tooltip
              content="净余留=本月划出+线下核算+提取余留-本月划入+其他划入-其他划出"
              placement="top">
              <i
                class="el-icon-question"></i>
            </el-tooltip>
            </div>
            <span v-else>{{ row[item.fieldName] }} </span>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <ul>
          <li>
            <span>核算工厂:</span><span>{{ info.factoryName }}</span>
          </li>
          <li>
            <span>核算月份:</span><span>{{ info.accountingMonth }}</span>
          </li>
          <li>
            <span>人数:</span><span>{{ statiSticInfo.people }}</span>
          </li>
        </ul>
      </template>
    </table-panel>
    <edit-dialog
      v-if="visible"
      :visible="visible"
      @cancel="handleCancel"
    ></edit-dialog>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import { moneyFormat, moneyDelete } from "@/utils";
import moment from "moment";
import { calculateTableWidth } from "@/utils";
import editDialog from "./editDialog.vue";
export default {
  components: { editDialog },
  mixins: [tableMixin],
  data() {
    return {
      collectionIsShow: false,
      collectionIsShows: false,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      list: [],
      tableData: [], //表格数据
      reduceTableData: [],
      adjustList: [],
      loading: "",
      resizeOffset: 50,
      filterParam: {},
      factoryId: "",
      //表格表头信息
      tableHeader: [],
      info: {},
      statiSticInfo: {},
      visible: false,
    };
  },
  created() {},
  computed: {
    filterName() {
      let { accountingMonth, factoryName } = this.info;
      return {
        factory: `${moment(accountingMonth).format("YYYY年MM月")} ${factoryName}`,
        date: `${moment(accountingMonth)
          .startOf("month")
          .format("YYYY年MM月DD日")} - ${moment(accountingMonth)
          .endOf("month")
          .format("YYYY年MM月DD日")}`,
      };
    },
  },
  watch: {
    $route: {
      handler(value) {
        if (value.path.includes("payroll")&&value.path.includes("customized")) {
          this.info = JSON.parse(value.query.data);
          this.factoryId = this.info.factoryId;
          this.getList();
          this.getDebitList();
        }
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {
    this.$bus.$off("customizedEditSummary");
    this.$bus.$on("customizedEditSummary", () => {
      this.visible = true;
    });
  },
  methods: {
    //获取汇总表
    getList() {
      this.loading = true;
      this.$api.workbench
        .summarySheet({
          factoryId: this.factoryId,
          accountingMonth: this.info.accountingMonth,
        })
        .then(
          ({
            data: {
              headItems,
              positiveTerm,
              negativeTerm,
              salaryItems,
              negativeTotalObj,
              positiveTotalObj,
              adjustTerm,
              adjustTotalObj,
            },
          }) => {
            this.list = [
              {
                itemName: "add",
                serialNumber: "",
              },
            ]
              .concat(positiveTerm)
              .concat([
                {
                  itemName: "",
                  serialNumber: "合计",
                  ...positiveTotalObj,
                },
              ]);
            this.adjustList = [
              {
                itemName: "adjust",
                serialNumber: "",
              },
            ]
              .concat(adjustTerm)
              .concat([
                {
                  itemName: "",
                  serialNumber: "净余留工资",
                  ...adjustTotalObj,
                },
              ]);
            this.reduceTableData = [
              {
                itemName: "reduce",
                serialNumber: "",
              },
            ]
              .concat(negativeTerm)
              .concat([
                {
                  itemName: "",
                  serialNumber: "合计",
                  ...negativeTotalObj,
                },
                {
                  itemName: "",
                  serialNumber: "实发工资",
                  ...salaryItems,
                },
              ]);
            let items = {
              serialNumber: "100",
            };
            this.tableHeader = headItems.map((item) => {
              if (Object.keys(items).includes(item.fieldName)) {
                Object.keys(items).forEach((key) => {
                  if (key == item.fieldName) {
                    item.width = items[item.fieldName];
                  }
                });
              } else {
                item.width = this.flexWidth(
                  item.fieldName,
                  this.tableData,
                  item.columnName
                );
              }
              return item;
            });
            let totalWidth = this.tableHeader.reduce((pre, cur) => {
              return (pre += Number(cur.width));
            }, 0);
            if (totalWidth <= this.$refs.tableRef.$el.clientWidth) {
              this.tableHeader.forEach((item) => {
                delete item.width;
              });
            }
            this.tableData = [...this.list, ...this.adjustList, ...this.reduceTableData];
          }
        )
        .finally(() => {
          this.loading = false;
        });
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      //表格合并行
      if (
        rowIndex === 0 ||
        rowIndex == this.list.length ||
        rowIndex == this.list.length + this.adjustList.length
      ) {
        if (columnIndex === 0) {
          return {
            rowspan: 1,
            colspan: this.tableHeader.length,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }
      if (
        [
          this.list.length - 1,
          this.list.length + this.adjustList.length - 1,
          this.tableData.length - 1,
          this.tableData.length - 2,
        ].includes(rowIndex)
      ) {
        //重点在else
        if (columnIndex === 0) {
          return {
            rowspan: 1,
            colspan: 2,
          };
        } else if (columnIndex == 1) {
          return {
            rowspan: 0, //清除就是这俩属性设置为0
            colspan: 0, //清除就是这俩属性设置为0
          };
        }
      }
    },
    //计算表格列宽度
    flexWidth(...args) {
      return calculateTableWidth(...args);
    },
    filterTitle(value) {
      let params = {
        add:
          "增加项目" +
          (this.list.length < 2 ? this.list.length : `(1-${this.list.length - 2})`),
        adjust:
          "调整项目" +
          (this.adjustList.length < 2
            ? this.adjustList.length
            : `(1-${this.adjustList.length - 2})`),
        reduce:
          "减少项目" +
          (this.reduceTableData.length < 2
            ? this.reduceTableData.length
            : `(1-${this.reduceTableData.length - 3})`),
      };
      return params[value];
    },
    filterData(value){
      if(!value) return '-'
      return moneyFormat(value)
    },
    //表格下方统计
    getDebitList() {
      this.$api.statiStics
        .statisticsSalary({
          factoryId: this.factoryId,
          accountingMonth: this.info.accountingMonth,
        })
        .then(({ data }) => {
          this.statiSticInfo = data;
        });
    },
    //合计
    getSum(list) {
      let obj = {};
      list.forEach((item) => {
        Object.keys(item).forEach((key) => {
          const values = list.map((item) => Number(item[key]));
          if (
            !values.every((it) => isNaN(it)) &&
            key != "itemName" &&
            key != "serialNumber"
          ) {
            obj[key] = 0;
          }
        });
      });
      let num = list
        .filter((item) => item.serialNumber != "合计")
        .reduce(
          (prev, curr) => {
            Object.keys(prev).forEach((k) => {
              if (moneyDelete(curr[k])) prev[k] += Number(moneyDelete(curr[k]));
            });
            return prev;
          },
          { ...obj }
        );
      return num;
    },
    handleCancel(type) {
      this.visible = false;
      if (type == "cancel") return;
      this.getList();
    },
  },
};
</script>
<style lang="stylus" scoped>
ellipsis() {
  text-align: center;
  font-size: 17px;
  font-weight: 600;
}

.header_tableName {
  ellipsis();
  font-size: 20px;
  margin-bottom: 8px;
}

.header_date {
  ellipsis();
}



>>>.table-panel-footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  >ul {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      white-space: nowrap
      padding: 0 10px;
    }
  }
}
</style>
