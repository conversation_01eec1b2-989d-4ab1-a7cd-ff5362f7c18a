<template>
  <qDialog :visible="visible" title="同步更新" :innerHeight="100" width="400px" :isLoading="isLoading"
    @cancel="handleCancel" @confirm="handleConfirm" :before-close="handleCancel">
    <p style="line-height:25px">
      点击【同步更新】会清空原本调整后的
      计件工资（系统）分配数据刷新至最新版本。
      是否确认同步更新</p>
  </qDialog>
</template>

<script>
export default {
  name: "commonDialog",
  props: {
    visible: {
      type: Boolean,
      required: true
    },
  },
  data() {
    return {
      isLoading: false
    };
  },
  methods: {
    handleCancel() { this.$emit('cancel', 'cancel'); },
    handleConfirm() {
      this.isLoading = true;
      const { factoryId, accountingMonth } = JSON.parse(this.$Base64.decode(this.$route.query.data));
      let params = {
        factoryId,
        accountingMonth
      };
      this.$api.plateTypeDataUpload.pieceRateWage.syncUpdate(params).then(({ success }) => {
        if (success) {
          this.$message({
            message: "更新中!",
            type: "success",
          });
          this.$emit('cancel', 'confirm', 'success');
        }
      }).catch(error => {
        this.$emit('cancel', 'confirm', 'error');
        // setTimeout(() => {
        //   this.$message.error("更新失败,请稍后重新点击更新按钮!")
        // }, 120000)
      }).finally(() => {
        this.isLoading = false;
      });
    }
  }
};
</script>

<style lang="stylus" scoped></style>