<template>
  <!-- 特殊工资单查看 -->
  <content-panel>
    <template v-slot:search>
      <search-box
        class="search-box">
        <el-form size="mini"
          :inline="true"
          :model="searchForm"
          label-width="93px"
          ref="searchForm"
          label-position="right">
          <el-form-item
            label="结算月份:"
            prop="accountingMonth">
            <el-date-picker
              v-model="searchForm.accountingMonth"
              type="month"
              placeholder="选择月份">
            </el-date-picker>
          </el-form-item>
          <el-form-item
            label="员工姓名:"
            prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch">
              <template
                slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames',$event)"
                  ref="childrenStaffNames"
                  titleName="员工姓名" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item
            label="厂牌编号:"
            prop="staffCode">
            <el-input
              v-model.trim="searchForm.staffCode"
              size="mini"
              clearable
              @keyup.enter.native="onSearch">
              <template
                slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes',$event)"
                  ref="childrenStaffCodes"
                  titleName="厂牌编号" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item
            label="工厂名称:"
            prop="factoryId">
            <el-select
              v-model="searchForm.factoryId"
              filterable
              clearable
              placeholder="请选择工厂名称"
              @change="onSearch">
              <el-option
                v-for="item in tabList"
                :key="item.value"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="单据状态:"
            prop="handleStatus">
            <el-select
              v-model="searchForm.handleStatus"
              filterable
              clearable
              placeholder="请选择单据状态"
              @change="onSearch">
              <el-option
                v-for="item in items"
                :key="item.value"
                :label="item.name"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="工资条类型:"
            prop="type">
            <el-select
              v-model="searchForm.type"
              filterable
              clearable
              placeholder="请选择工资条类型"
              @change="onSearch">
              <el-option
                v-for="item in payrollOptions"
                :key="item.value"
                :label="item.name"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template
          v-slot:right>
          <el-button
            size="small"
            type="primary"

            @click="onSearch">
            查询
          </el-button>
          <el-button
            size="small"
            type="warning"
            @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel
      ref="tablePanel">
      <template
        v-slot:header-right>
        <div ref="btnRight">
          <el-button
            size="small"
            type="primary"
            @click="handleExport">
            导出
          </el-button>
        </div>
      </template>
      <el-table stripe border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :height="maxTableHeight"
        :data="tableData">
        <el-table-column
          label="序号"
          width="60" fixed
          type="index">
        </el-table-column>
        <el-table-column
          prop="accountingMonth"
          label="结算月份"
          width="80" fixed>
        </el-table-column>
        <el-table-column
          prop="staffName"
          label="员工姓名"
          width="100" fixed
          align="left">
        </el-table-column>
        <el-table-column
          prop="staffCode"
          label="厂牌编号"
          width="130"
          align="left">
        </el-table-column>
        <el-table-column
          prop="idCard"
          label="身份证号码"
          width="140"
          align="left">
        </el-table-column>
        <el-table-column
          prop="factoryName"
          label="工厂名称"
          width="130"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="no"
          label="单据编号"
          width="130"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="办理状态"
          prop="handleStatus"
          width="100"
          align="left">
        </el-table-column>
        <el-table-column
          prop="type"
          label="工资条类型"
          width="100"
          align="left">
        </el-table-column>
        <el-table-column
          prop="attendances"
          label="出勤天数"
          width="100"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="工资总额"
          width="100"
          align="left"
          show-overflow-tooltip>
          <template
            slot-scope="{row}">
            {{row.salary|moneyFormat}}
          </template>
        </el-table-column>
        <el-table-column
          label="其他扣款"
          width="100"
          align="left"
          show-overflow-tooltip>
          <template
            slot-scope="{row}">
            {{row.otherDeduct|moneyFormat}}
          </template>
        </el-table-column>
        <el-table-column
          label="厂服扣款"
          width="100"
          align="left"
          show-overflow-tooltip>
          <template
            slot-scope="{row}">
            {{row.uniformDeduct|moneyFormat}}
          </template>
        </el-table-column>
        <el-table-column
          label="生活费"
          width="100"
          align="left"
          show-overflow-tooltip>
          <template
            slot-scope="{row}">
            {{row.living|moneyFormat}}
          </template>
        </el-table-column>
        <el-table-column
          label="保险"
          width="100"
          align="left"
          show-overflow-tooltip>
          <template
            slot-scope="{row}">
            {{row.insurance|moneyFormat}}
          </template>
        </el-table-column>
        <el-table-column
          label="工会"
          width="100"
          align="left"
          show-overflow-tooltip>
          <template
            slot-scope="{row}">
            {{row.labour|moneyFormat}}
          </template>
        </el-table-column>
        <el-table-column
          label="借支扣款"
          width="100"
          align="left"
          show-overflow-tooltip>
          <template
            slot-scope="{row}">
            {{row.loan|moneyFormat}}
          </template>
        </el-table-column>
        <el-table-column
          label="扣款合计"
          width="100"
          align="left"
          show-overflow-tooltip>
          <template
            slot-scope="{row}">
            {{row.totalDeduct|moneyFormat}}
          </template>
        </el-table-column>
        <el-table-column
          label="实发工资"
          align="left"
          width="100"
          show-overflow-tooltip>
          <template
            slot-scope="{row}">
            {{row.actualSalary|moneyFormat}}
          </template>
        </el-table-column>
        <el-table-column
          prop="auditName"
          label="审核人员"
          width="100"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="tableName"
          label="制表人员"
          width="100"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="办理日期"
          width="100"
          align="left"
          show-overflow-tooltip>
          <template
            slot-scope="scope">
            {{ scope.row.handleTime | shortDate }}
          </template>
        </el-table-column>
        <el-table-column
          prop="remark"
          label="备注说明"
          align="left"
          width="200"
          show-overflow-tooltip>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div
          style="text-align: right">
          <el-pagination
            @size-change="onSizeChange"
            @current-change="onNumChange"
            :current-page="pageNum"
            :page-size="pageSize"
            :total="total"
            :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>
        </div>
      </template>
    </table-panel>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import moment from 'moment'
export default {
  name: "SoftwareSpecialPayroll",
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      searchForm: {
        accountingMonth: "",
        staffName: "",
        staffCode: "",
        factoryId: "",
        type: "",
        handleStatus: ""
      },
      tableData: [],
      loading: false,
      // resizeOffset: 53,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      tabList: [],
      params: {},
      filterParam: {},
      //工资条类型
      payrollOptions: Object.freeze([
        {
          name: "辞职工资",
          value: "1",
        },
        {
          name: "自离工资",
          value: "2",
        },
        {
          name: "延发工资",
          value: "3",
        },
        {
          name: "其他工资",
          value: "4",
        },
      ]),
      items: [{
        name: "未提交",
        value: "0",
        type: "notSubmitted",
        number: 0
      },
      {
        name: "审核中",
        value: "1",
        type: "review",
        number: 0
      },
      {
        name: "退回",
        value: "3",
        type: "back",
        number: 0
      },
      {
        name: "已审核",
        value: "2",
        type: "audited"
      },
      {
        name: "已作废",
        value: "4",
        type: "voided"
      }
      ],
    };
  },
  created() {
    this.$api.softwareSystemManage.getBasicPermission
      .getBasicPermissionAll()
      .then((res) => {
          this.tabList = res.data.map((item) => ({
            label: item.name,
            name: item.name,
            id: item.id,
          })) || [];
      });
  },
  watch: {
    $route: {
      handler(value) {
        if (value.path.includes("specialPayroll")&&value.path.includes("software")) {
          this.getList();
        }
      },
      deep: true,
      immediate: true
    },
  },
  methods: {
    //特殊工资单管理列表
    getList() {
      this.loading = true
      this.$api.softwareInformation.payrollManagement
        .specialSalaryDetail({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: {
            ...this.filterParam,
            ...this.params
          },
        })
        .then(({ data: { list, total } }) => {
          this.tableData = list || [];
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter()
      this.$refs.childrenStaffCodes.resetFilter()
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (this.filterParam.accountingMonth) {
        this.filterParam.accountingMonth = moment(this.filterParam.accountingMonth).format('YYYY-MM')
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0)
          this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = []
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0)
          this.params[name] = data;
      }
    },
    //导出
    handleExport() {
      let params = {
        ...this.filterParam,
        ...this.params,
      };
      this.$api.common.doExport('softwareExportspecialsalarydetail', params).then(() => {
        this.$message.success('导出操作成功，请前往导出记录查看详情');
      });
    },
    onSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(val) {
      this.pageNum = val;
      this.getList();
    },
  },
};
</script>

<style lang="stylus" scoped></style>

