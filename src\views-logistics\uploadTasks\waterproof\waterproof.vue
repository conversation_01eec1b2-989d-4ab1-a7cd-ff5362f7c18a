<template>
  <!-- 雨布补贴 -->
  <content-panel>
    <template v-slot:search>
      <search-box
        class="search-box">
        <el-form size="mini"
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          label-position="right">
          <el-form-item
            label="区域:"
            prop="areaId">
            <el-select
              v-model="searchForm.areaId"
              filterable
              clearable
              placeholder="请选择区域"
              @change="onSearch">
              <el-option
                v-for="item in areaList"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="核算班组:"
            prop="groupId">
            <el-select
              v-model="searchForm.groupId"
              filterable
              clearable
              placeholder="请选择核算班组"
              @change="onSearch">
              <el-option
                v-for="item in groupList"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            v-show="activeName == 'person'"
            label="员工姓名:"
            prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch">
              <template
                slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="员工姓名" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item
            v-show="activeName == 'person'"
            label="厂牌编号:"
            prop="staffCode">
            <el-input
              v-model.trim="searchForm.staffCode"
              size="mini"
              clearable
              @keyup.enter.native="onSearch">
              <template
                slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenStaffCodes"
                  titleName="厂牌编号" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item
            label="分摊状态:"
            v-show="activeName == 'groups'"
            prop="apportionedState">
            <el-select
              v-model="searchForm.apportionedState"
              filterable
              clearable
              @change="onSearch">
              <el-option
                v-for="item in apportionedList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template
          v-slot:right>
          <el-button
            size="small"
            type="primary"

            @click="onSearch">
            查询
          </el-button>
          <el-button
            size="small"
            type="warning"
            @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <div class="content">
      <div
        class="content_title">
        <el-tabs
          v-model="activeName"
          ref="tabs">
          <el-tab-pane
            label="班组"
            name="groups">
          </el-tab-pane>
          <el-tab-pane
            label="个人"
            name="person">
          </el-tab-pane>
        </el-tabs>
        <div
          class="content_btn">
          <el-button
            v-show="activeName === 'groups'&&permission"
            size="small"
            type="primary"
            @click="handleImport('groups')">
            班组导入
          </el-button>
          <el-button
           v-show="activeName === 'groups'"
            size="small"
            type="primary"
            @click="batchApportioned"
            >批量分摊</el-button
          >
          <el-button
            v-show="activeName === 'person'&&permission"
            size="small"
            type="primary"
            @click="handleImport('person')">
            个人导入
          </el-button>
          <el-button
            v-show="activeName === 'person'"
            size="small"
            type="primary"
            @click="handleExport">
            导出
          </el-button>
        </div>
      </div>
      <component
        :is="componentName"
        :key="activeName"
        :ref="activeName">
      </component>
    </div>
    <Import
      v-if="ImportVisible"
      :visible="ImportVisible"
      @cancel="cancel"
      @confirm="confirm"
      :importInfo="importInfo" />
  </content-panel>
</template>

<script>
import Groups from './component/groups/groups'
import Person from './component/person/person'
export default {
  name: "logisticsWaterproof",
  components: {
    Groups,
    Person,
  },
  data() {
    return {
      searchForm: {
        staffCode: "",
        staffName: "",
        areaId: "",
        groupId: "",
        apportionedState:""
      },
      apportionedList:[
        {label:'全部',value:''},
        {label:'已完成',value:'Y'},
        {label:'未完成',value:'N'},
      ],
      filterParam: {},
      permission: false,
      activeName: "groups",
      componentName: "groups",
      ImportVisible: false,
      importInfo: {},
      areaList: [],
    };
  },
  watch: {
    activeName: {
      handler(value) {
        this.componentName = value;
        this.getList()
      },
    },
    $route: {
      async handler(value) {
        if (value.path.includes("waterproof") && value.path.includes("logistics")) {
          this.getAreaList()
          await this.getPermission()
          this.getList();
        }
      },
      immediate: true,
      deep: true
    }
  },
  computed: {
    groupList() {
      return this.areaList && this.searchForm.areaId && this.areaList.find(item => item.id === this.searchForm.areaId).process || []
    }
  },
  methods: {
    getList() {
      this.$nextTick(() => {
        this.$refs[this.activeName].init({ ...this.filterParam, ...this.params }, 1, this.permission)
      })
    },
    //区域列表
    async getAreaList() {
      let params = {
        pageSize: 50,
        pageNum: 1,
        filterData: {
        },
      }
      const { data: { list } } = await this.$api.logisticsSystemManage.getBasicPermission.getAllFactory(params)
      this.areaList = list || []
    },
    //是否具有待办任务操作权限
    getPermission() {
      const { factoryId, accountingMonth } = JSON.parse(this.$Base64.decode(this.$route.query.data))
      let params = {
        factoryId,
        accountingMonth,
        type: "220",
      };
      return this.$api.logisticsWorkbench.agencyPermission(params).then(({ data }) => {
        this.permission = data || false;
      });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.filterParam = {};
      this.onSearch();
    },
    //搜索
    onSearch() {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      this.getList()
    },
    //导入
    handleImport(type) {
      this.title = "导入";
      this.ImportVisible = true;
      const { factoryId, accountingMonth, id } = JSON.parse(this.$Base64.decode(this.$route.query.data))
      this.importInfo = type == 'groups' ? {
        reportName: "logisticsWaterproofGroupImport",
        paramMap: {
          columnValue: "物流-班组雨布补贴",
          factoryId,
          accountingMonth,
          id,
        },
      } : {
        reportName: "logisticsWaterproofPersonImport",
        paramMap: {
          columnValue: "物流-个人雨布补贴",
          factoryId,
          accountingMonth,
          id,
        },
      }
    },
      // 批量分摊
      batchApportioned(){
      this.$refs[this.activeName].batchApportioned()
    },
    //导出
    handleExport() {
      const { factoryId, accountingMonth, } = JSON.parse(this.$Base64.decode(this.$route.query.data))
      let params = {
        factoryId,
        accountingMonth,
      };
      if (Object.keys(this.filterParam).length) {
        params = {
          ...params,
          ...this.filterParam,
        };
      }
      this.$api.common
        .doExport("logisticsExportWaterproof", { ...params, ...this.params })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success("导出操作成功，请前往导出记录查看详情");
          }
        });
    },
    cancel(value) {
      this.ImportVisible = value;
    },
    confirm(value) {
      this.ImportVisible = value;
      this.getDebitList();
    },
  },
};
</script>

<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}

.content_title {
  display: flex;
  align-items: center;

  .el-tabs {
    flex: 1;
    margin-right: 10px;
  }
}
</style>
