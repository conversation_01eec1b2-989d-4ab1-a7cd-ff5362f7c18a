<template>
  <qDialog
    :visible="isVisible"
    :innerScroll="false" 
    :innerHeight="200"
    :title="title"
    width="300px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
  <!-- <p v-if="title == '批量核算'">
      是否批量核算当前选中内容，设定在执行月份中全部还清？
    </p> -->
    <p v-if="title === '批量退回'">是否确认批量退回?</p>
    <p v-if="title === '批量删除'">是否确认批量删除?</p>
    <p v-if="status == '0'">是否确认删除?</p>
    <p v-if="status == '1'">退回后，已处理内容会被删除</p>
    <p v-if="title == '批量确认'">是否批量操作所选内容</p>
  </qDialog>
</template>

<script>
export default {
  name: "commonDialog",
  props: {
    title: {
      type: String,
      required: true,
    },
    isVisible: {
      type: Boolean,
      required: true,
    },
    info: Object,
    filterParam: {
      type: Object,
      default: {},
    },
    selectall: {
      type: Boolean,
      required: false,
    },
    status:{
      type: String,
      required: true,
    },
    commonData:{
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      isLoading: false,
    };
  },
  methods: {
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    async handleConfirm() {
      this.isLoading = true;
      let params = {
        ids: this.info.idList,
        isAll:0
      };
      if (this.selectall) {
        //全选
        params = {
          isAll: 1,
          ...this.filterParam,
          type:this.filterParam.type == "0" ? "" : +this.filterParam.type - 1,
        };
      }

      if (this.title == "批量核算") {
        this.$api.logisticsInformation.rewardLedger
          .batchBusinessAccounting(params)
          .then(({ data }) => {
            this.$notify.success({
              title: "成功",
              message: data,
            });
            this.$emit("cancel", "confirm"); 
          })
          .finally(() => {
            this.isLoading = false;
          });
      } else if (this.title == "批量删除") {
        this.$api.logisticsInformation.punishment
          .batchDeleteReward(params)
          .then(({ data }) => {
            this.$notify.success({
              title: "成功",
              message: data,
            });
            this.$emit("cancel", "confirm");
          })
          .finally(() => {
            this.isLoading = false;
          });
      }else if(this.title == "批量确认"){
        this.$api.logisticsInformation.punishment
          .multipleHandle(params)
          .then(({data}) => {
            this.$notify.success({
              title: "成功",
              message: data,
            });
            this.$emit("cancel", "confirm");
          })
          .finally(() => {
            this.isLoading = false;
          });
      }else if(this.title == "退回"){
        let retutnApi = this.status == "1" ? "rollback" : "rollBackNoAccounting";
        this.$api.logisticsInformation.rewardLedger[retutnApi]({ id: this.commonData.id }) 
            .then(() => {
              this.$notify.success({
                title: "成功",
                message: "退回成功",
              });
              this.$emit("cancel", {
                type: "confirm",
                isVisible: false,
              });
            })
            .finally(() => {
              this.isLoading = false;
            });
      } else {
        this.$api.logisticsInformation.rewardLedger
          .rollback(params)
          .then(({ data }) => {
            this.$notify.success({
              title: "成功",
              message: data,
            });
            this.$emit("cancel", "confirm");
          })
          .finally(() => {
            this.isLoading = false;
          });
      }
    },
  },
};
</script>

<style lang="stylus" scoped></style>
