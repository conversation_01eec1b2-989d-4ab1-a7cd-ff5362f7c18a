<template>
  <qDialog
    :visible="isVisible"
    :innerScroll="false"
    :title="title"
    width="300px"
    :innerHeight="150"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <!-- <p v-if="title == '批量核算'">
      是否批量核算当前选中内容，设定在执行月份中全部还清？
    </p> -->
    <p v-if="title == '批量退回'">是否确认批量退回?</p>
    <p v-if="title == '批量删除'">是否确认批量删除?</p> 
    <p v-if="status == '0'&&title !== '批量退回'">是否确认退回</p>
    <p v-if="status=='1'">退回后，已处理内容会被删除。</p>
    <p v-if="title =='批量确认'">是否批量操作所选内容</p>
  </qDialog>
</template>

<script>
export default {
  name: "commonDialog",
  props: {
    title: {
      type: String,
      required: true,
    },
    isVisible: {
      type: Boolean,
      required: true,
    },
    editForm: Object,
    info: Object,
    filterParam: {
      type: Object,
      default: {},
    },
    selectall: {
      type: Boolean,
      required: false,
    },
    status:{
      type: String,
      required: true,
    }
  },
  data() {
    return {
      isLoading: false,
    };
  },
  methods: {
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    async handleConfirm() {
      let params = {
        ids: this.info.idList,
        isAll: 0,
      };
      if (this.selectall) {
        //全选
        params = {
          isAll: 1,
          ...this.filterParam,
          type: this.filterParam.type == "0" ? "" : +this.filterParam.type - 1,
        };
      }
      this.isLoading = true;
      if (this.title == "批量核算") {
        this.$api.plateTypeInformation.rewardLedger
          .batchBusinessAccounting(params)
          .then(({ data }) => {
            this.$notify.success({
              title: "成功",
              message: data,
            });
            this.$emit("cancel", "confirm");
          })
          .finally(() => {
            this.isLoading = false;
          });
      } else if (this.title == "批量删除") {
        this.$api.plateTypeInformation.rewardLedger
          .batchDeleteReward(params)
          .then(({ data }) => {
            this.$notify.success({
              title: "成功",
              message: data,
            });
            this.$emit("cancel", "confirm");
          })
          .finally(() => {
            this.isLoading = false;
          });
      }else if(this.title == "批量确认"){
        this.$api.information.punishment
          .multipleHandle(params)
          .then(({data}) => {
            this.$notify.success({
              title: "成功",
              message: data,
            });
            this.$emit("cancel", "confirm");
          })
          .finally(() => {
            this.isLoading = false;
          });
      }else if(this.title == "退回"){
        // this.$api.plateTypeInformation.rewardLedger
        //   .rollback({ ids: [row.id] || "" })
        //   .then(() => {
        //     this.$notify.success({
        //       title: "成功",
        //       message: "退回成功",
        //     });
        //     this.getList();
        //     this.getStatistics();
        //   });
        let retutnApi = this.status == '1' ? "rollback" : "rollBackNoAccounting"
          this.$api.plateTypeInformation.rewardLedger[retutnApi]({ id: this.editForm.id })
            .then(({ data }) => {
              if (!data) {
                this.$notify.success({
                  title: "成功",
                  message: "退回成功",
                });
                this.$emit("cancel", {
                  type: "confirm",
                  isVisible: false,
                });
              } else {
                this.$emit("cancel", {
                  type: "cancel",
                  isVisible: false,
                });
              }
            })
            .finally(() => {
              this.isLoading = false;
            });
      }else {
        this.$api.plateTypeInformation.rewardLedger
          .rollback(params)
          .then(({ data }) => {
            this.$notify.success({
              title: "成功",
              message: data,
            });
            this.$emit("cancel", "confirm");
          })
          .finally(() => {
            this.isLoading = false;
          });
      }
    },
  },
};
</script>

<style lang="stylus" scoped></style>
