<template>
  <qDialog
    :visible="isVisible"
    :innerScroll="false"
    :title="title"
    width="500px"
    @confirm="handleConfirm"
    @cancel="handleCancel"
    :before-close="handleCancel"
  >
    <el-form
      :model="addForm"
      ref="addForm"
      label-width="111px"
      :rules="rules"
      size="small"
    >
      <el-form-item
        label="厂牌编号:"
        v-show="title == '新增'"
        :prop="title == '新增' && !isConfirm ? 'staffCode' : ''"
        class="staffCode"
      >
        <el-input v-model.trim="addForm.staffCode" clearable>
          <template slot="append">
            <el-button type="primary" @click="searchEmployee('staffCode')">
              查询
            </el-button>
          </template>
        </el-input>
        <span class="error_info" v-show="isShowCode">{{ message }}</span>
      </el-form-item>
      <el-form-item label="厂牌编号:" v-show="title == '编辑'">{{
        addForm.staffCode
      }}</el-form-item>
      <el-form-item
        label="身份证号:"
        v-show="title == '新增'"
        :prop="title == '新增' ? 'idCard' : ''"
        class="staffCode"
      >
        <el-input v-model.trim="addForm.idCard" clearable maxlength="18" show-word-limit>
          <template slot="append">
            <el-button type="primary" @click="searchEmployee('idCard')"> 查询 </el-button>
          </template>
        </el-input>
        <span class="error_info" v-show="isShowIdCard">{{ message }}</span>
      </el-form-item>
      <el-form-item
        label="身份证号:"
        prop="idCard"
        v-show="title == '编辑'"
        class="staffCode"
      >
        <template>
          <span style="margin-right: 10px">{{ addForm.idCard }}</span>
          <i
            v-if="addForm.idCard"
            :class="['iconfont', showType ? 'icon-guanbi' : ' icon-yincangmima']"
            @click="toggle"
          ></i>
        </template>
      </el-form-item>
      <el-form-item label="员工姓名:">{{ addForm.staffName }}</el-form-item>
      <el-form-item label="核算月份:">{{ addForm.accountingMonth }}</el-form-item>
      <el-form-item label="分配厂牌:" prop="assignStaffCode">
        <el-radio-group v-model="addForm.assignStaffCode">
          <el-radio v-for="item in codeList" :label="item" :key="item">{{
            item
          }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="个人扣款:" prop="deductAmount">
        <el-input
          v-model="addForm.deductAmount"
          clearable
          oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
          placeholder="请输入金额"
          @blur="onBlur"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="备注说明:">
        <el-input
          type="textarea"
          resize="none"
          rows="3"
          maxlength="300"
          show-word-limit
          v-model.trim="addForm.comments"
        >
        </el-input>
      </el-form-item>
    </el-form>
  </qDialog>
</template>

<script>
import { moneyFormat, moneyDelete } from "@/utils";
export default {
  name: "addDialog",
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    editForm: Object,
  },
  data() {
    return {
      addForm: {
        staffCode: "",
        staffName: "",
        accountingMonth: JSON.parse(this.$Base64.decode(this.$route.query.data)).accountingMonth,
        deductAmount: "",
        comments: "",
        idCard: "",
        assignStaffCode: "",
        idCardRsa: "",
      },
      rules: {
        staffCode: [
          {
            required: true,
            message: "员工编号不能为空",
            trigger: "blur",
          },
        ],
        idCard: [
          {
            required: true,
            message: "身份证号不能为空",
            trigger: "blur",
          },
        ],
        deductAmount: [
          {
            required: true,
            message: "请输入扣款金额",
            trigger: "blur",
          },
          {
            validator: (rule, value, callback) => {
              if (!/^-?\d{1,5}(\.\d{1,2})?$/.test(moneyDelete(value))) {
                callback(new Error("小数点前面仅支持5位数,小数点后面仅支持2位数"));
              }
              callback();
            },
            trigger: "blur",
          },
        ],
        assignStaffCode: [
          {
            required: true,
            message: "请选择厂牌编号",
            trigger: "change",
          },
        ],
      },
      codeList: [],
      showType: false,
      idCardOrigin: "",
      idCardDecoded: "",
      isShowCode: false,
      isShowIdCard: false,
      isConfirm: false,
      message: "",
      idCardMd5: "",
      idCard: "",
    };
  },
  created() {
    if (this.title == "编辑") {
      this.getDetails();
    }
  },
  watch:{
    'addForm.idCard':{
      handler(value){
          !value && (this.isShowIdCard = false)
      }
    },
    'addForm.staffCode':{
       handler(value){
         !value && (this.isShowCode = false)
      }
    }
  },
  methods: {
    //查询员工信息
    searchEmployee(name) {
      this.$refs.addForm.validateField(name, (valid) => {
        if (valid) {
          name == "staffCode" ? (this.isShowCode = false) : (this.isShowIdCard = false);
          return;
        }
        const { factoryId, accountingMonth } = JSON.parse(this.$Base64.decode(this.$route.query.data));
        let params = {
          factoryId,
          accountingMonth,
          [name]: this.addForm[name],
        };
        this.isConfirm = false;
        if (name == "idCard") {
          this.isConfirm = true;
          this.idCard = this.addForm.idCard;
          params.idCard = this.idCard;
        }
        this.$api.logisticsDataUpload.employeeAttendance.queryStaff({moduleId:"4",...params}).then((res) => {
          let data = res.data || [];
          if (data.length == 0) {
            this.message = "该人员无考勤数据！";
            name == "staffCode" ? (this.isShowCode = true) : (this.isShowIdCard = true);
            this.$nextTick(() => {
              this.$refs.addForm.clearValidate();
            });
          } else {
            this.isShowCode = false;
            this.isShowIdCard = false;
          }
          this.codeList = data.staffCodes || [];
          this.idCardMd5 = data.idCardMd5 || "";
          this.addForm = {
            ...this.addForm,
            staffCode: (name == "idCard" ? "" : data.staffCode) || "",
            staffName: data.staffName || "",
            idCard: (name == "idCard" ? this.idCard : data.idCard) || "",
            assignStaffCode:
              name != "idCard"
                ? this.codeList.length == 1
                  ? this.codeList[0]
                  : data.staffCode
                : "",
          };
        });
      });
    },
    //个税扣款详情
    async getDetails() {
      const { data } = await this.$api.logisticsWorkbench.viewEdit({
        id: this.editForm.id,
      });
      this.idCardOrigin = data.idCard || "";
      this.codeList = data.staffCodes || [];
      this.idCardMd5 = data.idCardMd5 || "";
      this.addForm = {
        ...this.addForm,
        staffCode: data.staffCode || "",
        idCard: data.idCard || "",
        staffName: data.staffName || "",
        deductAmount: moneyFormat(data.deductAmount) || "",
        comments: data.comments || "",
        assignStaffCode:
          (this.codeList.length == 1 ? this.codeList[0] : data.staffCode) || "",
        idCardRsa: data.idCardRsa || "",
      };
    },
    //身份证展示/隐藏
    toggle() {
      if (!this.showType) {
        if (this.idCardDecoded) {
          this.addForm.idCard = this.idCardDecoded;
          this.showType = true;
          return;
        }
        if (this.addForm.staffCode.includes("SG")) {
          this.$api.information.employee
            .idCardDecodeStaff({ idCardRsa: this.addForm.idCardRsa })
            .then((res) => {
              this.addForm.idCard = res.data;
              this.idCardDecoded = res.data;
              this.showType = true;
            });
        } else {
          this.$api.information.employee.decrypt(this.addForm.idCardRsa).then((res) => {
            this.addForm.idCard = res.data;
            this.idCardDecoded = res.data;
            this.showType = true;
          });
        }
      } else {
        this.addForm.idCard = this.idCardOrigin;
        this.showType = false;
      }
    },
    onBlur() {
      if (!this.addForm.deductAmount) return;
      this.addForm.deductAmount = moneyFormat(moneyDelete(this.addForm.deductAmount));
    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      if (!this.addForm.staffCode) {
        this.isConfirm = true;
      }
      this.$nextTick(() => {
        this.$refs.addForm.validate((valid) => {
          if (!valid) return;
          if (this.title == "新增") {
            if (!this.addForm.staffName) {
              this.$message.warning("员工姓名为空,请先查询员工姓名");
              return;
            }
            let params = {
              staffCode: this.addForm.staffCode,
              factoryId: JSON.parse(this.$Base64.decode(this.$route.query.data)).factoryId,
              accountingMonth: JSON.parse(this.$Base64.decode(this.$route.query.data)).accountingMonth,
              deductAmount: moneyDelete(this.addForm.deductAmount),
              comments: this.addForm.comments,
              assignStaffCode: this.addForm.assignStaffCode,
              idCardMd5: this.idCardMd5,
            };
            this.$api.logisticsWorkbench.addIndividualTax(params).then((res) => {
              this.$message({
                type: "success",
                message: "新增成功!",
              });
              this.$emit("cancel", "confirm");
            });
          } else {
            let params = {
              id: this.editForm.id,
              staffCode: this.addForm.staffCode,
              factoryId: JSON.parse(this.$Base64.decode(this.$route.query.data)).factoryId,
              accountingMonth: JSON.parse(this.$Base64.decode(this.$route.query.data)).accountingMonth,
              deductAmount: moneyDelete(this.addForm.deductAmount),
              comments: this.addForm.comments,
              assignStaffCode: this.addForm.assignStaffCode,
              idCardMd5: this.idCardMd5,
            };
            this.$api.logisticsWorkbench.editIndividualTax(params).then((res) => {
              this.$message({
                type: "success",
                message: "修改成功!",
              });
              this.$emit("cancel", "confirm");
            });
          }
        });
      });
    },
  },
};
</script>

<style lang="stylus" scoped>
.staffCode{
  >>>.el-form-item__label:before{
    display: none
}
}
.el-form-item {
  display: flex;

  >>>.el-form-item__content {
    width: 100%;
    margin-left: 0 !important;
  }
}

>>>.el-textarea__inner {
  padding-right: 50px;
}

.el-textarea {
  >>>.el-input__count {
    right: 20px !important;
  }
}

>>>.el-input-group__append {
  background: #24c69a;
  color: #fff;

  .el-button {
    padding: 5px 20px;
  }
}
.error_info{
    color: #F23D20;
    font-size: 12px;
    line-height: 1;
    padding-top: 4px;
    position: absolute;
    top: 100%;
    left: 0;
}
</style>
