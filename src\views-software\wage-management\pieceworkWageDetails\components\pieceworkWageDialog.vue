<template>
  <qDialog innerScroll :visible="visible" :showFooter="false" :innerScroll="true" :innerHeight="height" :title="title"
    width="850px" :before-close="editCancel">
    <div class="wage-details">
      <!-- 计件工资详情 -->
      <div class="info-section">
        <div class="title">计件工资详情</div>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">核算工厂：</span>
            <span class="value">{{ details.factoryName }}</span>
          </div>
          <div class="info-item">
            <span class="label">核算月份：</span>
            <span class="value">{{ details.accountingMonth }}</span>
          </div>
          <div class="info-item">
            <span class="label">核算班组：</span>
            <span class="value">{{ details.processName }}</span>
          </div>
          <div class="info-item">
            <span class="label">员工姓名：</span>
            <span class="value">{{ details.staffName }}</span>
          </div>
          <div class="info-item">
            <span class="label">厂牌编号：</span>
            <span class="value">{{ details.staffCode }}</span>
          </div>
          <div class="info-item">
            <span class="label">个人计件：</span>
            <span class="value">{{ details.personPiece | filterData }}</span>
          </div>
          <div class="info-item">
            <span class="label">集体计件：</span>
            <span class="value">{{ details.groupPiece | filterData }}</span>
          </div>
          <div class="info-item">
            <span class="label">总计件工资：</span>
            <span class="value">{{ details.totalPiece | filterData }}</span>
          </div>
        </div>
      </div>

      <!-- 个人计件明细 -->
      <div class="detail-section">
        <div class="title">个人计件明细</div>
        <el-table :data="details.persons" border>
          <el-table-column prop="index" label="序号" width="80">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="smallProcessCode" label="小工序编码" />
          <el-table-column prop="smallProcessName" label="小工序名称" />
          <el-table-column prop="amount" label="金额">
            <template slot-scope="{ row }">
              {{ row.amount | filterData }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 集体计件明细 -->
      <div class="detail-section">
        <div class="title">集体计件明细</div>
        <el-table :data="filteredGroups" border>
          <el-table-column width="100" prop="processName" label="核算班组" />
          <el-table-column width="100" prop="coefficient" label="员工系数" />
          <el-table-column width="100" prop="totalWorkHour" label="出勤小时" />
          <el-table-column prop="hourlyWages" label="班组总计时工资">
            <template slot-scope="{ row }">
              {{ row.hourlyWages | filterData }}
            </template>
          </el-table-column>
          <el-table-column prop="mesPiece" label="班组总计件工资">
            <template slot-scope="{ row }">
              {{ row.mesPiece | filterData }}
            </template>
          </el-table-column>
          <el-table-column prop="groupPiece" label="集体计件">
            <template slot-scope="{ row }">
              {{ row.groupPiece | filterData }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 计算公式说明 -->
      <div class="formula-section">
        <p>注：各参数计算公式</p>
        <ol>
          <li>总计件工资=个人计件+集体计件</li>
          <li>集体计件=（班组的总计件工资/班组的总计时工资）*（员工系数*本厂出勤小时）</li>
        </ol>
      </div>
    </div>
  </qDialog>
</template>

<script>
import { moneyFormatZh } from "@/utils";
export default {
  name: "detailsPopup",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    height: {
      type: Number,
      default: 560,
    },
    details: {
      type: Object,
      default: () => ({
        factoryName: '',
        factoryId: '',
        accountingMonth: '',
        id: '',
        staffCode: '',
        staffName: '',
        processName: '',
        totalPiece: 0,
        personPiece: 0,
        groupPiece: 0,
        persons: [],
        groups: []
      })
    },
  },
  filters: {
    filterData(value) {
      return !value ? "-" : moneyFormatZh(value);
    },
  },
  computed: {
    filteredGroups() {
      return (this.details.groups || []).filter(item => item.mesPiece !== 0);
    }
  },
  data() {
    return {
      title: '查看明细'
    };
  },
  methods: {
    editCancel() {
      this.$emit("cancel", {
        type: "cancel",
        visible: false,
      });
    }
  },
};
</script>

<style lang="stylus" scoped>
.wage-details {

  .info-section {
    margin-bottom: 20px;

    .title {
      margin-bottom: 15px;
      font-size: 14px
      color:#000;
      border-bottom: 1px solid #ccc;
      padding-bottom:10px;
      font-weight: bold;
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 15px;
    }

    .info-item {
      .label {
        color: #000;
        margin-right: 8px;
      }

      .value {
        color: #000;
        font-weight: 500;
      }
    }
  }

  .detail-section {
    margin-bottom: 20px;

    .title {
      margin-bottom: 15px;
      font-size: 14px;
      font-weight: bold;
      color:#000;
      border-bottom: 1px solid #ccc;
      padding-bottom:10px;
    }
  }

  .formula-section {
    margin-top: 20px;
    color: #000;
    font-size: 14px;

    p {
      margin-bottom: 10px;
    }

    ol {
      padding-left: 20px;

      li {
        margin-bottom: 5px;
      }
    }
  }
}
</style>