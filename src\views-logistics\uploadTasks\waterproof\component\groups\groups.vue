<template>
  <div>
    <table-panel
      ref="tablePanel">
      <el-table stripe border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :height="maxTableHeight"
        :data="tableData"
        @selection-change="handleSelectionChange"
        >
        <!-- :selectable="selectable" -->
        <el-table-column width="40" type="selection"></el-table-column>
        <el-table-column
          prop="area"
          label="区域"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="groupName"
          label="核算班组"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="accountingMonth"
          label="核算月份"
          align="left">
        </el-table-column>
        <el-table-column
          label="补贴金额"
          align="left">
          <template
            slot-scope="{row}">
            {{ row.subsidyAmount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          prop="apportionedState"
          label="分摊状态"
          width="100"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="操作"
          align="left"
          width="170"
          v-if="permission">
          <template
            slot-scope="{row}">
            <el-button
              type="text"
              size="small"
              @click="setUpAllocation(row)">
              设置分摊</el-button>
            <el-button
              type="text"
              size="small"
              @click="handleDelete(row)">
              删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div
          class="table_footer">
          <ul>
            <li>
              <span>核算月份:</span><span>{{ infoList.accountingMonth }}</span>
            </li>
            <li>
              <span>班组数量:</span><span>{{ infoList.groupTotal }}</span>
            </li>
            <li>
              <span>雨布补贴:</span><span>{{ infoList.groupTotalAmount | moneyFormat }}</span>
            </li>
          </ul>
        </div>
        <el-pagination
          :current-page="pageNum"
          :page-size="pageSize"
          :total="total"
          :page-sizes="[50, 100, 200]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onSizeChange"
          @current-change="onNumChange">
        </el-pagination>
      </template>
    </table-panel>
    <share-dialog
      v-if="visible"
      :visible="visible"
      :formData="formData"
      @cancel="handleCancel" />
  </div>
</template>

<script>
import tableMixin from '@/utils/tableMixin'
import pagePathMixin from "@/utils/page-path-mixin";
import shareDialog from '@/views-logistics/uploadTasks/components/shareDialog';
export default {
  name: 'groups',
  components: { shareDialog },
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      pageNum: 1,
      pageSize: 50,
      total: 0,
      loading: false,
      permission: false,
      visible: false,
      resizeOffset: 55,
      formData: {},
      tableData: [],
      infoList: {},
      defaultPageNum: 0,
      filterData: {},
      permission: false,
      idList: [],
    }
  },
  methods: {
    init(data = {}, pageNum = 1, pieceWageFlag) {
      this.permission = pieceWageFlag
      this.defaultPageNum = pageNum
      this.filterData = data
      this.getList()
      this.getStatistic()
    },
    //获取班组列表
    getList() {
      this.loading = true;
      const { factoryId, accountingMonth } = JSON.parse(this.$Base64.decode(this.$route.query.data));
      this.$api.logisticsDataUpload.waterproof
        .listWithGroup({
          pageNum: this.defaultPageNum || this.pageNum,
          pageSize: this.pageSize,
          filterData: {
            factoryId,
            accountingMonth,
            ... this.filterData,
          },
        })
        .then(({ data: { list, total } }) => {
          this.tableData = list || [];
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //获取统计信息
    getStatistic() {
      const { factoryId, accountingMonth } = JSON.parse(this.$Base64.decode(this.$route.query.data));
      this.$api.logisticsDataUpload.waterproof
        .stsWithGroup({
          factoryId,
          accountingMonth,
          ... this.filterData,
        })
        .then(({ data }) => {
          this.infoList = data && {
            ...data,
            accountingMonth,
          } || {};
        })
    },
    //设置分摊
    setUpAllocation(row) {
      this.visible = true;
      this.formData = {
        area:row.area || '',
        groupId: row.groupId || '',
        groupName: row.groupName || '',
        factoryId: row.factoryId || '',
        accountingMonth: row.accountingMonth || '',
        wageItem: 3,
        type:'waterproof'
      }
    },
    //删除
    handleDelete(row) {
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$api.logisticsDataUpload.waterproof
        .deleteWithGroup({
            id: row.id,
          })
          .then(() => {
            this.$notify.success({
              title: "成功",
              message: "删除成功",
            });
            this.getList();
            this.getStatistic();
          });
      });
    },
    handleSelectionChange(val) {
      this.idList = val && val.map((item) => item.id);
    },
    // 批量分摊
    batchApportioned() {
      if (this.idList.length === 0) {
        this.$message({
          message: "请先勾选需要批量确认的内容",
          type: "warning",
        });
        return;
      }
      this.$confirm("将批量分摊, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const { factoryId, accountingMonth } = JSON.parse(this.$Base64.decode(this.$route.query.data));
          let params = {
            ids: this.idList,
            accountingMonth,
            factoryId,

          };
          this.$api.logisticsDataUpload.waterproof
            .batchApportion(params)
            .then((res) => {
              if (res.code == 200) {
                this.$message.success("批量分摊成功");
                this.getList();
              }
            });
        })
        .catch(() => {});
    },
    handleCancel(type) {
      this.visible = false;
      if(type== 'confirm'){
        this.getList()
      }

    },
    onSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
  }
}
</script>

<style lang="stylus" scoped>
ul {
  list-style: none;
  display: flex;
  padding: 0;
}

i {
  font-style: normal;
}

>>>.table-panel-footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .table_footer {
    overflow: auto;

    >ul {
      display: flex;
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        white-space: nowrap;
        padding: 0 10px;
      }
    }
  }
}
</style>