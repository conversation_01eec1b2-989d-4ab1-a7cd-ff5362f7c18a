<template>
  <div class="text-outer-wrapper">
    <div 
      ref="textNode"
      class="text-wrapper"
      :style="{width: textWidth}"
      @mouseenter="handleMouseEnter" 
      @mouseleave="handleMouseLeave">
      <slot>
        {{content}}
      </slot>
    </div>
    <el-tooltip 
      ref="tooltip"
      :effect="tooltipEffect" 
      placement="top" 
      :content="tooltipContent">
    </el-tooltip>
  </div>
</template>

<script>
import {debounce} from '@/utils'
import { getStyle } from 'element-ui/src/utils/dom';
export default {
  name: 'TextTip',
  props: {
    tooltipEffect: {
      type: String,
      default: 'dark'
    },
    content: String,
    width: [Number, String]
  },
  computed: {
    textWidth() {
      const widthType = typeof this.width;
      if (!this.width || !['number', 'string'].includes(widthType)) {
        return '50px';
      }
      
      if (widthType === 'number') {
        return `${this.width}px`;
      } else {
        if (this.width.startsWith('calc')) {
          return this.width;
        } else if (!isNaN(parseInt(this.width, 10))) {
          return `${parseInt(this.width, 10)}px`;
        } else {
          return '50px';
        }
      }
    }
  },
  data() {
    return {
      tooltipContent: '',
      isShowTip: false,
      activateTooltip: () => {}
    }
  },
  methods: {
    handleMouseEnter() {
      const textNode = this.$refs.textNode;
      const range = document.createRange();
      range.setStart(textNode, 0);
      range.setEnd(textNode, textNode.childNodes.length);
      const rangeWidth = range.getBoundingClientRect().width;
      const padding = (parseInt(getStyle(textNode, 'paddingLeft'), 10) || 0) +
        (parseInt(getStyle(textNode, 'paddingRight'), 10) || 0);

      if ((rangeWidth + padding > textNode.offsetWidth || textNode.scrollWidth > textNode.offsetWidth) && this.$refs.tooltip) {
        const tooltip = this.$refs.tooltip;
        this.tooltipContent = textNode.innerText || textNode.textContent;
        tooltip.referenceElm = textNode;
        tooltip.$refs.popper && (tooltip.$refs.popper.style.display = 'none');
        tooltip.doDestroy();
        tooltip.setExpectedState(true);
        this.activateTooltip(tooltip);
      }
    },
    handleMouseLeave() {
      const tooltip = this.$refs.tooltip;
      if (tooltip) {
        tooltip.setExpectedState(false);
        tooltip.handleClosePopper();
      }
    }
  },
  created() {
    this.activateTooltip = debounce((tooltip) => tooltip.handleShowPopper(), 50);
  }
}
</script>

<style lang="stylus" scoped>
.text-outer-wrapper
  display: inline-block
  line-height: 1
  .text-wrapper
    box-sizing: border-box
    display: inline-block
    padding: 0
    min-width: 50px
    white-space: nowrap
    overflow: hidden
    text-overflow: ellipsis
</style>