<template>
  <q-dialog
    :visible="isVisible"
    :title="title"
    width="500px"
    :innerScroll="false"
    @close="onCancel"
    @cancel="onCancel"
    @confirm="onConfirm"
  >
  <div class="illustrate">说明:导入时，员工须有本厂的考勤，否则无法导入</div>
    <el-form
      ref="uploadForm"
      :model="uploadForm"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="数据导入:" prop="file">
        <el-upload
          class="upload"
          :http-request="upload"
          action="*"
          accept=".xls,.xlsx"
          :before-upload="beforeUpload"
          :on-change="onFileChange"
          :auto-upload="false"
          ref="upload"
        >
          <el-button size="small" type="primary">选择文件</el-button>
          <!-- <div slot="tip" class="el-upload__tip">只能上传xls、xlsx文件，且大小不能超过10MB</div> -->
        </el-upload>
        <div style="text-align: left" v-if="showdownload">
          <el-link
            type="primary"
            :underline="false"
            @click.stop="downloadTemplate"
            >下载模板</el-link
          >
        </div>
      </el-form-item>
      <el-form-item label="导入类型:" prop="type">
        <el-radio-group v-model="uploadForm.type" action="*">
          <el-radio label="0">
            <span>覆盖更新</span>
            <!-- <el-tooltip
              class="item"
              effect="dark"
              content="系统先删除原有数据,已最新导入数据为准"
              placement="top-start"
            >
              <i class="el-icon-question"></i>
            </el-tooltip> -->
          </el-radio>
          <el-radio label="1">
            <span>增量导入</span>
            <el-tooltip
              style="margin-left: 10px"
              class="item"
              effect="dark"
              placement="top-start"
            >
              <div slot="content">
                <div>1. 覆盖更新，系统先删除原有数据，以最新导入数据为准。</div>
                <div>
                  2. 增量导入，系统会保留原有数据，做增量处理，若员工姓名和厂牌编号相同时进行覆盖更新。
                </div>
              </div>
              <i class="el-icon-question"></i>
            </el-tooltip>
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
  </q-dialog>
</template>

<script>
import { SUB_APP_CODE } from "@/utils/constant";
import axios from "@/utils/axios";
let timer = null;
export default {
  name: "ImportCommon",
  props: {
    // 重新对外暴露visible，控制显示隐藏
    visible: {
      type: Boolean,
      default: false,
    },
    importInfo: {
      type: Object,
      required: true,
    },
    showdownload: {
      default: false,
      type: Boolean,
    },
  },
  computed: {
    // 通过计算属性，对.sync进行转换，外部也可以直接使用visible.sync
    isVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
    title() {
      return this.importInfo ? this.importInfo.title : "导入";
    },
    reportName() {
      return this.importInfo
        ? this.importInfo.reportName
        : "cdmsCustomerImport";
    },
  },
  data() {
    return {
      uploadForm: {
        file: "",
        type: "0",
      },
      channelOptions: [],
      rules: {
        file: [{ required: true, message: "文件不能为空", trigger: "change" }],
      },
    };
  },
  methods: {
    beforeUpload(file) {
      const excelTypes = [
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      ];
      if (!excelTypes.includes(file.type)) {
        this.$message.error("只能上传Excel文件");
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 10MB!");
        return false;
      }
      return true;
    },
    onFileChange(file) {
      this.uploadForm.file = file;
      this.$refs.uploadForm.validateField("file");
    },
    upload({ file }) {
      console.log("import:", file);
      const loading = this.$loading({
        lock: true,
        text: "文件导入中...",
        spinner: "el-icon-loading",
      });
      const extData = {
        reportName: this.reportName,
        appCode: "was-customized",
        paramMap: [
          {
            columnName: "导入类型",
            columnValue: "",
            fieldName: "importType",
            fieldValue: this.uploadForm.type,
          },
          {
            columnName: "导入工厂",
            columnValue: this.importInfo.paramMap.factoryId,
            fieldName: "factoryId",
            fieldValue: this.importInfo.paramMap.factoryId,
          },
          {
            columnName: "任务类型",
            columnValue: this.importInfo.paramMap.columnValue,
            fieldName: "taskType",
            fieldValue: this.importInfo.paramMap.columnValue,
          },
          {
            columnName: "导入月份",
            columnValue: this.importInfo.paramMap.accountingMonth,
            fieldName: "accountingMonth",
            fieldValue: this.importInfo.paramMap.accountingMonth,
          },
        ],
      };
      this.$api.common
        .importFile(file, extData)
        .then((res) => {
          this.$notify.success({
            title: "成功",
            message: "操作成功",
          });
          this.onCancel();
          this.$emit("after");
        })
        .catch((err) => {
          this.$message.error("导入数据失败，请重新导入！");
        })
        .finally(() => {
          loading.close();
        });
    },
    getLabelByValue(options = [], value) {
      const option = options.find((item) => item.value === value);
      return (option && option.label) || "";
    },
    onCancel() {
      this.isVisible = false;
    },
    onConfirm() {
      this.$refs.uploadForm.validate((valid) => {
        if (!valid) return;

        this.$refs.upload.submit();
      });
    },
    downloadTemplate() {
      return axios
        .get(
          `/qu-platform-excel-api/excel/cfg/getTemplate/${this.reportName}`,
          this.columnDTOs,
          {
            responseType: "blob",
          }
        )
        .then((response) => {
          this.downLoadLink(response.data.templatePath, response.data.fileName);
        });
    },
    downLoadLink(filePath, fileName) {
      let _this = this;
      var xhr = new XMLHttpRequest();
      xhr.open("GET", filePath, true);
      xhr.responseType = "blob";
      xhr.onload = function () {
        if (xhr.status === 200) {
          if (window.navigator.msSaveOrOpenBlob) {
            navigator.msSaveBlob(xhr.response, fileName);
          } else {
            var link = document.createElement("a");
            var body = document.querySelector("body");

            link.href = window.URL.createObjectURL(xhr.response);
            link.download = `xxx厂xx年xx月${_this.importInfo.paramMap.columnValue}表.xlsx`;

            // fix Firefox
            link.style.display = "none";
            body.appendChild(link);

            link.click();
            body.removeChild(link);
            window.URL.revokeObjectURL(link.href);
          }
        }
      };
      xhr.send();
    },
  },
  created() {},
  beforeDestroy() {
    timer && clearInterval(timer);
    timer = null;
  },
};
</script>

<style lang="stylus" scoped>
>>>.el-dialog__header{
  height:20px
}
.illustrate{
  color:#FF8900;
  padding:10px;
  margin-left:9px
}
.upload
  >>> .el-upload__tip
    line-height 30px
    margin-top 0
</style>
