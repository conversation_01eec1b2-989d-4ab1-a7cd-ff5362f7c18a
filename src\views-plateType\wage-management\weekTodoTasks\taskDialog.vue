<template>
  <qDialog :visible="visible" :innerScroll="false" :isShowCancelBtn="isAllTrue" :title="title" :isLoading="isLoading"
    width="500px" @confirm="handleConfirm" @cancel="handleCancel" :before-close="handleCancel">
    <div>
      <taskVerify v-loading="loading" :list="listData" :isAllTrue="isAllTrue" :title="`验证成功！是否${title}？`" />
    </div>
    <!-- <el-form :model="formData" ref="formRef" label-width="120px" class="formData">
      <el-form-item label="">
        <el-radio-group v-model="formData.type">
          <el-radio label="1">通过</el-radio>
          <el-radio label="0">不通过</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="formData.type == 0" label="驳回原因:" prop="reason">
        <el-input type="textarea" resize="none" rows="3" maxlength="20" show-word-limit v-model.trim="formData.reason">
        </el-input>
      </el-form-item>
    </el-form> -->
  </qDialog>
</template>

<script>
export default {
  name: "taskDialog",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    taskInfo: Object,
  },
  data() {
    return {
      formData: {
        type: "1",
        reason: "",
      },
      listData: [],
      loading: false,
      isLoading: false
    };
  },
  created() {
    this.isLoading = false;
    this.nodeVerification();
  },
  computed: {
    isAllTrue() {
      return this.listData.map((item) => item.icon).every((item) => item);
    },
  },
  methods: {
    //结束收集校验
    nodeVerification() {
      this.loading = true;
      this.$api.plateTypePieceWageSystem.weekTodoTasks
        .checkWeekTodoTasks(this.taskInfo.id)
        .then(({ data }) => {
          this.listData = data || [];
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleConfirm() {
      if (!this.isAllTrue) {
        this.$emit("cancel", "cancel");
      } else {
        this.isLoading = true;
        if (!this.isAllTrue) return;
        this.$api.plateTypePieceWageSystem.weekTodoTasks
          .nextWeekTodoTasks(this.taskInfo.id)
          .then((res) => {
            this.$message({
              message: `${this.title}成功`,
              type: "success",
              duration: 1500,
            });
            this.$emit("cancel", "confirm");
          }).finally(() => {
            this.isLoading = false;
          });

      }


    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
  },
};
</script>

<style lang="scss" scoped></style>
