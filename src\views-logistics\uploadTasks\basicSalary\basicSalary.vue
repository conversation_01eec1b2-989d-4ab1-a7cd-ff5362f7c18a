<template>
  <content-panel>
    <!-- 基本工资 -->
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          label-position="left"
          size="mini"
        >
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="员工姓名"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input
              clearable
              v-model.trim="searchForm.staffCode"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenStaffCodes"
                  titleName="厂牌编号"
                />
              </template>
            </el-input>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button
            size="small"
            type="primary"

            @click="onSearch"
          >
            查询
          </el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-right>
        <el-button size="small" type="primary" @click="addDuites">
          新增
        </el-button>
        <el-button
          v-show="permission"
          size="small"
          type="primary"
          @click="handleImport"
        >
          导入
        </el-button>
      </template>
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :data="tableData"
        :height="maxTableHeight"
      >
        <el-table-column
          label="员工姓名"
          prop="staffName"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="厂牌编号"
          prop="staffCode"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column label="区域" prop="areaName" show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="班组" prop="groupName" show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="核算月份"
          prop="accountingMonth"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="总出勤天数"
          prop="totalWorkDay"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column label="绩效等级" prop="perLevel" show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="基本工资" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ row.baseWage | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="left"
          width="150"
          v-if="permission"
        >
          <template slot-scope="{ row }">
            <el-button type="text" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button size="small" type="text" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div class="table_footer">
          <ul>
            <li>
              <span>核算月份:</span><span>{{ infoList.accountingMonth }}</span>
            </li>
            <li>
              <span>人数:</span><span>{{ infoList.people }}</span>
            </li>
            <li>
              <span>基本工资:</span
              ><span>{{ infoList.totalBaseWage | moneyFormat }}</span>
            </li>
          </ul>
        </div>
        <el-pagination
          :current-page="pageNum"
          :page-size="pageSize"
          :total="total"
          :page-sizes="[50, 100, 200]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onSizeChange"
          @current-change="onNumChange"
        >
        </el-pagination>
      </template>
    </table-panel>
    <edit-dialog
      v-if="visible"
      :visible="visible"
      :formData="formData"
      @cancel="handleCancel"
    ></edit-dialog>
    <add-dialog
      v-if="addVisible"
      :visible="addVisible"
      @cancel="handleAddCancel"
    ></add-dialog>
    <Import
      :visible="ImportVisible"
      @cancel="cancel"
      @confirm="confirm"
      :importInfo="importInfo"
    />
  </content-panel>
</template>
<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import editDialog from "./editDialog";
import addDialog from "./addDialog";
export default {
  name: "logisticsBasicSalary",
  mixins: [tableMixin,pagePathMixin],
  components: {
    editDialog,
    addDialog,
  },
  data() {
    return {
      searchForm: {
        staffCode: "",
        staffName: "",
      },
      infoList: {},
      tableData: [],
      loading: false,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      filterParam: {},
      params: {},
      importInfo: {},
      permission: false,
      resizeOffset: 70,
      visible: false,
      formData: {},
      ImportVisible: false,
      addVisible: false,
    };
  },
  watch: {
    $route: {
      handler(value) {
        if (
          value.path.includes("basicSalary") &&
          value.path.includes("logistics")
        ) {
          const { factoryId, accountingMonth, id } = JSON.parse(
            this.$Base64.decode(this.$route.query.data)
          );
          this.importInfo = {
            reportName: "logisticsBaseWageImport",
            paramMap: {
              columnValue: "物流-基本工资",
              factoryId,
              accountingMonth,
              id,
            },
          };
          this.getList();
          this.getDebitList();
          this.getPermission();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    //获取基本工资列表
    getList() {
      this.loading = true;
      const { factoryId, accountingMonth } = JSON.parse(this.$Base64.decode(this.$route.query.data));
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        filterData: {
          factoryId,
          accountingMonth,
          ...this.filterParam,
          ...this.params,
        },
      };
      this.$api.logisticsDataUpload.basicSalary
        .baseWageList(params)
        .then(({ data: { list, total } }) => {
          this.tableData = list || [];
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 获取底部数据信息
    getDebitList() {
      const { factoryId, accountingMonth } = JSON.parse(this.$Base64.decode(this.$route.query.data));
      let params = {
        factoryId,
        accountingMonth,
        ...this.filterParam,
        ...this.params,
      };
      this.$api.logisticsDataUpload.basicSalary
        .baseWageStatistic(params)
        .then(({ data }) => {
          this.infoList =
            (data && {
              ...data,
              accountingMonth,
            }) ||
            {};
        });
    },
    //是否具有待办任务操作权限
    getPermission() {
      const { factoryId, accountingMonth } = JSON.parse(this.$Base64.decode(this.$route.query.data));
      let params = {
        factoryId,
        accountingMonth,
        type: "80",
      };
      this.$api.logisticsWorkbench.agencyPermission(params).then((res) => {
        this.permission = res.data;
      });
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0)
          this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
      this.getDebitList();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    addDuites() {
      this.addVisible = true;
    },
    //编辑
    handleEdit(row) {
      this.visible = true;
      this.formData = {
        id: row.id || "",
        amount: row.baseWage || "",
      };
    },
    handleDelete(row) {
      this.$confirm("此操作将永久删除该条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          console.log(row.id);
          this.$api.logisticsDataUpload.basicSalary.removeWage({ id: row.id }).then((res) => {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getList();
              this.getDebitList();
            });
        })
        .catch(() => {});
    },
    onSizeChange(pageSize) {
      this.pageSize = pageSize;
      // 切换每页条数后从第一页查询
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    handleCancel(type) {
      this.visible = false;
      if (type == "cancel") return;
      this.getList();
      this.getDebitList();
    },
    handleAddCancel(type) {
      this.addVisible = false;
      if (type == "cancel") return;
      this.getList();
      this.getDebitList();
    },
    handleImport() {
      this.title = "导入";
      this.ImportVisible = true;
    },
    cancel(value) {
      this.ImportVisible = value;
    },
    confirm(value) {
      this.ImportVisible = value;
    },
  },
};
</script>
<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}

ul {
  list-style: none;
  display: flex;
  padding: 0;
}

i {
  font-style: normal;
}

>>>.table-panel-footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .table_footer {
    overflow: auto;

    >ul {
      display: flex;
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        white-space: nowrap;
        padding: 0 10px;
      }
    }
  }
}
</style>
