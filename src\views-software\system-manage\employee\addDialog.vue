<template>
  <qDialog :visible="isVisible" :title="title" :innerHeight="450" width="900px" :isLoading="isLoading"
    @cancel="handleCancel" @confirm="handleConfirm" :before-close="handleCancel">
    <div style="display: flex; justify-content: space-between">
      <span>员工基本信息</span>
    </div>
    <el-form size="small" :model="addForm" label-width="98px" label-position="right" :rules="rules" ref="addForm">
      <div class="employee_info" v-if="title == '新增员工' || (title == '编辑员工' && addForm.isSync != 1)">
        <el-row>
          <el-col :span="12">
            <el-form-item label="员工姓名:" prop="staffName">
              <el-input type="text" v-model.trim="addForm.staffName" placeholder="请输入姓名" maxlength="20" clearable
                show-word-limit>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="员工类型:">
              试工员工
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="厂牌编号:">
              {{ addForm.staffCode }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属工厂:" prop="factoryId">
              <el-select v-model="addForm.factoryId" placeholder="请选择工厂名称" filterable class="item">
                <el-option v-for="item in tabList" :key="item.id" :label="item.label" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">

          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="员工状态:" prop="status">
              {{ addForm.status == 2 ? "离职" : "" }}
            </el-form-item>
          </el-col>
          <el-col :span="12">

          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="入职日期:" prop="entryTime">
              <el-date-picker class="item" v-model="addForm.entryTime" type="date" placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="离职日期:" prop="dimissionTime">
              <el-date-picker class="item" v-model="addForm.dimissionTime" type="date" placeholder="选择日期"
                :picker-options="endTimeOptions">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <div class="employee_info" v-if="title == '编辑员工' && addForm.isSync == 1">
      <ul>
        <li>
          <i>员工姓名:</i>
          <span>{{ addForm.staffName }}</span>
        </li>
        <li>
          <i>员工类型:</i>
          <span> 正式员工 </span>
        </li>
        <li>
          <i>身份证号:</i>
          <span>{{ addForm.idCard }}</span>
          <i v-show="addForm.idCard" :class="['iconfont', showType ? 'icon-guanbi' : ' icon-yincangmima']"
            @click="decodes"></i>
        </li>
        <li>
          <i>厂牌编号:</i>
          <span>{{ addForm.staffCode }}</span>
        </li>
        <li>
          <i>所属工厂:</i>
          <span>{{ addForm.factoryName }}</span>
        </li>
        <li>

        </li>
        <li>
          <i>员工状态:</i>
          <span>{{ addForm.status == 1 ? "在职" : "离职" }}</span>
        </li>
        <li>

        </li>
        <li>
          <i>入职日期:</i>
          <span>{{ addForm.entryTime | shortDate }}</span>
        </li>
        <li>
          <i>离职日期:</i>
          <span v-if="addForm.dimissionTime">{{
            addForm.dimissionTime | shortDate
          }}</span>
          <span v-else>无</span>
        </li>
      </ul>
    </div>
    <div style="display: flex; justify-content: space-between; margin-top: 5px">
      <span>银行卡信息</span>
    </div>
    <el-form :model="bank" ref="bankForm" size="small" label-width="98px">
      <div class="employee_info">
        <el-row>
          <el-col :span="12">
            <el-form-item label="农业银行:" style="display: flex" prop="abcNumberDecode">
              <el-input oninput="value=value.replace(/[^\d]/g, '')" v-model.trim="bank.abcNumberDecode"
                placeholder="请输入农业银行卡号" maxlength="19" show-word-limit clearable>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="光大银行:" style="display: flex" prop="cebNumberDecode">
              <el-input oninput="value=value.replace(/[^\d]/g, '')" type="text" v-model.trim="bank.cebNumberDecode"
                placeholder="请输入光大银行卡号" maxlength="16" clearable show-word-limit>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
  </qDialog>
</template>

<script>
import moment from "moment";
export default {
  name: "addDialog",
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    title: String,
    editForm: Object,
  },
  data() {
    return {
      addForm: {
        staffName: "",
        staffCode: "",
        idCard: "",
        idCardOrigin: "",
        factoryId: "",
        factoryName: "",
        status: "2",
        entryTime: "",
        dimissionTime: "",
        isSync: "",
      },
      bank: {
        abcNumberDecode: "", //农业银行卡号
        cebNumberDecode: "", //光大银行卡号
      },
      rules: {
        staffName: [{ required: true, message: "员工姓名不能为空", trigger: "blur" }],
        idCard: [
          {
            validator: (rule, value, callback) => {
              if (value && value.length < 18) {
                return callback(new Error("身份证号必须为18位"));
              }
              callback();
            },
            trigger: "blur",
          },
        ],
        factoryId: [{ required: true, message: "工厂名称不能为空", trigger: "change" }],
        entryTime: [{ required: true, message: "在职时间不能为空", trigger: "change" }],
        dimissionTime: [
          { required: true, message: "离职时间不能为空", trigger: "change" },
        ],
      },
      endTimeOptions: {
        disabledDate: (time) => this.filterTime(time),
      },
      tabList: [],
      showType: false,
      idCardDecoded: "",
      idCardRsa: "",
      isLoading: false,
    };
  },
  async created() {
    await this.$api.softwareSystemManage.getBasicPermission.getBasicPermissionAll().then((res) => {
      this.tabList = res.data.map((item) => ({
        label: item.name,
        name: item.name,
        id: item.id,
        process: item.process,
      })) || [];
    });
    if (this.title == "编辑员工") {
      this.addForm = { ...this.addForm, ...this.editForm };
      this.getDetails();
    }
  },
  methods: {
    filterTime(time) {
      return (
        time.getTime() <
        moment(moment(this.addForm.entryTime).format("YYYY-MM-DD"))
          .startOf("day")
          .format("x")
      );
    },
    //获取员工详情
    getDetails() {
      this.$api.softwareInformation.employee
        .staffDetail({ id: this.addForm.id })
        .then(({ data }) => {
          this.idCardRsa = data.idCardRsa || "";
          for (const key in this.addForm) {
            this.addForm[key] = data[key] || "";
          }
          this.bank = {
            abcNumberDecode:
              data.abcNumberDecode == "-1" ? "" : data.abcNumberDecode || "", //农业银行卡号
            cebNumberDecode:
              data.cebNumberDecode == "-1" ? "" : data.cebNumberDecode || "", //光大银行卡号
          };
          this.addForm = {
            ...this.addForm,
            factoryId: data.factoryName && data.factoryId || '',
            idCardOrigin: data.idCard || "",
          };
        });
    },
    //身份证解码
    decodes() {
      if (this.showType) {
        this.addForm.idCard = this.addForm.idCardOrigin;
        this.showType = false;
      } else {
        if (this.idCardDecoded) {
          this.addForm.idCard = this.idCardDecoded;
          this.showType = true;
          return;
        }
        this.$api.information.employee.decrypt(this.idCardRsa).then((res) => {
          this.addForm.idCard = res.data;
          this.idCardDecoded = res.data;
          this.showType = true;
        });
      }
    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      this.$refs.addForm.validate((valid) => {
        if (!valid) return;
        if (this.title === "新增员工") {
          this.$refs.addForm.validate((valid) => {
            if (!valid) return;
            this.isLoading = true;
            this.$api.information.employee
              .addEmployee(
                JSON.stringify({
                  staffName: this.addForm.staffName,
                  staffCode: "",
                  idCard: this.addForm.idCard,
                  cebNumber: this.bank.cebNumberDecode,
                  abcNumber: this.bank.abcNumberDecode,
                  factoryId: this.addForm.factoryId,
                  status: this.addForm.status,
                  isSync: "2",
                  entryTime: moment(this.addForm.entryTime).format("YYYY-MM-DD"),
                  dimissionTime: moment(this.addForm.dimissionTime).format("YYYY-MM-DD"),
                })
              )
              .then((res) => {
                this.$notify.success({
                  title: "成功",
                  message: "新增员工成功",
                });
                this.$emit("cancel", "confirm");
              }).finally(() => {
                this.isLoading = false;
              });
          });
        } else {
          if (this.addForm.isSync == 2) {
            this.$refs.addForm.validate((valid) => {
              if (!valid) return;
              this.isLoading = true;
              this.$api.information.employee
                .addEmployee(
                  JSON.stringify({
                    id: this.editForm.id,
                    staffName: this.addForm.staffName,
                    staffCode: this.addForm.staffCode,
                    idCard: this.addForm.idCard,
                    cebNumber: this.bank.cebNumberDecode,
                    abcNumber: this.bank.abcNumberDecode,
                    factoryId: this.addForm.factoryId,
                    status: this.addForm.status,
                    isSync: "2",
                    entryTime: moment(this.addForm.entryTime).format("YYYY-MM-DD"),
                    dimissionTime: moment(this.addForm.dimissionTime).format(
                      "YYYY-MM-DD"
                    ),
                  })
                )
                .then((res) => {
                  this.$notify.success({
                    title: "成功",
                    message: "修改员工成功",
                  });
                  this.$emit("cancel", "confirm");
                }).finally(() => {
                  this.isLoading = false;
                });
            });
          } else {
            this.isLoading = true;
            this.$api.information.employee
              .addEmployee(
                JSON.stringify({
                  id: this.editForm.id,
                  staffName: this.addForm.staffName,
                  staffCode: this.addForm.staffCode,
                  idCard: this.addForm.idCard,
                  cebNumber: this.bank.cebNumberDecode,
                  abcNumber: this.bank.abcNumberDecode,
                  factoryId: this.addForm.factoryId,
                  status: this.addForm.status,
                  isSync: "1",
                  entryTime: this.addForm.entryTime
                    ? moment(this.addForm.entryTime).format("YYYY-MM-DD")
                    : "",
                  dimissionTime: this.addForm.dimissionTime
                    ? moment(this.addForm.dimissionTime).format("YYYY-MM-DD")
                    : "",
                })
              )
              .then((res) => {
                if (res.code === 200) {
                  this.$message({
                    message: "修改员工成功",
                    type: "success",
                  });
                  this.visible = false;
                  this.$emit("cancel", "confirm");
                }
              }).finally(() => {
                this.isLoading = false;
              });
          }
        }
      });
    },
  },
};
</script>

<style lang="stylus" scoped>
.el-form {
  .employee_info {
    padding: 10px;
    border: 1px solid #ccc;

    .el-form-item {
      display: flex;
    }

    >>> .el-form-item__content {
      flex: 1;

      &::before, &::after {
        display: none;
      }

      margin-left: 0 !important;
      display: flex;

      // flex-wrap: wrap;
      // justify-content: space-around;
      .item {
        width: 100%;
      }
    }
  }

  .bank_info {
    margin-top: 5px;
    border: 1px solid #ccc;

    >>> .el-form-item__content {
      margin-left: 0 !important;
    }
  }
}

ul {
  list-style: none;
  padding: 0 15px;
  margin-bottom: 0;
}

.employee_info {
  margin-top: 5px;
  border: 1px solid #ccc;

  ul {
    display: flex;
    flex-wrap: wrap;

    li {
      width: 50%;
      margin-bottom: 20px;

      i {
        font-style: normal;
      }
    }
  }
}

.bank_info {
  margin-top: 5px;
  border: 1px solid #ccc;

  ul {
    display: flex;
    margin: 0;

    li {
      width: 50%;
      padding: 12px 0;

      i {
        font-style: normal;
      }
    }
  }
}

.bank {
  display: flex;
  padding-left: 20px;
  margin: 20px 0;

  .el-form-item {
    margin-bottom: 5px;
    width: 52%;

    >>>.el-form-item__label {
      width: 88px;
    }

    >>>.el-form-item__content {
      width: 100% !important;
      display: flex;

      .el-button {
        margin-left: 30px;
      }
    }
  }
}
</style>
