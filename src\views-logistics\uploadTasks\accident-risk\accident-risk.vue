<template>
  <!--意外险 -->
  <content-panel class="security">
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          label-position="left"
          size="mini"
        >
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="员工姓名"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌号:" prop="staffCode">
            <el-input
              v-model.trim="searchForm.staffCode"
              size="mini"
              clearable
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenStaffCodes"
                  titleName="厂牌号"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="分配状态" prop="allocationStatus">
            <el-select
              v-model="searchForm.allocationStatus"
              placeholder="请选择分配状态"
              clearable
              @change="onSearch"
            >
              <el-option
                v-for="item in stateArr"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="考勤状态:" prop="attendStatus">
            <el-select
              v-model="searchForm.attendStatus"
              placeholder="请选择考勤状态"
              clearable
              @change="onSearch"
            >
              <el-option
                v-for="item in stateArr"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary"  @click="onSearch">
            查询
          </el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-left>
        <ul>
          <li>核算月份:{{ info.accountingMonth }}</li>
          <li>核算工厂:{{ debitInfo.factoryName || '' }}</li>
          <li>人数:{{ debitInfo.people }}</li>
          <li>扣款金额:{{ debitInfo.deductAmount | moneyFormat }}</li>
        </ul>
      </template>
      <template v-slot:header-right>
        <template v-if="permission">
          <el-button
            size="small"
            type="primary"

            @click="batchDelete"
          >
            批量删除
          </el-button>
          <el-button size="small" type="primary" @click="handleAdd">
            新增
          </el-button>
          <el-button
            size="small"
            type="primary"
            @click="handleImport">
            导入
          </el-button>
        </template>
      </template>
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :data="tableData"
        select-all
        :height="maxTableHeight"
        style="width: 100%"
        @select-all="handleALL"
        :row-class-name="tableRowClassName"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="40"> </el-table-column>
        <el-table-column label="员工姓名" prop="staffName" width="100" show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="厂牌号" prop="staffCode" width="120"> </el-table-column>
        <el-table-column label="身份证号" prop="idCard" width="160"> </el-table-column>
        <el-table-column label="核算班组" prop="groupName" width="180"> </el-table-column>
        <el-table-column label="扣款金额" prop="socialSecurityDeduct" width="100">
          <template slot-scope="{ row }">
            {{ row.deductAmount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column label="考勤状态" prop="attendStatus" width="80">
          <template slot-scope="{ row }">
            <span :style="{ color: row.attendStatus == 'abnormal' ? 'red' : '#0BB78E' }">
              {{ row.attendStatus | stateStr }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="分配状态" prop="allocationStatus" width="80">
          <template slot-scope="{ row }">
            <span  :style="{ color: row.allocationStatus == 'abnormal' ? 'red' : '#0BB78E' }">
              {{ row.allocationStatus | stateStr }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="修改时间" prop="updateTime" width="150">
          <template slot-scope="{ row }">
            {{ row.updateTime | dateFormat }}
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="comments" show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="操作"
          fixed="right"
          prop="remarks"
          align="center"
          width="140"
          v-if="permission"
        >
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button
              @click="handleDelete(scope.row)"
              slot="reference"
              type="text"
              size="mini"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <ul>
          <!-- <li>核算月份:{{ info.accountingMonth }}</li>
          <li>核算工厂:{{ debitInfo.factoryName || '' }}</li>
          <li>人数:{{ debitInfo.people }}</li>
          <li>扣款金额:{{ debitInfo.deductAmount | moneyFormat }}</li> -->
          <!-- <li>20元保险:{{ debitInfo.totalTwentySecurity | moneyFormat }}</li> -->
        </ul>
        <el-pagination
          :current-page="pageNum"
          :page-size="pageSize"
          :total="total"
          :page-sizes="[50, 100, 200]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </template>
    </table-panel>
    <add-dialog
      v-if="showNewAdd"
      :visible.sync="showNewAdd"
      :title="title"
      :id="id"
      @cancel="handleCancel"
      @addOrEditSuccess="onSearch"
    ></add-dialog>
    <import-file
      v-if="showImportGialog"
      :visible.sync="showImportGialog"
      reportName="logisticsAccidentInsuranceImport"
      :importParames="importParames"
    />
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import addDialog from "./components/addDialog";
import importFile from '@/components/import-file/import-file'
export default {
  name: "logisticsAccidentRisk",
  components: { addDialog,importFile },
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      searchForm: {
        staffName: "",
        staffCode: "",
        allocationStatus: "",
        attendStatus: "",
      },
      idList: [],
      stateArr: [
        { label: '正常',value: 'normal'},
        { label: '异常',value: 'abnormal'},
      ],
      title: "",
      tableData: [],
      loading: false,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      visible: false,
      formData: {},
      filterParam: {},
      params: {},
      factoryId: "",
      debitInfo: { sysTotal: 0, totalSocialSecurity: 0 },
      isAll: 0,
      info: {},
      showNewAdd: false,
      showImportGialog: false,
      id: '',
      importParames: {},
      permission: ''
    };
  },
  created() {},
  watch: {
    $route: {
      handler(value) {
        if (value.path.includes("accidentRisk")&&value.path.includes("logistics")) {
          this.info = JSON.parse(this.$Base64.decode(value.query.data));
          this.factoryId = this.info.factoryId;
          console.log(this.info,"this.infor")
          this.getList();
          this.getDebitList();
          this.getPermission()
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    getList() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        filterData: {
          accountingMonth: this.info.accountingMonth,
          factoryId: this.factoryId,
          ...this.filterParam,
        },
      };
       this.$api.accidentRisk.getPageList(params)
        .then(({ data: { list, total } }) => {
          this.tableData = list || [];
          this.total = total || 0;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    getDebitList() {
      let params = {
        accountingMonth: this.info.accountingMonth,
        factoryId: this.factoryId,
        ...this.filterParam,
      };
      this.$api.accidentRisk.statistics(params).then(
        ({data}) => {
          this.debitInfo = data || {}
        }
      );
    },
    tableRowClassName({ row, rowIndex }) {
      let color = "";
      for (let item of this.idList.values()) {
        if (item === row.id) color = "table-SelectedRow-bgcolor";
      }
      return color;
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.filterParam[name] = data;
      }

      this.pageNum = 1;
      this.getList();
      this.getDebitList();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    handleCurrentChange(val) {
      this.pageNum = val;
      this.getList();
    },
    focusEvent(name, data) {
      console.log('name:',name, 'data:',data)
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },

    //新增
    handleAdd() {
      this.title = "新增";
      this.showNewAdd = true;
    },
    //编辑
    handleEdit(row) {
      this.title = "编辑";
      this.id = row.id
      this.showNewAdd = true;

    },
    //删除
    handleDelete(row) {
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$api.accidentRisk.delete({
            id: row.id,
          }).then(() => {
            this.$notify.success({
              title: "成功",
              message: "删除成功",
            });
            this.getList();
            this.getDebitList();
          });
        })
        .catch(() => {});
    },
    //多选
    handleSelectionChange(val) {
      this.isAll = val.length == this.tableData.length ? 1 : 0;
      this.idList = val.map((item) => item.id);
    },
    handleALL(val) {
      this.isAll = 1;
    },
    // 批量删除
    batchDelete() {
      if (this.idList.length === 0) {
        this.$message({
          message: "请先勾选需要批量删除的内容",
          type: "warning",
        });
        return;
      }
       this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const { factoryId,accountingMonth} = this.info
          let formData = {
            factoryId,
            accountingMonth,
            isAll: this.isAll,
            ids: this.idList,
          };
          this.$api.accidentRisk.batchDelete(formData).then(() => {
            this.$notify.success({
              title: "成功",
              message: "删除成功",
            });
            this.getList();
            this.getDebitList();
          });
        })
    },
     //是否具有待办任务操作权限
    getPermission() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.info.accountingMonth,
        type: "350",
      };
      this.$api.logisticsWorkbench.agencyPermission(params).then((res) => {
        this.permission = res.data;
      });
    },

    handleCancel(type) {
      this.visible = false;
      if (type == "cancel") return;
      this.getList();
      this.getDebitList();
    },
    //导入
    handleImport() {
      const {factoryId,accountingMonth,id} = JSON.parse(this.$Base64.decode(this.$route.query.data))
      this.importParames = {factoryId,accountingMonth,backlogId:id}
      this.showImportGialog = true
    },
  },
  filters: {
    stateStr(value) {
      let map = {normal: '正常',abnormal: '异常'}
      return map[value]
    }
  }
};
</script>

<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}

.el-table {
  /deep/ .table-SelectedRow-bgcolor {
    .el-table__cell {
      background-color: #0bb78e29 !important;
    }
  }
}

.el-form--label-left {
  >>>.el-form-item__label {
    text-align: right !important;
  }
}

#item {
  margin: 0;
  padding: 5px;
}

.table-panel {
  position: relative;

  >>>.btn_right {
    position: absolute;
    right: 0;
    z-index: 2;
  }
}

ul {
  list-style: none;
  display: flex;
  padding: 0;
}

i {
  font-style: normal;
}

>>>.table-panel-footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  ul {
    display: flex;

    li {
      padding: 0 5px;
    }
  }
}
.upload-demo {
  width: 54px;
  display: inline-block;
  margin: 0 10px;
}
ul {
  list-style: none;
  display: flex;
  padding: 0;
}
li{
  padding:0 20px 0 0
}
</style>
