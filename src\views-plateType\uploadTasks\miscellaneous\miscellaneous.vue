<template>
  <!-- 杂工考勤 -->
  <content-panel>
    <template v-slot:search>
      <search-box
        class="search-box">
        <el-form
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          label-position="left"
          size="mini">
          <el-form-item
            label="员工姓名:"
            prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch">
              <template
                slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="员工姓名" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item
            label="厂牌编号:"
            prop="staffCode">
            <el-input
              clearable
              v-model.trim="searchForm.staffCode"
              size="mini"
              @keyup.enter.native="onSearch">
              <template
                slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenStaffCodes"
                  titleName="厂牌编号" />
              </template>
            </el-input>
          </el-form-item>
        </el-form>
        <template
          v-slot:right>
          <el-button
            size="small"
            type="primary"

            @click="onSearch">
            查询
          </el-button>
          <el-button
            size="small"
            type="warning"
            @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel
      ref="tablePanel">
      <template v-slot:header-left>
        <ul>
            <li>
              <span>核算工厂:</span><span>{{ infoList.factoryName }}</span>
            </li>
            <li>
              <span>核算月份:</span><span>{{ infoList.accountingMonth }}</span>
            </li>
            <li>
              <span>人数:</span><span>{{ debitInfo.people }}</span>
            </li>
            <li>
              <span>出勤合计天:</span><span>{{ debitInfo.workDays  }}</span>
            </li>
            <li>
              <span>加班合计时:</span><span>{{ debitInfo.overtimeHour  }}</span>
            </li>
          </ul>
      </template>
      <template
        v-slot:header-right>
        <el-button
          v-show="permission"
          size="small"
          type="primary"
          @click="handleImport">
          导入
        </el-button>
        <el-button
          v-show="permission"
          size="small"
          type="primary"
          @click="handleAdd">
          新增
        </el-button>
      </template>
      <el-table stripe border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :data="tableData"
        :height="maxTableHeight"
        style="width: 100%">
        <el-table-column
          label="序号"
          type="index"
          width="50">
        </el-table-column>
        <el-table-column
          label="员工姓名"
          prop="staffName"
          width="100"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="厂牌编号"
          prop="staffCode"
          width="120"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="身份证号"
          width="200"
          prop="idCard">
          <template
            slot-scope="{ row }">
            <div
              class="idCardBox">
              <div
                class="idCard">
                {{ row.idCard }}
              </div>
            </div>
            <i v-if="row.idCard"
              :class="['iconfont', row.isShow ? 'icon-guanbi' : ' icon-yincangmima']"
              @click="toggleEye(row)"></i>
          </template>
        </el-table-column>
        <el-table-column
          label="出勤合计(天)"
          prop="workDays"
          width="100"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="加班合计(时)"
          prop="overtimeHour"
          width="100"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="派出分厂(单位)"
          width="120"
          prop="dispatchedFactory"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="派入分厂(单位)"
          width="120"
          prop="assignedFactory"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="外出地点"
          prop="area"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="外出事由"
          prop="reasons"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="出勤日期"
          prop="attendanceDate"
          show-overflow-tooltip>
          <template
            slot-scope="{row}">
            {{filterData(row.startDate,row.endDate)}}
          </template>
        </el-table-column>
        <el-table-column
          label="备注说明"
          prop="remark"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="操作"
          align="left"
          width="150"
          fixed="right">
          <template
            slot-scope="scope">
            <el-button
              v-if="permission"
              size="small"
              type="text"
              @click="handlerEdit(scope.$index, scope.row)">
              编辑
            </el-button>
            <el-button
              v-if="permission"
              size="small"
              type="text"
              @click="handleDelete(scope.$index, scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <el-pagination
          :current-page="pageNum"
          :page-size="pageSize"
          :total="total"
          :page-sizes="[50, 100, 200]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onSizeChange"
          @current-change="onNumChange">
        </el-pagination>
      </template>
    </table-panel>
    <add-dialog v-if="visible"
      :visible="visible"
      :title="title"
      :editForm="editForm"
      @cancel="handleCancel"></add-dialog>
    <Import
      v-if="ImportVisible"
      :visible="ImportVisible"
      @cancel="cancel"
      @confirm="confirm"
      :importInfo="importInfo" />
  </content-panel>
</template>
<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import addDialog from './addDialog'
export default {
  name: "PlateTypeMiscellaneous",
  mixins: [tableMixin,pagePathMixin],
  components: {
    addDialog
  },
  data() {
    return {
      searchForm: {
        staffCode: "",
        staffName: "",
      },
      infoList: {},
      tableData: [{}],
      loading: false,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      filterParam: {},
      params: {},
      editForm: {},
      factoryId: "",
      permission: "",
      resizeOffset: 55,
      debitInfo: { sysTotal: 0, totalSocialSecurity: 0 },
      ImportVisible: false,
      visible: false,
    };
  },
  watch: {
    $route: {
      handler(value) {
        if (value.path.includes("miscellaneous") && value.path.includes("plateType")) {
          this.infoList = JSON.parse(this.$Base64.decode(value.query.data));
          this.factoryId = this.infoList.factoryId;
          this.importInfo = {
            reportName: "plankBackManAttendance",
            paramMap: {
              columnValue: "板木-杂工考勤",
              factoryId: this.factoryId,
              accountingMonth: this.infoList.accountingMonth,
              id: this.infoList.id,
            },
          };
          this.getList();
          this.getDebitList();
          this.getPermission();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    //获取杂工考勤列表
    getList() {
      this.loading = true;
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        filterData: {
          factoryId: this.factoryId,
          accountingMonth: this.infoList.accountingMonth,
          ...this.filterParam,
          ...this.params,
        },
      };
      this.$api.plateTypeDataUpload.backManAttendance
        .getBackManAttendanceList(params)
        .then(({ data: { list, total } }) => {
          this.tableData =
            list.map((item) => ({
              ...item,
              isShow: false,
              orginIdCrad: item.idCard,
              idCardDecoded: "",
            })) || [];
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 获取底部数据信息
    getDebitList() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.infoList.accountingMonth,
        ...this.filterParam,
        ...this.params,
      };
      this.$api.plateTypeDataUpload.backManAttendance.backManAttendanceStatistic(params).then((res) => {
        this.debitInfo = res.data
          ? res.data
          : {};
      });
    },
    //是否具有待办任务操作权限
    getPermission() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.infoList.accountingMonth,
        type: "25",
      };
      this.$api.plateTypeWorkbench.agencyPermission(params).then((res) => {
        this.permission = res.data;
      });
    },
    //身份证切换图标
    toggleEye(row) {
      if (row.isShow) {
        row.idCard = row.orginIdCrad;
        row.isShow = false;
      } else {
        if (row.idCardDecoded) {
          row.idCard = row.idCardDecoded;
          row.isShow = true;
          return;
        }
        this.$api.information.employee.decrypt(row.idCardRsa).then((res) => {
          row.idCard = res.data;
          row.idCardDecoded = res.data;
          row.isShow = true;
        });
      }
    },
    filterData(startDate, endDate) {
      if (!startDate && !endDate) return null
      return `${startDate} - ${endDate}`
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
      this.getDebitList();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    //新增
    handleAdd() {
      this.title = "新增";
      this.visible = true;
      this.editForm = {}
    },
    //编辑
    handlerEdit(index, row) {
      this.title = "编辑";
      this.visible = true;
      this.editForm = { id: row.id || "" };
    },
    //删除
    handleDelete(index, row) {
      this.$confirm("此操作将永久删除该条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$api.plateTypeDataUpload.backManAttendance
            .deleteBackManAttendance({ id: row.id })
            .then((res) => {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getList();
              this.getStatistic();
            });
        })
        .catch(() => { });
    },
    handleCancel(type) {
      this.visible = false;
      if (type == "cancel") return;
      this.getList();
      this.getDebitList();
    },
    onSizeChange(pageSize) {
      this.pageSize = pageSize;
      // 切换每页条数后从第一页查询
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    handleImport() {
      this.title = "导入";
      this.ImportVisible = true;
    },
    cancel(value) {
      this.ImportVisible = value;
    },
    confirm(value) {
      this.ImportVisible = value;
    },
    renderHeader(h, { column, index }) {
      let l = column.label.length;
      let f = 14; //每个字大小，其实是每个字的比例值，大概会比字体大小差不多大一点，
      column.minWidth = f * l; //字大小乘个数即长度 ,注意不要加px像素，这里minWidth只是一个比例值，不是真正的长度 //然后将列标题放在一个div块中，注意块的宽度一定要100%，否则表格显示不完全
      return h("div", { class: "table-head", style: { width: "100%" } }, [column.label]);
    },
  },
};
</script>
<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}

ul {
  height:34px
  list-style: none;
  display: flex;
  align-items: center;
  padding: 0;
  margin: 0
  li{
    margin-right: 10px;
  }
}

i {
  font-style: normal;
}


>>>.el-table__row {
  td {
    &:nth-child(4) {
      .cell {
        display: flex;
        justify-content: space-between;

        .idCardBox {
          width: 80%;

          .idCard {
            display: block;
            cursor: pointer;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
        }
      }
    }
  }
}
</style>
