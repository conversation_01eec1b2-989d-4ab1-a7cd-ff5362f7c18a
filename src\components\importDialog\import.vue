<template>
  <qDialog :visible="visible" :innerScroll="false" :title="title" width="400px" @cancel="handleCancel"
    @confirm="handleConfirm" :before-close="handleCancel">
    <el-form ref="uploadForm" :model="uploadForm" label-width="90px" size="small">
      <el-form-item label="数据导入:">
        <el-upload class="upload" :http-request="upload" action="*" accept=".xls,.xlsx" :before-upload="beforeUpload"
          :on-change="onFileChange" :auto-upload="false" ref="upload">
          <el-button size="small" type="primary">选择EXCEL</el-button>
          <div slot="tip" class="el-upload__tip">
            <el-button type="text" style="padding: 0; font-size: 14px" @click="downloadFile">下载模板</el-button>
            <div>只能上传xls、xlsx文件，且大小不能超过10MB</div>
          </div>
        </el-upload>
      </el-form-item>
    </el-form>
  </qDialog>
</template>

<script>
import { SUB_APP_CODE } from "@/api/api";
export default {
  name: "Import",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    importInfo: {
      type: Object,
      required: true,
    },
    title: {
      type: String,
      default: "导入",
    },
    productLineId: {
      type: String,
      default: "",
    },

  },
  data() {
    return {
      uploadForm: {
        file: "",
      },
      tempOptions: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    //获取Excel配置列表
    getList() {
      this.$api.importList
        .getExcelList({
          pageNum: 1,
          pageSize: 1000,
          filterData: {
            appCode: "was-customized",
            reportType: "IMPORT",
          },
        })
        .then(({ data: { list } }) => {
          this.tempOptions = list || [];
        });
    },
    beforeUpload(file) {
      const excelTypes = [
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      ];
      if (!excelTypes.includes(file.type)) {
        this.$message.error("只能上传Excel文件");
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 10MB!");
        return false;
      }
      return true;
    },
    onFileChange(file) {
      this.uploadForm.file = file.raw;
      this.$refs.uploadForm.validateField("file");
    },
    upload({ file }) {
      if (
        [
          "其他补贴",
          "其他扣款",
          "软体-其他补贴",
          "软体-其他扣款",
          "板木-其他补贴",
          "板木-其他扣款",
          "物流-其他补贴",
          "物流-其他扣款",
        ].includes(this.importInfo.paramMap.columnValue)
      ) {
        this.tableInfo2 = JSON.parse(sessionStorage.getItem("tableInfo")).filter(
          (v) => v.columnName != "核算月份" && v.columnName != "修改时间"
        );
        const loading = this.$loading({
          lock: true,
          text: "文件导入中...",
          spinner: "el-icon-loading",
        });
        let taskDTO = {
          excelImportDTO: {
            reportName: this.importInfo.reportName,
            appCode: "was-customized",
            paramMap: {
              customColumnDTO: this.tableInfo2,
              backlogId: this.importInfo.paramMap.id,
              otherDetailId: JSON.parse(this.$Base64.decode(this.$route.query.data)).otherId,
              factoryId: this.importInfo.paramMap.factoryId, //传id
              accountingMonth: this.importInfo.paramMap.accountingMonth,
              cloumnVO: this.tableInfo2,
              productLineId: this.productLineId
            },
          },
          columnDTOs: this.tableInfo2,
        };
        var formData = new FormData();
        formData.append("file", file);
        formData.append("taskDTO", JSON.stringify(taskDTO));
        this.$api.importList
          .getCustomColumnImportant(formData)
          .then((res) => {
            this.$notify.success({
              title: "成功",
              message: "操作成功",
            });
            this.$emit("after");
          })
          .catch((err) => {
            this.$message.error("导入数据失败，请重新导入！");
          })
          .finally(() => {
            loading.close();
          });
      } else {
        const loading = this.$loading({
          lock: true,
          text: "文件导入中...",
          spinner: "el-icon-loading",
        });
        // let fieldValue = this.taskOptions.find(
        //   (item) => item.value === this.uploadForm.taskType
        // ).name;
        // let obj
        // if (this.uploadForm.taskType != "员工管理") {
        //   obj = this.uploadForm.agencyType.value.split(",");
        // }
        // let planObj = this.agencyList.find(
        //   (item) => item.factoryName == obj[0] && item.accountingMonth == obj[1]
        // );
        let items = ["员工管理", "未付导入", "付款申请导入"];
        const columnNmas = ['板木-(周)员工考勤'];
        let paramMap = columnNmas.includes(this.importInfo.paramMap.columnValue) ? [
          {
            columnName: "导入工厂",
            columnValue: this.importInfo.paramMap.factoryId,
            fieldName: "factoryId",
            fieldValue: this.importInfo.paramMap.factoryId,
          },
          {
            columnName: "任务类型",
            columnValue: this.importInfo.paramMap.columnValue,
            fieldName: "taskType",
            fieldValue: this.importInfo.paramMap.columnValue,
          },
          {
            columnName: "导入月份",
            columnValue: this.importInfo.paramMap.accountingMonth,
            fieldName: "accountingMonth",
            fieldValue: this.importInfo.paramMap.accountingMonth,
          },
          {
            columnName: "导入周期",
            columnValue: this.importInfo.paramMap.accountingWeek,
            fieldName: "accountingWeek",
            fieldValue: this.importInfo.paramMap.accountingWeek,
          },
          {
            columnName: "产线ID",
            columnValue: this.productLineId,
            fieldName: "productLineId",
            fieldValue: this.productLineId,
          },

          {
            columnName: [
              "个税扣款",
              "分厂调整",
              "软体-分厂调整",
              "软体-个税扣款",
              "板木-分厂调整",
              "板木-个税扣款",
              "物流-分厂调整",
              "物流-个税扣款",
            ].includes(this.importInfo.paramMap.columnValue)
              ? "任务总览ID"
              : "代办任务ID",
            columnValue: this.importInfo.paramMap.id,
            fieldName: [
              "个税扣款",
              "分厂调整",
              "软体-分厂调整",
              "软体-个税扣款",
              "板木-分厂调整",
              "板木-个税扣款",
              "物流-分厂调整",
              "物流-个税扣款",
            ].includes(this.importInfo.paramMap.columnValue)
              ? "taskId"
              : "backlogId",
            fieldValue: this.importInfo.paramMap.id,
          },
        ] :
          items.includes(this.importInfo.paramMap.columnValue)
            ? [
              {
                columnName: "任务类型",
                columnValue: this.importInfo.paramMap.columnValue,
                fieldName: "taskType",
                fieldValue: this.importInfo.paramMap.columnValue,
              },
            ]
            : [
              {
                columnName: "导入工厂",
                columnValue: this.importInfo.paramMap.factoryId,
                fieldName: "factoryId",
                fieldValue: this.importInfo.paramMap.factoryId,
              },
              {
                columnName: "任务类型",
                columnValue: this.importInfo.paramMap.columnValue,
                fieldName: "taskType",
                fieldValue: this.importInfo.paramMap.columnValue,
              },
              {
                columnName: "导入月份",
                columnValue: this.importInfo.paramMap.accountingMonth,
                fieldName: "accountingMonth",
                fieldValue: this.importInfo.paramMap.accountingMonth,
              },
              {
                columnName: "产线ID",
                columnValue: this.productLineId,
                fieldName: "productLineId",
                fieldValue: this.productLineId,
              },

              {
                columnName: [
                  "个税扣款",
                  "分厂调整",
                  "软体-分厂调整",
                  "软体-个税扣款",
                  "板木-分厂调整",
                  "板木-个税扣款",
                  "物流-分厂调整",
                  "物流-个税扣款",
                ].includes(this.importInfo.paramMap.columnValue)
                  ? "任务总览ID"
                  : "代办任务ID",
                columnValue: this.importInfo.paramMap.id,
                fieldName: [
                  "个税扣款",
                  "分厂调整",
                  "软体-分厂调整",
                  "软体-个税扣款",
                  "板木-分厂调整",
                  "板木-个税扣款",
                  "物流-分厂调整",
                  "物流-个税扣款",
                ].includes(this.importInfo.paramMap.columnValue)
                  ? "taskId"
                  : "backlogId",
                fieldValue: this.importInfo.paramMap.id,
              },
            ];
        const extData = {
          reportName: this.importInfo.reportName,
          appCode: SUB_APP_CODE,
          paramMap,
        };
        this.$api.common
          .importFile(file, extData)
          .then((res) => {
            this.$notify.success({
              title: "成功",
              message: "操作成功",
            });
            this.$emit("after");
          })
          .catch((err) => {
            this.$message.error("导入数据失败，请重新导入！");
          })
          .finally(() => {
            loading.close();
          });
      }
    },
    downloadExcel(fileData, fileName) {
      const name = fileName + ".xlsx";
      const url = window.URL.createObjectURL(
        new Blob([fileData], { type: "application/vnd.ms-excel" })
      );
      const link = document.createElement("a");
      link.style.display = "none";
      link.href = url;
      link.setAttribute("download", name);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      setTimeout(() => {
        URL.revokeObjectURL(url);
      });
    },
    //下载模板
    async downloadFile() {
      if (
        ["其他补贴", "其他扣款", "软体-其他补贴", "软体-其他扣款", "板木-其他补贴", "板木-其他扣款", "物流-其他补贴",
          "物流-其他扣款",].includes(
            this.importInfo.paramMap.columnValue
          )
      ) {
        const api = ["其他补贴", "其他扣款"].includes(this.importInfo.paramMap.columnValue)
          ? "systemConfig"
          : ["软体-其他补贴", "软体-其他扣款"].includes(this.importInfo.paramMap.columnValue) ? "softwareSystemConfig" : ["物流-其他补贴", "物流-其他扣款"].includes(this.importInfo.paramMap.columnValue) ? 'logisticsSystemConfig' : "plateTypeSystemConfig";
        this.$api[api]
          .taskConfigDownload({
            id: JSON.parse(this.$Base64.decode(this.$route.query.data)).otherId,
          })
          .then((res) => {
            this.downloadExcel(res.data, JSON.parse(this.$Base64.decode(this.$route.query.data)).planName);
          });
      } else {
        let url = this.tempOptions.find(
          (item) => item.reportName === this.importInfo.reportName
        ).templatePath;
        try {
          let response = await fetch(url);
          let blob = await response.blob();
          let objectUrl = window.URL.createObjectURL(blob);
          let a = document.createElement("a");
          a.href = objectUrl;
          a.download = `xxx厂xx年xx月${this.importInfo.paramMap.columnValue}表.xlsx`;
          a.click();
          a.remove();
        } catch (error) {
          console.log(error);
        }
      }
    },
    handleCancel() {
      (this.uploadForm.file = ""),
        this.$emit("cancel", false),
        this.$refs.upload.clearFiles();
    },
    handleConfirm() {
      if (this.uploadForm.file == "") {
        this.$message({
          message: "请选择文件",
          type: "warning",
        });
      } else {
        this.$emit("confirm", false), this.$refs.upload.submit();
        this.$refs.upload.clearFiles();
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
