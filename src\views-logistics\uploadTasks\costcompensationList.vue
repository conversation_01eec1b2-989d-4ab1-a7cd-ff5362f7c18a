<template>
  <content-panel>
    <!-- 成本赔偿清单-->
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          size="mini"
          :inline="true"
          :model="searchForm"
          ref="searchForm"
        >
          <el-form-item label="流程编号:" prop="processCode">
            <el-input
              clearable
              v-model.trim="searchForm.processCode"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('processCodes', $event)"
                  @focusEvent="focusEvent('processCodes', $event)"
                  ref="childrenProcessCode"
                  titleName="流程编号"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="责任人:" prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="责任人"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input
              v-model.trim="searchForm.staffCode"
              size="mini"
              clearable
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenStaffCodes"
                  titleName="厂牌编号"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="扣款类型:" prop="deductType">
            <el-select
              v-model="searchForm.deductType"
              filterable
              clearable
              placeholder="请选择扣款类型"
              @change="onSearch"
            >
              <el-option
                v-for="item in payrollOptions"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="扣款状态:" prop="isPay">
            <el-select
              v-model="searchForm.isPay"
              filterable
              clearable
              placeholder="请选择扣款状态"
              @change="onSearch"
            >
              <el-option label="未扣款" value="0"> </el-option>
              <el-option label="已扣款" value="1"> </el-option>
            </el-select>
          </el-form-item>
          <!-- 是否有考勤 -->
          <el-form-item label="是否有考勤:" prop="hasAttend">
            <el-select
              v-model="searchForm.hasAttend"
              filterable
              clearable
              placeholder="请选择考勤状态"
              @change="onSearch"
            >
              <el-option label="全部" value=""> </el-option>
              <el-option label="是" value="1"> </el-option>
              <el-option label="否" value="0"> </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button
            size="small"
            type="primary"

            @click="onSearch"
          >
            查询
          </el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-right>
        <el-button size="small" type="primary" @click="handleExport">
          导出
        </el-button>
      </template>
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :height="maxTableHeight"
        :data="tableData"
      >
        <el-table-column type="index" width="50"> </el-table-column>
        <el-table-column
          prop="processCode"
          label="流程编号"
          width="150"
          align="left"
        >
        </el-table-column>
        <el-table-column
          prop="staffName"
          label="责任人"
          width="150"
          align="left"
        >
        </el-table-column>
        <el-table-column
          prop="staffCode"
          label="厂牌编号"
          width="150"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="deductType"
          label="扣款类型"
          width="150"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column label="扣款金额" width="150" align="left">
          <template slot-scope="{ row }">
            {{ row.deductAmount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          prop="hasAttend"
          label="是否有考勤"
          width="150"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <span :style="{ color: row.hasAttend == '否' ? 'red' : '' }">
              {{ row.hasAttend || "" }}</span
            >
          </template>
        </el-table-column>
        <el-table-column label="扣款状态" width="150" align="left">
          <template slot-scope="{ row }">
            <span :style="{ color: row.isPay ? '' : 'red' }">
              {{ row.deductStatus }}</span
            >
          </template>
        </el-table-column>
        <el-table-column
          prop="remark"
          label="备注说明"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div class="table_footer">
          <ul>
            <li>
              <span>工厂名称:</span><span>{{ info.factoryName }}</span>
            </li>
            <li>
              <span>核算月份:</span><span>{{ info.accountingMonth }}</span>
            </li>
            <li>
              <span>人数:</span><span>{{ costcompensationInfo.people }}</span>
            </li>
            <li>
              <span>成本赔偿:</span
              ><span>{{ costcompensationInfo.costDeduct | moneyFormat }}</span>
            </li>
            <!-- <li>
              <span>返工扣款:</span
              ><span>{{
                costcompensationInfo.reworkDeduct | moneyFormat
              }}</span>
            </li> -->
            <li>
              <span>低耗品:</span
              ><span>{{
                costcompensationInfo.lowConsumeDeduct | moneyFormat
              }}</span>
            </li>
            <!-- <li>
              <span>刀具:</span
              ><span>{{ costcompensationInfo.knifeDeduct | moneyFormat }}</span>
            </li> -->

            <li>
              未打卡扣款
              <!-- <el-tooltip
                content="单厂牌未打卡扣款超过300元，默认按300计算！"
                placement="top"
              >
                <i class="el-icon-question"></i> -->
              <!-- </el-tooltip> -->
              <span
                >:{{ costcompensationInfo.unClockDeduct | moneyFormat }}</span
              >
            </li>
          </ul>
        </div>
        <div style="text-align: right">
          <el-pagination
            @size-change="onSizeChange"
            @current-change="onNumChange"
            :current-page="pageNum"
            :page-size="pageSize"
            :total="total"
            :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper"
          >
          </el-pagination>
        </div>
      </template>
    </table-panel>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
export default {
  name: "LogisticsCostcompensationList",
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      searchForm: {
        processCode: "",
        staffCode: "",
        staffName: "",
        deductType: "",
        isPay: "",
        hasAttend: "",
      },
      costcompensationInfo: {},
      info: {},
      factoryId: "",
      tableData: [],
      loading: false,
      resizeOffset: 70,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      title: "",
      filterParam: {},
      params: {},
      //扣款类型
      payrollOptions: Object.freeze([
        {
          name: "成本赔偿",
          value: "1",
        },
        // {
        //   name: "返工扣款",
        //   value: "2",
        // },
        {
          name: "低耗品",
          value: "3",
        },
        // {
        //   name: "刀具",
        //   value: "4",
        // },
        {
          name: "未打卡扣款",
          value: "5",
        },
      ]),
    };
  },
  watch: {
    $route: {
      handler(value) {
        if (
          value.path.includes("costcompensationList") &&
          value.path.includes("logistics")
        ) {
          this.info = JSON.parse(this.$Base64.decode(value.query.data));
          this.factoryId = this.info.factoryId;
          this.getList();
          this.getStatistic();
          this.getStatisparamsdeductStatustic();

        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    //获取返工扣款列表
    getList() {
      this.loading = true;
      this.$api.logisticsDataUpload.costCompensation
        .getCostCompensation({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: {
            factoryId: this.factoryId,
            accountingMonth: this.info.accountingMonth,
            ...this.filterParam,
            ...this.params,
          },
        })
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.list || [];
            this.total = res.data.total;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //获取统计信息
    getStatistic() {
      this.$api.logisticsDataUpload.costCompensation
        .costCompensationStatistic({
          accountingMonth: this.info.accountingMonth,
          factoryId: this.factoryId,
          ...this.filterParam,
          ...this.params,
        })
        .then((res) => {
          if (res.code === 200) {
            this.costcompensationInfo = res.data || {};
          }
        });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.$refs.childrenProcessCode.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0)
          this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
      this.getStatistic();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    //导入
    handleImport() {
      this.ImportVisible = true;
    },
    //导出
    handleExport() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.info.accountingMonth,
      };
      if (Object.keys(this.filterParam).length) {
        params = {
          ...params,
          ...this.filterParam,
        };
      }
      this.$api.common
        .doExport("logisticsExportbpmdeductlist", { ...params, ...this.params })
        .then((res) => {
          this.$message.success("导出操作成功，请前往导出记录查看详情");
        });
    },
    onSizeChange(pageSize) {
      this.pageSize = pageSize;
      // 切换每页条数后从第一页查询
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    cancel(value) {
      this.ImportVisible = value;
    },
    confirm(value) {
      this.ImportVisible = value;
    },
  },
};
</script>

<style lang="stylus" scoped>
// >>> .el-input__suffix {
// .el-icon-circle-close:before {
// margin-left: -70px;
// }
// }
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}

>>> .el_icon_more {
  margin-top: 1px;
  padding: 1px;
  width: 40px;
  height: 24px;
  text-align: center;
  line-height: 22px;
  border: 1px solid rgba(121, 121, 121, 1);
  border-radius: 0px 2px 2px 0px;
  position: absolute;
  background-color: rgba(242, 242, 242, 1);
  right: -5px;

  &.active {
    color: #ff8900;
    border: 1px solid #ff8900;
  }
}

.code-collection {
  position: absolute;
  top: 45px;
  left: 90px;
  width: 250px;
  height: 436px;
  padding: 10px;
  box-sizing: border-box;
  background: #fff;
  -moz-box-shadow: 0px 0px 7px #BFBFBF;
  -webkit-box-shadow: 0px 0px 7px #BFBFBF;
  box-shadow: 0px 0px 7px #BFBFBF;
  border-radius: 5px;
  z-index: 2001;

  .title {
    color: #666;
  }

  .btn-div {
    padding: 10px 0 10px 10px;
    text-align: right;
    font-size: 12px;

    .qk, .gb, .ss {
      display: inline-block;
      padding: 4px 15px 2px;
      border-radius: 4px;
      border: 1px solid #dedede;
      margin-left: 10px;
      cursor: pointer;
    }

    .gb {
      background: #0bb78e;
      border: 1px solid #0bb78e;
      color: #fff;
    }

    .ss {
      background: #ff8900;
      border: 1px solid #ff8900;
      color: #fff;
    }
  }
}

.code-collection1 {
  position: absolute;
  top: 45px;
  left: 400px;
  width: 250px;
  height: 436px;
  padding: 10px;
  box-sizing: border-box;
  background: #fff;
  -moz-box-shadow: 0px 0px 7px #BFBFBF;
  -webkit-box-shadow: 0px 0px 7px #BFBFBF;
  box-shadow: 0px 0px 7px #BFBFBF;
  border-radius: 5px;
  z-index: 2001;

  .title {
    color: #666;
  }

  .btn-div {
    padding: 10px 0 10px 10px;
    text-align: right;
    font-size: 12px;

    .qk, .gb, .ss {
      display: inline-block;
      padding: 4px 15px 2px;
      border-radius: 4px;
      border: 1px solid #dedede;
      margin-left: 10px;
      cursor: pointer;
    }

    .gb {
      background: #0bb78e;
      border: 1px solid #0bb78e;
      color: #fff;
    }

    .ss {
      background: #ff8900;
      border: 1px solid #ff8900;
      color: #fff;
    }
  }
}

// .table-panel {
// position: relative;

// >>>.btn_right {
// position: absolute;
// top: 8px;
// right: 0;
// z-index: 2;
// }
// }
>>>.el-tabs__nav-wrap::after {
  height: 0;
}

ul {
  list-style: none;
  display: flex;
  padding: 0;
}

i {
  font-style: normal;
}

.stages {
  >.el-form {
    .el-form-item {
      display: flex;
    }
  }
}

>>>.table-panel-footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .table_footer {
    overflow: auto;

    >ul {
      display: flex;
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        white-space: nowrap
        padding: 0 10px;
      }
    }
  }
}

#add {
  .el-form-item {
    margin-bottom: 5px;
    display: flex;

    >>>.el-form-item__content {
      display: flex;
      align-items: center;
      flex: 1;
    }
  }

  .qy-number {
    width: 50%;
    position: relative;

    span {
      position: absolute;
      left: 0;
      top: 0;
      display: block;
      width: 30px;
      height: 30px;
      border-right: 1px solid #000;
      background: #f2f2f2;
      z-index: 2;
      text-align: center;
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
      border-right: 1px solid #8c8c8c;
      font-size: 14px;
    }

    >>>.el-input__inner {
      padding: 0 40px;
    }
  }
}
</style>
