<template>
  <!-- 数据配置 -->
  <content-panel>
    <template v-slot:search>
      <search-box
        class="search-box">
        <el-form
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          label-position="left"
          size="mini">
          <el-form-item
            label="数据名称:"
            prop="dataItemName">
            <el-input
              v-model.trim="searchForm.dataItemName"
              size="mini"
              placeholder="请输入数据名称"
              clearable
              @keyup.enter.native="onSearch">
            </el-input>
          </el-form-item>
        </el-form>
        <template
          v-slot:right>
          <el-button
            size="small"
            type="primary"
            @click="onSearch">
            查询</el-button>
          <el-button
            size="small"
            type="warning"
            @click="resetSearchForm">
            重置</el-button>
        </template>
      </search-box>
    </template>
    <table-panel
      ref="tablePanel">
      <template
        v-slot:header-right>
        <div ref="btnRight"
          style="display: flex; justify-content: space-between">
          <el-button
            @click="handleAdd"
            size="small"
            type="primary">新增
          </el-button>
        </div>
      </template>
      <el-table stripe border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :data="tableData"
        :height="maxTableHeight"
        style="width: 100%">
        <el-table-column
          prop="dataItemName"
          label="数据名称"
          width="230"
          align="left">
        </el-table-column>
        <el-table-column
          prop="dataType"
          label="数据类型"
          width="300"
          align="left">
          <template
            slot-scope="scope">
            {{ scope.row.dataType == 0 ? "固定配置" : "灵活配置" }}
          </template>
        </el-table-column>
        <el-table-column
          prop="isActive"
          label="启用状态"
          align="left">
          <template
            slot-scope="scope">
            <div>
              <el-switch
                @change="onSwitch(scope.row)"
                inactive-color="#ff4949"
                v-model="scope.row.isActive"
                :active-text="scope.row.isActive == 1 ? '启用' : '禁用'"
                :active-value="1"
                :inactive-value="0">
              </el-switch>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="comments"
          label="备注说明"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="otherPlanName"
          label="所属任务"
          align="left">
        </el-table-column>
        <el-table-column
          align="left">
          <template
            slot-scope="scope">
            <el-button
              size="small"
              type="text"
              @click="handleEdit(scope.row)">
              编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageNum"
        :page-size="pageSize"
        :total="total"
        :page-sizes="[50, 100, 200]"
        layout="total, sizes, prev, pager, next, jumper">
      </el-pagination>
    </table-panel>
    <add-dialog v-if="visible"
      :visible="visible"
      :title="title"
      :editForm="editForm"
      @cancel="handleCancel"></add-dialog>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import addDialog from './addDialog'
export default {
  name:'LogisticsConfiguration',
  mixins: [tableMixin,pagePathMixin],
  components: {
    addDialog
  },
  data() {
    return {
      searchForm: {
        dataItemName: "",
      },
      editForm: {},
      tableData: [],
      loading: false,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      filterParam: {},
      visible: false,
      title: "",
    };
  },
  created() {
    this.getList();
  },
  methods: {
    //获取数据配置列表
    getList() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        filterData: {
          ...this.filterParam,
        },
      };
      this.$api.logisticsDataConfiguration
        .dataConfigList(params)
        .then(({ data: { list, total } }) => {
          this.tableData = list || [];
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //搜索
    onSearch() {
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      this.pageNum = 1;
      this.getList();
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.filterParam = {}
      this.onSearch();
    },
    onSwitch(row) {
      this.$api.logisticsDataConfiguration.dataConfigToggle(row.id).then((res) => {
        if (row.isActive == 1) {
          this.$notify.success({
            title: "成功",
            message: "启用成功",
          });
        } else if (row.isActive == 0) {
          this.$notify.success({
            title: "成功",
            message: "禁用成功",
          });
        }
        this.getList();
      });
    },
    //新增
    handleAdd() {
      this.title = "新增";
      this.visible = true;
      this.editForm = {};
    },
    //编辑
    handleEdit(row) {
      this.title = "编辑";
      this.visible = true;
      this.editForm = {
        id:row.id || "",
        comments: row.comments || "",
        dataItemName: row.dataItemName || "",
      };
    },
    handleCancel(type) {
      this.visible = false;
      if (type == 'cancel') return
      this.getList()
    },
    //分页   页数条数改变函数
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    handleCurrentChange(val) {
      this.pageNum = val;
      this.getList();
    },
  },
};
</script>
<style>
.el-tooltip__popper {
  max-width: 30% !important;
}
</style>
<style lang="stylus" scoped>
.el-form-item--mini.el-form-item, .el-form-item--small.el-form-item {
  margin-bottom: 25px;
}

>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}
</style>
