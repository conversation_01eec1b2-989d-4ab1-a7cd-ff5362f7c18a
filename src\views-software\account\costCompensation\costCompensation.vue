<template>
  <!-- 成本赔偿扣款 -->
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          size="mini"
          :inline="true"
          :model="searchForm"
          label-width="103px"
          ref="searchForm"
          label-position="right"
        >
          <el-form-item label="核算工厂:" prop="factoryId">
            <el-select
              v-model="searchForm.factoryId"
              filterable
              clearable
              placeholder="请选择核算工厂"
              @change="onSearch"
            >
              <el-option
                v-for="item in tabList"
                :key="item.value"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="责任分厂:" prop="jobFactoryName">
            <el-input
              v-model="searchForm.jobFactoryName"
              clearable
              placeholder="请输入责任分厂"
              @keyup.enter.native="onSearch"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="责任人:" prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="责任人"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input
              v-model.trim="searchForm.staffCode"
              size="mini"
              clearable
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenStaffCodes"
                  titleName="厂牌编号"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="扣款类型:" prop="deductType">
            <el-select
              v-model="searchForm.deductType"
              filterable
              clearable
              placeholder="请选择扣款类型"
              @change="onSearch"
            >
              <el-option
                v-for="item in payrollOptions"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="执行年月:" prop="executeMonth">
            <el-date-picker
              v-model="searchForm.executeMonth"
              type="month"
              placeholder="选择日期"
              @change="onSearch"
            >
            </el-date-picker>
          </el-form-item>

          <el-form-item label="办理人员:" prop="handleName">
            <el-input
              v-model="searchForm.handleName"
              placeholder="请输入办理人员"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="流程编号:" prop="processCode">
            <el-input
              v-model="searchForm.processCode"
              placeholder="请输入流程编号"
              @keyup.enter.native="onSearch"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="金额范围:" prop="amountRangeList">
            <el-select
              v-model="searchForm.amountRangeList"
              filterable
              clearable
              placeholder="请选择金额范围"
              @change="onSearch"
              multiple
            >
              <el-option
                v-for="item in payList"
                :key="item.value"
                :label="item.label"
                :value="item"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button
            size="small"
            type="primary"

            @click="onSearch"
          >
            查询
          </el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-left>
        <el-tabs v-model="auditStatus">
          <el-tab-pane
            v-for="item in items"
            :key="item.name"
            :name="item.value"
          >
            <span slot="label">{{
              item.number != null ? `${item.name}(${item.number})` : item.name
            }}</span>
          </el-tab-pane>
        </el-tabs>
      </template>
      <template v-slot:header-right>
        <div ref="btnRight">
          <el-checkbox
            v-model="selectall"
            type="primary"
            v-if="['0', '1'].includes(auditStatus)"
            label="全选"
            border
            size="mini"
            style="margin-right: 10px"
            class="select"
          ></el-checkbox>
          <el-button size="small" type="primary" @click="handleExport">
            导出
          </el-button>
          <el-button
            v-show="auditStatus == '1'"
            size="small"
            type="primary"
            @click="handleImport"
          >
            导入
          </el-button>
          <el-button
            v-show="auditStatus == '1'"
            size="small"
            type="primary"
            @click="handleAdd"
          >
            新增
          </el-button>
          <el-button
            v-show="auditStatus == '0'"
            size="small"
            type="primary"
            @click="batchClculate"
          >
            批量核算
          </el-button>
          <el-button
            v-show="auditStatus == '1'"
            size="small"
            type="primary"
            @click="batchBack"
          >
            批量退回
          </el-button>
          <el-button
            v-show="auditStatus == '0'"
            size="small"
            type="primary"
            @click="batchDelete"
          >
            批量删除
          </el-button>
        </div>
      </template>
      <!-- :key="tableKey" -->
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :height="maxTableHeight"
        :data="tableData"
        @selection-change="handleSelectionChange"
          :key="tableKey"
      >
        <el-table-column
          v-show="['0', '1'].includes(auditStatus)"
          width="40"
          type="selection"
          :reserve-selection="true"
        >
        </el-table-column>
        <el-table-column
          prop="processCode"
          label="流程编号"
          width="130"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          v-if="['0', '1', '2', '3'].includes(auditStatus)"
          prop="factoryName"
          label="核算分厂"
          width="110"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="staffName"
          label="责任人"
          width="100"
          align="left"
        >
        </el-table-column>
        <el-table-column
          prop="staffCode"
          label="厂牌编号"
          width="130"
          align="left"
        >
        </el-table-column>
        <el-table-column
          v-if="['1', '2'].includes(auditStatus)"
          prop="actualStaffCode"
          label="实际扣款厂牌"
          width="110"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="jobFactoryName"
          label="责任分厂"
          width="110"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="deductType"
          label="扣款类型"
          width="100"
          align="left"
        >
        </el-table-column>
        <el-table-column
          v-if="['0', '2', '3'].includes(auditStatus)"
          prop="deductAmount"
          label="扣款金额"
          width="100"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <span
              :style="{
                color:
                  row.deductAmount == row.actualDeductAmount
                    ? '#0BB78E'
                    : 'red',
              }"
            >
              {{ row.actualDeductAmount | moneyFormat }}</span
            >
          </template>
        </el-table-column>
        <template v-if="auditStatus == '1'">
          <el-table-column
            prop="deductAmount"
            label="流程扣款金额"
            width="120"
            align="left"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="actualDeductAmount"
            label="实际扣款金额"
            width="120"
            align="left"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="payedAmount"
            label="已扣款金额"
            width="120"
            align="left"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="payingAmount"
            label="待扣款金额"
            width="120"
            align="left"
            show-overflow-tooltip
          >
          </el-table-column>
        </template>
        <el-table-column
          v-if="auditStatus == '4'"
          label="扣款金额"
          width="100"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            {{ row.deductAmount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          prop="executeMonth"
          label="执行年月"
          width="100"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="auditStatus"
          label="审批状态"
          width="100"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>

        <el-table-column
          prop="handleName"
          label="办理人员"
          width="80"
          align="left"
        >
        </el-table-column>
        <el-table-column
          prop="handleTime"
          label="办理时间"
          width="150"
          align="left"
        >
          <template slot-scope="{ row }">
            {{ row.handleTime | dateFormat }}
          </template>
        </el-table-column>
        <el-table-column
          prop="remark"
          label="备注说明"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="acceptTime"
          label="接收时间"
          width="150"
          align="left"
        >
          <template slot-scope="scope">
            {{ scope.row.acceptTime | dateFormat }}
          </template>
        </el-table-column>
        <el-table-column label="" width="160" align="left" fixed="right">
          <template slot-scope="scope">
            <el-button
              v-show="scope.row.auditStatus == '未处理'"
              style="margin-left: 0"
              size="small"
              type="text"
              @click="handleClculate(scope.$index, scope.row)"
            >
              核算
            </el-button>

            <el-button
              style="margin-left: 0"
              size="small"
              type="text"
              v-show="
                scope.row.auditStatus == '未处理' &&
                scope.row.processCode.includes('SDPK')
              "
              @click="handleDelete(scope.$index, scope.row)"
            >
              删除
            </el-button>
            <el-button
              style="margin-left: 0"
              size="small"
              type="text"
              v-show="
                scope.row.auditStatus && scope.row.auditStatus != '未处理'
              "
              @click="handleDetails(scope.row)"
            >
              查看详情
            </el-button>
            <template v-if="scope.row.auditStatus != '未处理'">
              <el-button
              style="margin-left: 0"
              size="small"
              type="text"
              v-show="scope.row.auditStatus == '已处理'"
              @click="handleBack(scope.$index, scope.row)"
            >
              退回
            </el-button>
            <el-button
              style="margin-left: 0"
              size="small"
              type="text"
              v-show="scope.row.auditStatus == '已完成' && scope.row.isAccounting == 0"
              @click="handleBack(scope.$index, scope.row)"
            >
              退回
            </el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div style="text-align: right">
          <el-pagination
            @size-change="onSizeChange"
            @current-change="onNumChange"
            :current-page="pageNum"
            :page-size="pageSize"
            :total="total"
            :page-sizes="[10,30,50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
          >
          </el-pagination>
        </div>
      </template>
    </table-panel>
    <!-- 新增-->
    <add-cost v-if="visible" :isVisible="visible" @cancel="handleCancel">
    </add-cost>
    <!-- 核算 详情-->
    <calculate
      v-if="calculateVisible"
      :isVisible="calculateVisible"
      :title="title"
      :editForm="editForm"
      @cancel="calculateCancel"
    >
    </calculate>
    <common-dialog
      v-if="backVisible"
      :isVisible="backVisible"
      :title="title"
      :info="info"
      :selectall="selectall"
      @cancel="backCancel"
      :filterParam="filterParam"
      :auditStatus ="auditStatus"
    >
    </common-dialog>
    <Import
      v-if="importVisible"
      :visible="importVisible"
      @cancel="cancel"
      @confirm="confirm"
      :importInfo="importInfo"
    />
    <!-- 查看详情 -->
    <details-dialog
      v-if="isVisibleDteails"
      :visible="isVisibleDteails"
      :formData="detailsData"
      @cancel="detailsCancel"
    ></details-dialog>
        <!-- 批量核算 -->
        <accoun-dialog
     v-if="accounDialogVisible"
      :isVisible="accounDialogVisible"
      :info="info"
      @cancel="accounCancel"
      :selectall="selectall"
      :filterParam="filterParam"
       @success="successPage"
      :total="total"
     ></accoun-dialog>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import moment from "moment";
import addCost from "./addCost";
import calculate from "./calculate";
import commonDialog from "./component/commonDialog";
import detailsDialog from "./component/detailsDialog";
import accounDialog from "./component/accounDialog";
export default {
  name: "softwareCompensation",
  mixins: [tableMixin,pagePathMixin],
  components: { addCost, calculate, commonDialog, detailsDialog, accounDialog},
  data() {
    return {
      selectall: false,
      searchForm: {
        processCode: "",
        factoryId: "",
        jobFactoryName: "",
        staffCode: "",
        staffName: "",
        deductType: "",
        executeMonth: moment().subtract(1, "month").format("YYYY-MM"),
        handleName: "",
        amountRangeList:""
      },
      editForm: {},
      detailsData: {},
      isVisibleDteails: false,
      tableKey: "",
      //扣款类型
      payrollOptions: Object.freeze([
        {
          name: "成本赔偿",
          value: "1",
        },
        {
          name: "返工扣款",
          value: "2",
        },
        {
          name: "低耗品",
          value: "3",
        },
        {
          name: "刀具",
          value: "4",
        },
        {
          name: "未打卡扣款",
          value: "5",
        },
      ]),
      items: [
        {
          name: "未处理",
          value: "0",
          type: "notSubmitted",
          number: 0,
        },
        {
          name: "已处理",
          value: "1",
          type: "review",
          number: 0,
        },
        {
          name: "已完成",
          value: "2",
          type: "back",
          number: 0,
        },
        // {
        //   name: "已归档",
        //   value: "3",
        //   type: "archive",
        //   number: 0,
        // },
      ],
      payList: [
        {
          label: "0~100元",
          value: "0",
          max: 100,
          min: 0,
        },
        {
          label: "100~300元",
          value: "1",
          max: 300,
          min: 100,
        },
        {
          label: "300~500元",
          value: "2",
          max: 500,
          min: 300,
        },
        {
          label: "500元以上",
          value: "3",
          max: "",
          min: 500,
        },
      ],
      auditStatus: "0",
      tabList: [],
      filterParam: {},
      params: {},
      importInfo: {
        reportName: "softwareImportbpmdeduct",
        paramMap: {
          columnValue: "软体-成本赔偿扣款台账",
        },
      },
      //表格数据
      tableData: [],
      idList: [],
      info: {},
      loading: false,
      // resizeOffset: 53,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      title: "",
      visible: false,
      backVisible: false,
      calculateVisible: false,
      importVisible: false,
      diffTime: 0,
      startTime: 0,
      accounDialogVisible:false,
    };
  },
  created() {
    this.$api.softwareSystemManage.getBasicPermission
      .getBasicPermissionAll()
      .then((res) => {
        this.tabList =
          res.data.map((item) => ({
            label: item.name,
            name: item.name,
            id: item.id,
          })) || [];
      });
  },
  watch: {
    $route: {
      handler(value) {
        if (
          value.path.includes("compensation") &&
          value.path.includes("software")
        ) {
          this.onSearch();
        }
      },
      deep: true,
      immediate: true,
    },
    auditStatus: {
      handler() {
        this.selectall = false
        this.$refs.tableRef.clearSelection()
        this.idList = [];
        this.getList();
        this.getStatistics();
      },
    },
    selectall: {
      handler() {
        if (this.selectall) {
          this.$refs.tableRef.toggleAllSelection();
        } else {
          this.$refs.tableRef.clearSelection();
        }
      },
    },
  },
  methods: {
    getRowKeys(row) {
      return row.id;
    },
    //成本扣款台账列表
    getList() {
      this.loading = true;
      this.tableData = [];
      this.tableKey = Math.random();
      this.$api.softwareInformation.costCompensation
        .deductList({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: {
            auditStatus: this.auditStatus,
            ...this.filterParam,
            ...this.params,
          },
        })
        .then(({ data: { list, total } }) => {
          this.tableData = list || [];
          this.total = total;
          if(this.selectall){
              this.tableData.forEach((row) => {
            this.$refs.tableRef.toggleRowSelection(row, true);
          });
            }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //获取统计信息
    async getStatistics() {
      let params = {
        auditStatus: this.auditStatus,
        ...this.filterParam,
        ...this.params,
      };
      const list =
        await this.$api.softwareInformation.costCompensation.deductStatistics(
          params
        );
      const data = list.data || {};
      if (Object.keys(data).length != 0) {
        this.items = this.items.map((item) => {
          for (const key in data) {
            if (item.type == key) {
              item.number = this.getTotal(list.data[item.type]);
            }
          }
          return item;
        });
      } else {
        this.items = [
          {
            name: "未处理",
            value: "0",
            type: "notSubmitted",
            number: 0,
          },
          {
            name: "已处理",
            value: "1",
            type: "review",
            number: 0,
          },
          {
            name: "审核中",
            value: "2",
            type: "back",
            number: 0,
          },
          // {
          //   name: "已归档",
          //   value: "3",
          //   type: "archive",
          //   number: 0,
          // },
          {
            name: "变动数据",
            value: "4",
            type: "audited",
          },
        ];
      }
    },
    getTotal(num) {
      if (isNaN(Number(num))) return 0;
      return Number(num) > 9999 ? "9999+" : num;
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.searchForm.executeMonth = moment()
        .subtract(1, "month")
        .format("YYYY-MM");
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (key === "executeMonth") {
          if (val) this.filterParam[key]= moment(val).format("YYYY-MM");
        }else if (key === "amountRangeList" && val.length) {
          this.filterParam.amountRangeList = val.map((item) => {
            return {
              maxAmount:item.max,
              minAmount:item.min
            };
          });
        }else if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0)
          this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
      this.getStatistics();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) {
          this.params[name] = data;
        }
      }
    },
    //新增
    handleAdd() {
      this.visible = true;
    },
    detailsCancel() {
      this.isVisibleDteails = false;
    },
    //批量核算
    batchClculate() {
      if (this.idList.length === 0 && !this.selectall) {
        this.$message({
          message: "请先勾选需要批量确认的内容",
          type: "warning",
        });
        return;
      }
      this.info = {
        idList: this.idList,
        payMonth: moment().subtract(1, "month").format("YYYY-MM"),
      };
      this.title = "批量核算";
      this.accounDialogVisible = true;
    },
    //批量退回
    batchBack() {
      if (this.idList.length === 0 && !this.selectall) {
        this.$message({
          message: "请先勾选需要批量确认的内容",
          type: "warning",
        });
        return;
      }
      this.info = { idList: this.idList };
      this.title = "批量退回";
      this.backVisible = true;
    },
    //批量删除
    batchDelete() {
      if (this.idList.length === 0 && !this.selectall) {
        this.$message({
          message: "请先勾选需要批量确认的内容",
          type: "warning",
        });
        return;
      }
      this.info = { idList: this.idList };
      this.title = "批量删除";
      this.backVisible = true;
    },
    //导入
    handleImport() {
      this.importVisible = true;
    },
    accounCancel(){
      this.accounDialogVisible = false;
    },
    successPage(){
      this.accounDialogVisible = false
      this.$refs.tableRef.clearSelection();
      this.onSearch()
    },
    //导出
    handleExport() {
      let nowTime = moment();
      this.diffTime = nowTime.diff(this.startTime, "second");
      if (this.diffTime < 11) {
        this.$message.warning("10s内不能重复点击");
        return;
      } else {
        this.startTime = nowTime;
      }
      let params = {
        ...this.filterParam,
        ...this.params,
      };
      this.$api.common.doExport("softwareExportbpmdeduct", params).then(() => {
        this.$message.success("导出操作成功，请前往导出记录查看详情");
      });
    },
    //查看详情
    handleDetails({ id }) {
      this.isVisibleDteails = true;
      this.detailsData = {
        id,
      };
    },
    //核算
    handleClculate(index, row) {
      this.editForm = {
        id: row.id,
      };
      this.title = "核算";
      this.calculateVisible = true;
    },
    //退回
    handleBack(index, row) {
      this.title = "退回";
      this.backVisible = true;
      this.info = row;
    },
    //删除
    handleDelete(index, row) {
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$api.softwareInformation.costCompensation
          .deleteBpmDeduct({
            id: row.id,
          })
          .then(() => {
            this.$notify.success({
              title: "成功",
              message: "删除成功",
            });
            this.getList();
            this.getStatistics();
          });
      });
    },
    cancellation(type) {
      if (type && type == "cancel") return;
      this.getList();
      this.getStatistics();
    },
    handleCancel(obj) {
      const { type, isVisible } = obj;
      this.visible = isVisible;
      this.cancellation(type);
    },
    calculateCancel(obj) {
      const { type, isVisible } = obj;
      this.calculateVisible = isVisible;
      this.cancellation(type);
    },
    backCancel(obj) {
      this.$refs.tableRef.clearSelection()
      const { type, isVisible } = obj;
      this.backVisible = isVisible;
      this.cancellation(type);
    },
    handleSelectionChange(val) {
      this.idList = (val && val.map((item) => item.id)) || [];
    },
    cancel(value) {
      this.importVisible = value;
    },
    confirm(value) {
      this.importVisible = value;
      this.getList();
    },
    onSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(val) {
      this.pageNum = val;
      this.getList();
    },
  },
};
</script>

<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}
.select{
  background:#0bb78e
  color:#fff
}
>>>.el-checkbox.is-bordered.el-checkbox--mini{
    height:25px !important
}
>>>.el-checkbox__input.is-checked+.el-checkbox__label{
    color:white !important
}
>>>.el-checkbox__input.is-checked .el-checkbox__inner{
   border-color:white !important
}
</style>
