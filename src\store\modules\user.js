import api from '@/api';
import { getToken, setToken, removeToken } from '@/utils/auth';
import router, { resetRouter } from '@/router';
import { setPermitList, removePermitList } from './permission';

const state = {
  token: getToken(),
  name: '',
  roles: [],
  permits: []
};

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token;
  },
  SET_NAME: (state, name) => {
    state.name = name;
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles;
  },
  SET_PERMITS: (state, permits) => {
    state.permits = permits;
  },
};

const actions = {
  // user login
  login({ commit, state }, userInfo) {
    const { userName, password } = userInfo;
    return new Promise((resolve, reject) => {
      // 每次登陆前，清除本地权限缓存
      removePermitList();
      api.common.login({
        userName: userName.trim(),
        password
      }).then(({ data }) => {
        setToken(data.token);
        if (data.token != state.token) {
          commit('menu/SET_MENUS', [], { root: true });
          commit('SET_PERMITS', []);
        }
        commit('SET_TOKEN', data.token);
        commit('SET_NAME', data.name);
        // 将权限列表写入local
        // setPermitList(data.ownerAuthority);
        resolve();
      }).catch(error => {
        reject(error);
      });
    });
  },

  // get user info
  getUserInfo({ commit, }) {
    return new Promise((resolve, reject) => {
      api.common.getUserInfo().then(response => {
        const { data } = response;

        if (!data) {
          reject('Verification failed, please Login again.');
        }
        const { name } = data;
        commit('SET_NAME', name);
        commit('SET_ROLES', ['admin']);
        resolve({
          roles: ['admin']
        });
      }).catch(error => {
        reject(error);
      });
    });
  },

  // user logout
  logout({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      commit('SET_TOKEN', '');
      commit('SET_ROLES', []);
      removeToken();
      resetRouter();
      dispatch('tagsView/delAllViews', null, { root: true });

      resolve();
    });
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '');
      commit('SET_ROLES', []);
      removeToken();
      resolve();
    });
  },
  setBaseData({ dispatch }) {
    dispatch('getButtonPermission');
    dispatch('getUserInfo');
    return dispatch('menu/getMenus', {}, { root: true });
  },
  getButtonPermission({ commit, state }) {
    if (state.permits && state.permits.length) {
      return Promise.resolve(state.permits);
    }
    return api.common.getButtonPermission().then(({ data, success, message }) => {
      if (Array.isArray(data)) {
        commit('SET_PERMITS', data);
        setPermitList(data);
        return data;
      }
      return [];
    }).catch(error => {
      console.log('获取按钮权限错误:', error);
      return [];
    });
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
