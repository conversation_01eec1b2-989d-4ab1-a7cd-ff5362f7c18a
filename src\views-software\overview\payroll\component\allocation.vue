<template>
  <!-- 分配表 -->
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          size="mini"
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          label-position="right"
        >
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="员工姓名"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input
              v-model.trim="searchForm.staffCode"
              size="mini"
              clearable
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenStaffCodes"
                  titleName="厂牌编号"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="核算班组:" prop="belongProcessId">
            <el-select
              v-model="searchForm.belongProcessId"
              placeholder=""
              filterable
              clearable
              @change="onSearch"
              @clear="onSearch"
            >
              <el-option
                v-for="item in teamOptinon"
                :key="item.name"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>

        <template v-slot:right>
          <el-button size="small" type="primary"  @click="onSearch">
            查询
          </el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <!-- <template
        v-slot:header-right>
        <div ref="btnRight">
          <el-button
            size="small"
            type="primary"
            @click="handleExport">
            导出
          </el-button>
        </div>
      </template> -->
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :data="tableData"
        :height="maxTableHeight"
        style="width: 100%"
      >
        <el-table-column label="员工姓名" width="100" prop="staffName"></el-table-column>
        <el-table-column label="厂牌编号" width="120" prop="staffCode"></el-table-column>
        <el-table-column
          label="核算工厂"
          width="130"
          prop="belongFactoryName"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="核算班组"
          width="120"
          prop="belongProcessName"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="核算月份"
          width="120"
          prop="accountingMonth"
        ></el-table-column>
        <el-table-column
          label="调整前金额"
          width="120"
          prop="beforeAmount"
          sortable
          :sort-method="beforeAmountSort"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="调增金额"
          width="120"
          prop="addAmount"
          sortable
          :sort-method="addAmountSort"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="调减金额"
          width="120"
          prop="minusAmount"
          sortable
          :sort-method="minusAmountSort"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="调整后金额"
          width="120"
          prop="afterAmount"
          sortable
          :sort-method="afterAmountSort"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="调整事由"
          width="100"
          prop="editReason"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="备注一"
          prop="comments"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column label="修改时间" width="150" prop="updateTime"></el-table-column>
      </el-table>
      <template v-slot:footer>
        <div class="table_footer">
          <ul>
            <li>
              <span>核算工厂:</span><span>{{ info.factoryName }}</span>
            </li>
            <li>
              <span>核算月份:</span><span>{{ info.accountingMonth }}</span>
            </li>
            <li>
              <span>人数:</span><span>{{ statiStics.people }}</span>
            </li>
            <li>
              <span>调增金额:</span><span>{{ statiStics.addAmount | moneyFormat }}</span>
            </li>
            <li>
              <span>调减金额:</span
              ><span>{{ statiStics.minusAmount | moneyFormat }}</span>
            </li>
            <li>
              <span>合计调整:</span
              ><span>{{ statiStics.totalAmount | moneyFormat }}</span>
            </li>
          </ul>
        </div>
        <div style="text-align: right">
          <el-pagination
            @size-change="onSizeChange"
            @current-change="onNumChange"
            :current-page="pageNum"
            :page-size="pageSize"
            :total="total"
            :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper"
          >
          </el-pagination>
        </div>
      </template>
    </table-panel>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import { moneyFormat, moneyDelete } from "@/utils";
export default {
  name: "SoftwareAllocation",
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      searchForm: {
        content: "",
        staffCode: "",
        staffName: "",
        belongProcessId: "",
      },
      visible: false,
      tableData: [], //表格数据
      loading: false,
      resizeOffset: 67,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      filterParam: {},
      params: {},
      teamOptinon: [],
      factoryId: "",
      info: {},
      statiStics: {},
      propName: "",
    };
  },
  mounted() {
    this.$bus.$off("softwareAllocationExport");
    this.$bus.$on("softwareAllocationExport", this.handleExport);
  },
  watch: {
    $route: {
      handler(value) {
        if (value.path.includes("payroll")&&value.path.includes("software")) {
          this.info = JSON.parse(value.query.data);
          this.factoryId = this.info.factoryId;
          this.getAvailableGroups();
          this.getList();
          this.getDebitList();
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    getList() {
      //获取分配表
      this.loading = true;
      this.$api.softwareWorkbench
        .getAllocation({
          pageSize: this.pageSize,
          pageNum: this.pageNum,
          filterData: {
            factoryId: this.factoryId,
            accountingMonth: this.info.accountingMonth,
            ...this.filterParam,
            ...this.params,
          },
        })
        .then(({ data }) => {
          this.tableData =
            data.list.map((item) => ({
              ...item,
              belongFactoryName: this.info.factoryName,
              beforeAmount:!item.beforeAmount?'-': moneyFormat(item.beforeAmount),
              addAmount:!item.addAmount?'-': moneyFormat(item.addAmount),
              minusAmount:!item.minusAmount?'-': moneyFormat(item.minusAmount),
              afterAmount:!item.afterAmount?'-': moneyFormat(item.afterAmount),
            })) || [];
          this.total = data.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //获取统计信息
    getDebitList() {
      let params = {
        accountingMonth: this.info.accountingMonth,
        factoryId: this.factoryId,
        ...this.filterParam,
        ...this.params,
      };
      this.$api.softwareStatiStics.statisticsAllot(params).then((res) => {
        this.statiStics = res.data
          ? res.data
          : {
              addAmount: 0,
              collectAmount: 0,
              minusAmount: 0,
              people: 0,
              remainAmount: 0,
              totalAmount: 0,
            };
      });
    },
    //根据工厂和核算月份查询班组列表
    getAvailableGroups() {
      this.$api.softwareWorkbench
        .availableGroups({
          factoryId: this.factoryId,
          accountingMonth: this.info.accountingMonth,
        })
        .then(({ data }) => {
          this.teamOptinon = data;
        });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
      this.getDebitList();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    beforeAmountSort(a, b) {
      return Number(moneyDelete(a.beforeAmount)) - Number(moneyDelete(b.beforeAmount));
    },
    addAmountSort(a, b) {
      return Number(moneyDelete(a.addAmount)) - Number(moneyDelete(b.addAmount));
    },
    minusAmountSort(a, b) {
      return Number(moneyDelete(a.minusAmount)) - Number(moneyDelete(b.minusAmount));
    },
    afterAmountSort(a, b) {
      return Number(moneyDelete(a.afterAmount)) - Number(moneyDelete(b.afterAmount));
    },
    //导出
    handleExport() {
      let params = {};
      if (Object.keys(this.filterParam).length) {
        params = {
          ...this.filterParam,
        };
      }
      this.$api.softwareWorkbench
        .allotExport({
          factoryId: this.info.factoryId,
          accountingMonth: this.info.accountingMonth,
          ...params,
          ...this.params,
        })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success("导出操作成功，请前往导出记录查看详情");
          }
        });
    },
    onSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(val) {
      this.pageNum = val;
      this.getList();
    },
  },
};
</script>

<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}
.el-form--label-left {
  .el-form-item {
    margin-right: 16px;
    margin-bottom: 10px;

    &.third {
      >>>.el-form-item__content {
        margin: 0 !important;
      }
    }
  }
}

.table-panel {
  position: relative;

  >>>.btn_right {
    position: absolute;
    right: 0;
    z-index: 2;
  }
}

ul {
  list-style: none;
  display: flex;
  padding: 0;
}

>>>.table-panel-footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .table_footer {
    overflow: auto;

    >ul {
      display: flex;
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        white-space: nowrap
        padding: 0 10px;
      }
    }
  }
}

>>>.el-tabs__nav-wrap::after {
  display: block;
  height: 0;
}
</style>
