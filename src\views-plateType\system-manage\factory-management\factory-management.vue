<template>
  <content-panel>
    <table-panel ref="tablePanel">
      <template v-slot:header-right>
        <el-button size="small" type="primary" @click="handleAdd"> 新增</el-button>
      </template>
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :data="tableData"
        :height="maxTableHeight"
        style="width: 100%"
      >
        <el-table-column
          prop="operateTime"
          label="工厂名称"
          align="left"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="staffName"
          label="关联Mes工厂"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column prop="operateUser" label="状态" align="left"> </el-table-column>
        <el-table-column
          prop="handleName"
          label="更新时间"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column label="操作" align="left" width="100">
          <template slot-scope="{ row }">
            <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageNum"
        :page-size="pageSize"
        :total="total"
        :page-sizes="[50, 100, 200]"
        layout="total, sizes, prev, pager, next, jumper"
      >
      </el-pagination>
    </table-panel>
    <add-dialog
      v-if="visible"
      :isVisible="visible"
      :title="title"
      :editForm="editForm"
      @cancel="handleCancel"
    ></add-dialog>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import addDialog from "./addDialog";
export default {
  name: "PlateTypeFactoryManagement",
  mixins: [tableMixin,pagePathMixin],
  components: { addDialog },
  data() {
    return {
      tableData: [],
      loading: false,
      resizeOffset: 45,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      visible: false,
      title: "",
      editForm: {},
    };
  },
  watch: {
    $route: {
      handler(value) {
        if (value.path.includes("factoryManagement")&&value.path.includes("plateType")) {
          this.getList();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    getList() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        filterData: {
          ...this.logForm,
        },
      };
      this.$api.agencyLog
        .logList(params)
        .then(({ data: { list, total } }) => {
          this.tableData = list || [];
          this.total = total || 0;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //新增
    handleAdd() {
      this.title = "新增";
      this.visible = true;
      this.editForm = {};
    },
    //编辑
    handleEdit(row) {
      this.title = "编辑";
      this.visible = true;
      this.editForm = {};
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    handleCurrentChange(val) {
      this.pageNum = val;
      this.getList();
    },
    handleCancel() {
      this.visible = false;
    },
  },
};
</script>

<style lang="stylus" scoped>
.el-form--label-left {
  .el-form-item {
    margin-right: 16px;
  }

  >>>.el-form-item__label {
    text-align: right !important;
  }
}
</style>
