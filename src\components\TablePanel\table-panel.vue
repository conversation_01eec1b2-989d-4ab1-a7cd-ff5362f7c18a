<template>
  <div class="table-panel">
    <div class="table-panel-header" v-if="hasHeader" :class="{'table-panel-header-bottom': hasHeader}">
      <div class="table-panel-header-left">
        <slot name="header-left"></slot>
      </div>
      <div class="table-panel-header-right" v-if="hasHeaderRight">
        <slot name="header-right"></slot>
      </div>
    </div>
    <div class="table-panel-content" ref="tableWrap">
      <slot></slot>
    </div>
    <div class="table-panel-footer" v-if="hasFooter" :class="{'table-panel-footer-top': hasFooter}">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TablePanel',
  computed: {
    hasHeaderLeft() {
      return !!this.$slots['header-left'];
    },
    hasHeaderRight() {
      return !!this.$slots['header-right'];
    },
    hasHeader() {
      return this.hasHeaderLeft || this.hasHeaderRight || false;
    },
    hasFooter() {
      return this.$slots.footer ? true : false;
    },
  }
}
</script>

<style lang="stylus" scoped>
>>>.table-panel-header-left {
  overflow: hidden;
  margin-right: 20px;
  .el-tabs__header {
    margin: 0;
  }
}

.table-panel {
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-bottom {
      padding-bottom: 10px;
    }

    &-left {
      flex: 1;
    }

    &-right {
      // padding 0 10px
    }
  }

  &-footer-top {
    padding-top: 10px;
  }
}
</style>

