<template>
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form size="mini" :inline="true" :model="searchForm" ref="searchForm" label-position="right">
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input clearable v-model.trim="searchForm.staffName" size="mini" @keyup.enter.native="onSearch">
              <template slot="append">
                <search-batch @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)" ref="childrenStaffNames" titleName="员工姓名" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input v-model.trim="searchForm.staffCode" size="mini" clearable @keyup.enter.native="onSearch">
              <template slot="append">
                <search-batch @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)" ref="childrenStaffCodes" titleName="厂牌编号" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="身份证号:" prop="idCard">
            <el-input v-model.trim="searchForm.idCard" size="mini" clearable @keyup.enter.native="onSearch">
              <template slot="append">
                <search-batch @seachFilter="onSearch('idCards', $event)" @focusEvent="focusEvent('idCards', $event)"
                  ref="childrenIdCardss" titleName="身份证号" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="核算月份:" prop="accountingMonth">
            <el-date-picker v-model="searchForm.accountingMonth" type="month" placeholder="请选择日期" @change="onSearch">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="工厂名称:" prop="factoryId">
            <el-select v-model="searchForm.factoryId" placeholder="请选择工厂" @change="onSearch">
              <el-option v-for="item in tabList" :key="item.id" :label="item.label" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="人员状态:" prop="personStatus">
            <el-input v-model.trim="searchForm.personStatus" clearable placeholder="请输入人员状态"
              @keyup.enter.native="onSearch"></el-input>
          </el-form-item>
          <el-form-item label="确认来源:" prop="confirmSource">
            <el-select v-model="searchForm.confirmSource" placeholder="请选择确认来源" @change="onSearch">
              <el-option label="全部" value="0"></el-option>
              <el-option label="手动生成" value="1"></el-option>
              <el-option label="虚拟生成" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="payStatus == '2'" label="发放方式:" prop="payType">
            <el-select v-model="searchForm.payType" placeholder="请选择发放方式" @change="onSearch">
              <el-option label="全部" value="0"></el-option>
              <el-option label="上卡" value="1"></el-option>
              <el-option label="特殊工资单" value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary" @click="onSearch">
            查询</el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置</el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-left>
        <el-tabs v-model="payStatus">
          <el-tab-pane v-for="item in items" :key="item.value" :label="item.label" :name="item.value"></el-tab-pane>
        </el-tabs>
      </template>
      <template v-slot:header-right>
        <el-button v-if="payStatus == '0'"
          v-permission="'was-customized$informationAccount$informationAccount$unpaidLedger$import'" size="small"
          type="primary" @click="handleUnpaidImport">
          未付导入
        </el-button>
        <el-button v-if="payStatus == '1'"
          v-permission="'was-customized$informationAccount$informationAccount$unpaidLedger$import'" size="small"
          type="primary" @click="handleImport">
          付款申请导入
        </el-button>
        <el-button v-if="payStatus == '2'" size="small" type="primary" @click="handleDeduction">
          扣款备注
        </el-button>
        <el-button v-permission="'was-customized$informationAccount$informationAccount$unpaidLedger$import'"
          size="small" type="primary" @click="handleExport">
          导出
        </el-button>
        <el-button v-if="payStatus == '0'"
          v-permission="'was-customized$informationAccount$informationAccount$unpaidLedger$paymentApplication'"
          size="small" type="primary" @click="paymentApplication">
          付款申请
        </el-button>
        <el-button v-if="payStatus == '1'"
          v-permission="'was-customized$informationAccount$informationAccount$unpaidLedger$withdraw'" size="small"
          type="primary" @click="handleWithdraw">
          取消申请
        </el-button>
        <el-button v-if="payStatus == '1'"
          v-permission="'was-customized$informationAccount$informationAccount$unpaidLedger$paymentConfirm'" size="small"
          type="primary" @click="paymentConfirm">
          付款确认
        </el-button>
      </template>
      <el-table stripe border v-loading="loading" ref="tableRef" highlight-current-row :height="maxTableHeight"
        :data="tableData" :key="key" :row-class-name="tableRowClassName" @selection-change="handleSelectionChange">
        <el-table-column width="40" type="selection">
        </el-table-column>
        <el-table-column label="序号" width="50" type="index">
        </el-table-column>
        <el-table-column prop="staffName" label="员工姓名" width="100" align="left" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="accountingMonth" label="工资月份" width="80" align="left">
        </el-table-column>
        <el-table-column prop="staffCode" label="厂牌编号" width="110" align="left" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="factoryName" label="工厂名称" width="110" align="left" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="idCard" width="140" label="身份证" align="left">
        </el-table-column>
        <el-table-column label="未付金额" width="100" align="left" show-overflow-tooltip>
          <template slot-scope="{row}">
            {{ row.notPayAmount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column label="其他扣款" width="100" align="left" show-overflow-tooltip>
          <template slot-scope="{row}">
            {{ row.otherDeduct | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column label="自离扣款" width="100" align="left" show-overflow-tooltip>
          <template slot-scope="{row}">
            {{ row.leaveSelfDeduct | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column label="实付金额" width="120" align="left" show-overflow-tooltip>
          <template slot-scope="{row}">
            {{ row.actualAmount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column prop="personStatus" label="人员状态" width="80" align="left">
        </el-table-column>
        <el-table-column prop="payStatus" label="付款状态" width="80" align="left">
        </el-table-column>
        <el-table-column v-if="payStatus != '0'" label="申请时间" align="left">
          <template slot-scope="{row}">
            {{ row.supplyTime | shortDate }}
          </template>
        </el-table-column>
        <el-table-column v-if="payStatus == '2'" prop="payType" label="发放方式" align="left">
        </el-table-column>
        <el-table-column v-if="payStatus == '2'" prop="confirmSource" label="确认来源" align="left">
        </el-table-column>
        <el-table-column v-if="payStatus == '2'" label="付款时间" align="left">
          <template slot-scope="{row}">
            {{ row.payTime | shortDate }}
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="其他扣款备注" align="left" show-overflow-tooltip>
        </el-table-column>
        <el-table-column v-if="payStatus == '0'" label="操作" width="150" align="left" fixed="right">
          <template slot-scope="{row}">
            <el-button v-permission="'was-customized$informationAccount$informationAccount$unpaidLedger$edit'"
              style="margin-left: 0" size="small" type="text" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button v-permission="'was-customized$informationAccount$informationAccount$unpaidLedger$delete'"
              style="margin-left: 0" size="small" type="text" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div style="text-align: right">
          <el-pagination @size-change="onSizeChange" @current-change="onNumChange" :current-page="pageNum"
            :page-size="pageSize" :total="total" :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>
        </div>
      </template>
    </table-panel>
    <edit-dialog v-if="visible" :isVisible="visible" :paymentId="paymentId" @handleCancel="handleCancel"></edit-dialog>
    <Import v-if="ImportVisible" :visible="ImportVisible" :title="title" @cancel="cancel" @confirm="confirm"
      :importInfo="importInfo" />
    <deduction-dialog v-if="deductionVisible" :visible="deductionVisible" :deductionList="deductionList"
      @deductionCancel="deductionCancel">
    </deduction-dialog>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import moment from 'moment';
import editDialog from './editDialog';
import deductionDialog from './deductionDialog';
export default {
  name: "UnpaidLedger",
  mixins: [tableMixin, pagePathMixin],
  components: { editDialog, deductionDialog },
  data() {
    return {
      searchForm: {
        staffCode: "",
        staffName: "",
        idCard: "",
        payStatus: "",
        factoryId: "",
        accountingMonth: "",
        payType: "",
        personStatus: "",
        supplyTime: ""
      },
      tabList: [],
      tableData: [],
      items: Object.freeze([{
        label: "未付款名单",
        name: "未付款",
        value: "0"
      },
      {
        label: "待付款名单",
        name: "待付款",
        value: "1"
      },
      {
        label: "已付款名单",
        name: "已付款",
        value: "2"
      }]),
      loading: false,
      payStatus: "0",
      // resizeOffset: 53,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      title: "",
      paymentId: "",
      visible: false,
      ImportVisible: false,
      deductionVisible: false,
      filterParam: {},
      params: {},
      importInfo: {},
      list: [],
      deductionList: [],
      key: 0,
      switchingTab: false, // 防止快速切换标志
      tabSwitchTimer: null // 防抖定时器
    };
  },
  created() {
    this.$api.systemManage.getBasicPermission
      .getBasicPermissionAll()
      .then((res) => {
        if (res.code === 200) {
          this.tabList = res.data.map((item) => ({
            label: item.name,
            name: item.name,
            id: item.id,
          }));
        }
      });
  },
  mounted() {
    // 添加全局错误处理，捕获 DOM 操作相关错误
    this.handleDOMErrors();
  },
  beforeDestroy() {
    // 清理切换标志和定时器，防止内存泄漏
    this.switchingTab = false;
    if (this.tabSwitchTimer) {
      clearTimeout(this.tabSwitchTimer);
      this.tabSwitchTimer = null;
    }
  },
  watch: {
    $route: {
      handler(value) {
        if (value.path.includes('unpaidLedger') && value.path.includes("customized")) {
          this.key = 0;
          this.getList();
        }
      },
      deep: true
    },
    payStatus: {
      handler() {
        // 使用防抖处理快速切换
        this.debouncedTabSwitch();
      },
      immediate: true
    }
  },
  methods: {
    // 处理DOM操作错误
    handleDOMErrors() {
      // 捕获全局的 nextTick 错误
      const originalNextTick = this.$nextTick;
      this.$nextTick = (callback) => {
        return originalNextTick.call(this, () => {
          try {
            if (callback) callback();
          } catch (error) {
            if (error.message && error.message.includes('insertBefore')) {
              console.warn('DOM操作错误已被捕获并忽略:', error.message);
              return;
            }
            throw error;
          }
        });
      };
    },
    // 防抖处理标签切换
    debouncedTabSwitch() {
      // 清除之前的定时器
      if (this.tabSwitchTimer) {
        clearTimeout(this.tabSwitchTimer);
      }

      // 设置新的定时器
      this.tabSwitchTimer = setTimeout(() => {
        this.$nextTick(() => {
          this.key++;
          this.getList();
        });
      }, 50); // 50ms 防抖延迟
    },
    //获取列表
    getList() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        filterData: {
          ...this.filterParam,
          ...this.params,
          payStatus: this.payStatus,
          payType: this.filterParam.payType == '0' ? '' : this.filterParam.payType,
          confirmSource: this.filterParam.confirmSource == '0' ? '' : this.filterParam.confirmSource,
        }
      };
      this.$api.information.unpaidLedger.unPaidList(params).then(({ data: { list, total } }) => {
        this.tableData = list || [];
        this.total = total || 0;
      }).catch(error => {
        console.error('获取列表数据失败:', error);
        this.$message.error('获取数据失败，请重试');
      }).finally(() => {
        this.loading = false;
      });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.$refs.childrenIdCardss.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (key == 'accountingMonth' && val) {
          this.filterParam.accountingMonth = moment(val).format('YYYY-MM');
        } else if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0)
          this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) {
          this.params[name] = data;
        }
      }
    },
    tableRowClassName({ row, rowIndex }) {
      return row.status == '待付款' && this.payStatus == '0' ? "table-SelectedRow-bgcolor" : '';
    },
    //编辑
    handleEdit(row) {
      this.paymentId = row.id;
      this.visible = true;
    },
    //删除
    handleDelete({ id }) {
      this.$confirm("此操作将永久删除该条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$api.information.unpaidLedger
            .deleteUnPaid({ id })
            .then((res) => {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getList();
            });
        })
        .catch(() => {

        });
    },
    handleSelectionChange(value) {
      this.list = value && value.map(item => item.id);
    },
    //未付导入
    handleUnpaidImport() {
      this.ImportVisible = true;
      this.title = "未付款台账";
      this.importInfo = {
        reportName: "importNotPay",
        paramMap: {
          columnValue: "未付款台账",
        }
      };
    },
    //待付导入
    handleImport() {
      this.ImportVisible = true;
      this.title = "付款申请导入";
      this.importInfo = {
        reportName: "importSupplyPay",
        paramMap: {
          columnValue: "付款申请导入",
        }
      };
    },
    //导出
    handleExport() {
      let params = {};
      if (Object.keys(this.filterParam).length) {
        params = {
          ...this.filterParam,
          payType: this.filterParam.payType == '0' ? '' : this.filterParam.payType,
          confirmSource: this.filterParam.confirmSource == '0' ? '' : this.filterParam.confirmSource,
        };
      }
      this.$api.common
        .doExport("exportUnpaid", { ...params, ...this.params, })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success("导出操作成功，请前往导出记录查看详情");
          }
        });
    },
    //付款申请
    paymentApplication() {
      this.$api.information.unpaidLedger
        .unPaidSupplyPay({ ids: this.list })
        .then((res) => {
          this.$notify({
            title: '成功',
            message: '申请成功',
            type: 'success'
          });
          this.getList();
        });
    },
    //取消申请
    handleWithdraw() {
      this.$api.information.unpaidLedger
        .unPaidCancelSupply({ ids: this.list })
        .then((res) => {
          this.$notify({
            title: '成功',
            message: '取消申请成功',
            type: 'success'
          });
          this.getList();
        });
    },
    //付款确认
    paymentConfirm() {
      this.$api.information.unpaidLedger
        .unPaidPayConfirm({ ids: this.list })
        .then((res) => {
          this.$notify({
            title: '成功',
            message: '付款确认成功',
            type: 'success'
          });
          this.getList();
        });
    },
    //批量备注
    handleDeduction() {
      this.title = "扣款备注";
      this.deductionVisible = true;
      this.deductionList = this.list;
    },
    cancel(value) {
      this.ImportVisible = value;
    },
    confirm(value) {
      this.ImportVisible = value;
    },
    handleCancel(type) {
      this.visible = false;
      if (type == 'cancel') return;
      this.getList();
    },
    deductionCancel(type) {
      this.deductionVisible = false;
      if (type == 'cancel') return;
      this.getList();
    },
    onSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(val) {
      this.pageNum = val;
      this.getList();
    },
  }
};
</script>

<style lang="stylus" scoped>
>>>.table-SelectedRow-bgcolor {
  .el-table__cell {
    background-color: #FACD91 !important;
  }
}
</style>