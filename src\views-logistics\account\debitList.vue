<template>
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          size="mini"
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          label-position="right"
        >
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="员工姓名"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input
              v-model.trim="searchForm.staffCode"
              size="mini"
              clearable
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenStaffCodes"
                  titleName="厂牌编号"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="借支类型:" prop="type">
            <el-select
              v-model="searchForm.type"
              clearable
              placeholder="请选择借支类型"
              @change="onSearch"
            >
              <el-option
                v-for="item in debitOptions"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="还款状态:" prop="isPay">
            <el-select
              v-model="searchForm.isPay"
              clearable
              placeholder="请还款状态"
              @change="onSearch"
            >
              <el-option
                v-for="item in isPayOptions"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary"  @click="onSearch">
            查询</el-button
          >
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置</el-button
          >
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
       <template v-slot:header-right>
          <el-button
            size="small"
            type="primary"
            @click="handleExport"
          >
            导出
          </el-button>
      </template>
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :height="maxTableHeight"
        :data="tableData"
      >
        <el-table-column
          prop="processCode"
          label="流程编号"
          width="130"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column prop="staffName" label="员工姓名" width="100" align="left">
        </el-table-column>
        <el-table-column prop="staffCode" label="厂牌编号" width="130" align="left">
        </el-table-column>
        <el-table-column prop="type" label="借支类型" width="130" align="left">
        </el-table-column>
        <el-table-column
          prop="processAmount"
          label="申请金额"
          width="100"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            {{ row.processAmount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          prop="realAmount"
          label="审定金额"
          width="100"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            {{ row.realAmount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          prop="actualAmount"
          label="实付金额"
          width="100"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            {{ row.actualAmount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          prop="payedAmount"
          label="已还款"
          width="100"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            {{ row.payedAmount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          prop="payingAmount"
          label="待还款"
          width="100"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            {{ row.payingAmount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          prop="accountingMonth"
          label="核算月份"
          width="100"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="periodAmount"
          label="还款金额"
          width="100"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            {{ row.periodAmount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          prop="isPay"
          label="还款状态"
          width="100"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="remark"
          label="备注说明"
          width="150"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column prop="updateTime" label="修改时间" align="left">
          <template slot-scope="scope">
            {{ scope.row.updateTime | dateFormat }}
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <ul>
          <li>核算工厂:{{ factoryName1 }}</li>
          <li>核算月份:{{ accountingMonth }}</li>
          <li>人数:{{ debitInfo.sysTotal }}</li>
          <li>还款金额:{{ debitInfo.paymentAmount | moneyFormat }}</li>
        </ul>
        <div style="text-align: right">
          <el-pagination
            @size-change="onSizeChange"
            @current-change="onNumChange"
            :current-page="pageNum"
            :page-size="pageSize"
            :total="total"
            :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper"
          >
          </el-pagination>
        </div>
      </template>
    </table-panel>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
export default {
  name: "LogisticsDebitList",
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      searchForm: {
        searchValue: "",
        type: "",
        isPay: "",
        staffCode: "",
        staffName: "",
      },
      factoryName: "",
      factoryName1: "",
      accountingMonth: "",
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      debitOptions: [
        {
          name: "因公借支",
          value: "1",
        },
        {
          name: "员工生活费借支",
          value: "2",
        },
        {
          name: "工伤生活费借支",
          value: "3",
        },
        {
          name: "工伤医疗费借支",
          value: "4",
        },
      ],
      isPayOptions: [
        {
          name: "未还款",
          value: "0",
        },
        {
          name: "已还款",
          value: "1",
        },
      ],
      debitInfo: {},
      factoryId: "",
      tableData: [],
      filterParam: {},
      params: {},
      loading: true,
      resizeOffset: 72,
      pageSize: 50,
      pageNum: 1,
      total: 0,
    };
  },
  watch: {
    $route: {
      handler(value) {
        if (value.path.includes("debitList")&&value.path.includes("logistics")) {
          this.debitInfo = JSON.parse(this.$Base64.decode(value.query.data));
          this.factoryName1 = this.debitInfo.factoryName;
          this.factoryId = this.debitInfo.factoryId;
          this.accountingMonth = this.debitInfo.accountingMonth;
          this.getList();
          this.getDebitList();
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    // 获取借支清单列表
    getList() {
      this.loading = true;
      this.$api.logisticsInformation.debitAccount
        .getDebitDetailAccountList({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: {
            factoryId: this.factoryId,
            accountingMonth: this.accountingMonth,
            ...this.filterParam,
            ...this.params,
          },
        })
        .then((res) => {
          this.tableData = res.data.list;
          this.total = res.data.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 获取借支台账统计
    getDebitList() {
      let params = {
        accountingMonth: this.accountingMonth,
        factoryId: this.factoryId,
        ...this.filterParam,
        ...this.params,
      };
      this.$api.logisticsInformation.debitAccount.debitListStatistics(params).then((res) => {
        if (res.code === 200) {
          this.debitInfo = res.data
            ? res.data
            : {
                paymentAmount: 0,
                sysTotal: 0,
              };
        }
      });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
      this.getDebitList();
    },
    //导出
    handleExport() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.accountingMonth,
      };
      if (Object.keys(this.filterParam).length) {
        params = {
          ...params,
          ...this.filterParam,
        };
      }
      this.$api.common
        .doExport("logisticsExportborrowinglist", { ...params, ...this.params })
        .then((res) => {
            this.$message.success("导出操作成功，请前往导出记录查看详情");
        });
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    onSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(val) {
      this.pageNum = val;
      this.getList();
    },
  },
};
</script>

<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}
ul {
  list-style: none;
  display: flex;
  padding: 0;
}

i {
  font-style: normal;
}

>>>.table-panel-footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  ul {
    display: flex;

    li {
      padding: 0 5px;
    }
  }
}
>>>.el-tabs__nav-wrap::after {
  height: 0;
}
</style>
