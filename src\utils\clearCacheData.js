export default {
  data() {
    return {
      clearCacheData: {},
    };
  },
  methods: {
    //切换tab不清除缓存,否则清除缓存
    isClearCacheData(data, list = {}) {
      if (!this.clearCacheData) {
        Object.keys(list).forEach((callBack) => callBack());
      }
      const newVlaue = JSON.parse(data);
      if (
        this.clearCacheData &&
        Object.values(this.clearCacheData).toString() !=
          Object.values(newVlaue).toString()
      ) {
        this.$nextTick(() => {
          this.resetSearchForm();
        });
      }
      this.clearCacheData = JSON.parse(data);
    },
  },
};
