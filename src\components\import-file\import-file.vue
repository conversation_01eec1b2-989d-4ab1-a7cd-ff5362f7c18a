<template>
  <q-dialog
    :visible="isVisible"
    title="导入"
    width="500px"
    :innerScroll="false"
    @close="onCancel"
    @cancel="onCancel"
    @confirm="onConfirm"
  >
    <el-form ref="uploadForm" :model="uploadForm" :rules="rules" label-width="100px">
      <el-form-item label="数据导入:" prop="file">
        <el-upload
          class="upload"
          :http-request="upload"
          action="*"
          accept=".xls,.xlsx"
          :before-upload="beforeUpload"
          :on-change="onFileChange"
          :auto-upload="false"
          ref="upload">
          <el-button size="small" type="primary">选择EXCEL</el-button>
          <div slot="tip" class="el-upload__tip">
            <el-button
              type="text"
              style="padding: 0; font-size: 14px"
              @click="downloadTemp"
            >
              下载模板
            </el-button>
            <div>只能上传xls、xlsx文件，且大小不能超过10MB</div>
          </div>
        </el-upload>
      </el-form-item>
    </el-form>
  </q-dialog>
</template>

<script>
import {SUB_APP_CODE} from '@/api/api'
import {downloadFile } from '@/utils/downloadFile'
export default {
  name: 'ImportFile',
  props: {
    // 重新对外暴露visible，控制显示隐藏
    visible: {
      type: Boolean,
      default: false,
    },
    reportName: {
      type: String,
      default: ''
    },
    importParames: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  computed: {
    // 通过计算属性，对.sync进行转换，外部也可以直接使用visible.sync
    isVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
  },
  data() {
    return {
      uploadForm: {
        activity: '',
        file: ''
      },
      rules: {
        file: [{ required: true, message: '文件不能为空', trigger: 'change' }]
      }
    }
  },
  methods: {
    beforeUpload(file) {
      const excelTypes = ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];
      if (!excelTypes.includes(file.type)) {
        this.$message.error('只能上传Excel文件');
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过 10MB!');
        return false;
      }
      return true;
    },
    onFileChange(file) {
      this.uploadForm.file = file;
      this.$refs.uploadForm.validateField('file');
    },
    upload({file}) {
      console.log('import:', file);
      const loading = this.$loading({
        lock: true,
        text: '文件导入中...',
        spinner: 'el-icon-loading'
      });
      let paramMap =  []

      for(let[key,value] of Object.entries(this.importParames)) {
        paramMap.push({fieldName: key,fieldValue:value})
      }

      const extData = {
        reportName: this.reportName,
        appCode: SUB_APP_CODE,
        paramMap,
      };
      this.$api.common.importFile(file, extData)
        .then((res) => {
          this.$notify.success({
            title: '成功',
            message: '导入数据成功'
          });
          this.onCancel();
          this.$emit('after',res.data || null);
        })
        .catch(err => {
          this.$message.error('导入数据失败，请重新导入！');
        })
        .finally(() => {
          loading.close();
        });
    },
    onCancel() {
      this.isVisible = false
    },
    onConfirm() {
      this.$refs.uploadForm.validate((valid) => {
        if (!valid) return;
        this.$refs.upload.submit();
      })
    },
    downloadTemp() {
      this.$api.importList.getTemplate({reportName: this.reportName}).then(res => {
        if (res.success) {
          const {fileName,templatePath} = res.data
          downloadFile(templatePath,fileName)
        }
      })
    },
  },
  created() {}
}
</script>

<style lang="stylus" scoped>
.upload
  >>> .el-upload__tip
    line-height 30px
    margin-top 0

</style>