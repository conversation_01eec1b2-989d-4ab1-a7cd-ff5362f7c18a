<template>
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form size="mini" :inline="true" :model="searchForm" ref="searchForm" label-width="90px" class="rangeTime"
          label-position="right">
          <el-form-item label="核算班组:" prop="groupId">
            <el-select v-model="searchForm.groupId" filterable clearable placeholder="请选择核算班组"
              @change="onChangeProcess">
              <el-option v-for="item in groupList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="员工姓名:" prop="staffName">
            <el-input clearable v-model.trim="searchForm.staffName" size="mini" @keyup.enter.native="onSearch">
              <template slot="append">
                <search-batch @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)" ref="childrenStaffNames" titleName="员工姓名" />
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input v-model.trim="searchForm.staffCode" size="mini" clearable @keyup.enter.native="onSearch">
              <template slot="append">
                <search-batch @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)" ref="childrenStaffCodes" titleName="厂牌编号" />
              </template>
            </el-input>
          </el-form-item>
        </el-form>

        <template v-slot:right>
          <el-button size="small" type="primary" @click="onSearch">查询</el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">重置</el-button>
        </template>
      </search-box>
    </template>

    <table-panel ref="tablePanel">
      <el-table stripe border v-loading="loading" ref="tableRef" highlight-current-row :height="maxTableHeight"
        :data="tableData" :key="tableKey" :row-key="getRowKey">
        <el-table-column type="index" width="50"></el-table-column>
        <el-table-column prop="staffName" label="员工姓名" width="100" align="left"></el-table-column>
        <el-table-column prop="staffCode" label="厂牌编号" width="120" align="left"></el-table-column>
        <el-table-column prop="groupName" label="核算班组" align="left"></el-table-column>
        <el-table-column prop="beforeAmount" label="调整前计件金额" align="left">
          <template slot="header">
            <span>调整前计件金额</span>
            <el-tooltip placement="top">
              <div slot="content">
                调整前计件金额=系统自动汇总金额+手工分摊金额。
              </div>
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="inAmount" label="本月划入" align="left"></el-table-column>
        <el-table-column prop="outAmount" label="本月划出" align="left"></el-table-column>
        <el-table-column prop="afterAmount" label="调整后计件金额" align="left">
           <template slot="header">
            <span>调整后计件金额</span>
            <el-tooltip placement="top">
              <div slot="content">
                调整后计件金额=调整前计件金额+本月划入（-本月划出）。
              </div>
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="reason" label="调整事由" show-overflow-tooltip align="left"></el-table-column>
        <el-table-column prop="updateTime" label="修改时间" align="left"></el-table-column>

        <el-table-column label="操作" align="left" width="235">
          <template slot-scope="scope">
            <el-button type="text" size="mini"  v-if="permission && detailInfo.nodeName == '分厂调整-未提交'" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="text" size="mini" @click="handleEditRecord(scope.row)">调整记录</el-button>
            <el-button type="text" size="mini"  v-if="permission && detailInfo.nodeName == '分厂调整-未提交'" @click="handleDel(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <template v-slot:footer>
        <div class="table_footer">
          <ul>
            <li><span>核算工厂:</span><span>{{ info.factoryName }}</span></li>
            <li><span>核算月份:</span><span>{{ info.accountingMonth }}</span></li>
            <li><span>调整前计件金额:</span><span>{{ adjustInfo.beforeAmount | moneyFormat }}</span></li>
            <li><span>本月划入:</span><span>{{ adjustInfo.inAmount | filterDataPiece }}</span></li>
            <li><span>本月划出:</span><span>{{ adjustInfo.outAmount | filterDataPiece }}</span></li>
            <li><span>调整后计件金额:</span><span>{{ adjustInfo.afterAmount | moneyFormat }}</span></li>
          </ul>
        </div>

        <div style="text-align: right">
          <el-pagination @size-change="onSizeChange" @current-change="onNumChange" :current-page="pageNum"
            :page-size="pageSize" :total="total" :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper"></el-pagination>
        </div>
      </template>
    </table-panel>
  </content-panel>
</template>

<script>
import moment from "moment";
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import { moneyFormatZh } from "@/utils";

// 常量定义
const ALLOT_METHOD = 'person';
const DEFAULT_PAGE_SIZE = 50;
const DEFAULT_PAGE_NUM = 1;

export default {
  name: "PersonFactoryAdjustment",
  mixins: [tableMixin, pagePathMixin],
  props: { 
    permission: {
      type: String|Boolean,
      required: true,
    },
    detailInfo: Object,
  },
  data() {
    return {
      searchForm: {
        staffCode: "",
        groupId: '',
        allotMethod: ALLOT_METHOD,
        staffName: "",
      },
      filterParam: {
        allotMethod: ALLOT_METHOD,
      },
      groupList: [],
      info: {}, // 详情
      adjustInfo: {},
      params: {},
      tableData: [],
      loading: false,
      resizeOffset: 55,
      pageSize: DEFAULT_PAGE_SIZE,
      pageNum: DEFAULT_PAGE_NUM,
      total: 0,
      tableKey: "",
    };
  },

  filters: {
    filterData(value) {
      return !value ? "-" : moneyFormatZh(value);
    },
    filterDataPiece(value) {
      return !value ? "0" : moneyFormatZh(value);
    }
  },

  async created() {
    await this.initializeData();
    this.onSearch();
  },

  methods: {
    // 初始化数据
    async initializeData() {
     
        this.info = JSON.parse(this.$Base64.decode(this.$route.query.data));
        await this.loadGroupList();
      
    },

    // 加载班组列表
    async loadGroupList() {
     
        const res = await this.$api.plateTypeSystemManage.getBasicPermission.getBasicGroupList(this.info.factoryId);
        this.groupList = res.data.map(item => ({
          label: item.name,
          name: item.name,
          id: item.id,
        }));
     
    },

    // 获取行键
    getRowKey(row) {
      return row.id;
    },

    // 构建API参数
    buildApiParams(additionalParams = {}) {
      return {
        accountingMonth: this.info.accountingMonth,
        factoryId: this.info.factoryId,
        allotMethod: ALLOT_METHOD,
        ...this.filterParam,
        ...this.params,
        ...additionalParams
      };
    },

    // 获取分厂调整统计
    async getDebitList() {
     
        const params = this.buildApiParams();
        const { data } = await this.$api.plateTypeWorkbench.factoryAdjustment.getFactoryAdjustmentStatistics(params);
        this.adjustInfo = data || {};
       
    },

    // 班组变更处理
    onChangeProcess() {
      this.onSearch();
    },

    // 编辑
    handleEdit(row) {
      this.$emit('handleEdit', row);
    },

    // 删除
    handleDel(row) {
      this.$emit('handleDelete', row);
    },

    // 调整记录
    handleEditRecord(row) {
      this.$emit('handleEditRecord', row);
    },

    // 获取列表
    async getList() {
      await this.getDebitList();
      this.loading = true;

      try {
        const { data: { list, total } } = await this.$api.plateTypeWorkbench.factoryAdjustment.getFactoryAdjustmentList({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: this.buildApiParams(),
        });

        this.tableData = list || [];
        this.total = total;
        this.tableKey = Math.random();
      }  
      finally {
        this.loading = false;
      }
    },

    // 重置搜索表单
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.resetBatchSearch();
      this.filterParam = { allotMethod: ALLOT_METHOD };
      this.params = {};
      this.onSearch();
    },

    // 重置批量搜索
    resetBatchSearch() {
      if (this.$refs.childrenStaffNames) {
        this.$refs.childrenStaffNames.resetFilter();
      }
      if (this.$refs.childrenStaffCodes) {
        this.$refs.childrenStaffCodes.resetFilter();
      }
    },

    // 构建过滤参数
    buildFilterParams() {
      const filterParam = { allotMethod: ALLOT_METHOD };

      for (const [key, val] of Object.entries(this.searchForm)) {
        if (this.isValidValue(val)) {
          filterParam[key] = val;
        }
      }

      return filterParam;
    },

    // 检查值是否有效
    isValidValue(val) {
      return typeof val !== "undefined" && val !== null && val !== "";
    },

    // 搜索
    onSearch(name, data) {
      this.filterParam = this.buildFilterParams();

      if (name && data && Array.isArray(data) && data.length > 0) {
        this.filterParam[name] = data;
      }

      this.pageNum = DEFAULT_PAGE_NUM;
      this.getList();
    },

    // 焦点事件处理
    focusEvent(name, data) {
      if (Array.isArray(data)) {
        this.params[name] = data;
      }
    },

    // 页面大小变更
    onSizeChange(val) {
      this.pageSize = val;
      this.pageNum = DEFAULT_PAGE_NUM;
      this.getList();
    },

    // 页码变更
    onNumChange(val) {
      this.pageNum = val;
      this.getList();
    },
  },
};
</script>

<style lang="stylus" scoped>
ul {
  list-style: none;
  display: flex;
  align-items: center;
  padding: 0;
  margin: 0;
  margin-top: 10px;

  li {
    margin-right: 10px;
  }
}

>>> .table-panel-footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .table_footer {
    overflow: auto;

    > ul {
      display: flex;
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        white-space: nowrap;
        padding: 0 10px;
      }
    }
  }
}
</style>