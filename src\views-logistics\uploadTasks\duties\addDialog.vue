<template>
  <qDialog
    :visible="visible"
    title="新增"
    :innerScroll="false"
    width="400px"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <el-form ref="addForm" inline :model="addForm" :rules="rules">
      <el-form-item
        class="staffName"
        label="核算月份:"
        prop="accountingMonth"
        label-width="130px"
      >
        {{ addForm.accountingMonth }}
      </el-form-item>
      <el-form-item label="厂牌编号:" label-width="130px" prop="staffCode">
        <el-input
          class="staffCode"
          v-model="addForm.staffCode"
          clearable
          placeholder="请输入厂牌编号"
          size="small"
          style="width: 220px"
        >
          <template slot="append">
            <el-button type="primary" @click="searchEmployee"> 查询 </el-button>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item class="staffName" label="员工姓名:" label-width="130px">
        {{ addForm.staffName }}
      </el-form-item>
      <el-form-item label="职务:" label-width="130px" prop="duty">
        <div style="display: flex; align-items: center">
          <el-select
            v-model="addForm.duty"
            placeholder="请选择绩效"
            clearable
          >
            <el-option
              v-for="item in performanceOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
          <el-tooltip
            style="margin-left: 10px"
            class="item"
            effect="dark"
            content="职务必须是以下之一:装车组长、卸车组长、配货组长、装卸大组长"
            placement="top-end"
          >
            <i class="el-icon-question"></i>
          </el-tooltip>
        </div>
      </el-form-item>
    </el-form>
  </qDialog>
</template>
<script>
import { moneyFormat, moneyDelete } from "@/utils";
export default {
  name: "editDialog",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      addForm: {
        accountingMonth: "",
      },
      rules: {
        staffCode: [
          { required: true, message: "厂牌编号不能为空", trigger: "blur" },
        ],
        duty: [{ required: true, message: "职务不能为空", trigger: "change" }],
        accountingMonth: [{ required: true, trigger: "blur" }],
      },
      dutyOptions: [],
      performanceOptions:[],
    };
  },
  created() {
    const { factoryId, accountingMonth } = JSON.parse(this.$Base64.decode(this.$route.query.data));
    this.addForm = {
      factoryId,
      accountingMonth,
    };
    this.getSelection()
  },
  methods: {
    getSelection() {
      this.$api.logisticsDataUpload.basicSalary
        .getOptionWage({ sortCode: "DutySubsidy" })
        .then((res) => {
          this.performanceOptions = (res.data || []).map((item) => {
            return {
              label: item,
              value: item,
            };
          });
        });
    },
    //查询员工信息
    searchEmployee() {
      if (!this.addForm.staffCode) {
        this.$message.warning("请输入厂牌编号");
        return;
      }
      this.$api.information.employee
        .employeeDetails({ staffCode: this.addForm.staffCode })
        .then(({ data }) => {
          this.$set(this.addForm, "staffName", data.staffName || "");
        });
    },

    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      this.$refs.addForm.validate((valid) => {
        if (!valid) return;
        this.$api.logisticsDataUpload.positionAllowance
          .addDuty({
            ...this.addForm,
          })
          .then(() => {
            this.$notify.success({
              title: "成功",
              message: "新增成功",
            });
            this.$emit("cancel", "confirm");
          });
      });
    },
  },
};
</script>

<style lang="stylus" scoped>
.staffCode {
  >>>.el-input-group__append {
    background: #24c69a;
    color: #fff;

    .el-button {
      padding: 5px 10px;
    }
  }
}
.el-form-item {
  display: flex;

  >>>.el-form-item__label {
    text-align: right;
  }

  >>>.el-form-item__content {
    width: 100%;
    margin-left: 0 !important;
  }
}
.staffName {
  >>>.el-form-item__label {
    &::before {
      content: '*';
      color: #F23D20;
      margin-right: 4px;

    }
  }
}
</style>
