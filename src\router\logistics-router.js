import Layout from "@/layout";
const logisticsRouter = [
  {
    path: "/logistics/workbench",
    component: Layout,
    redirect: "/logistics/workbench/overview",
    meta: {
      title: "工作台",
    },
    children: [
      {
        name: "LogisticsTask",
        path: "overview",
        component: () => import("@/views-logistics/overview/agencyTask/agencyTask"),
        meta: {
          title: "待办任务",
        },
      },
      {
        name: "LogisticsTaskDetails",
        path: "taskDetails",
        component: () => import("@/views-logistics/overview/taskDetails"),
        meta: {
          title: "任务明细",
        },
      },
      {
        name: "logisticsPayrollTable",
        path: "payroll",
        component: () => import("@/views-logistics/overview/payroll/payroll"),
        meta: {
          title: "工资表",
        },
      },
      {
        name: "LogisticsAdjustment",
        path: "adjustment",
        component: () => import("@/views-logistics/overview/adjustment/adjustment"),
        meta: {
          title: "分厂调整",
        },
      },
      {
        name: "LogisticsUpload",
        path: "upload",
        component: () => import("@/views-logistics/overview/upload/upload"),
        meta: {
          title: "个税扣款",
        },
      },
    ],
  },
  {
    path: "/logistics/account",
    component: Layout,
    redirect: "/logistics/account/debitLedger",
    meta: {
      title: "信息台账",
    },
    children: [
      {
        name: "LogisticsRemaining",
        path: "remaining",
        component: () => import("@/views-logistics/account/remaining/ocale/ocale"),
        meta: {
          title: "余留管理",
        },
      },
      {
        name: "LogisticsRemainingDetailed",
        path: "remainingDetailed",
        component: () => import("@/views-logistics/account/remaining/remaining"),
        meta: {
          title: "余留调整明细",
        },
      },
      {
        name: "LogisticsdebitLedger",
        path: "debitLedger",
        component: () =>
          import("@/views-logistics/account/debitLedger/debitLedger"),
        meta: {
          title: "借支台账",
        },
      },
      {
        name: "LogisticsDebitList",
        path: "debitList",
        component: () => import("@/views-logistics/account/debitList"),
        meta: {
          title: "借支清单",
        },
      },
      {
        name: "LogisticsReward",
        path: "reward",
        component: () => import("@/views-logistics/account/reward/reward"),
        meta: {
          title: "奖惩台账",
        },
      },
      {
        name: "LogisticsRewardList",
        path: "rewardList",
        component: () => import("@/views-logistics/account/rewardList"),
        meta: {
          title: "奖惩清单",
        },
      },
      {
        name: "LogisticsPayroll",
        path: "specialPayrollMgent",
        component: () =>
          import("@/views-logistics/account/payrollManagement/payrollManagement"),
        meta: {
          title: "特殊工资单管理",
        },
      },
      {
        name: "LogisticsSpecialPayroll",
        path: "specialPayroll",
        component: () => import("@/views-logistics/account/specialPayroll"),
        meta: {
          title: "特殊工资单查看",
        },
      },
      {
        name: "LogisticsCompensation",
        path: "compensation",
        component: () =>
          import("@/views-logistics/account/costCompensation/costCompensation"),
        meta: {
          title: "成本赔偿扣款台账",
        },
      },
      {
        name: "LogisticsUnpaidLedger",
        path: "unpaidLedger",
        component: () => import("@/views-logistics/account/unpaidLedger/unpaidLedger"),
        meta: {
          title: "未付款台账",
        },
      },
      {
        name: "LogisticsSalarySearch",
        path: "salarySearch",
        component: () => import("@/views-logistics/account/salarySearch"),
        meta: {
          title: "工资查询",
        },
      },
    ],
  },
  {
    path: "/logistics/uploadTasks",
    component: Layout,
    redirect: "/logistics/uploadTasks/attendance",
    meta: {
      title: "数据上传",
    },
    children: [
      {
        name: "LogisticsAttendance",
        path: "attendance",
        component: () => import("@/views-logistics/uploadTasks/attendance/attendance"),
        meta: {
          title: "员工考勤",
        },
      },
      {
        name: "LogisticsWage",
        path: "wage",
        component: () => import("@/views-logistics/uploadTasks/wage/wage"),
        meta: {
          title: "计件工资",
        },
      },
      {
        name: "LogisticsCollective",
        path: "collective",
        component: () => import("@/views-logistics/uploadTasks/collective"),
        meta: {
          title: "集体账户",
        },
      },
      {
        name: "LogisticsSubsidy",
        path: "subsidy",
        component: () => import("@/views-logistics/uploadTasks/subsidy/subsidy"),
        meta: {
          title: "其他补贴",
        },
      },
      {
        name: "LogisticsDeduction",
        path: "deduction",
        component: () => import("@/views-logistics/uploadTasks/deduction/deduction"),
        meta: {
          title: "其他扣款",
        },
      },
      {
        name: "LogisticsSecurity",
        path: "security",
        component: () => import("@/views-logistics/uploadTasks/security/security"),
        meta: {
          title: "社保扣款",
        },
      },
      {
        name: "LogisticsPuncandeduct",
        path: "puncandeduct",
        component: () => import("@/views-logistics/uploadTasks/puncandeduct/puncandeduct"),
        meta: {
          title: "未打卡扣款",
        },
      },
      {
        name: "LogisticsUnionfee",
        path: "unionfee",
        component: () => import("@/views-logistics/uploadTasks/unionfee/unionfee"),
        meta: {
          title: "工会费",
        },
      },
      {
        name: "LogisticsCompanysubsidies",
        path: "companysubsidies",
        component: () => import("@/views-logistics/uploadTasks/companysubsidies"),
        meta: {
          title: "公司补贴",
        },
      },
      {
        name: "LogisticsHousingsubsidies",
        path: "housingsubsidies",
        component: () => import("@/views-logistics/uploadTasks/housingsubsidies"),
        meta: {
          title: "住房补贴",
        },
      },
      {
        name: "LogisticsFactoryservicededuction",
        path: "factoryservicededuction",
        component: () => import("@/views-logistics/uploadTasks/factoryservicededuction"),
        meta: {
          title: "厂服扣款",
        },
      },
      {
        name: "LogisticsFactorycarddeduction",
        path: "factorycarddeduction",
        component: () => import("@/views-logistics/uploadTasks/factorycarddeduction"),
        meta: {
          title: "厂牌扣款",
        },
      },
      {
        name: "LogisticsLifefei",
        path: "lifefei",
        component: () => import("@/views-logistics/uploadTasks/lifefei"),
        meta: {
          title: "生活费",
        },
      },
      {
        name: "LogisticsPhysicalexamination",
        path: "physicalexamination",
        component: () => import("@/views-logistics/uploadTasks/physicalexamination/physicalexamination"),
        meta: {
          title: "体检费",
        },
      },
      {
        name: "LogisticsCostcompensationList",
        path: "costcompensationList",
        component: () => import("@/views-logistics/uploadTasks/costcompensationList"),
        meta: {
          title: "成本赔偿清单",
        },
      },
      {
        name: "LogisticsCostcompensation",
        path: "costcompensation",
        component: () => import("@/views-logistics/uploadTasks/costcompensation"),
        meta: {
          title: "成本赔偿",
        },
      },
      {
        name: "logisticsMiscellaneous",
        path: "miscellaneous",
        component: () => import("@/views-logistics/uploadTasks/miscellaneous-attendance"),
        meta: {
          title: "杂工考勤",
        },
      },
      {
        name: "logisticsMentorship",
        path: "mentorship",
        component: () => import("@/views-logistics/uploadTasks/mentorship-apprenticeship"),
        meta: {
          title: "师带徒补贴",
        },
      },
      {
        name: "logisticsPallet",
        path: "pallet",
        component: () => import("@/views-logistics/uploadTasks/pallet/pallet"),
        meta: {
          title: "卸草垫子补贴",
        },
      },
      {
        name: "logisticsWaterproof",
        path: "waterproof",
        component: () => import("@/views-logistics/uploadTasks/waterproof/waterproof"),
        meta: {
          title: "雨布补贴",
        },
      },
      {
        name: "logisticsHandyman",
        path: "handyman",
        component: () => import("@/views-logistics/uploadTasks/handyman/handyman"),
        meta: {
          title: "杂工计件",
        },
      },
      {
        name: "logisticsDuties",
        path: "duties",
        component: () => import("@/views-logistics/uploadTasks/duties/duties"),
        meta: {
          title: "职务补贴",
        },
      },
      {
        name: "logisticsMinimumWage",
        path: "minimumWage",
        component: () => import("@/views-logistics/uploadTasks/minimumWage/minimumWage"),
        meta: {
          title: "保底工资",
        },
      },
      {
        name: "logisticsBasicSalary",
        path: "basicSalary",
        component: () => import("@/views-logistics/uploadTasks/basicSalary/basicSalary"),
        meta: {
          title: "基本工资",
        },
      },
      {
        name: "logisticsTechnical",
        path: "technical",
        component: () => import("@/views-logistics/uploadTasks/technical/technical"),
        meta: {
          title: "技术补贴",
        },
      },
      {
        name: "logisticsExpatriate",
        path: "expatriate",
        component: () => import("@/views-logistics/uploadTasks/expatriate/expatriate"),
        meta: {
          title: "外派补贴",
        },
      },
      {
        name: "logisticsDiningLife",
        path: "diningLife",
        component: () => import("@/views-logistics/uploadTasks/diningLife"),
        meta: {
          title: "用餐生活补贴",
        },
      },
      {
        name: "logisticsFlood",
        path: "flood",
        component: () => import("@/views-logistics/uploadTasks/flood"),
        meta: {
          title: "防洪补贴",
        },
      },
      {
        name: "logisticsInternalPurchase",
        path: "internalPurchase",
        component: () => import("@/views-logistics/uploadTasks/Internal-purchase"),
        meta: {
          title: "内购补贴",
        },
      },
      {
        name: "logisticsAddGoods",
        path: "addGoods",
        component: () => import("@/views-logistics/uploadTasks/add-goods"),
        meta: {
          title: "加货补贴",
        },
      },
      {
        name: "logisticsForklift",
        path: "forklift",
        component: () => import("@/views-logistics/uploadTasks/forklift"),
        meta: {
          title: "叉车培训费扣款",
        },
      },
      {
        name: "logisticsTemperature",
        path: "temperature",
        component: () => import("@/views-logistics/uploadTasks/temperature"),
        meta: {
          title: "高温补贴",
        },
      },
      {
        name: "logisticsRisk",
        path: "risk",
        component: () => import("@/views-logistics/uploadTasks/risk"),
        meta: {
          title: "风险补贴",
        },
      },
      {
        name: "logisticsWorkOvertime",
        path: "workOvertime",
        component: () => import("@/views-logistics/uploadTasks/work-overtime"),
        meta: {
          title: "加班补贴",
        },
      },
      {
        name: "logisticsNewEmployee",
        path: "newEmployee",
        component: () => import("@/views-logistics/uploadTasks/new-employee"),
        meta: {
          title: "新员工培训补贴",
        },
      },
      {
        name: "logisticsNoPiece",
        path: "noPiece",
        component: () => import("@/views-logistics/uploadTasks/noPiece/noPiece"),
        meta: {
          title: "无计件补贴",
        },
      },
      {
        name: "logisticsAccidentRisk",
        path: "accidentRisk",
        component: () => import("@/views-logistics/uploadTasks/accident-risk/accident-risk"),
        meta: {
          title: "意外险",
        },
      },
      {
        name: "logisticsThresholdFee",
        path: "thresholdFee",
        component: () => import("@/views-logistics/uploadTasks/threshold-fee/threshold-fee"),
        meta: {
          title: "门槛费",
        },
      },
    ],
  },
  {
    path: "/logistics/system",
    component: Layout,
    redirect: "/logistics/system/dataconfiguration",
    meta: {
      title: "系统配置",
    },
    children: [
      {
        name: "logisticsPageConfig",
        path: "pageconfig",
        component: () => import("@/views-logistics/system-config/pageConfig"),
        meta: {
          title: "表头管理",
        },
      },
      {
        name: "LogisticsTaskConfig",
        path: "taskconfig",
        component: () =>
          import("@/views-logistics/system-config/taskConfig/taskConfig"),
        meta: {
          title: "任务配置",
        },
      },
      {
        name: "LogisticsConfiguration",
        path: "dataconfiguration",
        component: () =>
          import("@/views-logistics/system-config/dataConfiguration/dataConfiguration"),
        meta: {
          title: "数据配置",
        },
      },
      //物流参数配置
      {
        name: "Logisticsterconfiguration",
        path: "parameter",
        component: () =>
          import("@/views-logistics/system-config/parameter/parameter"),
        meta: {
          title: "参数配置",
        },
      },
    ],
  },
  {
    path: "/logistics/manage",
    component: Layout,
    redirect: "/logistics/manage/employee",
    meta: {
      title: "系统管理",
    },
    children: [
      {
        name: "LogisticsBasicInformation",
        path: "basic",
        component: () =>
          import("@/views-logistics/system-manage/basic-information/basic-information"),
        meta: {
          title: "基础信息",
        },
      },
      {
        name: "LogisticsEmployee",
        path: "employee",
        component: () => import("@/views-logistics/system-manage/employee/employee"),
        meta: {
          title: "员工管理",
        },
      },
      {
        name: "LogisticsDataPermission",
        path: "data",
        component: () =>
          import(
            "@/views-logistics/system-manage/data-permission/data-permission"
          ),
        meta: {
          title: "数据权限",
        },
      },
      {
        name: "LogisticsSystemLog",
        path: "systemlog",
        component: () => import("@/views-logistics/system-manage/system-log"),
        meta: {
          title: "系统日志",
        },
      },
    ],
  },
  {
    path: "/logistics/reportManager",
    component: Layout,
    redirect: "/logistics/reportManager/wageTransfer-analyze",
    children: [
      {
        name: "LogisticsSalaryDifferenceWarningForm",
        path: "salaryDifferenceWarningForm",
        component: () => import("@/views-logistics/reportManager/salaryDifferenceWarningForm/salaryDifferenceWarningForm"),
        meta: {
          title: "工资差异预警表",
        },
      }
    ],
  },
  {
    path: "/logistics/file",
    component: Layout,
    redirect: "/logistics/file/import",
    children: [
      {
        name: "LogisticsImportFile",
        path: "import",
        component: () => import("@/views-logistics/import-export/import-list"),
        meta: {
          title: "导入列表",
        },
      },
      {
        name: "logisticsExportFile",
        path: "export",
        component: () => import("@/views-logistics/import-export/export-list"),
        meta: {
          title: "导出列表",
        },
      },
    ],
  },
];
export default logisticsRouter;
