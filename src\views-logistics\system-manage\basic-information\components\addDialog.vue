<template>
  <q-dialog
    :visible="isVisible"
    :innerScroll="false"
    :title="title"
    width="500px"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel">
    <el-form :model="addForm"
      ref="addForm"
      label-width="90px"
      size="small">
      <el-form-item
        label="核算班组:" prop="groupName">
        <el-input
          v-model.trim="addForm.groupName"
          clearable
          placeholder="请输入文本">
        </el-input>
      </el-form-item>
      <el-form-item
        label="启用状态:"
        prop="enable">
        <el-switch
          inactive-color="#ff4949"
          v-model="addForm.enable"
          :active-text="addForm.enable == '1' ? '启用' : '禁用'"
          :active-value="1"
          :inactive-value="0">
        </el-switch>
      </el-form-item>
      <el-form-item
        label="备注说明:"
        label-position="top">
        <el-input
          type="textarea"
          resize="none"
          rows="5"
          maxlength="300"
          show-word-limit
          v-model.trim="addForm.remark">
        </el-input>
      </el-form-item>
    </el-form>
  </q-dialog>
</template>

<script>
export default {
  name: "addDialog",
  props: {
    isVisible: {
      type: Boolean,
      required: true
    },
    title: {
      type: String,
      required: true
    },
    editForm: Object
  },
  data() {
    return {
      addForm: {
        groupName: "",
        enable: 1,
        remark: ""
      }
    }
  },
  created() {
    this.addForm = {
      ...this.addForm,
      ...this.editForm,
      groupName:this.editForm.name
    }
  },
  methods: {
    handleCancel() {
      this.$emit('cancel', 'cancel')
    },
    handleConfirm() {
      if (this.title == "新增") {
        this.$api.logisticsSystemManage.getBasicPermission.addGroup({ factoryId: this.addForm.factoryId, ...this.addForm }).then(({ success }) => {
          if (success)
            this.$notify({
              title: '成功',
              message: '新增成功',
              type: 'success'
            });
          this.$emit('cancel', 'cancel')
          this.$bus.$emit('logistivsAddCancel', 'confirm')
        })
      } else {
        this.$api.logisticsSystemManage.getBasicPermission.editGroup(this.addForm).then(({ success }) => {
          if (success)
            this.$notify({
              title: '成功',
              message: '修改成功',
              type: 'success'
            });
          this.$emit('cancel', 'cancel')
          this.$bus.$emit('logistivsAddCancel', 'confirm')
        })
      }
    }
  }
}
</script>

<style lang="stylus" scoped>
>>>.el-textarea__inner {
  padding-right: 50px;
}

.el-textarea {
  >>>.el-input__count {
    right: 20px !important;
  }
}
</style>
