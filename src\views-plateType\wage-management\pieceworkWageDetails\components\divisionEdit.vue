<template>
  <qDialog
    :visible="isVisible"
    :innerScroll="true"
    :isAuto="true"
    :title="title"
    width="850px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <el-form
      :model="calculateForm"
      ref="calculateForm"
      label-width="120px"
      :rules="rules"
      size="small"
      :key="upKey"
    >  
      <template>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="核算工厂:" prop="factoryId">
              <el-select
                v-model="calculateForm.factoryId"
                filterable
                clearable
                 @change="onChangeFactory"
                placeholder="请选择核算工厂"
              >
               <el-option v-for="item in tabList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
             <el-form-item label="核算月份:" prop="accountingMonth">
              <el-date-picker :clearable="false" v-model="calculateForm.accountingMonth" value-format="yyyy-MM" type="month"
              placeholder="选择月份" clearable>
            </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="大工序编码:" prop="bigProcessCode">
              <el-select
                v-model="calculateForm.bigProcessCode"
                filterable
                @change="onChangeBigProcess"
                clearable
                placeholder="请选择大工序编码"
              >
               <el-option v-for="item in process.bigProcessList" :key="item.id" :label="item.code" :value="item.code">
              </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="小工序名称:" prop="smallProcessName">
              <el-select
                v-model="calculateForm.smallProcessName"
                filterable
                clearable
                 @change="onChangeProcess"
                placeholder="请选择小工序名称"
              >
               <el-option v-for="item in  processList" :key="item.id" :label="item.name" :value="item.name">
              </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="小工序编码:" prop="smallProcessNameCode">
              {{ calculateForm.smallProcessNameCode }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="金额:"
              class="processCode"
              prop="amount"
            >
              {{ calculateForm.amount }}
            </el-form-item>
          </el-col>
        </el-row> 
           
        <el-row>
          <el-col :span="24">
            <el-form-item label="金额划分详情:" prop="processAumont">
              <span style="color: #afafaf; line-height: 40px">
                <span style="padding-right: 165px">实际分配班组</span>
                <span style="padding-right: 145px">实际分配金额</span>
                <span style="color: #000;">实际分配金额汇总:{{ totalAmount }}</span>
              </span>
              <div
                class="details"
                v-for="(item, index) in details"
                :key="item.date"
              >
                <div class="details_content">
                  <el-select 
                    v-model="item.groupId"
                    placeholder="请选择实际分配班组"
                    filterable
                  >
                    <el-option
                      v-for="item in groupList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                       :disabled="isGroupSelected(item.id,index)"
                    >
                    </el-option>
                  </el-select>  
                  <el-input  
                    class="repaymentAmount"
                    v-model.trim="item.allotAmount"
                    placeholder="请输入金额"
                    oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+5)}"
                    @blur="onStageBlur(index, item)"
                  >
                  <template slot="append">元</template>
                  </el-input>
                </div>
                <div class="details_btn">
                  <el-button
                    type="text"
                    @click="handleAdd(item,index)" 
                  >
                    增加
                  </el-button>
                  <el-button
                    v-show="details.length > 1 && index != 0"
                    type="text"
                    @click="handleDelete(item, index)"
                  >
                    删除
                  </el-button>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

      
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注说明:" prop="remarks">
              <el-input
                type="textarea"
                v-model="calculateForm.remarks"
                resize="none"
                rows="3"
                show-word-limit
                maxlength="100"
                placeholder="请输入备注说明"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row> 
      </template>
    </el-form>
  </qDialog>
</template>

<script>
import moment from "moment";
import { moneyDelete } from "@/utils";
import NP from "number-precision";
NP.enableBoundaryChecking(false);
export default {
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    factoryList:Array,
    title: {
      type: String,
      required: true,
    },
    editForm: Object,
  }, 
  data() {
    return {
      upKey: 0, 
      details: [],  
      groupList: [],
      factoryCode: "", //核算工厂编码
      calculateForm: {
        bigProcessCode: "", //大工序编码
        factoryId: "", //核算工厂
        accountingMonth:  moment().subtract(1, "month").format("YYYY-MM"),//核算月份
        smallProcessNameCode:"",//小工序编码
        smallProcessName:"",//小工序名称
        amount:"",//金额
        remarks: "",//备注说明
        processAumont:""
      },  
      fileList: [],
      filterParam: {},
      rules: {
        factoryId: [
          { required: true, message: "请选择核算工厂", trigger: "change" },
        ],

        accountingMonth: [
          { required: true, message: "请选择核算月份", trigger: "change" },
        ],
        bigProcessCode: [
          { required: true, message: "请选择大工序编码", trigger: "change" },
        ],
        smallProcessName: [
          { required: true, message: "请选择小工序名称", trigger: "change" },
        ],
        processAumont: [
          { required: true, message: "金额划分异常", trigger: "blur" }
        ],
      }, 
      tabList: [],
      smallProcess:{},//数据
      processList:[],//小工序
      totalAmount:"",
      isLoading: false,  
    };
  },
  async created() {  
    
  },
  watch:{
    isVisible: {
        handler(newVal) {
          if (newVal) {
              
             this.tabList = this.factoryList 
              this.calculateForm={
                ...this.calculateForm,
                ...this.editForm
              }
              this.$api.plateTypeSystemManage.getBasicPermission.getBasicGroupList(this.calculateForm.factoryId).then((res) => {
                this.groupList = res.data.map((item) => ({
                  label: item.name,
                  name: item.name,
                  id: item.id,
                }));
                  this.details = this.calculateForm.details || []
                  let total = this.details.reduce(function (pre, cur, index, arr) {
      
                    return NP.plus(pre, Number(moneyDelete(cur.allotAmount)));
                  }, 0);  
                  if(this.details.length){
                      this.calculateForm.processAumont=1
                  }
                  this.totalAmount=total 
                 if(!this.details.length){ 
                   this.deductDetail();
                   return
                 }
                  
              });
              
          }
        },
        immediate: true,
      },
  },
  computed: {
    process() {
      return {
        processList:
          (this.calculateForm.factoryId &&
            this.tabList.find((item) => item.id == this.calculateForm.factoryId)
              .process) ||
          [],
        bigProcessList:
          (this.calculateForm.factoryId &&
            this.tabList.find((item) => item.id == this.calculateForm.factoryId)
              .bigProcess) ||
          [],
      };
    },
  },

  methods: {  
      //判断班组是否已被选择
    isGroupSelected(groupId,currentIndex){
      return this.details.some((item,index)=>{
        return item.groupId===groupId && index!==currentIndex
      })
    },
    //统计
    total(arr) {
      let total = arr.reduce(function (pre, cur, index, arr) {
        if (index === 0) {
          return pre + 0;
        }
        return NP.plus(pre, Number(moneyDelete(cur.repaymentAmount)));
      }, 0);
      return total;
    }, 
    //切换工厂
    onChangeFactory(val){
      let factory=  this.tabList.find((item) => item.id == val) 
      this.factoryCode=factory.factoryCode 
       this.$api.plateTypeSystemManage.getBasicPermission.getBasicGroupList(val).then((res) => {
        this.groupList = res.data.map((item) => ({
          label: item.name,
          name: item.name,
          id: item.id,
        }));
      });
    },
    //切换大工序
    onChangeBigProcess(val){
      this.processList=[]
      this.$api.plateTypeSystemManage.getBasicPermission.getlistMesProcessByCode({
        bigProcessCode:val,
        factoryCode:this.factoryCode,
      }).then((res) => {
        this.processList = res.data || [];
      });
    },
    //切换小工序
    onChangeProcess(val){ 
      this.$api.plateTypePieceWageSystem.getBigProcess({
        bigProcessCode:this.calculateForm.bigProcessCode,
        factoryId:this.calculateForm.factoryId,
        smallProcessName:val,
        accountingMonth:this.calculateForm.accountingMonth,
      }).then(({data}) => { 
        if(data&&data.amount){
          this.smallProcess=data
          this.calculateForm.amount=data.amount
          this.calculateForm.smallProcessNameCode=data.smallProcessCode
        }
      });
    }, 
    //增加
    handleAdd(item,index) { 
      if(!item.allotAmount){
       this.$message({
          message: "核算班组金额不能为空",
          type: "warning",
        });
        return
      }
      this.details.push({ 
        allotAmount: "",
        groupId: "",
      });
     
    },
  
    handleDelete(v, value) { 
      this.details.splice(value, 1);
    }, 
    onStageBlur(num, it) {  
    
      if (this.checkAmount(it.allotAmount)) return; 
       
      if (Number(it.allotAmount) > Number(this.calculateForm.amount)) {
        this.$message.error("实际分配金额不能大于计件金额");
        it.allotAmount = "";
        return;
      }
      let total = this.details.reduce(function (pre, cur, index, arr) {
        
        return NP.plus(pre, Number(moneyDelete(cur.allotAmount)));
      }, 0); 
 
      if (Number(total) > Number(this.calculateForm.amount)) {
        this.$message.error("实际分配金额汇总不能大于计件金额");
        it.allotAmount = "";
        return;
      }
      it.allotAmount=Number(moneyDelete(it.allotAmount))
      this.calculateForm.processAumont=it.allotAmount
      this.totalAmount=total
    },
    //校验金额
    checkAmount(value) { 
      let flag = false;
      let amount = moneyDelete(value);
      if (!amount) {
        this.$message({
          message: "分配金额不能为空",
          type: "warning",
        });
        flag = true;
      }
   
      if (!flag) {
        if (!/^-?\d{1,10}(\.\d{1,4})?$/.test(amount)) {
          this.$message({
            message: "分配金额仅支持小数点前面仅支持10位数,小数点后面仅支持4位数",
            type: "warning",
          });
          flag = true;
        }
      }
      return flag;
    },
    //成本扣款台账详情
    async deductDetail() {  
       
      // 一进来给这个数组赋值一条默认值数据
      this.details = new Array(1).fill().map(() => {
        return { 
          allotAmount: "",
          groupId: "",
        };
      });
 
    },
    
 
    handleCancel() { 
      this.$emit("cancel", {
        type: "cancel",
        isVisible: false, 
      });
    },
    handleConfirm() { 
      let total= this.totalAmount || 0
      let amounts= this.calculateForm.amount  || 0 
      if (Number(total).toFixed(5) != Number(amounts).toFixed(5)) {
        this.$message.error("分配金额和计件金额不一致，请调整后再操作"); 
        return;
      }
      this.$refs.calculateForm.validate((valid) => { 
        if (!valid) return;
        this.isLoading = true;
        for (const [key, val] of Object.entries(this.calculateForm)) {
          if (typeof val !== "undefined" && val !== null && val !== "") {
            this.filterParam[key] = val;
          }
        }
        
        let params = {
          accountingMonth:this.calculateForm.accountingMonth,
          bigProcessCode:this.calculateForm.bigProcessCode,
          factoryId:this.calculateForm.factoryId,
          smallProcessName:this.calculateForm.smallProcessName,
          remarks:this.calculateForm.remarks,
          details:this.details,
        };
        console.log(params,'数据')
        let fullApi =
          this.title == "新增"
            ? "addMesPieceWageDivision"
            : "editMesPieceWageDivision";
        let msg = this.title == "新增" ? "新增成功" : "编辑成功";
        this.$api.plateTypePieceWageSystem[fullApi](params)
          .then(() => {
            this.$notify({
              title: "成功",
              message: msg,
              type: "success",
            });
            this.$emit("cancel", "confirm");
          })
          .finally(() => {
            this.isLoading = false;
          });
      });
    }, 
  },
};
</script>

<style lang="stylus" scoped>
>>>.el-form-item__label {
  text-align: right;
}

.processCode {
  >>>.el-form-item__label {
    &::before {
      content: '*';
      color: #F23D20;
      margin-right: 4px;
      visibility: hidden;
    }
  }
}

>>>.el-input-group__append {
  background: #24c69a;
  color: #fff;

  .el-button {
    padding: 5px 10px;
  }
}
 

.el-col {
  padding-right: 10px;
}
.details_btn{
  margin-left:20px
}
.el-upload__tip {
  padding-left: 10px;
  display: inline-block;
  color: #24c69a;
}

>>>.el-textarea__inner {
  padding-right: 50px;
}

.el-textarea {
  >>>.el-input__count {
    right: 20px !important;
  }
}

.details {
  display: flex;
  padding-bottom: 18px;

  &_content { 
    display: flex; 

    >.el-select, >.el-input, >span {
    
    }
  }

  &_btn {
    width: 20%;

    >.el-button {
      mar;
    }
  }
}

.deduction {
  width: 100%;
  margin: 0;
  padding: 0;

  li {
    span {
      padding-right: 10px;
    }
  }
}
 .details_content{
  >>>.el-select{
      width:200px; 
      margin-right:50px
  }
  >>>.el-input{
    width:200px
  }
  >>>.el-input__inner {
     width:200px; 
  }
 }
</style>
