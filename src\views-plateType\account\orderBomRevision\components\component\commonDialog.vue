<template>
  <qDialog
    :visible="isVisible"
    :innerScroll="true"
    :innerHeight="80"
    :title="title"
    width="400px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <p v-if="title == '批量核算'">
      是否批量核算当前选中内容，设定在执行月份中全部还清？
    </p>
    <p v-if="title == '批量退回'">是否确认批量退回?</p>
    <p v-if="title == '批量删除'">是否确认批量删除?</p>
    <p v-if="auditStatus == '1'&&title !== '批量退回'">是否确认退回</p>
    <p v-if="title == '批量退回'|| title =='退回'">退回后,已处理内容会被删除</p>
  </qDialog>
</template>

<script>
export default {
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    info: [Object],
    filterParam: {
      type: Object,
      required: {},
    },
    selectall: {
      type: Boolean,
      required: false,
    },
    auditStatus:{
      type:String,
      required: true,
    }
  },
  data() {
    return {
      isLoading: false,
    };
  },
  methods: {
    handleCancel() {
      this.$emit("cancel", {
        type: "cancel",
        isVisible: false,
      });
    },
    handleConfirm() {
      let params = {
        ids: this.info.idList,
        isAll: 0,
      };
      if (this.selectall) {
        //全选
        params = {
          isAll: 1,
          ids: this.info.idList,
          ...this.filterParam
        };
      } 
      this.isLoading = true; 
      switch (this.title) {
        case "批量核算":
          this.$api.plateTypeInformation.orderBomRevision
            .orderBomRevisionBatchAccounting({...params,isAccounting:1})
            .then(({ data }) => {
              this.$notify.success({
                title: "成功",
                message: data,
              });
              this.$emit("cancel", {
                type: "confirm",
                isVisible: false,
              });
            })
            .finally(() => {
              this.isLoading = false;
            });
          break;
        case "退回": 
          this.$api.plateTypeInformation.orderBomRevision.orderBomRevisionReturn(this.info.id)
            .then(({ data }) => {
              if (!data) {
                this.$notify.success({
                  title: "成功",
                  message: "退回成功",
                });
                this.$emit("cancel", {
                  type: "confirm",
                  isVisible: false,
                });
              } else {
                this.$emit("cancel", {
                  type: "cancel",
                  isVisible: false,
                });
              }
            })
            .finally(() => {
              this.isLoading = false;
            });
          break;

        case "批量退回": 
          this.$api.plateTypeInformation.orderBomRevision
            .orderBomRevisionBatchReturn({...params,isAccounting:0})
            .then(({ data }) => {
              this.$notify.success({
                title: "成功",
                message: data,
              });
              this.$emit("cancel", {
                type: "confirm",
                isVisible: false,
              });
            })
            .finally(() => {
              this.isLoading = false;
            });
          break;
            //批量删除
        default:
          this.$api.plateTypeInformation.orderBomRevision
            .orderBomRevisionBatchDel({...params,isAccounting:0})
            .then(({ data }) => {
              this.$notify.success({
                title: "成功",
                message: data,
              });
              this.$emit("cancel", {
                type: "confirm",
                isVisible: false,
              });
            })
            .finally(() => {
              this.isLoading = false;
            });
          break;
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
