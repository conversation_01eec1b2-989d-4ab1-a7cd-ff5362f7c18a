<template>
  <content-panel>
    <!-- 返工扣款 -->
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          size="mini"
          :inline="true"
          :model="searchForm"
          ref="searchForm"
        >
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="员工姓名"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input
              v-model.trim="searchForm.staffCode"
              size="mini"
              clearable
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenStaffCodes"
                  titleName="厂牌编号"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="核算班组:" prop="groupId">
            <el-select
              v-model="searchForm.groupId"
              placeholder=""
              filterable
              clearable
              @change="onSearch"
              @clear="onSearch"
            >
              <el-option
                v-for="item in teamOptinon"
                :key="item.name"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="扣款状态:" prop="isPay">
            <el-select
              v-model="searchForm.isPay"
              filterable
              clearable
              placeholder="请选择扣款状态"
              @change="onSearch"
            >
              <el-option
                v-for="item in deductstatus"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="考勤状态:" prop="attendStatus">
            <el-select
              v-model="searchForm.attendStatus"
              filterable
              clearable
              placeholder="请选择考勤状态"
              @change="onSearch"
            >
              <el-option
                v-for="item in attendancestatus"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button
            size="small"
            type="primary"

            @click="onSearch"
          >
            查询
          </el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-left>
        <ul>
          <li>
            <span>核算工厂:</span><span>{{ info.factoryName }}</span>
          </li>
          <li>
            <span>核算月份:</span
            ><span>{{ reworkdeductionInfo.accountingMonth }}</span>
          </li>
          <li>
            <span>人数:</span>
            <span>{{ reworkdeductionInfo.people }}</span>
          </li>

          <li>
            <span>扣款金额:</span>
            <span>{{ reworkdeductionInfo.amount }}</span>
          </li>
          <li>
              <el-tooltip placement="top">
                <div slot="content">
                  仅统计考勤状态正常的返工扣款数据。
                </div>
                <i class="el-icon-question"></i>
              </el-tooltip>
          </li>
        </ul>
      </template>
      <template
        v-slot:header-right
        v-if="info.status != '待提交' || info.roleName != '分厂文员'"
      >
        <!-- <template v-if="info.status != '已完成'"> -->
          <template v-if="permission">
          <el-button size="small" type="primary" @click="handleUpdate"
            >同步更新</el-button
          >
          <el-button size="small" type="primary" @click="handleAdd">
            新增</el-button
          >
          <el-button size="small" type="primary" @click="handleImport">
            导入</el-button
          >
          <el-button size="small" type="primary" @click="batchDelete">
            批量删除</el-button
          >
        </template>
        <el-button size="small" type="primary" @click="handleExport">
          导出</el-button
        >
      </template>
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :height="maxTableHeight"
        :data="tableData"
        @selection-change="handleSelectionChange"
        :row-key="getRowKeys"
      >
        <!-- :reserve-selection="true" -->
        <el-table-column width="40" type="selection"></el-table-column>
        <el-table-column
          prop="staffName"
          label="员工姓名"
          min-width
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="staffCode"
          label="厂牌编号"
          min-width
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="groupName"
          label="核算班组"
          min-width
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="deductAmount"
          label="扣款金额"
          min-width
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="考勤状态"
          min-width
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <span :style="{ color: row.attendStatus == '异常' ? 'red' : '' }">{{
              row.attendStatus
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="扣款状态"
          min-width
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <span :style="{ color: row.isPay == '未扣款' ? 'red' : '' }">{{
              row.isPay
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="remarks"
          label="备注说明"
          min-width
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="updateTime"
          label="修改时间"
          min-width
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="操作"
          width="150"
          fixed="right"
          v-if="permission"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              @click="handleEdit(scope.$index, scope.row)"
            >
              编辑</el-button
            >

            <el-button
              slot="reference"
              type="text"
              size="mini"
              @click="handleDelete(scope.row)"
            >
              删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <el-pagination
          @size-change="onSizeChange"
          @current-change="onNumChange"
          :current-page="pageNum"
          :page-size="pageSize"
          :total="total"
          :page-sizes="[50, 100, 200]"
          layout="total, sizes, prev, pager, next, jumper"
        >
        </el-pagination>
      </template>
    </table-panel>
    <add-dialog
      v-if="visible"
      :visible="visible"
      :title="title"
      :editForm="editForm"
      @cancel="handleCancel"
      :info="info"
    ></add-dialog>
    <importDialog
      :showdownload="true"
      v-if="ImportVisible"
      :importInfo="importInfo"
      :visible.sync="ImportVisible"
      @after="onSearch"
    />
    <!-- <Import
      :visible="ImportVisible"
      @cancel="cancel"
      @confirm="confirm"
      :importInfo="importInfo"
    /> -->
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import addDialog from "./addDialog";
import importDialog from "./ImportOffline";
export default {
  name: "Reworkdeduction",
  mixins: [tableMixin,pagePathMixin],
  components: {
    addDialog,
    importDialog,
  },
  data() {
    return {
      searchForm: {
        staffCode: "",
        staffName: "",
        groupName: "",
        isPay: "",
        attendStatus: "",
        groupId: "",
      },
      reworkdeductionInfo: {},
      info: {},
      factoryId: "",
      tableData: [],
      loading: false,
      resizeOffset: 53,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      permission: "",
      title: "",
      visible: false,
      filterParam: {},
      params: {},
      ImportVisible: false,
      importInfo: {},
      editForm: {},
      selection: [],
      teamOptinon: [],
      deductstatus: [
        { label: "全部", value: "" },
        { label: "已扣款", value: "1" },
        { label: "未扣款", value: "0" },
      ],
      attendancestatus: [
        { label: "全部", value: "" },
        { label: "正常", value: "normal" },
        { label: "异常", value: "abnormal" },
      ],
    };
  },
  watch: {
    $route: {
      handler(value) {
        if (
          value.path.includes("reworkdeduction") &&
          value.path.includes("customized")
        ) {
          this.info = JSON.parse(this.$Base64.decode(value.query.data));
          this.factoryId = this.info.factoryId;
          this.importInfo = {
            reportName: "reworkCutImport",
            paramMap: {
              columnValue: "返工扣款",
              factoryId: this.factoryId,
              accountingMonth: this.info.accountingMonth,
              id: this.info.id,
            },
            title: "导入",
          };
          this.getList();
          this.getStatistic();
          this.getPermission();
          this.getAvailableGroups();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    handleSelectionChange(selection) {
      this.selection = selection;
    },
    //获取返工扣款列表
    getList() {
      this.loading = true;
      this.$api.dataUpload.reworkCut
        .getReworkCut({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: {
            factoryId: this.factoryId,
            accountingMonth: this.info.accountingMonth,
            ...this.filterParam,
            ...this.params,
          },
        })
        .then(({ data: { list, total }, success }) => {
          if (success) {
            this.tableData = list;
            this.total = total;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //是否具有代办任务操作权限
    getPermission() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.info.accountingMonth,
        type: "22",
      };
      this.$api.workbench.agencyPermission(params).then((res) => {
        this.permission = res.data;
      });
    },
    //获取统计信息
    getStatistic() {
      this.$api.dataUpload.reworkCut
        .reworkCutStatistic({
          accountingMonth: this.info.accountingMonth,
          factoryId: this.factoryId,
          ...this.filterParam,
          ...this.params,
        })
        .then(({ success, data }) => {
          console.log("data", data);

          if (success) {
            this.reworkdeductionInfo = data || {};
          }
        });
    },
    //根据工厂和核算月份查询班组列表
    getAvailableGroups() {
      this.$api.dataUpload.employeeAttendance
        .getGroups({
          factoryId: this.factoryId,
          accountingMonth: this.info.accountingMonth,
        })
        .then(({ data }) => {
          this.teamOptinon = data;
        });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0)
          this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
      this.getStatistic();
    },
    //刷新
    handleUpdate() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.info.accountingMonth,
      };
      this.$api.dataUpload.reworkCut
        .checkRefresh(params)
        .then((res) => {
          if (res.data) {
            this.$confirm("返工扣款数据有变动，请确认是否刷新", "刷新", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }).then(() => {
              this.confirmRefresk();
            });
          } else {
            this.confirmRefresk();
          }
        })
        .catch((res) => {
          this.$message.error(res.message);
        });
    },
    confirmRefresk() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.info.accountingMonth,
      };
      this.$api.dataUpload.reworkCut.getRerefresh(params).then((res) => {
        if (res.code == 200) {
          this.$message.success("刷新成功");
          this.getList();
          this.getStatistic();
        }
      });
    },
    //新增
    handleAdd() {
      this.title = "新增";
      this.visible = true;
      this.editForm = {};
    },
    // 删除
    handleDelete(row) {
      let params = {
        factoryId: row.factoryId,
        accountingMonth: row.accountingMonth,
        staffCode: row.staffCode,
        id: row.id,
      };
      this.$confirm("此操作将永久删除该条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$api.dataUpload.reworkCut.getRedelete(params).then((res) => {
            this.$message({
              type: "success",
              message: "删除成功!",
            });
            this.getList();
            this.getStatistic();
          });
        })
        .catch(() => {});
    },
    getRowKeys(row) {
      return row.id;
    },
    // 批量删除
    batchDelete() {
      if (!this.selection.length) {
        this.$message({
          message: "请先勾选需要批量删除的内容",
          type: "warning",
        });
        return;
      }
      let params = {
        ids: this.selection.map((item) => item.id),
        factoryId: this.factoryId,
        accountingMonth: this.info.accountingMonth,
      };
      this.$confirm(
        `确认要删除选中数据吗？确认后，物理删除当前数据。`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          this.$api.dataUpload.reworkCut
            .getRedelete(params)
            .then((res) => {
              this.$notify.success({
                title: "成功",
                message: "批量删除成功",
              });
              this.getList();
              this.getStatistic();
            })
            .finally(() => {
              this.loading = false;
            });
        })
        .catch(() => {});
    },
    // 编辑
    handleEdit(index, row) {
      let list = [];
      Object.keys(row).forEach((key) => {
        if (key.includes("field")) {
          list.push({ [key]: row[key] });
        }
      });
      this.title = "编辑";
      this.visible = true;
      this.editForm = {
        staffCode: row.staffCode || "",
        staffName: row.staffName || "",
        accountingMonth: row.accountingMonth || "",
        groupId: row.groupId || "",
        accountingMonth: row.accountingMonth || "",
        deductAmount: row.deductAmount,
        id: row.id,
        remarks: row.remarks || "",
      };
    },
    // 导出
    handleExport() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.info.accountingMonth,
        ...this.filterParam,
      };
      this.$api.common.doExport("exportRewordCut", params).then((res) => {
        if (res.code == 200) {
          this.$message.success("导出操作成功，请前往导出记录查看详情");
        }
      });
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    //导入
    handleImport() {
      sessionStorage.setItem("tableInfo", JSON.stringify(this.tableInfo));
      this.title = "导入";
      this.ImportVisible = true;
    },
    handleCancel(type) {
      this.visible = false;
      if (type == "cancel") return;
      this.getList();
      this.getStatistic();
    },
    onSizeChange(pageSize) {
      this.pageSize = pageSize;
      // 切换每页条数后从第一页查询
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    cancel(value) {
      this.ImportVisible = value;
    },
    confirm(value) {
      this.ImportVisible = value;
    },
  },
};
</script>

<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
  }
  ul {
     height: 34px;
     list-style: none;
     display: flex;
     align-items: center;
     padding: 0;
     margin: 0

      li {
        margin-right: 10px;
      }
    }

>>>.el-tabs__nav-wrap::after {
  height: 0;
}

ul {
  list-style: none;
  display: flex;
  padding: 0;
}

i {
  font-style: normal;
}
</style>
