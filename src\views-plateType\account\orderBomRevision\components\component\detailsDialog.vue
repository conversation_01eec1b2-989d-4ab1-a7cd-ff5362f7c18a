<template>
  <qDialog
    :visible="visible"
    :innerScroll="true"
    :innerHeight="500"
    title="查看详情"
    width="900px"
    :showFooter="false"
    :before-close="handleCancel"
  >
    <el-form   label-width="115px" size="small">
      <el-row>
        <el-col :span="12">
          <el-form-item label="是否核算:">
          {{ formData.isAccounting*1 == 0 ? "否":"是" }}
          </el-form-item>
        </el-col> 
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="流程编号:">
            {{ formData.processCode }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="processCode" label="工厂名称:">
            {{ formData.factoryName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="厂牌编号:">
            {{ formData.staffCode }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="员工姓名:">
            {{ formData.staffName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="执行年月:">
            {{ formData.accountingMonth }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="申请日期:">
            {{ formData.applyDate | dateFormats}}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="核算工序:">
            {{ formData.processName }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="分配方式:">
            {{ formData.allotMethod }}
          </el-form-item>
        </el-col>
      </el-row>
         <el-row>
        <el-col :span="12">
          <el-form-item label="类型:">
            {{ formData.type }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="金额:">
            {{ formData.amount }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="实际核算工厂:">
            {{ formData.actualFactoryName  }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="实际核算工序:">
            {{ formData.actualProcessName }}
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="实际执行年月:">
           {{ formData.actualAccountingMonth | shortDate }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item   label="实际核算厂牌:">
            {{ formData.actualStaffCode  }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="实际增扣金额:">
            {{ formData.actualAmount | moneyFormat }}
          </el-form-item>
        </el-col>

      </el-row>  
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注说明:" prop="remark">
            <span>{{ formData.remarks }}</span>
          </el-form-item>
        </el-col>
      </el-row> 
    </el-form>
  </qDialog>
</template>

<script>
export default {
  name: "detailsDialog",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    formData: Object,
  },
  data() {
    return {
     
    };
  }, 
  methods: { 
    handleCancel() {
      this.$emit("cancel");
    },
  },
};
</script>

<style lang="stylus" scoped>
.deduction {
  width: 100%;
  margin: 0;
  padding: 0;

  li {
    span {
      padding-right: 10px;
    }
  }
}
</style>
