import Cookies from 'js-cookie'

export const TokenKey = process.env.BUILD_ENV === 'prod' ? 'iopToken' : 'iopTokenDev';
export const domain = process.env.BUILD_ENV === 'local' ? '' : '.quanyou.com.cn';

export function getToken() {
  const token = (Cookies.get(TokenKey) || '');
  return token;
}

export function setToken(token) {
  token && Cookies.set(TokenKey, token, {
    path: '/',
    domain: domain
  })
}

export function removeToken() {
  Cookies.remove(TokenKey, {
    path: '/',
    domain: domain
  })
}

export function hasToken() {
  const token = Cookies.get(TokenKey);
  return token && token !== 'undefined' && token !== 'null';
}
