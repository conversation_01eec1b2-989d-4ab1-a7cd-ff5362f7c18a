<template>
  <div class="dialog-wrap">
    <el-dialog 
      :visible.sync='isVisible'
      v-bind="$attrs"
      v-on="$listeners"
      :close-on-press-escape="false" 
      :close-on-click-modal="false">
      <slot name="fixed"></slot>
      <div v-if="innerScroll" class="scorll-wrap" :style="{height:isAuto?'auto': innerScrollHeight}">
        <el-scrollbar class="dialog-scrollbar" wrap-class="dialog-scroll-wrap">
            <!-- 内容区默认插槽 -->
            <div class="dialog-content ">
              <slot></slot>
            </div>
        </el-scrollbar>
      </div>
      <!-- 内容区默认插槽 -->
      <div v-else class="dialog-content">
        <slot></slot>
      </div>
      
      <!-- el-dialog的footer插槽 -->
      <template #footer v-if="showFooter">
        <!-- 对外重新暴露footer插槽，使其仍可自定义 -->
        <slot name="footer">
          <span slot="footer" class="dialog-footer">
            <el-button @click="handleCancel" v-if="isShowCancelBtn">取 消</el-button>
            <el-button :loading="isLoading" type="primary" @click="handleConfirm">确 定</el-button>
          </span>
        </slot>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { debounce } from '@/utils'
export default {
  name: 'qDialog',
  //默认情况下父作用域的不被认作 props 的 attribute 绑定 (attribute bindings) 
  //将会“回退”且作为普通的 HTML attribute 应用在子组件的根元素上。
  //通过设置 inheritAttrs 到 false，这些默认行为将会被去掉
  inheritAttrs: false,
  props: {
    // 重新对外暴露visible，控制显示隐藏
    visible: {
      type: Boolean,
      default: false
    },
    showFooter: {
      type: Boolean,
      default: true
    },
    // dialog内部是否需要滚动
    innerScroll: {
      type: Boolean,
      default: true
    },
    // dialog内部是否需要滚动
    isLoading: {
      type: Boolean,
      default: false
    },
    // 滚动区域高度
    innerHeight: {
      type: Number
    },
    // 内容区外的高度，如标题，底部按钮，及空白，非精确值
    offsetHeight: {
      type: Number,
      default: 190
    },
    //是否显示取消按钮
    isShowCancelBtn:{
      type: Boolean,
      default: true
    },
    isAuto:{
      type: Boolean,
      default: false
    }
  },
  computed: {
    // 通过计算属性，对.sync进行转换，外部也可以直接使用visible.sync
    isVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    }
  },
  data() {
    return {
      innerScrollHeight: this.innerHeight + 'px' || 'auto',
      debounceFn: null
    };
  },
  methods: {
    handleCancel() {
      this.$emit('cancel');
    },
    handleConfirm() {
      this.$emit('confirm');
    },
    getScrollHeight() {
      // 内容区外的高度，如标题，底部按钮，及空白，非精确值
      const HEIGHT_EXCEPT_CONTENT = 190;
      return document.body.clientHeight - this.offsetHeight;
    },
    updateScrollHeight() {
      // 设置了固定高度时，不自动调整
      if (!this.innerHeight) {
        this.innerScrollHeight = this.getScrollHeight() + 'px';
      }
    },
    listenResize() {
      if (this.innerScroll) {
        this.debounceFn = debounce(this.updateScrollHeight, 20);
        window.addEventListener('resize', this.debounceFn);
      }
    },
    removeListen() {
      window.removeEventListener('resize', this.debounceFn);
    }
  },
  mounted() {
    this.updateScrollHeight();
    this.listenResize();
  },
  beforeDestroy() {
    this.removeListen();
  }
}
</script>

<style lang="stylus" scoped>
  .dialog-wrap,.el-dialog__wrapper
    .dialog-content
      padding-right 15px
    >>> .el-dialog
      margin 50px auto !important
      margin-bottom 5px !important
    >>> .el-dialog__header
      position relative
      padding 10px !important
      background #24c69a
      .el-dialog__title
        font-size 14px
        color #ffffff
      .el-dialog__headerbtn
        top 50%
        transform translate(0,-50%)
        .el-icon
          color #ffffff
    >>> .el-dialog__body
      padding 15px 0 10px 15px

    .scorll-wrap
      .dialog-scrollbar
        height 100%
      >>> .dialog-scroll-wrap
        overflow-x hidden !important
</style>