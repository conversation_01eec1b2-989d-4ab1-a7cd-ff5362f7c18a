<template>
  <qDialog
    :visible="isVisible"
    :title="title"
    :innerScroll="false"
    width="400px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
  <p v-if="title == '批量删除'">确认删除吗?</p>
    <template v-if="title == '批量编辑'">
      <p>是否批量开启或关闭20元扣款？</p>
      <span>状态:</span>
      <el-radio-group v-model="isInsurance" style="margin-left: 20px;">
        <el-radio :label="1">开启</el-radio>
        <el-radio :label="0">关闭</el-radio>
      </el-radio-group>
    </template>
  </qDialog>
</template>

<script>
export default {
  name: "batchDeleteDialog",
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    formData: Object,
    title:{
      type:String,
      required: true,
    }
  },
  data(){
    return {
      isLoading: false,
      isInsurance:1
    }
  },
  methods: {
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      const { factoryId, accountingMonth } = JSON.parse(this.$Base64.decode(this.$route.query.data));
      this.isLoading = true;
      let params = {
        ids:  this.formData.idList,
        isAll: 0,
        factoryId,
        accountingMonth,
      };
      if(this.title == "批量编辑"){
        params.isInsurance = this.isInsurance
      }
      if(this.title=='批量删除'){
           this.$api.softwareDataUpload.SocialSecurity.batchDelete(params).then((res) => {
        this.$notify.success({
          title: "成功",
          message: "批量删除成功",
        });
        this.$emit("cancel", "confirm");
      }).finally(()=>{
          this.isLoading = false;
        });
      }else if(this.title=='批量编辑'){
        this.$api.softwareDataUpload.SocialSecurity.batchOnOrOffInsurance(params)
          .then((res) => {
            this.$notify.success({
              title: "成功",
              message: '批量编辑成功',
            });
            this.$emit("cancel", "confirm");
          })
          .finally(() => {
            this.isLoading = false;
          });
      }   
    },
  },
};
</script>

<style lang="scss" scoped></style>
