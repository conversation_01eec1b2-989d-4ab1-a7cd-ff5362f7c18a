<template>
  <qDialog
    :visible="isVisible"
    :title="title"
    :innerHeight="200"
    width="700px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <el-form :model="addForm" :rules="rules" ref="addForm" size="small">
      <el-row>
        <el-col :span="12">
          <el-form-item class="staffName" label-width="150px" label="员工姓名:">{{
            addForm.staffName
          }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            v-if="title == '新增'"
            label="厂牌编号:"
            label-width="130px"
            prop="staffCode"
          >
            <el-input
              class="staffCode"
              v-model.trim="addForm.staffCode"
              clearable
              placeholder="请输入厂牌编号"
            >
              <template slot="append">
                <el-button type="primary" @click="onSearch"> 查询 </el-button>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item v-else class="staffName" label-width="130px" label="厂牌编号:">{{
            addForm.staffCode
          }}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item
            class="afterAmount"
            label-width="150px"
            label="调整后实发:"
            prop="afterAmount"
          >
            <el-input
              oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
              v-model="addForm.afterAmount"
              clearable
              placeholder="请输入调整后实发"
              @blur="onBlur"
            >
              <template slot="append"> 元 </template>
            </el-input></el-form-item
          >
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item class="staffName" label-width="125px" label="备注说明:">
            <el-input
              v-model="addForm.remark"
              type="textarea"
              resize="none"
              maxlength="300"
              show-word-limit
              rows="3"
            ></el-input
          ></el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </qDialog>
</template>

<script>
import { moneyFormat, moneyDelete } from "@/utils";
export default {
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    editForm: Object,
  },
  data() {
    return {
      addForm: {
        staffName: "",
        staffCode: "",
        afterAmount: "",
        remark: "",
      },
      isLoading:false,
      rules: {
        staffCode: [{ required: true, message: "厂牌编号不能为空", trigger: "blur" }],
        afterAmount: [
          { required: true, message: "调整后实发不能为空", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (!/^-?\d{1,5}(\.\d{1,2})?$/.test(moneyDelete(moneyDelete(value)))) {
                return callback(new Error("小数点前面仅支持5位数,小数点后面仅支持2位数"));
              }
              callback();
            },
            trigger: "blur",
          },
        ],
      },
    };
  },
  async created() {
    if (this.title == "编辑") {
      await this.getDetail();
    }
  },
  methods: {
    //分厂调详情
    getDetail() {
      return this.$api.softwareWorkbench
        .factoryEditSalaryDetail({ id: this.editForm.id })
        .then(({ data, success }) => {
          if (success)
            this.addForm = {
              staffName: data.staffName || "",
              staffCode: data.staffCode || "",
              afterAmount: moneyFormat(data.afterAmount) || "",
              remark: data.reason || "",
            };
        });
    },
    //查询员工信息
    onSearch() {
      this.$refs.addForm.validateField("staffCode", async (valid) => {
        if (valid) return;
        await this.$api.information.employee
          .employeeDetails({ staffCode: this.addForm.staffCode })
          .then(({ data }) => {
            this.addForm = {
              ...this.addForm,
              staffName: data.staffName || "",
            };
          });
      });
    },
    onBlur() {
      this.$refs.addForm.validateField("afterAmount", (valid) => {
        if (valid) {
          this.addForm.afterAmount = "";
          return;
        }
        this.addForm.afterAmount = moneyFormat(moneyDelete(this.addForm.afterAmount));
      });
    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      this.$refs.addForm.validate(async (valid) => {
        if (!valid) return;
        this.isLoading = true;
        if (this.title == "新增") {
          const { accountingMonth, factoryId } = JSON.parse(this.$Base64.decode(this.$route.query.data));
          let params = {
            accountingMonth,
            editAmount: moneyDelete(this.addForm.afterAmount),
            factoryId,
            reason: this.addForm.remark,
            staffCode: this.addForm.staffCode,
            staffName: this.addForm.staffName,
          };
          await this.$api.softwareWorkbench.addSaveFactoryEdit(params).then(() => {
            this.$notify.success({
              title: "成功",
              message: "新增成功",
            });
            this.$emit("cancel", "confirm");
          }).finally(()=>{
            this.isLoading = false;
          });
        } else {
          let params = {
            editAmount: moneyDelete(this.addForm.afterAmount),
            id: this.editForm.id,
            reason: this.addForm.remark,
          };
          await this.$api.softwareWorkbench.updateSaveFactoryEdit(params).then(({ success }) => {
            if (success) {
              this.$notify.success({
                title: "成功",
                message: "编辑成功",
              });
              this.$emit("cancel", "confirm");
            }
          }).finally(()=>{
            this.isLoading = false;
          });
        }
      });
    },
  },
};
</script>

<style lang="stylus" scoped>
.staffCode {
  >>>.el-input-group__append {
    background: #24c69a;
    color: #fff;

    .el-button {
      padding: 5px 20px;
    }
  }
}

.el-row {
  &::before, &::after {
    display: none;
  }
}

.el-form-item {
  display: flex;

  >>>.el-form-item__label {
    text-align: left;
  }

  >>>.el-form-item__content {
    width: 100%;
    margin-left: 0 !important;
  }
}

.staffName {
  >>>.el-form-item__label {
    &::before {
      content: '*';
      color: #F23D20;
      margin-right: 4px;
      visibility: hidden;
    }
  }
}

>>>.el-textarea__inner {
  padding-right: 50px;
}

.el-textarea {
  >>>.el-input__count {
    right: 20px !important;
  }
}

.afterAmount {
  margin-bottom: 25px;
}
</style>
