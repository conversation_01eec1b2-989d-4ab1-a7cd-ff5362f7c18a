<template>
  <div
    style="margin-bottom:5px">
    <div class="header">
      {{title}}</div>
    <div class="content">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      required: true
    }
  }
}
</script>

<style lang="stylus" scoped>
.header {
  line-height: 20px;
  padding: 5px 0;
  font-weight: bold;
  font-size: 14px;
  color :#24c69a
}
</style>