<template>
  <qDialog :visible="visible"
    :innerScroll="true"
    :innerHeight="650"
    title="查看详情"
    width="1000px"
    :modal-append-to-body="false"
    append-to-body
    :isShowCancelBtn="false"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel">
    <common title="员工信息">
      <el-form
        :model="staffInfo"
        label-width="106px"
        ref="staffInfo"
        size="small">
        <el-row>
          <el-col :span="8">
            <el-form-item

              label="员工姓名:"
              prop="staffName">
              {{staffInfo.staffName}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="厂牌编号:"
              prop="staffCode">
              {{staffInfo.staffCode}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item

              label="身份证号:"
              prop="idCard">
              <template>
                <span>{{ staffInfo.idCard }}</span>
                <i v-if="staffInfo.idCard"
                  :class="[
                  'iconfont',
                  showType ? 'icon-guanbi' : ' icon-yincangmima',
                ]"
                  @click="toggle"></i>
              </template>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item
              label="工厂名称:"
              prop="factoryId">
              {{staffInfo.factoryId}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="工序名称:"
              prop="processId">
              {{staffInfo.processId}}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </common>
    <common title="表单信息">
      <el-form
        :model="formData"
        label-width="106px"
        ref="formData"
        size="small">
        <el-row>
          <el-col :span="8">
            <el-form-item

              label="单据编号:"
              prop="code">
              {{formData.no}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item

              label="制表人员:"
              prop="idCard">
              {{formData.tableName}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item

              label="办理日期:"
              prop="staffName">
              {{formData.handleTime}}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item
              label="工资表类型:"
              prop="type">
              {{formData.type}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item

              label="审核人员:">
              {{formData.auditName}}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </common>
    <div>
      <div class="header">
        <span>工资单详情</span>
        <div class="btn">
          <el-button
            size="small"
            type="primary"
            @click="modifyRecord">
            修改记录
          </el-button>
        </div>
      </div>
      <el-table stripe border
        v-loading="loading"
        ref="tableRef"
        :height="200"
        highlight-current-row
        :data="tableList"
        :row-class-name="tableRowClassName"
        @select-all='handleSelectionAll'
        @selection-change="handleSelectionChange">
        <el-table-column
          width="40"
          type="selection">
        </el-table-column>
        <el-table-column
          prop="accountingMonth"
          label="结算月份"
          width="75"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="出勤天数"
          width="75"
          align="left"
          show-overflow-tooltip>
          <template
            slot-scope="{row}">
            <span
              :style="{'color':(row.attendances.sysAmount==row.attendances.settlementAmount)?'#0BB78E':'red'}">
              {{row.attendances.settlementAmount}}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="工资总额"
          width="85"
          align="left"
          show-overflow-tooltip>
          <template
            slot-scope="{row}">
            <span
              :style="{'color':(row.salary.sysAmount==row.salary.settlementAmount)?'#0BB78E':'red'}">
              {{row.salary.settlementAmount|moneyFormat}}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="其他扣款"
          width="85"
          align="left"
          show-overflow-tooltip>
          <template
            slot-scope="{row}">
            <span
              :style="{'color':(row.otherDeduct.sysAmount==row.otherDeduct.settlementAmount)?'#0BB78E':'red'}">
              {{row.otherDeduct.settlementAmount|moneyFormat}}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="厂服扣款"
          width="85"
          align="left"
          show-overflow-tooltip>
          <template
            slot-scope="{row}">
            <span
              :style="{'color':(row.uniformDeduct.sysAmount==row.uniformDeduct.settlementAmount)?'#0BB78E':'red'}">
              {{row.uniformDeduct.settlementAmount|moneyFormat}}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="生活费"
          width="85"
          align="left"
          show-overflow-tooltip>
          <template
            slot-scope="{row}">
            <span
              :style="{'color':(row.living.sysAmount==row.living.settlementAmount)?'#0BB78E':'red'}">

              {{row.living.settlementAmount|moneyFormat}}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="保险"
          width="85"
          align="left"
          show-overflow-tooltip>
          <template
            slot-scope="{row}">
            <span
              :style="{'color':(row.insurance.sysAmount==row.insurance.settlementAmount)?'#0BB78E':'red'}">

              {{row.insurance.settlementAmount|moneyFormat}}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="工会"
          width="85"
          align="left"
          show-overflow-tooltip>
          <template
            slot-scope="{row}">
            <span
              :style="{'color':(row.labour.sysAmount==row.labour.settlementAmount)?'#0BB78E':'red'}">

              {{row.labour.settlementAmount|moneyFormat}}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="借支"
          width="85"
          align="left"
          show-overflow-tooltip>
          <template
            slot-scope="{row}">
            <span
              :style="{'color':(row.loan.sysAmount==row.loan.settlementAmount)?'#0BB78E':'red'}">

              {{row.loan.settlementAmount|moneyFormat}}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="自离扣款"
          width="85"
          align="left"
          show-overflow-tooltip>
          <template
            slot-scope="{row}">
            <span
              :style="{'color':(row.leaveDeduct.sysAmount==row.leaveDeduct.settlementAmount)?'#0BB78E':'red'}">
              {{row.leaveDeduct.settlementAmount|moneyFormat}}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="扣款合计"
          width="85"
          align="left"
          show-overflow-tooltip>
          <template
            slot-scope="{row}">
            <span
              :style="{'color':(row.totalDeduct.sysAmount==row.totalDeduct.settlementAmount)?'#0BB78E':'red'}">

              {{row.totalDeduct.settlementAmount|moneyFormat}}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="实发工资"
          align="left"
          show-overflow-tooltip>
          <template
            slot-scope="{row}">
            <span
              :style="{'color':(row.actualSalary.sysAmount==row.actualSalary.settlementAmount)?'#0BB78E':'red'}">
              {{row.actualSalary.settlementAmount|moneyFormat}}
            </span>
          </template>
        </el-table-column>
      </el-table>
      <div
        class="table-footer">
        <div
          id="table_footer">
          <div
            class="footer_left">
            <span>合计人民币（小写）:</span>
            <span>{{smallNum}}</span>
          </div>
          <div
            class="footer_left">
            <span>合计人民币（大写）:</span>
            <span>{{bigNum}}</span>
          </div>
        </div>
        <div
          class="form_content">
          <span
            style="display:block;padding:10px 0 ">备注说明:1.工资总额包含（计件工资、生活补贴、奖励、返工工资、住房补贴等）
            2.其他扣款包含（处罚、成本赔偿、低耗扣款、返工扣款等）</span>
         <span>{{remarks}}</span>
        </div>
      </div>
    </div>
  </qDialog>
</template>
<script>
import common from './component/common'
import salaryDetails from './component/salaryDetails'
import { getSum, dealBigMoney } from "./common";
import { moneyFormat } from "@/utils";
export default {
  name: "addPayroll",
  mixins: [salaryDetails],
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    detailsInfo: [Object]
  },
  components: { common, },
  data() {
    return {
      //员工信息
      staffInfo: {
        staffName: "",
        staffCode: "",
        idCard: "",
        idCardOrigin: "",
        idCardDecoded: "",
        factoryId: "",
        processId: "",
      },
      //表单信息
      formData: {
        no: "",
        tableName: "",
        handleTime: "",
        type: "",
        auditName: ""
      },
      //工资条类型
      payrollOptions: Object.freeze([
        {
          name: "辞职工资",
          value: "1",
        },
        {
          name: "自离工资",
          value: "2",
        },
        {
          name: "延发工资",
          value: "3",
        },
        {
          name: "其他工资",
          value: "4",
        },
      ]),
      idCardRsa: "",
      remarks:"",
      commonTitle: "",
      commonVisible: false,
      showType: false,
    }
  },
  created() {
    this.getDetail()
  },
  methods: {
    //特殊工资单详情
    async getDetail() {
      const { data } = await this.$api.information.payrollManagement.salaryDetail({ id: this.detailsInfo.id })
      this.idCardRsa = data.idCardRsa
      this.remarks = data.remark
      this.staffInfo = {
        staffName: data.staffName || '',
        staffCode: data.staffCode || '',
        idCard: data.idCard,
        idCardOrigin: data.idCard || '',
        idCardDecoded: '',
        factoryId: data.factoryName || '',
        processId: data.processName || '',
      }
      this.formData = {
        no: data.no || '',
        tableName: data.tableName || '',
        handleTime: data.handleTime || '',
        type: data.type && this.payrollOptions.find(item => item.value == data.type).name,
        auditName: data.auditName || ''
      }
      data.list.forEach((v)=>{ //没有自离扣款手动添加
        if(!v.leaveDeduct){
          v.leaveDeduct = {
            settlementAmount:"0.00",
            sysAmount:"0.00"
          }
        }
      })
      this.tableList = data.list && data.list.sort((a, b) => new Date(a.accountingMonth).getTime() - new Date(b.accountingMonth).getTime())  || [];
      this.smallNum = moneyFormat(getSum(this.tableList));
      this.bigNum = dealBigMoney(this.smallNum);
    },
    //身份证解码
    toggle() {
      if (this.showType) {
        this.staffInfo.idCard = this.staffInfo.idCardOrigin;
        this.showType = false;
      } else {
        if (this.staffInfo.idCardDecoded) {
          this.staffInfo.idCard = this.staffInfo.idCardDecoded;
          this.showType = true;
          return;
        }
        this.$api.information.employee
          .decrypt(this.idCardRsa)
          .then((res) => {
            this.staffInfo.idCard = res.data
            this.staffInfo.idCardDecoded = res.data;
            this.showType = true
          });
      }
    },
    handleCancel() {
      this.$emit('cancel', {
        type: "cancel",
        isVisible: false
      })
    },
    handleConfirm() {
      this.$emit('cancel', {
        type: "cancel",
        isVisible: false
      })
    }
  }
}
</script>

<style lang="stylus" scoped>
>>>.el-form-item__label {
  text-align: left;
}

>>>.el-input-group__append {
  background: #24c69a;
  color: #fff;

  .el-button {
    padding: 5px 10px;
  }
}

>>>.el-select {
  width: 100%;
}

.el-col {
  padding-right: 10px;
}

>>>.el-table__header .el-table-column--selection {
  .el-checkbox {
    visibility: hidden;
    z-index: 99999;
  }
}

.processCode {
  >>>.el-form-item__label {
    &::before {
      content: '*';
      color: #F23D20;
      margin-right: 4px;
      visibility: hidden;
    }
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;

  >span {
    color: #24c69a;
    font-weight: bold;
    font-size: 14px;
  }
}

.table-footer {
  margin-top: 10px;

  #table_footer {
    display: flex;
    justify-content: space-between;
  }
}

>>>.el-table__body {
  .table_row {
    .el-table__cell {
      background: pink !important;
    }
  }
}
</style>