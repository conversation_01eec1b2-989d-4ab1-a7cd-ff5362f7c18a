<template>
  <!-- 环境补贴参数补贴 -->
  <content-panel class="panel-tabs">
    <div class="panel-headers">
      <div class="headers-left">
        <b>启用开关</b>
        <el-switch :disabled="!isModifyType" class="mg15" v-model="configForm.onOff" active-value="on" inactive-value="off"></el-switch>
        <el-tooltip content="开启后配置生效,反之不生效" placement="right">
          <i class="el-icon-question"></i>
        </el-tooltip>
        <span class="mg15">新员工上满<el-input :disabled="!isModifyType" v-model="configForm.conditionValue" type="text" style="width: 80px; margin: 0px 10px"
            maxlength="4" onkeyup="value=value.replace(/[^0-9]/g, '')" ></el-input>天后才能享受环境补贴</span>
      </div>

      <div class="headers-right">
        <el-button size="small" type="primary" @click="isModifyType = true" v-if="!isModifyType">编辑</el-button>
        <template v-else>
          <el-button size="small" type="primary" @click="changeSave" :loading="saveLoading">保存</el-button>
          <el-button size="small" type="warning" @click="changeCancel">取消</el-button>
        </template>
      </div>
    </div>
    <b>补贴标准配置</b>
    <div class="panel-content">
      <el-row :gutter="10" class="content-top">
        <el-col :span="1" >序号</el-col>
        <el-col :span="2" >核算工厂</el-col>
        <el-col :span="5" >核算班组</el-col>
        <el-col :span="3" >关联设置</el-col>
        <el-col :span="3" >计件工序</el-col>
        <el-col :span="2" >入职年限</el-col>
        <el-col :span="2" >环境补贴标准</el-col>
        <el-col :span="3" >操作</el-col>
      </el-row>

      <el-row :gutter="10" v-for="(item, index) in ruleList" :key="index">
        <div class="content-item">
          <el-col :span="1"  style="line-height: 30px">{{ item.sort }}
          </el-col>
          <el-col :span="2" >
            <el-select :disabled="!isModifyType" v-model="item.factoryId" filterable clearable :append-to-body="false" placeholder="请选择核算工厂" prop="factoryId"
              @change="changeFactoryId(item)">
              <el-option v-for="item in factoryNameOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-col>
          <el-col :span="5" >
       
            <select-multiple :isDisabled="!isModifyType" :contentTips="getHiddenName(item,item.shiftGroup,item.processesOption)" :class="`shiftGroup`+index"
              :className="`shiftGroup`+index" v-model="item.shiftGroup" :options="item.processesOption" labelText="name" valueText="id"></select-multiple>
          </el-col>

          <el-col :span="3" >
            <el-select :disabled="!isModifyType" v-model="item.relationSet" placeholder="请选择关联设置" prop="relationSet">
              <el-option v-for="item in settingList" :disabled="item.disabled" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-col>
          <el-col :span="3" >
            <select-multiple :isDisabled="!isModifyType || item.relationSet == 1" :contentTips="getHiddenName(item,item.pieceProcess,item.pieceOption)"
              :class="`pieceProcess`+index" :className="`pieceProcess`+index" v-model="item.pieceProcess" :options="item.pieceOption" labelText="processName"
              valueText="id"></select-multiple>
          </el-col>
          <el-col :span="2" >
            <el-select :disabled="!isModifyType" v-model="item.condition" placeholder="请选择入职年限" prop="condition">
              <el-option v-for="item in yearList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-col>
          <el-col :span="3" >
            <el-input :disabled="!isModifyType" type="text"
              oninput="value=value.replace(/[^\d.]/g, '');if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+ 3)}" placeholder="请输入"
              v-model="item.criterion" @blur="checkAmount(item)">
              <template slot="append">元/天</template>
            </el-input>
          </el-col>
          <el-col :span="3" >
            <div class="details_btn">
              <el-button type="text" @click="newbuilt(index)" :disabled="!isModifyType">
                新建
              </el-button>
              <el-button type="text" @click="changeCopy(item, index)" :disabled="!isModifyType">
                复制
              </el-button>
              <el-button type="text" style="color: red" @click="handleDelete(index)" v-show="ruleList.length > 1 && index != 0" :disabled="!isModifyType">
                删除
              </el-button>
            </div>
          </el-col>
        </div>
      </el-row>
    </div>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import { moneyDelete } from "@/utils";
export default {
  name: "managementsystem",
  mixins: [tableMixin],
  data() {
    return {
      configForm: {
        onOff: "on",
        taskType: "ENVIRONMENT_SUBSIDY",
        conditionValue: "",
      },

      isModifyType: false,
      saveLoading: false,
      // 表格相关
      tableData: [],
      ruleList: [
        {
          sort: 1,
          factoryId: "",
          shiftGroup: [],
          relationSet: "",
          criterion: "",
          processesOption: [],
        },
      ],
      tabList: [],
      conditionsList: [
        {
          label: "考勤大于等于28天小于30天",
          value: "考勤大于等于28天小于30天",
        },
        { label: "考勤大于等于30天", value: "考勤大于等于30天" },
      ],
      settingList: [
        { label: "计件工序查询", value: "0", disabled: true },
        { label: "核算班组查询", value: "1", disabled: false },
        { disabled: true, label: "关联查询", value: "2" },
      ],
      yearList: [
        { label: "3个月内", value: "entryMonths>0&&entryMonths<3" },
        { label: "3月至1年内", value: "entryMonths>=3&&entryMonths<12" },
        { label: "6个月内", value: "entryMonths>0&&entryMonths<6" },
        { label: "6个月至1年内", value: "entryMonths>=6&&entryMonths<12" },
        { label: "1年至2年内", value: "entryMonths>=12&&entryMonths<24" },
        { label: "2年至3年内", value: "entryMonths>=24&&entryMonths<36" },
        { label: "3年至3年以上", value: "entryMonths>=36" },
        { label: "按出勤天数直接核算", value: "1==1" },
      ],
      reasonList: [],
    };
  },
  async created() {
    await this.$api.plateTypeSystemManage.getBasicPermission
      .getQuFactory({ moduleId: 3 })
      .then((res) => {
        this.tabList = res.data || [];
        this.getList();
      });
  },
  computed: {
    factoryNameOptions() {
      return (
        this.tabList.length > 0 &&
        this.tabList.map((item) => ({
          id: item.id,
          name: item.name,
          process: item.process,
        }))
      );
    },
  },
  methods: {
    getHiddenName(item, value, list) {
      if (!value) return
      let arr = []
      value.forEach((v) => {
        list.forEach((i) => {
          if (v == i.id) {
            arr.push(i.name ? i.name : i.processName)
          }
        })
      })
      // item.resultText = arr.join('/')
      return arr

    },
    changeFactoryId(item) {
      item.shiftGroup = []; //选择工厂时置空之前选中的班组
      item.processesOption = this.factoryNameOptions.find(
        (v) => v.id == item.factoryId
      ).process;
    },
    changeCancel() {
      this.isModifyType = false;
      this.getList();
    },
    // 判断是否有相同对象元素
    hasDuplicateObjects(array) {
      // const seen = new Set();
      // for (let i = 0; i < array.length; i++) {
      //   const objString = JSON.stringify(array[i]);
      //   if (seen.has(objString)) {
      //     return true;
      //   }
      //   seen.add(objString);
      // }
      // return false;
      let result = false;
      for (let i = 0; i < array.length; i++) {
        for (let j = i + 1; j < array.length; j++) {
          if (
            array[i].factoryId == array[j].factoryId &&
            array[i].condition == array[j].condition &&
            this.intersection(array[i].shiftGroup, array[j].shiftGroup)
          ) {
            result = true;
            this.$message.closeAll();
            this.$message.error(
              `第${array[i].sort}条和${array[j].sort}条核算标准配置中存在相同配置,请查看！`
            );
          }
        }
      }
      return result;
    },
    intersection(arr1, arr2) {
      if (!arr2) {
        arr2 = []
      }
      if (!arr1) {
        arr1 = []
      }
      if (!arr1.length && !arr2.length) return true
      let arr = arr1.filter((value) => arr2.includes(value));
      return arr.length;
    },
    changeSave() {
      if (this.configForm.conditionValue=="" ) {
        this.$message.error("请输入新员工上满天数");
        return;
      }
      
      if (!this.ruleList.every((item) => item.factoryId)) {
        this.$message.error("请选择核算工厂！");
        return;
      }
      if (
        !this.ruleList.every(
          (item) => item.shiftGroup && item.shiftGroup.length
        )
      ) {
        this.$message.error("请选择核算班组！");
        return;
      }
      if (!this.ruleList.every((item) => item.relationSet)) {
        this.$message.error("请选择关联设置");
        return;
      }
      // if (!this.ruleList.every(item => item.pieceProcess)) {
      //   this.$message.error('请选择计件工序')
      //   return
      // }
      if (!this.ruleList.every((item) => item.condition)) {
        this.$message.error("请选择入职年限");
        return;
      }
      if (!this.ruleList.every((item) => item.criterion)) {
        this.$message.error("请输入补贴标准");
        return;
      }
      let checkSame = JSON.parse(JSON.stringify(this.ruleList)).map((item) => {
        return {
          condition: item.condition,
          factoryId: item.factoryId,
          shiftGroup: item.shiftGroup.flat(),
          sort: item.sort,
        };
      });

      if (this.hasDuplicateObjects(checkSame)) return;

      let params = {
        ...this.configForm,
        ruleList: this.ruleList.map((item) => {
          return {
            factoryId: item.factoryId,
            shiftGroup: item.shiftGroup.flat(),
            relationSet: item.relationSet,
            criterion: item.criterion,
            taskType: "ENVIRONMENT_SUBSIDY",
            condition: item.condition,
          };
        }),
      };

      this.saveLoading = true;
      this.$api.plateTypeSystemConfig
        .configUpdate(params)
        .then(() => {
          this.$message.success("保存成功");
          this.getList();
        })
        .finally(() => {
          this.saveLoading = false;
          this.isModifyType = false;
        });
    },
    //获取页面配置
    getList() {
      this.loading = true;
      this.$api.plateTypeSystemConfig
        .getConfigList("ENVIRONMENT_SUBSIDY")
        .then(({ data }) => {
          const { onOff, id, ruleList, conditionValue } = data;
        
          ruleList.forEach((v, index) => {
            v.processesOption = this.factoryNameOptions.find(
              (item) => v.factoryId == item.id
            ).process;
            v.sort = index + 1;
            v.relationSet = String(v.relationSet);
          });
          if (ruleList.length) {
            this.configForm = {
            onOff: onOff ? onOff : "on",
            taskType: "ENVIRONMENT_SUBSIDY",
            id,
            conditionValue:String(conditionValue),
          };
            this.ruleList = ruleList;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //新建
    newbuilt() {
      let len = this.ruleList.length;
      this.ruleList.push({
        sort: len + 1,
        factoryId: "",
        shiftGroup: [],
        criterion: "",
        processesOption: [],
      });
    },
    //复制
    changeCopy(item) {
      let copyItem = JSON.parse(JSON.stringify(item));
      copyItem.sort = this.ruleList.length + 1;
  
      this.ruleList.push(copyItem);
    },
    //删除
    handleDelete(index) {
      this.ruleList.splice(index, 1);
    },
    //校验金额
    checkAmount(item) {
      let flag = false;
      let amount = moneyDelete(item.criterion);
      if (!amount) {
        this.$message({
          message: "金额不能为空",
          type: "warning",
        });
        flag = true;
      }
      if (!flag) {
        if (!/^-?\d{1,5}(\.\d{1,2})?$/.test(amount)) {
          this.$message({
            message: "小数点前面仅支持5位数,小数点后面仅支持2位数",
            type: "warning",
          });
          item.criterion = "";
          // flag = true;
          // if (name) this.calculateForm[name] = this.payingAmount;
        }
      }
    },
  },
};
</script>

<style lang="stylus" scoped>
.enable {
  font-size: 14px;
  font-weight: 530;
}

.mg15 {
  margin: 0px 15px;
}

>>>.is-group .cell {
  white-space: pre-line;
}

.panel-tabs {
  >>> .main-area {
    padding-top: 0;
    height: 84vh;
  }
}

.tabs-row {
  margin-bottom: 5px;
  display: flex;
  align-items: center;
}

.tabs {
  >>> .el-tabs__header {
    margin-bottom: 0;
  }
}

>>>.table_active {
  background: pink !important;
}

>>> .itemVal {
  margin-left: 5px;
  width: 100%;
  padding: 0 5px;
}

>>> .el-input-group__append {
  background: #aaa;
  color: white;
  padding: 0 7px;
}

.details {
  display: flex;
  padding-bottom: 18px;

  &_content {
    flex: 1;
    display: flex;
    justify-content: space-between;

    > .el-select, > .el-input, > span {
      flex: 1 0 30%;
    }
  }

  &_btn {
    width: 20%;
  }
}
</style>

<style lang="stylus" scoped>
>>> .el-input input {
  height: 30px;
  line-height: 30px;
  /* width: 95px; */
  padding: 0px 10px;
}

>>>.el-cascader {
  height: 30px;
  line-height: 30px;
}

.panel-headers {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px 0px;

  .headers-left {
    display: flex;
    align-items: center;
  }

  .headers-right {
  }
}

.panel-content {
  margin-top: 20px;
  // border:1px solid red;
  overflow:hidden;
  overflow-y:scroll;
  height:90%;
  .content-top {
    font-size: 14px;
    font-weight: bold;
  }

  .content-item {
    margin-top: 10px;
  }

  .details_btn {
    display: flex;
    align-items: center;

    >>>.el-button {
      padding: 5px;
    }
  }
}
</style>
