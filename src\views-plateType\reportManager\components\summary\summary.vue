<template>
  <!-- 工资分析表 -->
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          size="mini"
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          label-position="right"
        >
          <el-form-item label="核算工厂:" prop="factoryId">
            <el-select
              v-model="searchForm.factoryId"
              filterable
              placeholder="请选择工厂"
              @change="onSearch"
              clearable
            >
              <el-option
                v-for="item in tabList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="核算月份:" prop="accountingMonth">
            <el-date-picker
              @change="onSearch"
              v-model="searchForm.accountingMonth"
              type="month"
              placeholder="请选择日期"
              :clearable="false"
              :picker-options="pickerOptions"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button
            size="small"
            type="primary"

            @click="onSearch"
          >
            查询</el-button
          >
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置</el-button
          >
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <div class="header_tableName">{{filterName}}</div>
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :data="tableData"
        :height="maxTableHeight"
      >
      <el-table-column
          prop="factoryName"
          align="center"
          label="核算工厂"
          width="150"
          fixed
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="totalWorkDay"
          align="center"
          label="本厂出勤天数"
          width="105"
          show-overflow-tooltip
        >
        </el-table-column>
          <el-table-column
            prop="backManDays"
            class-name="customOne"
            min-width="105"
            align="center"
            label="杂工出勤天数"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="totalAttendance"
            class-name="customOne"
            label="总出勤天数"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="pieceWage"
            class-name="customOne"
            label="补贴+计件工资"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.pieceWage |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="leftover"
            class-name="customOne"
            label="本月划出"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.leftover |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="incomingSalary"
            class-name="customOne"
            label="本月划入"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.incomingSalary |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            label="提取余留"
            width="100"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.extractResidual |  filterData }}
            </template>
          </el-table-column>
          <el-table-column
            prop="remain"
            class-name="customOne"
            label="净余留"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.remain |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="remainRate"
            class-name="customOne"
            label="净余留率"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="salary"
            class-name="customOne"
            label="应发工资"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.salary |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="actualSalary"
            class-name="customOne"
            label="实发工资"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.actualSalary |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="dailyAvgSalary"
            class-name="customOne"
            label="日均应发"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.dailyAvgSalary |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="dailyAvgActualSalary"
            class-name="customOne"
            label="日均实发"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.dailyAvgActualSalary |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="reworkAmount"
            class-name="customOne"
            label="返工扣款"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.reworkAmount |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="compensationAmount"
            class-name="customOne"
            label="成本赔偿"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.compensationAmount |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="consumptionAmount"
            class-name="customOne"
            label="低耗品"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.consumptionAmount |  filterData}}
            </template>
          </el-table-column>
          <el-table-column
            prop="punishDeduct"
            class-name="customOne"
            label="处罚"
            min-width="120"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              {{ row.punishDeduct |  filterData}}
            </template>
          </el-table-column>
      </el-table>
    </table-panel>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import moment from "moment";
import { moneyFormat } from "@/utils"
export default {
  name: "plateTypelaborEfficiency",
  mixins: [tableMixin, pagePathMixin],
  data() {
    return {
      searchForm: {
        factoryId: "",
        accountingMonth: moment().subtract(1, "months").format("YYYY-MM")
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      tabList: [],
      tableData: [], //表格数据
      loading: false,
      filterParam: {}
    };
  },
  async created() {
    await this.getFactory();
    this.onSearch();
  },
  computed: {
    filterName() {
      let accountingMonth = this.searchForm.accountingMonth
      return `${moment(accountingMonth).format("YYYY年MM月")} 板木工资划拨汇总表`
    }
  },
  filters: {
    filterData(value) {
      return !value ? "-" : moneyFormat(value);
    },
  },
  methods: {
    //获取工厂
    getFactory() {
      return this.$api.plateTypeSystemManage.getBasicPermission
        .getBasicPermissionAll({ moduleId: 3 })
        .then(({ data }) => {
          this.tabList = data || [];
        });
    },
    //获取板木-工资划拨分析汇总表
    getList() {
      this.loading = true;
      this.$api.plateTypeReportManagement
        .wageTransferAnalyzeSummary({ ...this.filterParam })
        .then((res) => {
          this.tableData = res.data;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.filterParam = {};
      this.searchForm.factoryId = '';
      this.onSearch();
    },
    //搜索
    onSearch() {
      let month = this.searchForm.accountingMonth;
      this.searchForm.accountingMonth = month
        ? month
        : moment().subtract(1, "months").format("YYYY-MM");
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (key == "accountingMonth" && val) {
          this.filterParam.accountingMonth = moment(val).format("YYYY-MM");
        } else if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      this.pageNum = 1;
      this.getList();
    }
  },
};
</script>
<style lang="stylus" scoped>
ellipsis() {
  font-size: 16px;
  font-weight: 600;
}

.header_tableName {
  ellipsis();
  font-size: 22px;
  text-align: center;
  margin-bottom: 8px;
  margin-top: 34px;
}

>>>.el-table__cell {
  .cell {
    width: 100% !important;
    padding: 0 8px !important;
  }
}

>>>.el-table__fixed {
  height: auto !important;
  bottom: 17px !important;
}
</style>
