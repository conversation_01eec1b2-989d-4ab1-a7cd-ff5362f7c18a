<template>
  <qDialog
    :visible="visible"
    :innerScroll="false"
    :innerHeight="500"
    :title="title"
    width="500px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <el-form
      :model="addForm"
      :rules="rules"
      ref="addForm"
      size="small"
      label-width="120px"
    >
      <el-form-item label="工厂名称:" prop="factoryId">
        <el-select
          v-model="addForm.factoryId"
          style="width: 250px"
          filterable
          placeholder="请选择工厂名称"
          clearable
        >
          <el-option
            v-for="item in tabList"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="大工序名称:" prop="groupName">
        <el-input
          v-model="addForm.groupName"
          style="width: 250px"
          placeholder="请输入大工序名称"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="大工序编码:" prop="code">
        <el-input
          v-model="addForm.code"
          style="width: 250px"
          placeholder="请输入大工序编码"
          clearable
          maxlength="10"
          show-word-limit
        ></el-input>
      </el-form-item>
      <el-form-item label="排序" prop="sortIndex">
        <el-input
          class="input-number"
          v-model="addForm.sortIndex"
          style="width: 250px"
          placeholder="请输入排序"
          type="number"
          :controls="false"
          oninput="value=value.replace(/[^\d]/g,'')"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="启用状态:" prop="enable">
        <el-switch
          inactive-color="#ff4949"
          v-model="addForm.enable"
          :active-text="addForm.enable == '1' ? '启用' : '禁用'"
          :active-value="1"
          :inactive-value="0"
        >
        </el-switch>
      </el-form-item>
      <el-form-item class="staffName" label="备注:" prop="remark">
        <el-input
          type="textarea"
          resize="none"
          rows="5"
          maxlength="50"
          show-word-limit
          v-model.trim="addForm.remark"
        >
        </el-input>
      </el-form-item>
    </el-form>
  </qDialog>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    editForm: Object,
  },
  data() {
    return {
      addForm: {
        factoryId: "",
        groupName: "",
        enable: 1,
        code: "",
        remark: "",
        sortIndex: ""
      },
      tabList: [],
      showType: false,
      isLoading: false,
      rules: {
        factoryId: [
          { required: true, message: "请选择工厂", trigger: "change" },
        ],
        groupName: [
          { required: true, message: "请输入大工序名称", trigger: "blur" },
        ],
        code: [
          { required: false, message: "请输入大工序编码", trigger: "blur" },
        ],
        sortIndex: [
          { required: true, message: "请输入排序", trigger: "blur" },
        ],
        enable: [
          { required: true, message: "请选择启用状态", trigger: "change" },
        ],
      },
    };
  },
  async created() {
    await this.getBigGroupList();
    this.addForm = {
      ...this.addForm,
      ...this.editForm,
    };
  },
  methods: {
    getBigGroupList() {
      return this.$api.softwareSystemManage.getBasicPermission
        .getBasicPermissionAll()
        .then(({ data }) => {
          this.tabList = data.map((item) => ({
            label: item.name,
            name: item.name,
            id: item.id,
          }));
        });
    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      this.$refs.addForm.validate((valid) => {
        if (!valid) return;
        this.isLoading = true;
        let params = {
            parentId: 0,
            treeLevel: 2,
            ...this.addForm,
            groupName:this.addForm.groupName.trim(),
            code:this.addForm.code.trim()
          };
        if (this.title == "新增") {
          this.$api.softwareSystemManage.getBasicPermission
            .addGroup(params)
            .then(({ success }) => {
              if (success)
                this.$notify({
                  title: "成功",
                  message: "新增成功",
                  type: "success",
                });
              this.$emit("cancel", "confirm");
            })
            .finally(() => {
              this.isLoading = false;
            });
        } else {
          this.$api.softwareSystemManage.getBasicPermission
            .editGroup(params)
            .then(({ success }) => {
              if (success)
                this.$notify({
                  title: "成功",
                  message: "修改成功",
                  type: "success",
                });
              this.$emit("cancel", "confirm");
            })
            .finally(() => {
              this.isLoading = false;
            });
        }
      });
    },
  },
};
</script>

<style lang="stylus" scoped></style>
<style >
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }
  input[type="number"] {
    -moz-appearance: textfield;
  }
</style>

