<template>
  <qDialog :visible="visible" :innerScroll="false" title="余留调整" width="500px" :isLoading="isLoading"
    @cancel="handleCancel" @confirm="handleConfirm" :before-close="handleCancel">
    <el-form label-width="98px" :model="remainingForm" :rules="rules" ref="remainingForm" size="small">
      <el-form-item label="核算月份:" prop="accountingMonth">
        <el-select v-model="remainingForm.accountingMonth" filterable clearable placeholder="请选择核算月份"
          @change="changeMonth">
          <el-option v-for="item in months" :key="item" :label="item" :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="余留金额:">{{
        remainingForm.allAmount | moneyFormat
      }}</el-form-item>
      <el-form-item label="调整方式:">
        <el-radio v-model="remainingForm.type" label="1">增加 </el-radio>
        <el-radio v-model="remainingForm.type" label="2">减少 </el-radio>
      </el-form-item>
      <el-form-item label="调整金额:">
        <el-input oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}" placeholder="请输入调整金额"
          v-model.trim="remainingForm.editAmount" @focus="onFocus" clearable @blur="onBlur(remainingForm.editAmount)">
        </el-input>
      </el-form-item>
      <el-form-item label="调整说明:">
        <el-input type="textarea" resize="none" :rows="3" v-model.trim="remainingForm.comments" maxlength="300"
          show-word-limit>
        </el-input>
      </el-form-item>
    </el-form>
  </qDialog>
</template>

<script>
import moment from "moment";
import { moneyFormat, moneyDelete } from "@/utils";
export default {
  name: "remainingAdjustment",
  props: {
    visible: { type: Boolean, required: true },
    formData: Object,
    months: { type: Array, required: true },
  },
  data: () => ({
    remainingForm: {
      type: "1",
      editAmount: "",
      comments: "",
      allAmount: "",
      accountingMonth: "",
    },
    rules: {
      accountingMonth: [{ required: true, message: "请选择核算月份", trigger: "change" }],
    },
    accountingMonthList: [], //核算月份
    isLoading: false
  }),
  created() {
    this.accountingMonthList = [
      moment().subtract(3, "months").startOf("month").format("YYYY-MM"),
      moment().subtract(2, "months").startOf("month").format("YYYY-MM"),
      moment().subtract(1, "months").startOf("month").format("YYYY-MM"),
    ].map((item, index) => ({ value: item, id: index + 1 }));
  },
  methods: {
    //根据月份获取余留
    changeMonth(val) {
      if (!val) return;
      this.$api.plateTypeInformation.remaining
        .getRemainByMonth({
          factoryId: this.formData.factoryId,
          accountingMonth: this.remainingForm.accountingMonth,
        })
        .then((res) => {
          this.remainingForm.allAmount = res.data;
        });
    },
    onBlur() {
      if (!/^-?\d{1,7}(\.\d{1,2})?$/.test(moneyDelete(this.remainingForm.editAmount))) {
        this.$message({
          message: "小数点前面仅支持7位数,小数点后面仅支持2位数",
          type: "warning",
        });
        this.remainingForm.editAmount = "";
        return;
      }
      this.remainingForm.editAmount = moneyFormat(
        moneyDelete(this.remainingForm.editAmount)
      );
    },
    onFocus() {
      this.remainingForm.editAmount = moneyDelete(this.remainingForm.editAmount);
    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      this.$refs.remainingForm.validate((valid) => {
        if (!valid) return;
        this.isLoading = true;
        this.$api.plateTypeInformation.remaining
          .addRemaining({
            factoryId: this.formData.factoryId,
            ...this.remainingForm,
            editAmount: moneyDelete(this.remainingForm.editAmount),
          })
          .then((res) => {
            this.$message({
              message: "余留调整成功",
              type: "success",
            });
            this.$emit("cancel", "confirm");
          }).finally(() => {
            this.isLoading = false;
          });
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
