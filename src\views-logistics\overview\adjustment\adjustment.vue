<template>
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          size="mini"
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          label-position="right"
        >
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="员工姓名"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input
              v-model.trim="searchForm.staffCode"
              size="mini"
              clearable
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenStaffCodes"
                  titleName="厂牌编号"
                />
              </template>
            </el-input>
          </el-form-item>
          <!-- <el-form-item
            label="分配状态:"
            prop="assignStatus">
            <el-radio-group
              v-model="searchForm.assignStatus"
              @input="onSearch">
              <el-radio
                label="0">全部
              </el-radio>
              <el-radio
                label="N">待分配
              </el-radio>
              <el-radio
                label="Y">已分配
              </el-radio>
            </el-radio-group>
          </el-form-item> -->
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary"  @click="onSearch">
            查询</el-button
          >
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置</el-button
          >
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-right>
        <!-- <el-button
          v-show="permission && detailInfo.nodeName == '分厂调整-未提交'"
          size="small"
          type="primary"
          @click="handleAdd"
        >
          新增</el-button
        > -->
         <el-button
          size="small"
          type="primary"
          @click="handleExport('0')"
        >
        导出调整前</el-button
        >
        <el-button size="small" type="primary" @click="handleExport('1')">
          导出</el-button
        >
        <el-button
          v-show="permission && detailInfo.nodeName == '分厂调整-未提交'"
          size="small"
          type="primary"
          @click="handleImport"
        >
          导入</el-button
        >
        <el-button
          v-show="permission && detailInfo.nodeName == '分厂调整-未提交'"
          size="small"
          type="primary"
          @click="handleSubmit"
        >
          提交</el-button
        >
      </template>
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :height="maxTableHeight"
        :data="tableData"
      >
        <el-table-column prop="staffName" label="员工姓名" align="left">
        </el-table-column>
        <el-table-column prop="staffCode" label="厂牌编号"  align="left">
        </el-table-column>
        <el-table-column
          prop="groupName"
          label="核算班组"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="申请补贴"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            {{ filterData(row.editAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="修改时间"  align="left">
          <template slot-scope="{row}">
            {{ row.updateTime | dateFormat }}
          </template>
        </el-table-column>
        <el-table-column
          prop="comments"
          label="备注说明"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column width="235" align="left">
          <template slot-scope="{ row }">
            <!-- <el-button
              v-if="permission && detailInfo.nodeName == '分厂调整-未提交'"
              size="small"
              type="text"
              @click="handleEdit(row)"
            >
              编辑
            </el-button> -->
            <el-button size="small" type="text" @click="handleRecords(row)">
              调整记录
            </el-button>
            <!-- <el-button
              v-if="permission && detailInfo.nodeName == '分厂调整-未提交'"
              type="text"
              size="small"
              @click="handleDelete(row)"
              >删除</el-button
            > -->
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <ul>
          <li>
            <span>核算月份:</span><span>{{ adjustInfo.accountingMonth }}</span>
          </li>
          <li>
            <span>人数:</span><span>{{ adjustInfo.people }}</span>
          </li>
          <li>
            <span>申请补贴金额:</span
            ><span>{{ adjustInfo.editAmount | moneyFormat }}</span>
          </li>
        </ul>
        <div style="text-align: right">
          <el-pagination
            @size-change="onSizeChange"
            @current-change="onNumChange"
            :current-page="pageNum"
            :page-size="pageSize"
            :total="total"
            :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper"
          >
          </el-pagination>
        </div>
      </template>
    </table-panel>
    <add-dialog
      v-if="isVisible"
      :isVisible="isVisible"
      :title="title"
      :editForm="editForm"
      @cancel="addCancel"
    ></add-dialog>
    <adjustment-records
      v-if="visible"
      :visible="visible"
      :editForm="editForm"
      @adjustmentCancel="adjustmentCancel"
    ></adjustment-records>
    <export-dialog
      v-if="exportVisible"
      :visible="exportVisible"
      @cancel="exportCancel"
    ></export-dialog>
    <Import
      v-if="ImportVisible"
      :visible="ImportVisible"
      @cancel="cancel"
      @confirm="confirm"
      :importInfo="importInfo"
    />
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import { SUB_APP_CODE } from "@/api/api";
import addDialog from "./addDialog.vue";
import adjustmentRecords from "./adjustmentRecords";
import exportDialog from "./exportDialog";
import { moneyFormat } from '@/utils'
export default {
  name: "LogisticsAdjustment",
  components: { addDialog, adjustmentRecords,exportDialog },
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      searchForm: {
        staffCode: "",
        staffName: "",
        // assignStatus: "0"
      },
      editForm: {},
      importInfo: {},
      tableData: [],
      filterParam: {},
      params: {},
      title: "",
      permission: false,
      loading: false,
      ImportVisible: false,
      visible: false,
      isVisible: false,
      resizeOffset: 65,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      columnHeads: [],
      adjustInfo: {},
      detailInfo:{},
      info: {},
      isLoading: false,
      exportVisible: false,
    };
  },
  watch: {
    $route: {
      handler(value) {
        if (value.path.includes("adjustment")&&value.path.includes("logistics")) {
          this.info = JSON.parse(this.$Base64.decode(value.query.data));
          this.factoryId = this.info.factoryId;
          this.importInfo = {
            reportName: "logisticsImportfactoryedit",
            paramMap: {
              columnValue: "分厂调整",
              factoryId: this.factoryId,
              accountingMonth: this.info.accountingMonth,
              id: this.info.id,
            },
          };
          this.getTaskDetail();
          this.getList();
          this.getDebitList();
          this.getPermission();
          this.getColumnHeads();
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    // 获取分厂调整列表
    getList() {
      this.loading = true;
      this.$api.logisticsWorkbench
        .getEditSalaryList({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: {
            factoryId: this.factoryId,
            accountingMonth: this.info.accountingMonth,
            ...this.filterParam,
            ...this.params,
          },
        })
        .then(({ data: { list, total } }) => {
          this.tableData = list || [];
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 获取分厂调整统计
    getDebitList() {

      let params = {
        accountingMonth: this.info.accountingMonth,
        factoryId: this.factoryId,
        ...this.filterParam,
        ...this.params,
        // assignStatus: this.searchForm.assignStatus == '0' ? "" : this.filterParam.assignStatus
      };
      this.$api.logisticsWorkbench.getStatistic(params).then(({ data }) => {
        this.adjustInfo = data|| {};
      });
    },
    //获取待办任务详情
    getTaskDetail() {
      const {id } = JSON.parse(this.$Base64.decode(this.$route.query.data));
      this.$api.logisticsWorkbench
        .taskDetail({ taskId: id })
        .then(({ data }) => {
          this.detailInfo = data || {};
        });
    },
    //是否具有总览任务操作权限
    getPermission() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.info.accountingMonth,
        name: "FACTORY_EDIT",
      };
      this.$api.logisticsWorkbench.overviewPermission(params).then(({ data }) => {
        this.permission = data;
      });
    },
    //动态列头列表
    getColumnHeads() {

      let data = {
        factoryId: this.factoryId,
        accountingMonth: this.info.accountingMonth,
      };
      this.$api.logisticsWorkbench.columnHeads(data).then(({ success, data }) => {
        if (success) {
          this.columnHeads = data.map((item) => ({
            columnFormat: item.columnFormat,
            columnName: item.columnName,
            fieldName: item.fieldName,
            fieldType: item.fieldType,
          }));
        }
      });
    },
    //提交
    handleSubmit() {
      const { id } = JSON.parse(this.$Base64.decode(this.$route.query.data));
      this.$api.logisticsWorkbench
        .nextNode({ taskId: id })
        .then(({ success }) => {
          if (success) {
            this.$notify({
              title: "成功",
              message: "提交成功",
              type: "success",
            });
            this.getPermission();
            this.getTaskDetail()
          }
        });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
      this.getDebitList();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    filterData(value){
      if(!Number(value)) return '-'
      return moneyFormat(value)
    },
    //调整记录
    handleRecords(row) {
      this.editForm = {
        staffCode: row.staffCode,
      };
      this.visible = true;
    },
    //新增
    handleAdd() {
      this.editForm = {};
      this.title = "新增";
      this.isVisible = true;
    },
    //编辑
    handleEdit(row) {
      this.editForm = {
        id: row.id,
      };
      this.title = "编辑";
      this.isVisible = true;
    },
    //删除
    handleDelete(row) {
      this.$confirm("此操作将永久删除该条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$api.logisticsWorkbench.deleteFactoryEdit({ id: row.id }).then((res) => {
            this.$message({
              type: "success",
              message: "删除成功!",
            });
            this.getList();
            this.getDebitList();
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    //导入
    handleImport() {
      this.title = "导入";
      this.ImportVisible = true;
      this.innerScroll = false;
    },

    //导出
    handleExport(type) {
      if (type == 0) {
        this.exportVisible = true;
        return;
      }
      this.$api.logisticsWorkbench
        .factoryEditSalaryExport({
          factoryId: this.factoryId,
          accountingMonth: this.info.accountingMonth,
          isFilterEmpty: type == 0 ? 1 : 0,
          type,
        })
        .then((res) => {
          this.$message.success("导出操作成功，请前往导出记录查看详情");
        });
    },
    addCancel(type) {
      this.isVisible = false;
      if (type == "cancel") return;
      this.getList();
      this.getDebitList();
    },
    adjustmentCancel(type) {
      this.visible = false;
    },
    onSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(val) {
      this.pageNum = val;
      this.getList();
    },
    exportCancel(type) {
      this.exportVisible = false;
    },
    cancel(value) {
      this.ImportVisible = value;
    },
    confirm(value) {
      this.ImportVisible = value;
    },
  },
};
</script>

<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}

ul {
  list-style: none;
  display: flex;
  padding: 0;
}

i {
  font-style: normal;
}

>>>.table-panel-footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  ul {
    display: flex;

    li {
      white-space: nowrap
      padding: 0 10px;
    }
  }
}

>>>.el-tabs__nav-wrap::after {
  height: 0;
}
</style>
