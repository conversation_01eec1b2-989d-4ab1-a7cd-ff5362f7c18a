<template>
  <div>
    <table-panel
      ref="tablePanel">
      <el-table stripe border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :height="maxTableHeight"
        :data="tableData">
        <el-table-column
          prop="staffName"
          label="员工姓名"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="staffCode"
          label="厂牌编号"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="area"
          label="区域"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="groupName"
          label="核算班组"
          align="left">
        </el-table-column>
        <el-table-column
          prop="accountingMonth"
          label="核算月份"
          align="left">
        </el-table-column>
        <el-table-column
          label="补贴金额"
          align="left"
          show-overflow-tooltip>
          <template
            slot-scope="{row}">
            {{ row.subsidyAmount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          prop="sysComments"
          label="系统备注"
          align="left"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          label="操作"
          align="left"
          width="80"
          v-if="permission">
          <template
            slot-scope="{row}">
            <el-button
              v-show="row.sysComments=='个人'"
              type="text"
              size="small"
              @click="handleDelete(row)">
              删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div
          class="table_footer">
          <ul>
            <li>
              <span>核算月份:</span><span>{{ infoList.accountingMonth }}</span>
            </li>
            <li>
              <span>人数:</span><span>{{ infoList.personTotal }}</span>
            </li>
            <li>
              <span>卸草垫子补贴:</span><span>{{ infoList.personTotalAmount | moneyFormat }}</span>
            </li>
          </ul>
        </div>
        <el-pagination
          :current-page="pageNum"
          :page-size="pageSize"
          :total="total"
          :page-sizes="[50, 100, 200]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onSizeChange"
          @current-change="onNumChange">
        </el-pagination>
      </template>
    </table-panel>
  </div>
</template>

<script>
import tableMixin from '@/utils/tableMixin'
import pagePathMixin from "@/utils/page-path-mixin";
export default {
  name: 'groups',
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      pageNum: 1,
      pageSize: 50,
      total: 0,
      loading: false,
      resizeOffset: 55,
      tableData: [],
      infoList: {},
      permission: false,
      filterData: {}
    }
  },
  methods: {
    init(data = {}, pageNum = 1, pieceWageFlag) {
      this.permission = pieceWageFlag
      this.pageNum = 1
      this.filterData = data
      this.getList()
      this.getStatistic()
    },
    //获取班组列表
    getList() {
      this.loading = true;
      const { factoryId, accountingMonth } = JSON.parse(this.$Base64.decode(this.$route.query.data));
      this.$api.logisticsDataUpload.strawMattress
        .listWithPerson({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: {
            factoryId,
            accountingMonth,
            ... this.filterData,
          },
        })
        .then(({ data: { list, total } }) => {
          this.tableData = list || [];
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //获取统计信息
    getStatistic() {
      const { factoryId, accountingMonth } = JSON.parse(this.$Base64.decode(this.$route.query.data));
      this.$api.logisticsDataUpload.strawMattress
        .stsWithPerson({
          factoryId,
          accountingMonth,
          ... this.filterData,
        })
        .then(({ data }) => {
          this.infoList = data && {
            ...data,
            accountingMonth,
          } || {};
        })
    },
    //删除
    handleDelete(row) {
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$api.logisticsDataUpload.pieceRateWage
          .deletePieceWagePerson({
            id: row.id,
          })
          .then(() => {
            this.$notify.success({
              title: "成功",
              message: "删除成功",
            });
            this.getList();
            this.getStatistic();
          });
      });
    },
    onSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
  }
}
</script>

<style lang="stylus" scoped>
ul {
  list-style: none;
  display: flex;
  padding: 0;
}

i {
  font-style: normal;
}

>>>.table-panel-footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .table_footer {
    overflow: auto;

    >ul {
      display: flex;
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        white-space: nowrap;
        padding: 0 10px;
      }
    }
  }
}
</style>