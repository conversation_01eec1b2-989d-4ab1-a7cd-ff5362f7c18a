<template>
  <div :class="classObj" class="app-wrapper">
    <sidebar class="sidebar-container" />
    <div class="main-container">
      <div :class="{'fixed-header':fixedHeader}">
        <navbar />
        <tag-bar />
      </div>
      <app-main />
    </div>
  </div>
</template>
<script>
import Sidebar from './components/sidebar-float/sidebar-float'
import AppMain from './components/AppMain'
import TagBar from './components/tagbar/'
import Navbar from './components/nav-bar'
import { mapState } from 'vuex'

export default {
  components: {
    Sidebar,
    TagBar,
    Navbar,
    AppMain
  },
  data() {
    return {}
  },
  computed: {
    ...mapState({
      sidebar: state => state.app.sidebar
    }),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        hasTagsView: true,
        withoutAnimation: this.sidebar.withoutAnimation
      }
    },
    fixedHeader() {
      return true
    }
  }
}
</script>
<style lang="stylus" scoped>
// $sideBarWidth = 150px

.app-wrapper {
  &:after {
    content: '';
    display: table;
    clear: both;
  }

  position: relative;
  height: 100%;
  width: 100%;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - 150px);
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}
</style>


