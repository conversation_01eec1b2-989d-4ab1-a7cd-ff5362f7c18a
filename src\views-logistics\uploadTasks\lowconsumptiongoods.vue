<template>
  <content-panel>
    <!-- 低耗品 -->
    <template v-slot:search>
      <search-box class="search-box">
        <el-form size="mini" :inline="true" :model="searchForm" ref="searchForm">
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="员工姓名"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input
              v-model.trim="searchForm.staffCode"
              size="mini"
              clearable
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenStaffCodes"
                  titleName="厂牌编号"
                />
              </template>
            </el-input>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary"  @click="onSearch">
            查询
          </el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-right>
        <div ref="btnRight" style="display: flex; justify-content: space-between">
          <el-button
            v-show="permission"
            size="small"
            type="primary"
            @click="handleImport"
          >
            导入
          </el-button>
        </div>
      </template>
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :height="maxTableHeight"
        :data="tableData"
      >
        <el-table-column type="index" width="50"> </el-table-column>
        <el-table-column prop="staffName" label="员工姓名" width="100" align="left">
        </el-table-column>
        <el-table-column
          prop="staffCode"
          label="厂牌编号"
          width="100"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column prop="factoryName" label="部门/分厂" width="150" align="left">
        </el-table-column>
        <el-table-column
          prop="consumptionAmount"
          label="低耗品"
          width="150"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="{ row: { consumptionAmount } }">
            {{ consumptionAmount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          prop="compensationDate"
          label="赔偿日期"
          width="150"
          align="left"
        >
        </el-table-column>
        <el-table-column
          prop="compensationReason"
          label="赔偿原因"
          width="150"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column prop="teamLeader" label="组长" width="150" align="left">
        </el-table-column>
        <el-table-column
          prop="remark"
          label="备注说明"
          align="left"
          show-overflow-tooltip
        >
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <div class="table_footer">
          <ul>
            <li>
              <span>核算工厂:</span><span>{{ info.factoryName }}</span>
            </li>
            <li>
              <span>核算月份:</span><span>{{ info.accountingMonth }}</span>
            </li>
            <li>
              <span>人数:</span><span>{{ lowconsumptiongoodInfo.people }}</span>
            </li>
            <li>
              <span>低耗品:</span
              ><span>{{ lowconsumptiongoodInfo.amount | moneyFormat }}</span>
            </li>
          </ul>
        </div>
        <div style="text-align: right">
          <el-pagination
            @size-change="onSizeChange"
            @current-change="onNumChange"
            :current-page="pageNum"
            :page-size="pageSize"
            :total="total"
            :page-sizes="[50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper"
          >
          </el-pagination>
        </div>
      </template>
    </table-panel>
    <Import
      :visible="ImportVisible"
      @cancel="cancel"
      @confirm="confirm"
      :importInfo="importInfo"
    />
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
export default {
  name: "Lowconsumptiongoods",
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      searchForm: {
        staffCode: "",
        staffName: "",
        content: "",
      },
      lowconsumptiongoodInfo: {},
      info: {},
      factoryId: "",
      tableData: [],
      loading: false,
      resizeOffset: 53,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      permission: "",
      title: "",
      filterParam: {},
      params: {},
      ImportVisible: false,
      importInfo: {},
    };
  },
  watch: {
    $route: {
      handler(value) {
        if (value.path.includes("lowconsumptiongoods")&&value.path.includes("logistics")) {
          this.info = JSON.parse(this.$Base64.decode(value.query.data));
          this.factoryId = this.info.factoryId;
          this.importInfo = {
            reportName: "importLowConsumption",
            paramMap: {
              columnValue: "物流-低耗品",
              factoryId: this.factoryId,
              accountingMonth: this.info.accountingMonth,
              id: this.info.id,
            },
          };
          this.getList();
          this.getStatistic();
          this.getPermission();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    //获取低耗品列表
    getList() {
      this.loading = true;
      this.$api.dataUpload.lowConsumption
        .getLowConsumption({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: {
            factoryId: this.factoryId,
            accountingMonth: this.info.accountingMonth,
            ...this.filterParam,
            ...this.params,
          },
        })
        .then(({ data: { list, total }, success }) => {
          if (success) {
            this.tableData = list || [];
            this.total = total;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //是否具有代办任务操作权限
    getPermission() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.info.accountingMonth,
        type: "21",
      };
      this.$api.logisticsWorkbench.agencyPermission(params).then((res) => {
        this.permission = res.data;
      });
    },
    //获取统计信息
    getStatistic() {
      this.$api.dataUpload.lowConsumption
        .lowConsumptionStatistic({
          accountingMonth: this.info.accountingMonth,
          factoryId: this.factoryId,
          ...this.filterParam,
          ...this.params,
        })
        .then(({ success, data }) => {
          if (success) {
            this.lowconsumptiongoodInfo = data || {};
          }
        });
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
      this.getStatistic();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    //导入
    handleImport() {
      this.ImportVisible = true;
    },
    onSizeChange(pageSize) {
      this.pageSize = pageSize;
      // 切换每页条数后从第一页查询
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    cancel(value) {
      this.ImportVisible = value;
    },
    confirm(value) {
      this.ImportVisible = value;
    },
  },
};
</script>

<style lang="stylus" scoped>

>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}
>>>.el-tabs__nav-wrap::after {
  height: 0;
}

ul {
  list-style: none;
  display: flex;
  padding: 0;
}

i {
  font-style: normal;
}
>>>.table-panel-footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .table_footer {
    overflow: auto;

    >ul {
      display: flex;
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        white-space: nowrap
        padding: 0 10px;
      }
    }
  }
}
</style>
