<template>
  <!--周工资表 -->
  <content-panel>
    <table-panel ref="tablePanel">
      <div class="header_tableName">{{ filterName }}</div>
      <el-table :key="tableKey" stripe border v-loading="loading" ref="tableRef" highlight-current-row :data="tableData"
        :height="maxTableHeight" style="width: 100%">
        <el-table-column prop="bigProcessCode" label="大工序编码" width="120" align="left" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="bigProcessName" label="大工序名称" width="120" align="left" show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="本厂出勤天数" width="120"  prop="totalWork" align="left">
        </el-table-column>
        <el-table-column label="本厂加班小时" width="120"  prop="totalOvertime" align="left">
        </el-table-column>
        <el-table-column label="工作日出勤(天)" width="120"  prop="workdayWork" align="left">
        </el-table-column>
        <el-table-column label="工作日延时加班(小时)" width="160" prop="workdayOvertime" align="left" show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="周末出勤(天)" width="120"  prop="weekWork">
        </el-table-column>
        <el-table-column label="周末延时加班(小时)"  width="160" prop="weekOvertime">
        </el-table-column>
        <el-table-column label="节假日出勤(天)" width="150"  prop="holidayWork">
        </el-table-column>
        <el-table-column label="节假日延时加班(小时)" width="160" prop="holidayOvertime">
        </el-table-column>
        <el-table-column label="系统计件" width="100" prop="pieceWage">
          <template slot-scope="{ row }">
            {{ filterDatamoneyFormatZh(row.pieceWage || 0) }}
          </template>
        </el-table-column>
        <el-table-column label="调整金额" width="100" prop="editAmount"> 
          <template slot-scope="{ row, $index }"> 
            <div v-if="editingAmountIndex === $index" class="editable-cell">
              <el-input v-model="editingAmountValue" @blur="handleAmountSaveEdit(row, $index)"
                @keyup.enter="handleAmountSaveEdit(row, $index)" ref="editAmountInput" size="mini" style="width: 100%" />
            </div>
            <div v-else @click="handleAmountStartEdit(row, $index)" class="clickable-cell">
              {{ filterDatamoneyFormatZh(row.editAmount || 0) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="调整计件" width="100" prop="pieceWageEdit">
           <template slot="header">
            <span>调整计件</span>
            <el-tooltip placement="top">
              <div slot="content">
                调整计件=系统计件+调整金额
              </div>
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
          <template slot-scope="{ row, $index }">
            <!-- <div v-if="editingIndex === $index" class="editable-cell">
              <el-input v-model="editingValue" @blur="handleSaveEdit(row, $index)"
                @keyup.enter="handleSaveEdit(row, $index)" ref="editInput" size="mini" style="width: 100%" />
            </div> -->
          <div    class="clickable-cell">
              {{ filterDatamoneyFormatZh(row.pieceWageEdit || 0) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="日均计件" width="100" prop="dailyPieceWage">
          <template slot-scope="{ row }">
            {{ filterData(row.dailyPieceWage || 0) }}
          </template>
          <template slot="header">
            <span>日均计件</span>
            <el-tooltip placement="top">
              <div slot="content">
                日均计件=调整计件/考勤天数
              </div>
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <!-- <el-pagination :current-page="pageNum" :page-size="pageSize" :total="total" :page-sizes="[50, 100, 200]"
        @size-change="handleSizeChange" @current-change="handleCurrentChange"
        layout="total, sizes, prev, pager, next, jumper">
      </el-pagination> -->
    </table-panel>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import { moneyFormat, moneyDelete,moneyFormatZh } from "@/utils";
export default {
  name: "PlateTypePayrollList",
  mixins: [tableMixin, pagePathMixin],
  data() {
    return {
      tableData: [],
      tableKey: 0,
      loading: false,
      pageSize: 1000, //每页显示个数
      pageNum: 1, //当前页数
      filterName: '',
      editingIndex: -1, // 当前编辑的行索引
      editingValue: '', // 当前编辑的值
      editingAmountIndex: -1, // 当前编辑的行索引
      editingAmountValue: '', // 当前编辑的值
    };
  },

  watch: {
    $route: {
      handler(value) {
        if (
          value.path.includes("weeklyPayroll") &&
          value.path.includes("plateType")
        ) {
          let data = this.parseRouteQueryData();
          this.filterName = data.name;
          this.getList();
        }
      },
      deep: true,
      // immediate: true,
    },
  },
  created() {
    this.getList();
  },
  methods: {
    parseRouteQueryData(data = this.$route.query.data) {
      try {
        // 验证data是否为有效JSON字符串
        if (typeof data !== "string" || !data.trim()) {
          console.warn("Route query data is empty or invalid:", data);
          return {};
        }
        return JSON.parse(data || '{}');
      } catch (error) {
        console.error("Failed to parse route query data:", error);
        return {};
      }
    },
    filterData(value) {
      return !value ? "-" : moneyFormatZh(value);
    },
    filterDatamoneyFormatZh(value) {
      return !value ? "0" : moneyFormatZh(value);
    }, 
    //开始编辑调整金额
    handleAmountStartEdit(row, index) {
      this.editingAmountIndex = index;
      this.editingAmountValue = row.editAmount || '0';
      this.$nextTick(() => {
        if (this.$refs.editAmountInput) {
          this.$refs.editAmountInput.focus();
        }
      });
    },
    // 保存编辑调整金额
    async handleAmountSaveEdit(row, index) {
      this.loading = true;
      try {
        // 验证输入值
        const value = moneyDelete(this.editingAmountValue);
         if (value==0&&row.editAmount==0) {
          this.loading = false;
          // 退出编辑状态
          this.editingAmountIndex = -1;
          this.editingAmountValue = 0;
          return;
        }
        if (!/^-?\d{1,10}(\.\d{1,5})?$/.test(value)) {
          this.$message({
            message: "请输入有效的金额格式（小数点前最多10位，小数点后最多5位）",
            type: "warning",
          });
          return;
        }
         
        // 调用保存接口
        const { factoryId, accountingMonth, accountingWeek } = this.parseRouteQueryData();
        await this.$api.plateTypePieceWageSystem.weekTodoTasks.editWeekSalary({
          bigProcessName: row.bigProcessName,
          editAmount: value,
          factoryId,
          accountingMonth,
          accountingWeek
        });

        // 更新本地数据
        this.$set(this.tableData, index, {
          ...row,
          pieceWageEdit: value
        });
        this.getList();
        this.$message({
          message: "保存成功",
          type: "success",
        });
      } catch (error) {
        console.error('保存失败:', error);
        this.$message({
          message: "保存失败，请重试",
          type: "error",
        });
      } finally {
        this.loading = false;
        // 退出编辑状态
        this.editingAmountIndex = -1;
        this.editingAmountValue = '';
      }
    },
    // 开始编辑
    handleStartEdit(row, index) {
      this.editingIndex = index;
      this.editingValue = row.pieceWageEdit || '0';
      this.$nextTick(() => {
        if (this.$refs.editInput) {
          this.$refs.editInput.focus();
        }
      });
    },
    // 保存编辑
    async handleSaveEdit(row, index) {
      this.loading = true;
      try {
        // 验证输入值
        const value = moneyDelete(this.editingValue);
        if (!/^-?\d{1,10}(\.\d{1,5})?$/.test(value)) {
          this.$message({
            message: "请输入有效的金额格式（小数点前最多10位，小数点后最多5位）",
            type: "warning",
          });
          return;
        }

        // 调用保存接口
        const { factoryId, accountingMonth, accountingWeek } = this.parseRouteQueryData();
        await this.$api.plateTypePieceWageSystem.weekTodoTasks.editWeekSalary({
          bigProcessName: row.bigProcessName,
          editAmount: value,
          factoryId,
          accountingMonth,
          accountingWeek
        });

        // 更新本地数据
        this.$set(this.tableData, index, {
          ...row,
          pieceWageEdit: value
        });
        this.getList();
        this.$message({
          message: "保存成功",
          type: "success",
        });
      } catch (error) {
        console.error('保存失败:', error);
        this.$message({
          message: "保存失败，请重试",
          type: "error",
        });
      } finally {
        this.loading = false;
        // 退出编辑状态
        this.editingIndex = -1;
        this.editingValue = '';
      }
    },
    //获取列表
    getList() {
      const { factoryId, accountingMonth, accountingWeek } = this.parseRouteQueryData();
      this.loading = true;
      this.$api.plateTypePieceWageSystem.weekTodoTasks
        .getWeekListWeekSalary({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          accountingWeek,
          accountingMonth,
          factoryId,
        })
        .then((res) => {
          this.tableData = res.data;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.pageNum = val;
      this.getList();
    },
  },
};
</script>
<style lang="stylus" scoped>
ellipsis() {
  font-size: 16px;
  font-weight: 600;
}

.header_tableName {
  ellipsis();
  font-size: 22px;
  text-align: center;
  margin-bottom: 8px;
}

.clickable-cell {
  cursor: pointer;
  padding: 8px;
  min-height: 20px;

  &:hover {
    background-color: #f5f7fa;
  }
}

.editable-cell {
  padding: 4px;
}
</style>