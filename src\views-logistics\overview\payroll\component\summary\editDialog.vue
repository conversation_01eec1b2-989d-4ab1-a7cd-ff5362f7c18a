<template>
  <qDialog
    :visible="visible"
    :innerScroll="true"
    :innerHeight="300"
    title="手动核算编辑"
    width="1000px"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <p>金额合计:{{ total }}</p>
    <el-row :gutter="10">
      <el-col
        v-for="(item, index) in tableHeader"
        :key="item.columnName"
        :span="8"
        :class="[
          'item',
          list.includes(index) ? 'borderLeft' : '',
          [0, 1, 2].includes(index) ? 'borderTop' : '',
        ]"
        style="padding: 10px"
      >
        <div class="item_content">
          <div class="item_content_title">
            <span>{{ item.processName }}:</span>
          </div>
          <div class="item_content_input">
            <el-input
              onInput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
              v-model="item.amount"
              clearable
              placeholder=""
              @blur="onBlur(item)"
              :class="[item.isShow ? 'input_active' : '']"
            ></el-input>
          </div>
        </div>
        <span class="error_info">{{ item.message }}</span>
      </el-col>
    </el-row>
  </qDialog>
</template>

<script>
import { moneyFormat, moneyDelete } from "@/utils";
export default {
  name: "editFinancial",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      editForm: {
        bankCardAmount: "",
        cashAmount: "",
        delayedAmount: "",
        leaveSelfAmount: "",
        resignationAmount: "",
      },
      filterParam: {},
      tableHeader: [],
      tableData: [],
      list: [],
      total: 0,
    };
  },
  created() {
    this.getDetail();
  },
  methods: {
    //详情
    getDetail() {
      this.$api.logisticsWorkbench
        .viewDetail({
          factoryId: JSON.parse(this.$route.query.data).factoryId,
          accountingMonth: JSON.parse(this.$route.query.data).accountingMonth,
        })
        .then(({ data }) => {
          this.tableHeader =
            data.map((item) => ({
              ...item,
              amount: moneyFormat(item.amount),
              message: "",
              isShow: false,
            })) || [];
          this.getTotal();
          let num = 0;
          this.tableHeader.forEach((item) => {
            this.list.push(num * 3);
            num++;
          });
        });
    },
    onBlur(item) {
      this.tableHeader.forEach((v) => {
        v.message = "";
        v.isShow = false;
      });
      item.amount = moneyFormat(moneyDelete(item.amount));
      if (!this.setTitle(item).type) {
        item.message = this.setTitle(item).message;
        item.isShow = true;
        item.amount = "0.00";
      }
      this.getTotal();
    },
    setTitle(item) {
      if (item.amount == "")
        return {
          message: "金额不能为空",
          type: false,
        };
      if (!/^-?\d{1,7}(\.\d{1,2})?$/.test(moneyDelete(item.amount)))
        return {
          message: "小数点前面仅支持7位数,后面仅支持2位数",
          type: false,
        };
      return {
        message: "",
        type: true,
      };
    },
    getTotal() {
      let total = this.tableHeader.reduce(
        (pre, cur) => (pre += Number(moneyDelete(cur.amount))),
        0
      );
      this.total = moneyFormat(total);
    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      const { factoryId, accountingMonth } = JSON.parse(this.$route.query.data);
      let list = this.tableHeader.map((item) => ({
        factoryId,
        accountingMonth,
        processId: item.processId,
        amount: moneyDelete(item.amount),
      }));
      this.$api.logisticsWorkbench.editManualAccounting(list).then(() => {
        this.$notify.success({
          title: "成功",
          message: "编辑成功",
        });
        this.$emit("cancel", "confirm");
      });
    },
  },
};
</script>

<style lang="stylus" scoped>
$border=1px solid #ccc
.item{
    border: $border
    border-top: 0
    border-left: 0
  &_content{
    display: flex
    align-items: center
    &_title{
        width: 90px
        word-break: break-all
        text-align: right
    }
    &_input{
        flex: 1
        margin-left: 10px
    }
  }

}
>>>.el-row{
  margin: 0 !important
}
.error_info{
    width: calc(100% - 100px)
          display: block
          color: red
          text-align: center
          height: 20px
          float:right
        }
.borderLeft {
    border-left:$border
}
.borderTop{
    border-top:$border
}
.input_active{
  >>> input{
    border-color: red
  }
}
</style>
