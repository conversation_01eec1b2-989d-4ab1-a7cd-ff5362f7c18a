<template>
  <!-- 工资分析表 -->
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form size="mini" :inline="true" :model="searchForm" ref="searchForm" label-position="right">
          <el-form-item label="核算工厂:" prop="factoryId">
            <el-select v-model="searchForm.factoryId" filterable placeholder="请选择工厂" @change="onSearch">
              <el-option v-for="item in tabList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="核算月份:" prop="accountingMonth">
            <el-date-picker @change="onSearch" v-model="searchForm.accountingMonth" type="month" placeholder="请选择日期"
              :clearable="false" :picker-options="pickerOptions">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="核算大工序:" prop="bigProcessId">
            <el-select v-model="searchForm.bigProcessId" filterable clearable placeholder="请选择核算大工序" @change="onSearch">
              <el-option v-for="item in process.bigProcessList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="核算班组:" prop="processId">
            <el-select v-model="searchForm.processId" filterable clearable placeholder="请选择核算班组" @change="onSearch">
              <el-option v-for="item in process.processList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="入职日期:" prop="joinedDate">
            <el-select v-model="searchForm.joinedDate" filterable clearable placeholder="请选择入职日期" @change="onSearch">
              <el-option label="入职未满三个月" value="入职未满三个月">
              </el-option>
              <el-option label="入职三个月以上(含三个月)" value="入职三个月以上">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary" @click="onSearch">
            查询</el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置</el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-right>
        <el-button size="small" type="primary" @click="handleExport(0)">
          导出单厂</el-button>
        <el-button size="small" type="primary" @click="handleExport(1)">
          导出全部</el-button>
      </template>
      <div class="header_tableName">{{ filterName }}生产员工工资分析表</div>
      <el-table stripe border v-loading="loading" ref="tableRef" highlight-current-row :data="tableData"
        :height="maxTableHeight" :span-method="objectSpanMethod">
        <el-table-column prop="factoryName" align="center" label="工厂" width="150" fixed show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="accountingProcess" align="center" label="核算班组" width="100" fixed show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="bigProcessName" align="center" label="核算大工序" width="100" fixed show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="joinedDate" align="center" label="入职日期" width="120" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="people" align="center" label="人数" width="100" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ !row.people ? "-" : row.people }}
          </template>
        </el-table-column>
        <el-table-column prop="totalWorkDay" align="center" label="考勤汇总" width="100" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ !row.totalWorkDay ? "-" : row.totalWorkDay }}
          </template>
        </el-table-column>
        <el-table-column prop="incomingSalary" align="center" label="划入工资" width="100" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ filterData(row.incomingSalary || 0) }}
          </template>
        </el-table-column>
        <el-table-column prop="leftover" align="center" label="划出工资" width="100" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ filterData(row.leftover || 0) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="提取余留" width="100" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ filterData(row.extractResidual || 0) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="本月余留" width="100" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ filterData(row.residualWage || 0) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="增加项">
          <el-table-column v-for="item in filterTableHeader.addTableHeader" :key="item.columnName"
            :label="item.columnName" :prop="item.fieldName" :width="item.columnWidth" align="center">
            <template slot-scope="{ row }">
              {{ filterData(row[item.fieldName]) }}
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column align="center" label="减少项">
          <el-table-column v-for="item in filterTableHeader.reduceTableHeader" :key="item.columnName"
            :label="item.columnName" :prop="item.fieldName" :width="item.columnWidth" align="center">
            <template slot-scope="{ row }">
              {{ filterData(row[item.fieldName]) }}
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column align="center" label="实发工资" width="100" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ filterData(row.actualSalary || 0) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="日平均工资" width="100" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ filterData(row.totalAverageWages || 0) }}
          </template>
        </el-table-column>
        <el-table-column prop="serialNumber" align="center" label="备注" show-overflow-tooltip>
        </el-table-column>
      </el-table>
    </table-panel>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import { moneyFormat, calculateTableWidth } from "@/utils";
import moment from "moment";
export default {
  name: "plateTypeSalaryAnalysis",
  mixins: [tableMixin, pagePathMixin],
  data() {
    return {
      searchForm: {
        factoryId: "",
        accountingMonth: moment().subtract(1, "months").format("YYYY-MM"),
        processId: "",
        bigProcessId: "",
        joinedDate: "",
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      tabList: [],
      tableData: [], //表格数据
      loading: false,
      filterParam: {},
      //表格表头信息
      tableHeader: [],
      resizeOffset: 45,
      spanMap: {},
      spanIndex: 0,
      spanIndex2: 0,
    };
  },
  async created() {
    await this.getFactory();
    this.searchForm.factoryId = this.tabList[0].id;
    this.onSearch();
  },
  computed: {
    filterName() {
      let accountingMonth = this.searchForm.accountingMonth ? this.searchForm.accountingMonth : moment().subtract(1, "months").format("YYYY-MM");
      let factoryName =
        (this.searchForm.factoryId &&
          this.tabList.find((item) => item.id === this.searchForm.factoryId)
            .name) ||
        "";
      return `${moment(accountingMonth).format("YYYY年MM月")}${factoryName}`;
    },
    filterTableHeader() {
      return {
        addTableHeader:
          this.tableHeader.filter(
            (item) => item.dataType && item.dataType === 1
          ) || [],
        reduceTableHeader:
          this.tableHeader.filter(
            (item) => item.dataType && item.dataType === 2
          ) || [],
      };
    },
    process() {
      return {
        processList:
          (this.searchForm.factoryId &&
            this.tabList.find((item) => item.id == this.searchForm.factoryId)
              .process) ||
          [],
        bigProcessList:
          (this.searchForm.factoryId &&
            this.tabList.find((item) => item.id == this.searchForm.factoryId)
              .bigProcess) ||
          [],
      };
    },
  },
  methods: {
    //获取工厂
    getFactory() {
      return this.$api.plateTypeSystemManage.getBasicPermission
        .getBasicPermissionAll({ moduleId: 3 })
        .then(({ data }) => {

          this.tabList = data || [];
        });
    },
    //获取工资分析表
    getList() {
      this.loading = true;
      this.$api.plateTypeReportManagement
        .salaryAnalysis({ ...this.filterParam })
        .then(
          ({
            data: {
              headItems,
              salaryAnalysisDetail,
              hasThreeMonthsTotal = [],
              noThreeMonthsTotal = [],
              total = [],
            },
          }) => {
            this.tableHeader =
              (headItems &&
                headItems.map((item) => {
                  item.columnWidth = this.flexWidth(
                    item.fieldName,
                    this.tableData,
                    item.columnName
                  );
                  return item;
                })) ||
              [];
            this.tableData = salaryAnalysisDetail || [];
            if (!salaryAnalysisDetail || !salaryAnalysisDetail.length) return;
            this.computeSpanData(this.tableData);
            let list = [
              {
                factoryName: "小计",
                ...hasThreeMonthsTotal,
              },
              {
                factoryName: "小计",
                ...noThreeMonthsTotal,
              },
              {
                factoryName: "合计",
                ...total,
              },
            ];
            if (this.searchForm.joinedDate == "入职三个月以上") {
              list = [
                {
                  factoryName: "小计",
                  ...hasThreeMonthsTotal,
                },
                {
                  factoryName: "合计",
                  ...total,
                },
              ];
            }
            if (this.searchForm.joinedDate == "入职未满三个月") {
              list = [
                {
                  factoryName: "小计",
                  ...noThreeMonthsTotal,
                },
                {
                  factoryName: "合计",
                  ...total,
                },
              ];
            }
            this.tableData = [...this.tableData, ...list];
          }
        )
        .finally(() => {
          this.loading = false;
        });
    },
    // 遍历表格数据，计算合并行数据
    computeSpanData(list) {
      this.spanMap = {};
      list.forEach((item, index) => {
        this.recordSpanData(index, list);
      });
    },
    // 计算每一行的合并行数据
    recordSpanData(index, data) {
      if (index === 0) {
        this.spanMap[index] = {
          level1: 1,
          level2: 1,
        };
        this.spanIndex = 0;
        this.spanIndex2 = 0;
      } else {
        // 一级合并
        if (data[index].factoryName === data[index - 1].factoryName) {
          let spanRow = this.spanMap[this.spanIndex];
          spanRow.level1 += 1;
          // 二级合并
          let level2 = 1;
          if (
            data[index].accountingProcess === data[index - 1].accountingProcess
          ) {
            spanRow = this.spanMap[this.spanIndex2];
            spanRow.level2 += 1;
            // 当前行参与二级合并，当前行level2 = 0
            level2 = 0;
          } else {
            this.spanIndex2 = index;
          }
          this.spanMap[index] = {
            level1: 0,
            level2,
          };
        } else {
          this.spanMap[index] = {
            level1: 1,
            level2: 1,
          };
          this.spanIndex = index;
          this.spanIndex2 = index;
        }
      }
    },
    objectSpanMethod({ rowIndex, columnIndex }) {
      const spanRow = this.spanMap[rowIndex];
      if (!spanRow) {
        return;
      }
      if (columnIndex == 0) {
        return {
          rowspan: spanRow.level1,
          colspan: 1,
        };
      }
      // 二级合并的列
      if (columnIndex == 1) {
        return {
          rowspan: spanRow.level2,
          colspan: 1,
        };
      }
    },
    filterData(value) {
      return !value ? "-" : moneyFormat(value);
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.filterParam = {};
      this.searchForm.factoryId = this.tabList[0].id;
      this.onSearch();
    },
    //搜索
    onSearch() {
      let month = this.searchForm.accountingMonth;
      this.searchForm.accountingMonth = month ? month : moment().subtract(1, "months").format("YYYY-MM");
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (key == "accountingMonth" && val) {
          this.filterParam.accountingMonth = moment(val).format("YYYY-MM");
        } else if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      this.pageNum = 1;
      this.getList();
    },
    //计算表格列宽度
    flexWidth(...args) {
      return calculateTableWidth(...args);
    },
    //导出
    handleExport(num) {
      let exportForm = JSON.parse(JSON.stringify(this.filterParam));
      if (num) {
        exportForm.factoryId = "";
      }
      let params = {
        ...exportForm,
      };
      this.$api.plateTypeReportManagement
        .exportSalaryAnalysis(params)
        .then((res) => {
          this.$message.success("导出操作成功，请前往导出记录查看详情");
        });
    },
  },
};
</script>
<style lang="stylus" scoped>
ellipsis() {
  font-size: 16px;
  font-weight: 600;
}

.header_tableName {
  ellipsis();
  font-size: 22px;
  text-align: center;
  margin-bottom: 8px;
}

>>>.el-table__cell {
  .cell {
    width: 100% !important;
    padding: 0 8px !important;
  }
}

>>>.el-table__fixed {
  height: auto !important;
  bottom: 17px !important;
}
</style>
