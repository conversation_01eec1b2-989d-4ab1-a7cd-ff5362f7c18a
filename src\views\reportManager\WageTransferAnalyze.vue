<template>
  <content-panel class="panel-tabs">
    <el-row class="tabs-row">
      <el-col :span="22">
        <el-tabs v-model="activeTab" class="tabs">
          <el-tab-pane name="Detailed" label="明细表"></el-tab-pane>
          <el-tab-pane name="Summary" label="汇总表"></el-tab-pane>
        </el-tabs>
      </el-col>
      <el-col :span="2" class="table-btn-area">
        <el-button type="primary" size="small" @click="handleExport">导出</el-button>
      </el-col>
    </el-row>
    <component :is="activeTab" ref="wageTransferAnalyzeRef"></component>
  </content-panel>
</template>

<script>
import Detailed from './components/detailed/detailed.vue';
import Summary from './components/summary/summary.vue';
export default {
  name: 'CustomizedWageTransferAnalyze',
  components: {
    Detailed,
    Summary
  },
  data() {
    return {
      activeTab: 'Detailed'
    };
  },
  methods: {
    //导出
    handleExport() {
      let exportForm = JSON.parse(JSON.stringify(this.$refs.wageTransferAnalyzeRef.filterParam));
      console.log(exportForm, '数据');
      let params = {
        ...exportForm,
      };
      this.$api.reportManagement
        .exportWageTransferAnalyze(params)
        .then((res) => {
          this.$message.success("导出操作成功，请前往导出记录查看详情");
        });
    },
  },
};
</script>

<style lang="stylus" scoped>
.panel-tabs
    position: relative;
.panel-tabs
  >>> .main-area
    padding-top 0
.tabs-row
    display:flex;
    align-items:center;
.tabs-row:after
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 2px;
    background-color: #E4E7ED;
    z-index: 1;
.table-btn-area
    display:flex;
    justify-content:flex-end;
    margin-right:12px;
.tabs
  >>> .el-tabs__header
    margin-bottom 5px
    .el-tabs__nav-wrap::after{
      display:none;
    }
</style>
