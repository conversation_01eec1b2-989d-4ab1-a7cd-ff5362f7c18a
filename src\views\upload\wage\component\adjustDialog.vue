<template>
  <qDialog
    :visible="visible"
    title="计件调整"
    :innerHeight="500"
    width="1000px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <ul class="information">
      <li>姓名:{{ adjustForm.staffName }}</li>
      <li>厂牌编号:{{ adjustForm.staffCode }}</li>
      <li>核算工厂:{{ adjustForm.factoryName }}</li>
      <li>核算月份:{{ adjustForm.accountingMonth }}</li>
    </ul>
    <section>
      <div class="header">
        <div class="item">核算班组</div>
        <div class="item">计件工资（系统）</div>
        <div class="item">计件工资（调整）</div>
        <div class="item">新员工补贴</div>
        <div class="item">线下工资</div>
        <div class="item">计件总工资</div>
      </div>
      <ul class="raw_data">
        <li class="item">原始数据</li>
        <!-- 原始数据计件工资（系统） -->
        <li class="item">
          {{ rawData.systemPieceWage }}
        </li>
        <!-- 原始数据计件工资（调整） -->
        <li class="item">
          {{ rawData.offlineEdit }}
        </li>
        <!-- 原始数据补贴 -->
        <li class="item">
          {{ rawData.subsidy }}
        </li>
      <!-- 原始数据线下工资 -->
        <li class="item">
          {{ rawData.offlineWage }}
        </li>
        <!-- 原始数据计件总工资 -->
        <li class="item">
          {{ rawData.total }}
        </li>
      </ul>
      <ul class="raw_data" v-for="(item, index) in pieceList" :key="item.id">
        <li class="item">
          {{ item.processName }}
        </li>
        <li class="item">
          <el-input
            oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
            v-model="item.systemPieceWage"
            clearable
            @blur="onBlur('systemPieceWage', index)"
            @clear="onClear('systemPieceWage', index)"
          >
          </el-input>
        </li>

        <li class="item">
          <el-input
            oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
            v-model="item.offlineEdit"
            @blur="onBlur('offlineEdit', index)"
            @clear="onClear('offlineEdit', index)"
            clearable
          >
          </el-input>
        </li>
        <li class="item">
          <el-input 
          oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
            v-model="item.subsidy"
            @blur="onBlur('subsidy', index)"
            @clear="onClear('subsidy', index)"
            clearable>
          </el-input>
        </li>
        <li class="item">

          <el-input
            oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
            v-model="item.offlineWage"
            @blur="onBlur('offlineWage', index)"
            @clear="onClear('offlineWage', index)"
            clearable
          >
          </el-input>
        </li>
        <!-- 计件总工资 -->
        <li class="item">
          {{ item.total }}
        </li>
      </ul>
      <ul class="raw_data">
        <li class="item">合计</li>
        <li class="item">
          {{ totalData.systemPieceWage }}
        </li>
        <li class="item">
          {{ totalData.offlineEdit }}
        </li>
        <li class="item">
          {{ totalData.subsidy }}
        </li>
        <li class="item">
          {{ totalData.offlineWage }}
        </li>
        <!-- 计件总工资合计 -->
        <li class="item">
          {{ totalData.total }}
        </li>
      </ul>
      <el-form ref="form" :model="remarkForm">
        <el-form-item label="备注说明:">
          <el-input
            type="textarea"
            resize="none"
            rows="3"
            maxlength="300"
            show-word-limit
            v-model.trim="remarkForm.comments"
          >
          </el-input>
        </el-form-item>
      </el-form>
    </section>
  </qDialog>
</template>

<script>
import { moneyFormat, moneyDelete } from "@/utils";
import NP from "number-precision";
export default {
  name: "adjustDialog",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    adjustForm: Object,
  },
  data() {
    return {
      pieceList: [],
      //原始数据
      rawData: {},
      //合计数据
      totalData: {},
      remarkForm: {
        comments: "",
      },
      isLoading:false
    };
  },
  created() {
    this.adjustDetail();
  },
  methods: {
    //调账明细
    async adjustDetail() {
      const { factoryId, accountingMonth, staffCode } = this.adjustForm;
      let params = {
        factoryId,
        accountingMonth,
        staffCode,
      };
      const { data } = await this.$api.dataUpload.pieceRateWage.adjustDetail(params);
      this.remarkForm.comments = data[0].comments;
      this.pieceList = data.map((item) => ({
        id: item.id,
        processName: item.processName,
        systemPieceWage: moneyFormat(item.systemPieceWage),
        offlineEdit: moneyFormat(item.offlineEdit),
        subsidy:moneyFormat(item.subsidyAmount) || '0.00',
        offlineWage:moneyFormat(item.offlineWage) || '0.00',
        total: item.totalPieceWage
      }));
      this.init();
      this.getTotal();
    },
    //原始数据统计
    init() {
      let rawData = this.pieceList.reduce(
        (pre, cur) => {
          pre.systemPieceWage = NP.plus(pre.systemPieceWage,Number(moneyDelete(cur.systemPieceWage)))
          pre.offlineEdit = NP.plus(pre.offlineEdit,Number(moneyDelete(cur.offlineEdit)))
          pre.subsidy = NP.plus(pre.subsidy,Number(moneyDelete(cur.subsidy)))
          pre.offlineWage = NP.plus(pre.offlineWage,Number(moneyDelete(cur.offlineWage)))
          pre.total = NP.plus(pre.total,Number(moneyDelete(cur.total)))
          return pre;
        },
        { systemPieceWage: 0, offlineEdit: 0, total: 0, subsidy:0,offlineWage:0}
      );
      for (const key in rawData) {
        if (Object.hasOwnProperty.call(rawData, key)) {
          this.rawData[key] = moneyFormat(rawData[key]);
        }
      }
    },
    //合计统计
    getTotal() {
      let totalData = this.pieceList.reduce(
        (pre, cur) => {
          pre.systemPieceWage = NP.plus(pre.systemPieceWage,Number(moneyDelete(cur.systemPieceWage)))
          pre.offlineEdit = NP.plus(pre.offlineEdit,Number(moneyDelete(cur.offlineEdit)))
          pre.subsidy = NP.plus(pre.subsidy,Number(moneyDelete(cur.subsidy)))
          pre.offlineWage = NP.plus(pre.offlineWage,Number(moneyDelete(cur.offlineWage)))
          pre.total = NP.plus(pre.total,Number(moneyDelete(cur.total)))
          return pre;
        },
        { systemPieceWage: 0, offlineEdit: 0, total: 0,subsidy:0 ,offlineWage:0}
      );
      for (const key in totalData) {
        if (Object.hasOwnProperty.call(totalData, key)) {
          this.totalData[key] = moneyFormat(totalData[key]);
        }
      }
    },
    //数据校验
    checkData(value) {
      if (!/^-?\d{1,5}(\.\d{1,2})?$/.test(moneyDelete(moneyDelete(value)))) {
        this.$message({
          message: "小数点前面仅支持5位数,小数点后面仅支持2位数",
          type: "warning",
        });
      }
      return /^-?\d{1,5}(\.\d{1,2})?$/.test(moneyDelete(value));
    },
    //计算金额

    onBlur(type, index) {
      this.pieceList.forEach((m, n) => {
        if (n == index) {
          if (!this.checkData(m[type])) {
            m[type] = "0.00";
            return;
          }
          m[type] = moneyFormat(moneyDelete(m[type]));
          // m.total = moneyFormat(NP.plus(Number(moneyDelete(m.systemPieceWage)),Number(moneyDelete(m.offlineEdit))),Number(moneyDelete(m.subsidy)))
          m.total = moneyFormat(NP.plus(Number(moneyDelete(m.systemPieceWage)),Number(moneyDelete(m.offlineEdit)),Number(moneyDelete(m.subsidy)),Number(moneyDelete(m.offlineWage))))
        }
      });
      this.getTotal();
    },
    onClear(type, index) {
      this.pieceList.forEach((m, n) => {
        if (n == index) {
          m[type] = '0.00';
          m.total = moneyFormat(NP.plus(Number(moneyDelete(m.offlineEdit)),Number(moneyDelete(m.systemPieceWage)),Number(moneyDelete(m.subsidy))));
        }
      });
      this.getTotal();
    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      this.isLoading = true
      let params = {
        accountingMonth: this.adjustForm.accountingMonth,
        comments: this.remarkForm.comments || "",
        factoryId: this.adjustForm.factoryId,
        staffCode: this.adjustForm.staffCode,
        list: this.pieceList.map((item) => ({
          id: item.id,
          systemPiece: moneyDelete(item.systemPieceWage),
          editPiece: moneyDelete(item.offlineEdit),
          totalPiece: moneyDelete(item.total),
          subsidy: moneyDelete(item.subsidy),
          offlineWage: moneyDelete(item.offlineWage),

        })),
        onlyPiece: "1",
      };
      this.$api.dataUpload.pieceRateWage.adjustAmount(params).then((res) => {
        this.$notify.success({
          title: "成功",
          message: "调整成功！",
        });
        this.$emit("cancel", "confirm");
      }).finally(()=>{
        this.isLoading = false;
      });;
    },
  },
};
</script>

<style lang="stylus" scoped>
ul {
  padding: 0;
  list-style: none;
}

.information {
  display: flex;
  border: solid 1px #ccc;
  flex-wrap: wrap;
  padding: 10px 15px;
  margin: 0;
  margin-bottom: 10px;

  li {
    width: 50%;
    height: 45px;
    line-height: 45px;
  }
}

section {
  border: solid 1px #ccc;
  padding: 15px;
  .header,.raw_data{
    display: flex
  }
  .item {
   flex: 1
  }

  .raw_data {
    align-items: center;
    li {
      line-height: 20px;
      .el-input {
        width: 80%;
      }
    }
  }
}
.el-form-item{
  display: flex
 >>> .el-form-item__content{
    flex: 1
  }
}
>>>.el-textarea__inner {
  padding-right: 50px;
}

.el-textarea {
  >>>.el-input__count {
    right: 20px !important;
  }
}
</style>
