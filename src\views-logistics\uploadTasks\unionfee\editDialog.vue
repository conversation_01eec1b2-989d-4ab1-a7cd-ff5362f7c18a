<template>
  <qDialog :visible="visible"
    :innerScroll="false"
    :innerHeight="200"
    title="编辑" width="500px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel">
    <el-form ref="editForm" :model="editForm" label-width="100" size="small">
      <el-form-item label="扣款工厂:">
        <ul class="deductionFactory">
          <li v-for="item in deductionFactory" :key="item.factoryName">
            <span>{{ item.factoryName }}</span>
            <el-switch
              v-model="item.isCutPayment"
              :active-text="item.isCutPayment == 'Y' ? '扣款' : '不扣款'"
              active-value="Y"
              inactive-value="N"
              @change="onSwitch"
              :disabled="!item.editFlag"
            >
            </el-switch>
          </li>
        </ul>
      </el-form-item>
      <el-form-item label="扣款状态:">
        <span :style="{ color: editForm.cutStatus != 'abnormal' ? '#0BB78E' : 'red' }">
          {{ editForm.cutStatus == "abnormal" ? "异常" : "正常" }}</span
        >
      </el-form-item>
      <el-form-item label="备注说明:">
        <el-input
          type="textarea"
          :rows="3"
          resize="none"
          maxlength="300"
          show-word-limit
          v-model="editForm.remark"
        >
        </el-input>
      </el-form-item>
    </el-form>
  </qDialog>
</template>

<script>
export default {
  name: "editDialog",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    formData: Object,
  },
  data() {
    return {
      editForm: {
        isLocalDeduct: "",
        remark: "",
      },
      deductionFactory: [],
      isShowSwitch: [],
      isLoading: false,
    };
  },
  created() {
    this.getDetail();
  },
  methods: {
    //获取工会费详情
    getDetail() {
      this.$api.logisticsDataUpload.unionFee
        .detailUnionFee({ id: this.formData.id })
        .then(({success,data }) => {
          if (success) {
            this.deductionFactory = data.unionFeesDetails || [];
            this.editForm = {
              ...this.editForm,
              remark:data.remark || ""
            }
            this.onSwitch();
          }
        });
    },
    onSwitch() {
      this.isShowSwitch = this.deductionFactory
        .filter((item) => item.isCutPayment == "Y")
        .map((item) => item.isCutPayment);
      this.editForm.cutStatus = this.isShowSwitch.length > 1 ? "abnormal" : "normal";
    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      this.isLoading = true
      let params = {
        id: this.formData.id,
        detailDTOS: this.deductionFactory.map((item) => ({
          id: item.id,
          factoryId: item.factoryId,
          isCutPayment: item.isCutPayment,
        })),
        remark: this.editForm.remark,
      };
      this.$api.logisticsDataUpload.unionFee.editUnionFee(params).then(({ success }) => {
        if (success) {
          this.$notify({
            title: "成功",
            message: "修改成功",
            type: "success",
          });
        }
        this.$emit("cancel", "confirm");
      }).finally(()=>{
        this.isLoading = false;
      })
    },
  },
};
</script>

<style lang="stylus" scoped>
ul {
  list-style: none;
  display: flex;
  padding: 0;
}

.deductionFactory {
  flex-direction: column;
  margin: 0;

  >li {
    span {
      margin-right: 15px;
    }
  }
}
</style>
