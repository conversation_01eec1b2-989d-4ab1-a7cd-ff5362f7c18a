<template>
  <content-panel>
    <qDialog
      :visible="visible"
      :innerScroll="false"
      :innerHeight="600"
      :title="title"
      width="500px"
      :isLoading="isLoading"
      @cancel="handleCancel"
      @confirm="handleConfirm"
      :before-close="handleCancel"
    >
      <el-form
        :model="addForm"
        :rules="rules"
        ref="addForm"
        size="small"
        label-width="173px"
      >
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="核算工厂:" prop="factoryId">
              <el-select
                v-model="addForm.factoryId"
                filterable
                clearable
                style="width: 100%"
                placeholder="请选择核算工厂"
                @change="changeFactoryId"
              >
                <el-option
                  v-for="item in factoryList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row> 
         <el-row>
          <el-col :span="24">
            <el-form-item label="核算月份:" prop="month">
              <el-date-picker :clearable="false" v-model="addForm.month" value-format="yyyy-MM" type="month"
              placeholder="核算月份" clearable>
            </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="折套产量:" prop="criterion">
              <el-input
                clearable
                placeholder="请输入"
                v-model="addForm.criterion"
                type="number"
                class="no-spin-buttons"
              > 
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
     
      <el-row>
        <el-col :span="24">
          <el-form-item class="processCode" label="备注说明:" prop="remark">
            <el-input
              type="textarea"
              v-model="addForm.remark"
              resize="none"
              rows="3"
              show-word-limit
              maxlength="300"
              placeholder=""
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      </el-form>
    </qDialog> 
  </content-panel>
</template>
<script>
import { assignValue } from "@/utils"; 
export default { 
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    modifyData: {
      type: Object,
      default: {},
    },
    editForm: Object,
    modifyNum: Number,
  },
  computed: {
    title() {
      return this.modifyNum == 1
        ? "新增"
        : this.modifyNum == 2
        ? "编辑"
        : "复制";
    }, 
  },
  data() {
    return {
      addForm: {
        factoryId: "",    
        criterion:"",
        month:"",
        remark:"",
        id: "",
      },
      isModifyType: false,
      rules: {
        factoryId: [
          { required: true, message: "核算工厂不能为空", trigger: "change" },
        ],  
        month: [
          { required: true, message: "核算月份不能为空", trigger: "change" },
        ],  
        criterion: [
          { required: true, message: "折套产量不能为空", trigger: "change" },
        ],
      },
      factoryList: [],
      isLoading: false, 
    };
  },
  created() {
    this.getFactoryList();
    this.init();
  },
  methods: {
    getFactoryList() {
      this.$api.plateTypeSystemManage.getBasicPermission
        .getQuFactory({ moduleId: 3 })
        .then((res) => {
          this.factoryList = res.data || [];
        });
    },
    init() {
      if (this.modifyData) {
        this.initModifyData();
      }
    }, 
    confirmPiece(data) {
      let names = data.map((v) => {
        return v.processName;
      });
      this.addForm.pieceProcessStr = names.join(",");
      this.addForm.pieceProcess = data.map((v) => {
        return v.id;
      });
    },

    handleConfirm() {
      this.$refs.addForm.validate((valid) => {
        if (!valid) return;
        this.isLoading = true;
        let params = {
          ...this.addForm,
          taskType: "ENVIRONMENT_SUBSIDY",
        };
        // modifyNum:1新增 2编辑 3复制
        this.$api.plateTypeSystemConfig
          .configsaveOrUpdate(params)
          .then(() => {
            this.$notify({
              title: "成功",
              message: this.title + "成功",
              type: "success",
            });
            this.$emit("cancel", "confirm");
          })
          .finally(() => {
            this.isLoading = false;
          });
      });
    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
  },
};
</script>

<style lang="stylus" scoped>
.staffCode {
  >>>.el-input-group__append {
    background: #24c69a;
    color: #fff;

    .el-button {
      padding: 5px 20px;
    }
  }
}
.selectcolor{
  color: #fff;
  background-color: #fff;

}
.el-row {
  &::before, &::after {
    display: none;
  }

  display: flex;
  justify-content: space-between;
}

.el-form-item {
  display: flex;

  >>>.el-form-item__label {
    text-align: right;

  }

  >>>.el-form-item__content {
    width: 100%;
    margin-left: 0 !important;
  }
}

.assignedFactory {
  >>>.el-form-item__label {
    &::before {
      content: '*';
      color: #F23D20;
      margin-right: 4px;
      visibility: hidden;
    }
  }
}
>>>.el-date-editor {
  width: 100%;
}
.illustrate{
  color:#FF8900;
  padding:10px;
  margin-left:15px

}
>>>.el-input-group__append button.el-button{
  color: #fff;
  background-color: #0bb78e;
  border: 4px solid #0bb78e;
}
>>>.el-input.is-disabled .el-input__inner{
  background-color: #fff;
}
::v-deep .no-spin-buttons input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

::v-deep .no-spin-buttons input::-webkit-outer-spin-button {
  -webkit-appearance: none !important;
}

::v-deep .no-spin-buttons input[type="number"] {
  -moz-appearance: textfield !important;
}
</style>
