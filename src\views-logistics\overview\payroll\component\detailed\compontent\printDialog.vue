<template>
  <qDialog
    :visible="visible"
    :innerScroll="false"
    title="设置打印显示字段"
    width="800px"
    :innerHeight="500"
    :showFooter="false"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <el-row :gutter="20">
      <el-col :span="10" class="items_left">
        <el-input
          style="margin-bottom: 15px"
          v-model="value"
          clearable
          placeholder="请输入关键字"
        >
        </el-input>
        <el-table
          ref="multipleTable"
          stripe
          border
          v-loading="loading"
          :height="300"
          :data="list"
          highlight-current-row
          @select="handleSelect"
          @select-all="handleSelectAll"
        >
          <el-table-column
            width="40"
            type="selection"
            :selectable="handleSelectable"
          ></el-table-column>
          <el-table-column prop="columnName" label="字段名称">
          </el-table-column>
        </el-table>
      </el-col>
      <el-col :span="14">
        <el-tabs type="card">
          <el-tab-pane :label="`已选择(${flatSelectionData.length})`">
            <el-table
              border
              stripe
              :height="300"
              v-loading="isLoading"
              highlight-current-row
              :data="flatSelectionData"
            >
              <el-table-column
                label="序号"
                type="index"
                width="50"
              ></el-table-column>
              <el-table-column
                prop="columnName"
                label="字段名称"
              ></el-table-column>
              <el-table-column label="操作" width="60">
                <template slot-scope="{ row }">
                  <el-button
                    v-show="row.fieldRequired != -1"
                    style="padding: 5px 0; color: red"
                    type="text"
                    @click="handleDelete(row)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>
    <div style="text-align: right; padding: 20px 0 10px 0">
      <el-button type="primary" :loading="printLoading" @click="getPrintPreview"
        >打印预览</el-button
      >
    </div>
  </qDialog>
</template>

<script>
import { sallryPrintPreview } from "@/utils";
import moment from "moment";
export default {
  name: "printDialog",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    searchForm: {
      type: Object,
      default: {},
    },
    rowList: {
      type: Array,
      default: [],
    },
    params: {
      type: Object,
      default: {},
    },

  },
  data() {
    return {
      value: "",
      tableData: [],
      multipleTableData: [],
      loading: false,
      isLoading: false,
      printLoading: false,
      fileUrl: "",
      xdata: "",
    };
  },
  created() {
    this.getList();
  },
  computed: {
    list() {
      return this.value
        ? this.tableData.filter((item) => item.columnName.includes(this.value))
        : this.tableData;
    },
    flatSelectionData() {
      let arr = this.multipleTableData;
      let arrIds = Array.from(new Set(arr.map((v) => v.columnName)));
      let newArr = [];
      arrIds.forEach((columnName) => {
        newArr.push(arr.find((v) => v.columnName === columnName));
      });
      return newArr.sort((a, b) => a.colSort - b.colSort);
    },
  },
  watch: {
    list: {
      handler(val) {
        val.length && this.computedNode();
      },
      immediate: true,
    },
    flatSelectionData: {
      handler(value) {
        value.length > 30 && this.$message.warning("最多选择30个字段");
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    //获取列表
    getList() {
      this.loading = true;
      this.isLoading = true;
      // this.printLoading = true;
      let { factoryId, accountingMonth } = JSON.parse(this.$route.query.data);
      this.$api.logisticsWorkbench
        .getPrintHeader({ factoryId, accountingMonth })
        .then(({ data: { baseConfig, allConfig } }) => {
          this.multipleTableData = baseConfig || [];
          this.tableData = allConfig || [];
        })
        .finally(() => {
          this.loading = false;
          this.isLoading = false;
          // this.printLoading = false;
        });
    },
    handleSelectable(row, index) {
      let findObj =
        this.multipleTableData.find((v) => v.columnName === row.columnName) ||
        {};
      return findObj && findObj.fieldRequired != -1;
    },
    //获取打印预览的数据
    getPrintPreview() {
      if (this.flatSelectionData.length > 30) {
        return this.$message.warning("打印预览最多支持30条数据");
      }

      this.printLoading = true;
      let { factoryId, accountingMonth } = JSON.parse(this.$route.query.data);
      let params = {
        factoryId,
        accountingMonth,
        fields: this.flatSelectionData,
        ...this.searchForm,
        ...this.params
      };
      this.$api.logisticsWorkbench
        .exportSalaryPrint(params)
        .then((res) => {
          // let list = res.data.pageInfo.list.splice(0,30)
          // this.printPreview(list);
          this.printPreview(res.data.pageInfo.list);
        })
        .finally(() => {
          this.printLoading = false;
        });
    },
    //回显勾选
    computedNode() {
      console.log('list',this.list);
      this.$nextTick(() => {
        const data = this.list.filter((v) =>
          this.flatSelectionData.find((k) => k.columnName === v.columnName)
        );
        data.forEach((row) => {
          this.$refs.multipleTable.toggleRowSelection(row);
        });
      });
    },
    handleSelect(data, row) {
      let currentData = this.multipleTableData;
      const checkStatus = !currentData.find(
        (v) => v.columnName === row.columnName
      );
      if (checkStatus) {
        // 勾选
        this.multipleTableData = [...currentData, ...data];
      } else {
        // 取消勾选
        currentData = currentData.filter(
          (v) => v.columnName !== row.columnName
        );
        this.multipleTableData = currentData;
      }
    },
    handleSelectAll(data) {
      let currentData = this.multipleTableData;
      if (data.length === 0) {
        let clearData = this.tableData;
        let checkedData = currentData.filter(
          (v) => !clearData.find((k) => k.columnName === v.columnName)
        );
        this.multipleTableData = checkedData;
      } else {
        (data.length === this.tableData.length &&
          (this.multipleTableData = [...currentData, ...data])) ||
          (this.multipleTableData = currentData.filter((v) =>
            data.find((k) => k.columnName === v.columnName)
          ));
      }
    },
    handleDelete(row) {
      this.$refs.multipleTable.toggleRowSelection(
        this.list.find((item) => item.columnName == row.columnName)
      );
      this.multipleTableData = this.multipleTableData.filter(
        (v) => v.columnName !== row.columnName
      );
    },
    printPreview(pageInfo) {
      const { accountingMonth, factoryName } = JSON.parse(
        this.$route.query.data
      );
      const title = `${moment(accountingMonth).format(
        "YYYY年MM月"
      )}${factoryName}工资表`;
      const date = `日期:${moment(accountingMonth)
        .startOf("month")
        .format("YYYY年MM月DD日")}-${moment(accountingMonth)
        .endOf("month")
        .format("YYYY年MM月DD日")}`;
        sallryPrintPreview(pageInfo, this.flatSelectionData, title, date);

    },
    handleCancel() {
      this.$emit("cancel", "cancel");
    },
    handleConfirm() {
      this.$emit("cancel", "confirm");
    },
  },
};
</script>

<style lang="stylus" scoped>
</style>
