<template>
  <!-- 工序汇总表 -->
  <content-panel>
    <table-panel ref="tablePanel">
      <template v-slot:header-left>
        <div class="header_tableName">{{ filterName.factory }}工序汇总表</div>
        <div class="header_date">日期:{{ filterName.date }}</div>
      </template>
      <vxe-table
        :key="tableKey"
        ref="tableRef"
        resizable
        stripe
        border
        :loading="loading"
        :loading-config="{
          icon: 'vxe-icon-indicator roll',
          text: '正在拼命加载中...',
        }"
        highlight-hover-row
        :height="maxTableHeight"
        :data="tableData"
      >
        <template v-if="isTableShow">
          <!-- 非分组列 -->
          <vxe-column
            v-for="column in columnList.filter(col => !col.children)"
            :key="column.fieldName"
            :field="column.fieldName"
            :title="column.columnName"
            :width="column.width"
            :fixed="column.fixed"
            show-overflow
          >
          <template #default="{ row }">
              <span>
                <div v-if="column && column.fieldType === 'double'">
                  {{ filterData(row[column.fieldName]) }}
                </div>
                <div v-else>
                  {{ row[column.fieldName] }}
                </div>
              </span>
            </template>
          </vxe-column>

          <!-- 分组列 -->
          <!-- <vxe-colgroup
            v-for="group in columnList.filter(col => col.children)"
            :key="group.fieldName"
            :title="group.columnName"
            header-align="center"
            :fixed="group.fixed"
          >
            <vxe-column
              v-for="child in group.children"
              :key="child.fieldName"
              :field="child.fieldName"
              :title="child.columnName"
              :width="child.width"
              :fixed="child.fixed"
              show-overflow
            ></vxe-column>
          </vxe-colgroup> -->
        </template>
      </vxe-table>
    </table-panel>
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import { moneyFormat } from "@/utils";
import moment from "moment";
export default {
  mixins: [tableMixin,pagePathMixin],
  data() {
    return {
      columnList:[],//表头数据
      tableData: [], //表格数据
      isTableShow: true,
      headFixed: ['serialNumber','bigGroup','accountingProcess'], //固定
      loading: false,
      filterParam: {},
      tableKey: 0,
      factoryId: "",
      info: {},
    };
  },
  created() {},
  computed: {
    filterName() {
      let { accountingMonth, factoryName } = this.info;
      return {
        factory: `${moment(accountingMonth).format("YYYY年MM月")} ${factoryName}`,
        date: `${moment(accountingMonth)
          .startOf("month")
          .format("YYYY年MM月DD日")} - ${moment(accountingMonth)
          .endOf("month")
          .format("YYYY年MM月DD日")}`,
      };
    },
  },
  watch: {
    $route: {
      handler(value) {
        if (value.path.includes("payroll")&&value.path.includes("plateType")) {
          this.info = JSON.parse(value.query.data);
          this.factoryId = this.info.factoryId;
          this.getTabHead()
          this.getList();
        }
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {
  },
  methods: {
    getValueByStringLength(str) {
      if (!str) return 0;
      const length = str.length;
      if (str=='补贴') return 100;
      if (length <= 2) return 80;
      if (length <= 5) return 100;
      if (length <= 7) return 120;
      if (length <= 9) return 150;
      return 180;
    },
    getTabHead(){
      this.$api.plateTypeWorkbench
        .getProcessPayrollHead()
        .then(res => {
          this.columnList =  res.data.map(item => {
            return {
                columnName: item.columnName,
                fieldName: item.fieldName,
                fieldType: item.fieldType,
                fixed: item.fixed || this.headFixed.includes(item.fieldName) ? 'left' :'',
                width: item.width || this.getValueByStringLength(item.columnName)
            }
          })
          }
        )
    },
    getList() {
      this.loading = true;
      this.$api.plateTypeWorkbench
        .getProcessPayroll({
          factoryId: this.factoryId,
          accountingMonth: this.info.accountingMonth,
        })
        .then(res => {
          this.isTableShow = false;
          this.tableData=res.data
          this.isTableShow = true
          this.tableKey = Date.now() + Math.random();
          }
        )
        .finally(() => {
          this.loading = false;
        });
    },
    filterData(value){
      if(!value) return '-'
      return moneyFormat(value)
    }
  },
};
</script>
<style lang="stylus" scoped>
ellipsis() {
  font-size: 16px;
  font-weight: 600;
}

.header_tableName {
  ellipsis();
  font-size: 22px;
  text-align: center;
  margin-bottom: 8px;
}

.header_date {
  ellipsis();
  font-size: 18px;
  text-align: center;
}


>>>.table-panel-footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  >ul {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      white-space: nowrap
      padding: 0 10px;
    }
  }
}

section {
  display: flex;
  justify-content: space-between;

  .header_content_left {
    ellipsis();
  }

  .header_content_right {
    ellipsis();
  }
}
</style>
