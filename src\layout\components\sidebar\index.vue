<template>
  <div :class="{'has-logo':showLogo}">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <!-- :default-active="activeMenu" -->
      <el-menu
        :collapse="isCollapse"
        :unique-opened="false"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item v-for="menu in menus" :key="menu.id" :item="menu"/>
      </el-menu>
    </el-scrollbar>
  </div>
</template>
<script>
import Logo from './logo';
import SidebarItem from './sidebar-item'
import { mapGetters } from 'vuex'

export default {
  components: {
    Logo,
    SidebarItem
  },
  data() {
    return {
      showLogo: true
    }
  },
  computed: {
    ...mapGetters([
      'menus',
      'sidebar'
    ]),
    routes() {
      console.log('routes:', this.$router.options.routes)
      return this.$router.options.routes
    },
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    isCollapse() {
      return !this.sidebar.opened
    }
  }
}
</script>
<style lang="stylus" scoped>
 
</style>