const path = require('path')
const webpack = require('webpack')
const name = 'vue Admin Template'

function resolve(dir) {
  return path.join(__dirname, dir)
}

module.exports = {
  lintOnSave: false,
  publicPath: '',
  assetsDir: 'static',
  devServer: {
    port: 9526,
    proxy: {
      '/api': {
        target: 'https://wascs.quanyou.com.cn/',
        changeOrigin: true,
        pathRewrite: {
          '^/api': '/gw'
        },
      }
    }
  },
  configureWebpack: {
    // provide the app's title in webpack's name field, so that
    // it can be accessed in index.html to inject the correct title.
    name: name,
    resolve: {
      alias: {
        '@': resolve('src')
      }
    },
    plugins: [
      // 接收cross-env中的参数
      new webpack.DefinePlugin({
        'process.env': {
          BUILD_ENV: JSON.stringify(process.env.BUILD_ENV)
        }
      })
    ]
  },
  chainWebpack(config) {
    config.module
      .rule('vue')
      .use('vue-loader')
      .loader('vue-loader')
      .tap(options => {
        options.compilerOptions.preserveWhitespace = true
        return options
      })
      .end()

    config
      // https://webpack.js.org/configuration/devtool/#development
      .when(process.env.NODE_ENV === 'development',
        config => config.devtool('cheap-source-map')
      )

      config
      .plugin('html')
      .tap(args => {
        args[0].title = '定制工资管理';
        return args;
      })
  }
}
