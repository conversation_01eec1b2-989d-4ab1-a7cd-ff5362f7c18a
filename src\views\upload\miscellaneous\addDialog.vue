<template>
  <qDialog :visible="visible"
    :innerScroll="false"
    :innerHeight="500"
    :title="title"
    width="800px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel">
    <el-form :model="addForm"
      :rules="rules"
      ref="addForm"
      size="small"
      label-width="118px">
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item
            class="assignedFactory"
            label="员工姓名:">
            {{ addForm.staffName }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            v-if="title == '新增'"
            label="厂牌编号:"
            prop="staffCode">
            <el-input
              class="staffCode"
              v-model="addForm.staffCode"
              clearable
              placeholder="请输入厂牌编号">
              <template
                slot="append">
                <el-button
                  type="primary"
                  @click="searchEmployee">
                  查询
                </el-button>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item v-else
            label="厂牌编号:">{{
            addForm.staffCode
          }}</el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item
            class="assignedFactory"
            label="身份证号:"
            prop="idCard">
            <template>
              <span>{{ addForm.idCard }}</span>
              <i v-if="addForm.idCard"
                :class="['iconfont', showType ? 'icon-guanbi' : ' icon-yincangmima']"
                @click="toggle"></i>
            </template>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item
            class="assignedFactory"
            label="派出分厂:"
            prop="dispatchedFactory">
            <el-input
              v-model="addForm.dispatchedFactory"
              clearable
              maxlength="10"
              show-word-limit
              placeholder="请输入派出分厂"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            class="assignedFactory"
            label="派入分厂:"
            prop="assignedFactory">
            <el-input
              v-model="addForm.assignedFactory"
              clearable
              maxlength="10"
              show-word-limit
              placeholder="请输入派入分厂"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item
            class="assignedFactory"
            label="外出地点:"
            prop="area">
            <el-input
              v-model="addForm.area"
              clearable
              maxlength="10"
              show-word-limit
              placeholder="请输入外出地点"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            class="assignedFactory"
            label="外出事由:"
            prop="reasons">
            <el-input
              v-model="addForm.reasons"
              clearable
              maxlength="10"
              show-word-limit
              placeholder="请输入外出事由"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item
            class="assignedFactory"
            label="开始日期:"
            prop="startDate">
            <el-date-picker
              v-model="addForm.startDate"
              type="date"
              placeholder="选择开始日期">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            class="assignedFactory"
            label="结束日期:"
            prop="endDate">
            <el-date-picker
              v-model="addForm.endDate"
              type="date"
              :picker-options="endDateOptions"
              placeholder="选择结束日期">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item
            label="出勤合计:"
            prop="workDays">
            <el-input
              oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+2)}"
              v-model="addForm.workDays"
              clearable
              placeholder="请输入出勤合计">
              <template
                slot="append">
                天
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            class="assignedFactory"
            label="加班合计:"
            prop="overtimeHour">
            <el-input
              oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+2)}"
              v-model="addForm.overtimeHour"
              clearable
              placeholder="请输入加班合计">
              <template
                slot="append">
                时
              </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item
            class="assignedFactory"
            label="备注:"
            prop="remark">
            <el-input
              type="textarea"
              v-model="addForm.remark"
              resize="none"
              rows="3"
              maxlength="20"
              show-word-limit
              placeholder=""></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </qDialog>
</template>

<script>
import { numAdd } from "@/utils";
import moment from 'moment';
export default {
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    editForm: Object,
  },
  data() {
    return {
      addForm: {
        staffName: "",
        staffCode: "",
        idCard: "",
        idCardOrigin: "",
        idCardDecoded: "",
        assignedFactory: "",
        dispatchedFactory: "",
        area: "",
        reasons: "",
        workDays: "",
        overtimeHour: "",
        remark: "",
        startDate: "",
        endDate: "",
      },
      rules: {
        staffCode: [{ required: true, message: "厂牌编号不能为空", trigger: "blur" }],
        workDays: [
          { required: true, message: "出勤合计不能为空", trigger: "blur" },
          {
            pattern: /^(0|[0-9]{1,2})((\.\d{0,1})*)$/,
            message: "小数点前面仅支持2位数,小数点后面仅支持1位数",
            trigger: "blur",
          },
        ],
        overtimeHour: [
          {
            pattern: /^(0|[0-9]{1,2})((\.\d{0,1})*)$/,
            message: "小数点前面仅支持2位数,小数点后面仅支持1位数",
            trigger: "blur",
          },
        ],
      },
      showType: false,
      idCardRsa: "",
      isLoading: false,
    };
  },
  async created() {
    if (this.title == '编辑') {
      await this.getDetails();
    }
  },
  computed: {
    endDateOptions() {
      return {
        disabledDate: (time) => time.getTime() < new Date(this.addForm.startDate).getTime()
      }
    }
  },
  methods: {
    //杂工考勤详情
    getDetails() {
      return this.$api.dataUpload.backManAttendance.backManAttendanceView({ id: this.editForm.id }).then(({ data }) => {
        this.idCardRsa = data && data.idCardRsa || "";
        this.addForm.idCardOrigin = data && data.idCard || "";
        this.addForm.idCard = data && data.idCard || "";
        this.addForm = {
          ...this.addForm,
          ...data
        }
      })
    },
    //查询员工信息
    searchEmployee() {
      if (!this.addForm.staffCode) {
        this.$message.warning("请输入厂牌编号");
        return;
      }
      this.$api.information.employee
        .employeeDetails({ staffCode: this.addForm.staffCode })
        .then(({ data }) => {
          this.idCardRsa = data && data.idCardRsa || "";
          this.addForm = data && {
            ...this.addForm,
            staffName: data.staffName || "",
            idCard: data.idCard || "",
            idCardOrigin: data.idCard || "",
          } || this.addForm
        });
    },
    //身份证解码
    toggle() {
      if (this.showType) {
        this.addForm.idCard = this.addForm.idCardOrigin;
        this.showType = false;
      } else {
        if (this.addForm.idCardDecoded) {
          this.addForm.idCard = this.addForm.idCardDecoded;
          this.showType = true;
          return;
        }
        this.$api.information.employee.decrypt(this.idCardRsa).then((res) => {
          this.addForm.idCard = res.data;
          this.addForm.idCardDecoded = res.data;
          this.showType = true;
        });
      }
    },
    handleCancel() {
      this.$emit("cancel", 'cancel');
    },
    handleConfirm() {
      this.$refs.addForm.validate((valid) => {
        if (!valid) return;
        if (!this.addForm.staffName) {
          this.$message.warning("员工姓名为空,请先查询员工姓名");
          return;
        }
        this.isLoading = true;
        const { factoryId, accountingMonth } = JSON.parse(this.$Base64.decode(this.$route.query.data));
        let params = {
          ...this.addForm,
          factoryId,
          accountingMonth,
          startDate: this.addForm.startDate && moment(this.addForm.startDate).format('YYYY-MM-DD') || '',
          endDate: this.addForm.endDate && moment(this.addForm.endDate).format('YYYY-MM-DD') || '',
        };
        delete params.idCardDecoded;
        delete params.idCardOrigin;
        delete params.staffName;
        delete params.idCard;
        if (this.title == "新增") {
          this.$api.dataUpload.backManAttendance.addBackManAttendance(params).then(() => {
            this.$notify({
              title: "成功",
              message: "新增成功",
              type: "success",
            });
            this.$emit("cancel", "confirm");
          }).finally(()=>{
              this.isLoading = false;
          });
        } else {
          this.$api.dataUpload.backManAttendance
            .editBackManAttendance({
              ...params,
              id: this.editForm.id,
            })
            .then((res) => {
              this.$notify({
                title: "成功",
                message: "编辑成功",
                type: "success",
              });
              this.$emit("cancel", "confirm")
            }).finally(()=>{
              this.isLoading = false;
            });
        }
      });
    },
  },
};
</script>

<style lang="stylus" scoped>
.staffCode {
  >>>.el-input-group__append {
    background: #24c69a;
    color: #fff;

    .el-button {
      padding: 5px 20px;
    }
  }
}

.el-row {
  &::before, &::after {
    display: none;
  }

  display: flex;
  justify-content: space-between;
}

.el-form-item {
  display: flex;

  >>>.el-form-item__label {
    text-align: left;
  }

  >>>.el-form-item__content {
    width: 100%;
    margin-left: 0 !important;
  }
}

.assignedFactory {
  >>>.el-form-item__label {
    &::before {
      content: '*';
      color: #F23D20;
      margin-right: 4px;
      visibility: hidden;
    }
  }
}
</style>
