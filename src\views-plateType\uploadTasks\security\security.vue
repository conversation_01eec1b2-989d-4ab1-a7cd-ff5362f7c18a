<template>
  <!-- 社保扣款 -->
  <content-panel>
    <template v-slot:search>
      <search-box class="search-box">
        <el-form :inline="true" :model="searchForm" ref="searchForm" label-position="left" size="mini">
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input clearable v-model.trim="searchForm.staffName" size="mini" @keyup.enter.native="onSearch">
              <template slot="append">
                <search-batch @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)" ref="childrenStaffNames" titleName="员工姓名" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input v-model.trim="searchForm.staffCode" size="mini" clearable @keyup.enter.native="onSearch">
              <template slot="append">
                <search-batch @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)" ref="childrenStaffCodes" titleName="厂牌编号" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="20元保险:" prop="isTwentyInsurance">
            <el-select v-model="searchForm.isTwentyInsurance" placeholder="请选择状态" clearable @change="onSearch">
              <el-option v-for="item in insuranceOption" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="分配状态:" prop="deductStatus">
            <el-select v-model="searchForm.deductStatus" placeholder="请选择分配状态" clearable @change="onSearch">
              <el-option label="正常" value="normal"> </el-option>
              <el-option label="异常" value="abnormal"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="考勤状态:" prop="attendStatus">
            <el-select v-model="searchForm.attendStatus" placeholder="请选择考勤状态" clearable @change="onSearch">
              <el-option label="正常" value="normal"> </el-option>
              <el-option label="异常" value="abnormal"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="保险状态:" prop="twentyStatus">
            <el-select v-model="searchForm.twentyStatus" placeholder="请选择保险状态" clearable @change="onSearch">
              <el-option label="正常" value="normal"> </el-option>
              <el-option label="异常" value="abnormal"> </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary" @click="onSearch">
            查询
          </el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-left>
        <ul>
          <li>
            <span>核算工厂:</span><span>{{ info.factoryName }}</span>
          </li>
          <li>
            <span>核算月份:</span><span>{{ info.accountingMonth }}</span>
          </li>
          <li>
            <span>人数:</span><span>{{ debitInfo.sysTotal }}</span>
          </li>
          <li>
            <span>社保扣款:</span><span>{{ debitInfo.totalSocialSecurity | moneyFormat }}</span>
          </li>
          <li>
            <span>20元保险:</span><span>{{ debitInfo.totalTwentySecurity | moneyFormat }}</span>
          </li>
        </ul>
      </template>
      <template v-slot:header-right>
        <el-button size="small" type="primary" v-show="permission" @click="handleAdd">
          新增
        </el-button>
        <el-button size="small" type="primary" @click="handleImport" v-show="permission">
          导入
        </el-button>
        <el-button size="small" v-show="permission" type="primary" @click="bulkeDit">
          批量编辑
        </el-button>
        <el-button size="small" v-show="permission" type="primary" @click="batchDelete">
          批量删除
        </el-button>
        <!-- v-show="permission" -->
        <el-button size="small" type="primary" @click="handleExport">
          导出
        </el-button>
      </template>
      <el-table stripe border v-loading="loading" ref="tableRef" highlight-current-row :data="tableData" select-all
        :height="maxTableHeight" style="width: 100%" :row-class-name="tableRowClassName"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="40"> </el-table-column>
        <el-table-column label="员工姓名" prop="staffName" width="80" show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="厂牌编号" prop="staffCode" width="120" show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="核算月份" prop="accountingMonth" width="100">
        </el-table-column>
        <el-table-column label="身份证号" prop="idCard" width="180">
        </el-table-column>
        <el-table-column label="社保扣款" prop="socialSecurityDeduct">
          <template slot-scope="{ row }">
            {{ row.socialSecurityDeduct | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column label="20元保险" prop="twentySecurity" width="80">
        </el-table-column>
        <el-table-column label="厂牌分配状态" width="105" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span :style="{ color: row.deductStatus == 'normal' ? '' : 'red' }">{{ filterData(row.deductStatus)
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="考勤状态" width="80" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span :style="{ color: row.attendStatus == 'abnormal' ? 'red' : '' }">{{ filterData(row.attendStatus)
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="保险状态" width="80" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span :style="{ color: row.twentyStatus == 'abnormal' ? 'red' : '' }">{{ filterData(row.twentyStatus)
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="修改时间" prop="updateTime" width="150">
          <template slot-scope="{ row }">
            {{ row.updateTime | dateFormat }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" prop="remarks" align="left" width="150" v-if="permission">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button @click="handleDelete(scope.row)" slot="reference" type="text" size="mini">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <el-pagination :current-page="pageNum" :page-size="pageSize" :total="total" :page-sizes="[50, 100, 200]"
          layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
        </el-pagination>
      </template>
    </table-panel>
    <add-dialog v-if="visible" :visible="visible" :title="title" :editForm="editForm"
      @cancel="handleCancel"></add-dialog>
    <batch-delete-dialog v-if="isVisible" :isVisible="isVisible" :title="title" :formData="formData"
      @cancel="deleteCancel"></batch-delete-dialog>
    <Import v-if="ImportVisible" :visible="ImportVisible" @cancel="cancel" @confirm="confirm"
      :importInfo="importInfo" />
  </content-panel>
</template>

<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import addDialog from "./addDialog";
import batchDeleteDialog from "./batchDeleteDialog";
export default {
  name: "PlateTypeSecurity",
  components: { addDialog, batchDeleteDialog },
  mixins: [tableMixin, pagePathMixin],
  data() {
    return {
      searchForm: {
        staffCode: "",
        staffName: "",
        isTwentyInsurance: "",
        attendStatus: "",
        deductStatus: "",
        twentyStatus: "",
      },
      idList: [],
      insuranceOption: [
        { label: "开启", value: "1" },
        { label: "关闭", value: "0" },
      ],
      title: "",
      tableData: [],
      loading: false,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      visible: false,
      isVisible: false,
      ImportVisible: false,
      importInfo: {},
      editForm: {},
      formData: {},
      resizeOffset: 55, //设置table间距
      filterParam: {},
      params: {},
      factoryId: "",
      permission: "",
      debitInfo: { sysTotal: 0, totalSocialSecurity: 0 },
      info: {},
    };
  },
  created() { },
  watch: {
    $route: {
      handler(value) {
        if (
          value.path.includes("security") &&
          value.path.includes("plateType")
        ) {
          this.info = JSON.parse(this.$Base64.decode(value.query.data));
          this.factoryId = this.info.factoryId;
          this.importInfo = {
            reportName: "plankSocialsecuritydeductimport",
            paramMap: {
              columnValue: "板木-社保扣款",
              factoryId: this.factoryId,
              accountingMonth: this.info.accountingMonth,
              id: this.info.id,
            },
          };
          this.getList();
          this.getDebitList();
          this.getPermission();
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    //导出
    handleExport() {
      let params = {
        factoryId: this.factoryId,
        operateType: 'export',
        accountingMonth: this.info.accountingMonth,
      };
      if (Object.keys(this.filterParam).length) {
        params = {
          ...params,
          ...this.filterParam,
        };
      }
      this.$api.common
        .doExport("plankExportSocialSecurity", { ...params, ...this.params })
        .then((res) => {
          this.$message.success("导出操作成功，请前往导出记录查看详情");
        });
    },
    //社保扣款列表
    getList() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        filterData: {
          accountingMonth: this.info.accountingMonth,
          factoryId: this.factoryId,
          ...this.filterParam,
          ...this.params,
        },
      };
      this.$api.plateTypeDataUpload.SocialSecurity.getSocialSecurityList(params)
        .then(({ data: { list, total } }) => {
          this.tableData = list || [];
          this.total = total || 0;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //是否具有待办任务操作权限
    getPermission() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.info.accountingMonth,
        type: "8",
      };
      this.$api.plateTypeWorkbench.agencyPermission(params).then((res) => {
        this.permission = res.data;
      });
    },
    getDebitList() {
      let params = {
        accountingMonth: this.info.accountingMonth,
        factoryId: this.factoryId,
        ...this.filterParam,
        ...this.params,
      };
      this.$api.plateTypeDataUpload.SocialSecurity.getSocialSecurityStatistic(
        params
      ).then(({ data }) => {
        this.debitInfo = data || {};
      });
    },
    tableRowClassName({ row, rowIndex }) {
      let color = "";
      for (let item of this.idList.values()) {
        if (item === row.id) color = "table-SelectedRow-bgcolor";
      }
      return color;
    },
    filterData(data) {
      if (!data) return null;
      return data == "abnormal" ? "异常" : "正常";
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0)
          this.filterParam[name] = data;
      }

      this.pageNum = 1;
      this.getList();
      this.getDebitList();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },

    //新增
    handleAdd() {
      this.title = "新增";
      this.visible = true;
      this.editForm = {};
    },
    //编辑
    handleEdit(row) {
      this.title = "编辑";
      this.visible = true;
      this.editForm = {
        id: row.id || "",
      };
    },
    //删除
    handleDelete(row) {
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$api.plateTypeDataUpload.SocialSecurity.getSocialSecurityDelete({
            id: row.id,
          }).then(() => {
            this.$notify.success({
              title: "成功",
              message: "删除成功",
            });
            this.getList();
            this.getDebitList();
          });
        })
        .catch(() => { });
    },
    //多选
    handleSelectionChange(val) {
      this.idList = (val && val.map((item) => item.id)) || [];
    },
    // 批量删除
    batchDelete() {
      if (this.idList.length === 0) {
        this.$message({
          message: "请先勾选需要批量删除的内容",
          type: "warning",
        });
        return;
      }
      this.isVisible = true;
      this.title = '批量删除';
      this.formData = {
        idList: this.idList,
      };
    },
    //批量编辑
    bulkeDit() {
      if (this.idList.length === 0) {
        this.$message({
          message: "请先勾选需要批量编辑的内容",
          type: "warning",
        });
        return;
      }
      this.title = "批量编辑";
      this.isVisible = true;
      this.formData = {
        idList: this.idList,
      };
    },

    handleCancel(type) {
      this.visible = false;
      if (type == "cancel") return;
      this.getList();
      this.getDebitList();
    },
    deleteCancel(type) {
      this.isVisible = false;
      if (type == "cancel") return;
      this.getList();
      this.getDebitList();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },
    handleCurrentChange(val) {
      this.pageNum = val;
      this.getList();
    },
    //导入
    handleImport() {
      this.title = "导入";
      this.ImportVisible = true;
    },
    cancel(value) {
      this.ImportVisible = value;
    },
    confirm(value) {
      this.ImportVisible = value;
    },
  },
};
</script>

<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}

.el-table {
  /deep/ .table-SelectedRow-bgcolor {
    .el-table__cell {
      background-color: #0bb78e29 !important;
    }
  }
}

.el-form--label-left {
  >>>.el-form-item__label {
    text-align: right !important;
  }
}

#item {
  margin: 0;
  padding: 5px;
}

ul {
  height: 34px;
  list-style: none;
  display: flex;
  align-items: center;
  padding: 0;
  margin: 0
  li{
    margin-right: 10px;
  }
}

i {
  font-style: normal;
}
</style>
