<template>
  <content-panel>
    <!-- 体检费 -->
    <template v-slot:search>
      <search-box class="search-box">
        <el-form
          :inline="true"
          :model="searchForm"
          ref="searchForm"
          label-position="left"
          size="mini"
        >
          <el-form-item label="员工姓名:" prop="staffName">
            <el-input
              clearable
              v-model.trim="searchForm.staffName"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffNames', $event)"
                  @focusEvent="focusEvent('staffNames', $event)"
                  ref="childrenStaffNames"
                  titleName="员工姓名"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="厂牌编号:" prop="staffCode">
            <el-input
              clearable
              v-model.trim="searchForm.staffCode"
              size="mini"
              @keyup.enter.native="onSearch"
            >
              <template slot="append">
                <search-batch
                  @seachFilter="onSearch('staffCodes', $event)"
                  @focusEvent="focusEvent('staffCodes', $event)"
                  ref="childrenStaffCodes"
                  titleName="厂牌编号"
                />
              </template>
            </el-input>
          </el-form-item>
        </el-form>
        <template v-slot:right>
          <el-button size="small" type="primary"  @click="onSearch">
            查询
          </el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置
          </el-button>
        </template>
      </search-box>
    </template>
    <table-panel ref="tablePanel">
      <template v-slot:header-right>
        <el-button v-show="permission" size="small" type="primary" @click="handleImport">
          导入
        </el-button>
      </template>
      <el-table
        stripe
        border
        v-loading="loading"
        ref="tableRef"
        highlight-current-row
        :data="tableData"
        :height="maxTableHeight"
      >
        <el-table-column
          label="员工姓名"
          prop="staffName"
          width="110"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="厂牌编号"
          prop="staffCode"
          width="110"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column label="分配厂牌" width="80" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <span
              :style="{
                color: row.deductStatus == 'abnormal' ? 'red' : '',
              }"
              >{{ row.deductStatus == "abnormal" ? "异常" : "正常" }}</span
            >
          </template>
        </el-table-column>
        <el-table-column width="200" label="身份证号" prop="idCard">
          <template slot-scope="{ row }">
            <div style="display: flex; justify-content: space-between">
              <div class="idCardBox">
                <div class="idCard">
                  {{ row.idCard }}
                </div>
              </div>
              <i
                v-if="row.idCard"
                :class="['iconfont', row.isShow ? 'icon-guanbi' : ' icon-yincangmima']"
                @click="toggleEye(row)"
              ></i>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="性别" width="50" prop="gender" show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="年龄" width="50" prop="age"> </el-table-column>
        <el-table-column label="区域" prop="area" show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="岗位名称" prop="job"> </el-table-column>
        <el-table-column label="体检项目" prop="examItem" show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="体检费用" prop="examAmount" show-overflow-tooltip>
          <template slot-scope="{ row: { examAmount } }">
            {{ examAmount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column label="备注说明" prop="remark" show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="离职类型" prop="quitType" show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="离职日期" prop="quitDate" show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="操作" width="80" v-if="permission">
          <template slot-scope="{ row }">
            <el-button size="mini" type="text" @click="handleEdit(row)"> 编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <ul>
          <li>
            <span>核算月份:</span><span>{{ infoList.accountingMonth }}</span>
          </li>
          <li>
            <span>人数:</span><span>{{ debitInfo.people }}</span>
          </li>
          <li>
            <span>体检费:</span><span>{{ debitInfo.amount | moneyFormat }}</span>
          </li>
        </ul>
        <el-pagination
          :current-page="pageNum"
          :page-size="pageSize"
          :total="total"
          :page-sizes="[50, 100, 200]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onSizeChange"
          @current-change="onNumChange"
        >
        </el-pagination>
      </template>
    </table-panel>
    <edit-dialog
      v-if="visible"
      :visible="visible"
      :formData="formData"
      @cancel="handleCancel"
    ></edit-dialog>
    <Import
      v-if="ImportVisible"
      :visible="ImportVisible"
      @cancel="cancel"
      @confirm="confirm"
      :importInfo="importInfo"
    />
  </content-panel>
</template>
<script>
import tableMixin from "@/utils/tableMixin";
import pagePathMixin from "@/utils/page-path-mixin";
import editDialog from "./editDialog.vue";
export default {
  name: "LogisticsPhysicalexamination",
  mixins: [tableMixin,pagePathMixin],
  components: { editDialog },
  data() {
    return {
      searchForm: {
        staffCode: "",
        staffName: "",
      },
      infoList: {},
      tableData: [],
      loading: false,
      pageSize: 50,
      pageNum: 1,
      total: 0,
      filterParam: {},
      resizeOffset: 72,
      params: {},
      factoryId: "",
      permission: "",
      debitInfo: { sysTotal: 0, totalSocialSecurity: 0 },
      ImportVisible: false,
      visible: false,
      formData: {},
    };
  },
  watch: {
    $route: {
      handler(value) {
        if (value.path.includes("physicalexamination")&&value.path.includes("logistics")) {
          this.infoList = JSON.parse(this.$Base64.decode(value.query.data));
          this.factoryId = this.infoList.factoryId;
          this.importInfo = {
            reportName: "logisticsImportphysicalexam",
            paramMap: {
              columnValue: "物流-体检费",
              factoryId: this.factoryId,
              accountingMonth: this.infoList.accountingMonth,
              id: this.infoList.id,
            },
          };
          this.getList();
          this.getDebitList();
          this.getPermission();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    //获取体检费列表
    getList() {
      this.loading = true;
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        filterData: {
          factoryId: this.factoryId,
          accountingMonth: this.infoList.accountingMonth,
          ...this.filterParam,
          ...this.params,
        },
      };
      this.$api.logisticsDataUpload.physicalExam
        .getPhysicalExam(params)
        .then(({ data: { list, total } }) => {
          this.tableData =
            list.map((item, index) => ({
              ...item,
              isShow: false,
              orginIdCrad: item.idCard,
              idCardDecoded: "",
            })) ||
            [] ||
            [];
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //是否具有待办任务操作权限
    getPermission() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.infoList.accountingMonth,
        type: "170",
      };
      this.$api.logisticsWorkbench.agencyPermission(params).then((res) => {
        this.permission = res.data;
      });
    },
    //搜索
    onSearch(name, data) {
      // 每次搜索都初始化搜索条件，重新添加合法的条件
      this.filterParam = {};
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.filterParam[name] = data;
      }
      this.pageNum = 1;
      this.getList();
      this.getDebitList();
    },
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data) {
        if (Array.isArray(data) && data.length > 0) this.params[name] = data;
      }
    },
    //身份证解码
    toggleEye(row) {
      if (row.isShow) {
        row.idCard = row.orginIdCrad;
        row.isShow = false;
      } else {
        if (row.idCardDecoded) {
          row.idCard = row.idCardDecoded;
          row.isShow = true;
          return;
        }
        this.$api.information.employee.decrypt(row.idCardRsa).then((res) => {
          row.idCard = res.data;
          row.idCardDecoded = res.data;
          row.isShow = true;
        });
      }
    },
    //重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.$refs.childrenStaffNames.resetFilter();
      this.$refs.childrenStaffCodes.resetFilter();
      this.filterParam = {};
      this.params = {};
      this.onSearch();
    },
    // 获取底部数据信息
    getDebitList() {
      let params = {
        factoryId: this.factoryId,
        accountingMonth: this.infoList.accountingMonth,
        ...this.filterParam,
        ...this.params,
      };
      this.$api.logisticsDataUpload.physicalExam.physicalExamStatistic(params).then((res) => {
        if (res.code == 200) {
          this.debitInfo = res.data
            ? res.data
            : (this.debitInfo = {
                sysTotal: 0,
                totalSocialSecurity: 0,
              });
        }
      });
    },
    //编辑
    handleEdit(row) {
      this.visible = true;
      this.formData = {
        id: row.id || "",
      };
    },
    handleImport() {
      this.title = "导入";
      this.ImportVisible = true;
    },
    handleCancel(type) {
      this.visible = false;
      if (type == "cancel") return;
      this.getList();
      this.getDebitList();
    },
    onSizeChange(pageSize) {
      this.pageSize = pageSize;
      // 切换每页条数后从第一页查询
      this.pageNum = 1;
      this.getList();
    },
    onNumChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    cancel(value) {
      this.ImportVisible = value;
    },
    confirm(value) {
      this.ImportVisible = value;
    },
  },
};
</script>
<style lang="stylus" scoped>
>>> .el-form--inline .el-form-item {
  margin-right: 40px;
}

ul {
  list-style: none;
  display: flex;
  padding: 0;
}

i {
  font-style: normal;
}

ul {
  list-style: none;
  display: flex;
  padding: 0;
}

i {
  font-style: normal;
}

>>>.table-panel-footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  ul {
    display: flex;

    li {
      white-space: nowrap
      padding: 0 10px;
    }
  }
}
>>>.el-table__row {
  td {
    &:nth-child(5) {
      .cell {
        display: flex;
        justify-content: space-between;

        .idCardBox {
          width: 80%;

          .idCard {
            display: block;
            cursor: pointer;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
        }
      }
    }
  }
}
</style>
