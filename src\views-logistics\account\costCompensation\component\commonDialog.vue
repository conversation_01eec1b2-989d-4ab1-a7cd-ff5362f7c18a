<template>
  <qDialog
    :visible="isVisible"
    :innerScroll="true"
       :innerHeight="80"
    :title="title"
    width="400px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <!-- <p v-if="title == '批量核算'">
      是否批量核算当前选中内容，设定在执行月份中全部还清？
    </p> -->
    <p v-if="title == '批量退回'">是否确认批量退回?</p>
    <p v-if="title == '批量删除'">是否确认批量删除?</p>
   <p v-if="auditStatus == '1'&&title !== '批量退回'">是否确认退回</p>
    <p v-if= "auditStatus == '2'">退回后，已处理内容会被删除</p>
  </qDialog>
</template>

<script>
export default {
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    info: [Object],
    filterParam: {
      type: Object,
      required: {},
    },
    selectall: {
      type: Boolean,
      required: false,
    },
    auditStatus:{
      type: String,
      required: true,
    }
  },
  data() {
    return {
      isLoading: false,
    };
  },
  methods: {
    handleCancel() {
      this.$emit("cancel", {
        type: "cancel",
        isVisible: false,
      });
    },

    handleConfirm() {
      this.isLoading = true;

      let params = { isAll: "0", ids: this.info.idList };
      if (this.selectall) {
        //全选
        params = {
          isAll: 1,
          ...this.filterParam,
        };
      }
      switch (this.title) {
        case "批量核算":
          this.$api.logisticsInformation.costCompensation
            .batchBusinessAccounting(params)
            .then(({ data }) => {
              this.$notify.success({
                title: "成功",
                message: data,
              });
              this.$emit("cancel", {
                type: "confirm",
                isVisible: false,
              });
            })
            .finally(() => {
              this.isLoading = false;
            });
          break;
        case "退回":
      let retutnApi = this.auditStatus == "1" ? "backBpmDeduct" : "rollBackNoAccounting";
          this.$api.logisticsInformation.costCompensation[retutnApi]({ id: this.info.id })
            .then(() => {
              this.$notify.success({
                title: "成功",
                message: "退回成功",
              });
              this.$emit("cancel", {
                type: "confirm",
                isVisible: false,
              });
            });
          break;

        case "批量退回":
          this.$api.logisticsInformation.costCompensation
            .batchBackBpmDeduct(params)
            .then(({ data }) => {
              this.$notify.success({
                title: "成功",
                message: data,
              });
              this.$emit("cancel", {
                type: "confirm",
                isVisible: false,
              });
            })
            .finally(() => {
              this.isLoading = false;
            });
          break;

        default:
          this.$api.logisticsInformation.costCompensation
            .batchDeleteBpmDeduct(params)
            .then(({ data }) => {
              this.$notify.success({
                title: "成功",
                message: data,
              });
              this.$emit("cancel", {
                type: "confirm",
                isVisible: false,
              });
            })
            .finally(() => {
              this.isLoading = false;
            });
          break;
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
