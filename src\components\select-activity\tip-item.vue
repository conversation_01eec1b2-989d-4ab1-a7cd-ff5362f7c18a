<template>
  <div class="tip-item">
    <span>{{item.name}}</span>
    <i class="el-icon-close" @click="remove"></i>
  </div>
</template>

<script>
export default {
  name: 'TipItem',
  props: {
    item: Object
  },
  methods: {
    remove() {
      this.$emit('remove', this.item.id);
    }
  }
}
</script>

<style lang="stylus" scoped>
.tip-item
  display flex
  padding 5px 10px
  &:hover
    background-color #F5F7FA
  span
    flex 1
    max-width 170px
    overflow hidden
    white-space nowrap
    text-overflow ellipsis
  i
    width 10px
    padding 0 5px
    &:hover
      cursor pointer
</style>