<template>
  <qDialog
    :visible="isVisible"
    :innerScroll="true"
    :innerHeight="450"
    title="新增"
    width="850px"
    :isLoading="isLoading"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :before-close="handleCancel"
  >
    <el-form
      :model="addForm"
      ref="addForm"
      label-width="120px"
      :rules="rules"
      size="small"
    >
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item class="processCode" label="流程编号:">
            {{ addForm.processCode }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="核算工厂:" prop="factoryId">
            <el-select
              v-model="addForm.factoryId"
              filterable
              clearable
              placeholder="请选择核算工厂" 
            >
              <el-option
                v-for="item in tabList"
                :key="item.value"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
       <el-row :gutter="10">
        <el-col :span="12">
         <el-form-item label="分配方式:" prop="allotMethod">
            <el-select
              v-model="addForm.allotMethod"
              filterable
              clearable
              @change="changeAllotMethod"
              placeholder="请选择分配方式" 
            >
              <el-option
                v-for="item in allotMethodList"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
         <el-form-item label="厂牌编号:" prop="staffCode"  v-show="addForm.allotMethod=='person'">
            <el-input
              v-model.trim="addForm.staffCode"
              clearable
              placeholder="请输入厂牌编号"
            >
              <template slot="append">
                <el-button type="primary" @click="searchStaffCode">
                  查询
                </el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
           <el-form-item label="类型:" prop="type">
            <el-select
              v-model="addForm.type"
              filterable
              clearable
              placeholder="请选择类型" 
            >
              <el-option
                v-for="item in typeList"
                :key="item.name"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="processCode" label="员工姓名:" v-show="addForm.allotMethod=='person'">
            {{ addForm.staffName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
          <el-col :span="12">
          <el-form-item label="核算工序:" prop="processId">
            <el-select
              v-model="addForm.processId"
              filterable
              clearable
              placeholder="请选择核算工序"
            >
              <el-option
                v-for="item in processList"
                :key="item.value"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="执行年月:" prop="accountingMonth">
            <el-date-picker
              v-model="addForm.accountingMonth"
              type="month"
              :picker-options="pickerOptions"
              value-format="yyyy-MM"
              placeholder="选择日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
       

      </el-row> 
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="增扣金额:" prop="amount">
            <el-input
              oninput="if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"
              v-model="addForm.amount"
              clearable 
              placeholder="请输入增扣金额"
            >
             <template slot="append">元</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item> </el-form-item>
        </el-col>
      </el-row> 

      <el-row>
        <el-col :span="24">
          <el-form-item class="processCode" label="备注说明:" prop="remarks">
            <el-input
              type="textarea"
              v-model="addForm.remarks"
              resize="none"
              rows="3"
              show-word-limit
              maxlength="50"
              placeholder=""
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </qDialog>
</template>

<script>
import moment from "moment";
import {  moneyDelete } from "@/utils";
import NP from "number-precision";
NP.enableBoundaryChecking(false);
export default {
  name: "addCost",
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
        typeList: [
        {
          id: "plus",
          name: "增补",
        },
        {
          id: "minus",
          name: "扣减",
        }
      ], 
      addForm: {
        processId: "",
        factoryId: "",
        staffCode: "",
        staffName: "",
        accountingMonth:  moment().subtract(1, "months").format("YYYY-MM"),
        type:"",
        allotMethod: "person", 
        amount:"",
        remarks: "",
      },
      newStaffCode: "",
      proof: [],
      fileList: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      rules: {
        factoryId: [
          { required: true, message: "请选择核算工厂", trigger: "change" },
        ],
        processId: [
          { required: true, message: "请选择核算工序", trigger: "change" },
        ],
        allotMethod: [
          { required: true, message: "请选择分配方式", trigger: "change" },
        ],
        staffCode: [
          { required: true, message: "请输入厂牌编号", trigger: "blur" },
        ],
        accountingMonth: [
          { required: true, message: "请选择执行年月", trigger: "change" },
        ],

        type: [
          { required: true, message: "请选择扣款类型", trigger: "change" },
        ],
        amount: [
          { required: true, message: "请输入扣款金额", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (!/^-?\d{1,9}(\.\d{1,2})?$/.test(moneyDelete(value))) {
                callback(
                  new Error("小数点前面仅支持5位数,小数点后面仅支持2位数")
                );
              }
              callback();
            },
            trigger: "blur",
          },
        ],
      },
       //分配方式
      allotMethodList: Object.freeze([
        {
          name: "集体",
          value: "group",
        },
        {
          name: "个人",
          value: "person",
        }
      ]), 
      tabList: [],
      isLoading: false,
      num: 1,
    };
  },
  created() {
    this.$nextTick(() => {
      this.$api.plateTypeSystemManage.getBasicPermission
        .getBasicPermissionAll()
        .then((res) => {
          this.tabList =
            res.data.map((item) => ({
              label: item.name,
              name: item.name,
              id: item.id,
              ...item
            })) || [];
        });
    });
  },
  computed:{
    processList() { 
       return  (this.addForm.factoryId &&
            this.tabList.find((item) => item.id == this.addForm.factoryId)
              .process) ||
          [] 
    },
  },
  methods: {
   changeAllotMethod(value){
    if(value=='person'){
      this.rules={
        factoryId: [
          { required: true, message: "请选择核算工厂", trigger: "change" },
        ],
        allotMethod: [
          { required: true, message: "请选择分配方式", trigger: "change" },
        ],
        staffCode: [
          { required: true, message: "请输入厂牌编号", trigger: "blur" },
        ],
        accountingMonth: [
          { required: true, message: "请选择执行年月", trigger: "change" },
        ],
         processId: [
          { required: true, message: "请选择核算工序", trigger: "change" },
        ],
        type: [
          { required: true, message: "请选择扣款类型", trigger: "change" },
        ],
        amount: [
          { required: true, message: "请输入扣款金额", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (!/^-?\d{1,5}(\.\d{1,2})?$/.test(moneyDelete(value))) {
                callback(
                  new Error("小数点前面仅支持5位数,小数点后面仅支持2位数")
                );
              }
              callback();
            },
            trigger: "blur",
          },
        ],
      }
    }else{
      this.rules={
        factoryId: [
          { required: true, message: "请选择核算工厂", trigger: "change" },
        ],
          processId: [
          { required: true, message: "请选择核算工序", trigger: "change" },
        ],
        allotMethod: [
          { required: true, message: "请选择分配方式", trigger: "change" },
        ], 
        accountingMonth: [
          { required: true, message: "请选择执行年月", trigger: "change" },
        ],

        type: [
          { required: true, message: "请选择扣款类型", trigger: "change" },
        ],
        amount: [
          { required: true, message: "请输入扣款金额", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (!/^-?\d{1,5}(\.\d{1,2})?$/.test(moneyDelete(value))) {
                callback(
                  new Error("小数点前面仅支持5位数,小数点后面仅支持2位数")
                );
              }
              callback();
            },
            trigger: "blur",
          },
        ],
      }
    } 
   },
    //根据厂牌编号查询
    searchStaffCode() {
      this.$refs.addForm.validateField(["staffCode"], async (valid) => {
        if (valid) return;
        await this.$api.information.employee
          .employeeDetails({ staffCode: this.addForm.staffCode })
          .then(({ data }) => {
            this.newStaffCode = data.staffCode || "";
            this.addForm = {
              ...this.addForm,
              staffName: (data && data.staffName) || "",
            };
          });
      });
    },
   
 
  
  
    handleCancel() { 
      this.$emit("cancel", {
        type: "cancel",
        isVisible: false,
      });
    },
    async handleConfirm() {
     this.$refs.addForm.validate((valid) => {
        if (!valid) return; 
        this.isLoading = true 
        let params = {
          ...this.addForm, 
          accountingMonth: moment(this.addForm.accountingMonth).format("YYYY-MM"), 
          amount: moneyDelete(this.addForm.amount)
        };
        this.$api.plateTypeInformation.orderBomRevision.orderBomRevisionAdd(params).then((data) => {
          this.$notify.success({
            title: "成功",
            message: "新增成功",
          });
          this.$emit("cancel", {
            type: "confirm",
            isVisible: false,
          });
        }).finally(() => {
          this.isLoading = false;
        });
      });
    },
  },
};
</script>

<style lang="stylus" scoped>
>>>.el-form-item__label {
  text-align: left;
}

.processCode {
  >>>.el-form-item__label {
    &::before {
      content: '*';
      color: #F23D20;
      margin-right: 4px;
      visibility: hidden;
    }
  }
}

>>>.el-input-group__append {
  background: #24c69a;
  color: #fff;

  .el-button {
    padding: 5px 10px;
  }
}

>>>.el-select, >>>.el-date-editor {
  width: 100%;
}

.el-upload__tip {
  padding-left: 10px;
  display: inline-block;
  color: #24c69a;
}

>>>.el-textarea__inner {
  padding-right: 50px;
}

.el-textarea {
  >>>.el-input__count {
    right: 20px !important;
  }
}
.details {
  display: flex;
  padding-bottom: 18px;

  &_content {
    flex: 1;
    display: flex;
    justify-content: space-between;

    >.el-select, >.el-input, >span {
      flex: 1 0 30%;
    }
  }

  &_btn {
    width: 20%;

    >.el-button {
      mar;
    }
  }
}

/deep/.el-input__inner{
  background:white !important
}

</style>
